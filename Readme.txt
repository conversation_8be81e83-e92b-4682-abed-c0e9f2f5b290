
# AI Operations Hub: Comprehensive Enterprise AI Platform
This system is a comprehensive enterprise-grade platform that provides intelligent LLM routing, autonomous task execution, multi-agent orchestration, and responsible AI governance. The platform operates in multiple modes:

1. DIRECT LLM OPTIMIZATION: Intelligent semantic routing of individual LLM requests with cost-performance optimization
2. AUTONOMOUS WORKFLOW EXECUTION: Goal-driven planning engine that decomposes complex objectives into executable task workflows
3. MULTI-AGENT ORCHESTRATION: Complex collaborative AI workflows with agent coordination and communication
4. RESPONSIBLE AI GOVERNANCE: Comprehensive bias detection, explainability, robustness testing, and compliance monitoring
5. PROMPTOPS MANAGEMENT: Git-based prompt version control with A/B testing and GitOps integration
6. MODEL CONTEXT PROTOCOL (MCP) INTEGRATION: Universal tool access for Claude Desktop and other MCP clients
7. USER-FRIENDLY CHAT INTERFACE: ChatGPT-like interface with intelligent routing and real-time optimization

The system provides comprehensive logging, analytics, responsible AI governance, and enterprise-grade capabilities for modern AI operations.

1. AI Operations Hub System Architecture

PRODUCTION DEPLOYMENT: https://scale-llm.com

Core Services Architecture:
+-----------------------+
|                       |
|    User/Client        |
| - Web Dashboard       |
| - Chat Interface      |
| - MCP Clients         |
| - API Integrations    |
+-----------+-----------+
            |
            | HTTP(S) Requests:
            | - LLM APIs: /v1/chat/completions, /v1/completions, /v1/embeddings,
            |   /v1/images/generations, /v1/audio/*, /v1/moderations, /v1/fine-tuning/jobs
            | - Analytics: /v1/analytics/cache, /v1/cache/manage
            | - Planning: /v1/goals, /v1/goals/{id}/plan, /v1/goals/{id}/execute
            | - Multi-Agent: /v1/agents, /v1/workflows
            | - MCP: WebSocket connections to /mcp/connect, /mcp/status
            | - PromptOps: /promptops/*, /gitops/*
            V
+-----------+-----------+
|                       |
|   Proxy Gateway       |
|   (Port 8080)         |
| - Intelligent Routing |
| - MCP Host            |
| - Governance          |
| - Streaming Support   |
+-----------+-----------+
    |       |       |
    |       |       | Routes to Core Services:
    |       |       | - AI Optimizer (8085)
    |       |       | - Planning Service (8082)
    |       |       | - Multi-Agent Orchestrator (8083)
    |       |       | - Responsible AI Services (8084-8087)
    |       |       |
    V       V       V
+---+-------+-------+---+
|                       |
|   AI Optimizer        |
|   (Port 8085)         |
| - Semantic Routing    |
| - Cost Optimization   |
| - MCP Server          |
+-----------+-----------+
            |
            | Connects to External LLM Providers:
            | - OpenAI API
            | - Google Gemini API
            | - Anthropic Claude API
            |
+-----------+-----------+
|                       |
|   Planning Service    |
|   (Port 8082)         |
| - Goal Decomposition  |
| - Task Orchestration  |
| - MCP Server          |
+-----------+-----------+
            |
+-----------+-----------+
|                       |
| Multi-Agent Orchestrator |
|   (Port 8083)         |
| - Agent Registry      |
| - Workflow Designer   |
| - Agent Marketplace   |
+-----------+-----------+

Responsible AI Services:
+-----------+-----------+       +-----------+-----------+
|                       |       |                       |
| Bias Detection (8084) |       | Explainability (8085) |
| - AIF360/Fairlearn    |       | - LIME/SHAP           |
| - Fairness Metrics    |       | - Feature Importance  |
+-----------+-----------+       +-----------+-----------+

+-----------+-----------+       +-----------+-----------+
|                       |       |                       |
| Robustness Test (8086)|       | Compliance (8087)     |
| - Adversarial Testing |       | - EU AI Act           |
| - Vulnerability Scan  |       | - NIST AI RMF         |
+-----------+-----------+       +-----------+-----------+

Supporting Services:
+-----------+-----------+       +-----------+-----------+
|                       |       |                       |
| Evaluation (8088)     |       | Dashboard API (8089)  |
| - Quality Assessment  |       | - Analytics           |
| - MCP Server          |       | - Report Generation   |
+-----------+-----------+       +-----------+-----------+

+-----------+-----------+       +-----------+-----------+
|                       |       |                       |
| Policy Manager (8090) |       | Integration (8091)    |
| - Rule Management     |       | - GitOps Integration  |
| - Enforcement Logic   |       | - MCP Server          |
+-----------+-----------+       +-----------+-----------+

Data & Analytics Layer:
+-----------+-----------+       +-----------+-----------+
|                       |       |                       |
| Data Processor (8092) |       | Frontend (3000)       |
| - Event Processing    |       | - React Dashboard     |
| - Real-time Analytics |       | - Chat Interface      |
+-----------+-----------+       +-----------+-----------+

Storage & Infrastructure:
+-----------+-----------+       +-----------+-----------+
|                       |       |                       |
|    Redis Cache        |       |    ClickHouse         |
| - Live Metrics        |       | - Analytics Storage   |
| - Model Profiles      |       | - Time-series Data    |
| - Analysis Cache      |       | - Performance History |
+-----------+-----------+       +-----------+-----------+

+-----------+-----------+       +-----------+-----------+
|                       |       |                       |
|  Kafka Message Broker |       |    Prometheus         |
| - Event Streaming     |       | - Metrics Collection  |
| - Real-time Data      |       | - Performance Monitor |
+-----------+-----------+       +-----------+-----------+

2. Component Descriptions and Functionality

a. User/Client
Functionality: Comprehensive access to AI Operations Hub through multiple interfaces and integration methods.

Integration Modes:
- Chat Interface: User-friendly ChatGPT-like interface with intelligent routing
- Direct LLM API: Traditional text generation, image analysis, etc.
- Autonomous Planning: High-level goal submission for multi-step workflow execution
- Multi-Agent Orchestration: Complex collaborative AI workflows
- MCP Integration: Tool access through Claude Desktop and other MCP clients
- PromptOps Management: Git-based prompt version control and optimization

Communication:
- Chat Interface: Web dashboard at https://scale-llm.com
- Direct LLM APIs: Full OpenAI compatibility with intelligent routing
  * Chat: /v1/chat/completions, /v1/completions
  * Embeddings: /v1/embeddings
  * Images: /v1/images/generations
  * Audio: /v1/audio/speech, /v1/audio/transcriptions, /v1/audio/translations
  * Moderation: /v1/moderations
  * Fine-tuning: /v1/fine-tuning/jobs
  * Analytics: /v1/analytics/cache, /v1/cache/manage
- Planning: HTTP(S) POST to /v1/goals, /v1/goals/{id}/plan, /v1/goals/{id}/execute
- Multi-Agent: HTTP(S) POST to /v1/agents, /v1/workflows
- MCP: WebSocket connections to /mcp/connect, /mcp/status
- PromptOps: HTTP(S) requests to /promptops/*, /gitops/*

b. Proxy Gateway (Port 8080) [COMPREHENSIVE ENHANCEMENT]
Functionality: The central intelligent routing hub for all AI Operations Hub capabilities.

Core Capabilities:
- Intelligent LLM Routing: Semantic prompt analysis with optimal model selection
- MCP Host Integration: WebSocket connections for Model Context Protocol clients
- Governance Enforcement: Real-time policy enforcement and compliance checking
- Streaming Support: Real-time streaming and non-streaming response handling
- Conversation Management: Multi-turn conversation context with conversation_id support
- Request Caching: Advanced caching with semantic similarity matching

Routing Intelligence:
- Semantic Analysis: Automatic prompt analysis using external LLMs for intent detection
- Capability-Based Selection: Routes based on model capabilities rather than just cost
- Multi-Objective Optimization: Considers cost, performance, quality, and robustness
- Fallback Mechanisms: Robust keyword-based analysis when external analysis fails

Service Integration:
- AI Optimizer: Routes requests for intelligent model selection
- Planning Service: Handles autonomous goal-driven workflows
- Multi-Agent Orchestrator: Manages complex collaborative workflows
- Responsible AI Services: Enforces governance policies and compliance
- External LLM Providers: Direct integration with OpenAI, Google Gemini, Anthropic Claude

Communication:
- Receives: HTTP(S) and WebSocket requests from all client types
- Routes to: All core services based on request type and analysis
- Communicates with Redis: Model profiles, policies, live metrics, analysis cache
- Communicates with Kafka: Comprehensive logging of all request types and governance events

b2. Planning Service (Go Lang Service) [NEW COMPONENT]
Functionality: Autonomous Task Decomposition & Planning Engine for goal-driven workflow execution.

Core Capabilities:
- Goal Parsing: Converts natural language objectives into structured goals with success criteria and constraints
- Task Decomposition: Breaks down complex goals into executable tasks using LLM-assisted planning and templates
- Execution Orchestration: Manages task dependencies, parallel execution, and resource allocation
- State Management: Maintains persistent execution context across multi-step workflows
- Progress Monitoring: Provides real-time tracking of goal execution and task completion
- Adaptive Planning: Supports re-planning and failure recovery during execution

Task Execution Framework:
- LLM Tasks: Route through AI Optimizer for cost-optimized language model calls
- Data Tasks: Execute database queries and data retrieval operations
- API Tasks: Make HTTP requests to external services
- Analysis Tasks: Perform data analysis and statistical operations
- Validation Tasks: Verify results against success criteria
- Aggregation Tasks: Combine results from multiple tasks

Communication:
- Receives: HTTP(S) requests from Proxy Gateway for planning operations
- Sends: HTTP(S) requests to AI Optimizer for LLM routing during task execution
- Communicates with Redis: Stores execution context, goal state, and task results
- Communicates with Kafka: Publishes planning events and execution logs

c. Mock Backend GPU 1 & 2 (Simulated AI Services)
Functionality: Simulate actual AI model inference endpoints for both direct LLM requests and planning task execution.

Communication:

Receives: HTTP(S) requests from Proxy Gateway (direct LLM) and Planning Service (task execution).

Sends: HTTP(S) responses back to requesting services.

Monitored by: Prometheus (via cAdvisor metrics).

d. Policy Manager (Go Lang Service)
Functionality: Manages the "source of truth" for dynamic routing policies and model profiles.

Watches Firestore for any changes (additions, modifications, deletions) to policies and model profiles.

Propagates these changes to Redis by setting/deleting individual keys.

Publishes these changes to Redis Pub/Sub channels for real-time updates.

Exposes a basic HTTP API for manual CRUD operations (though typically automated via Firestore).

Communication:

Communicates with Firestore: Uses the Firestore API to set up snapshot listeners for policies and model_profiles collections.

Communicates with Redis:

SET operations to store Policy and ModelProfile objects as individual keys (e.g., policy:<ID>, model_profile:<ID>).

DEL operations to remove keys when documents are deleted in Firestore.

PUBLISH operations to policy_updates and model_profile_updates Pub/Sub channels.

e. Firestore (Google Cloud Service)
Functionality: A NoSQL cloud database that serves as the authoritative source of truth for Policies and Model Profiles.

Communication:

Receives: API calls from Policy Manager (for snapshot listeners, and potentially CRUD operations).

f. Redis (In-Memory Data Store) [ENHANCED]
Functionality: Serves multiple critical roles for both LLM optimization and planning workflows:

Cache: Stores Policies and Model Profiles for low-latency routing decisions.

Real-time Metrics Store: Stores Live Latency data for each backend.

Planning State Store: Maintains execution context, goal state, and task results for autonomous workflows.

Pub/Sub Broker: Facilitates real-time communication for cache synchronization and planning updates.

Communication:

Receives: SET/DEL/PUBLISH commands from Policy Manager, Data Processor, and Planning Service.

Sends: GET responses and Pub/Sub messages to all services including Planning Service.

g. Kafka (Message Broker) [ENHANCED]
Functionality: Provides highly scalable message queuing for both Inference Logs and Planning Events. Ensures reliable delivery and processing of all system events.

Communication:

Receives: Messages from Proxy Gateway (LLM logs) and Planning Service (planning events).

Sends: Messages to Data Processor for both LLM and planning analytics.

h. Data Processor (Go Lang Service) [ENHANCED]
Functionality: Processes both inference logs and planning events from Kafka, providing comprehensive analytics.

LLM Analytics:
- Consumes Inference Logs from Kafka
- Queries Prometheus for CPU/Memory usage during inference
- Calculates total cost (base cost + resource cost)
- Updates Live Latency in Redis

Planning Analytics:
- Consumes Planning Events from Kafka (goal creation, task execution, workflow completion)
- Tracks goal success rates, task performance, and workflow costs
- Calculates planning effectiveness metrics

Inserts enriched data into ClickHouse for both LLM and planning analytics.

Communication:

Receives: Messages from Kafka (Consumer).

Sends: HTTP GET requests to Prometheus API.

Communicates with Redis: SET operations to update Live Latency keys.

Communicates with ClickHouse: TCP/HTTP connections to insert processed logs.

i. ClickHouse (Analytics Database) [ENHANCED]
Functionality: High-performance columnar database storing both LLM inference logs and planning analytics for comprehensive system insights.

Data Storage:
- LLM inference logs for traditional optimization analytics
- Planning events: goal creation, task execution, workflow completion
- Goal success rates, task performance metrics, and workflow costs
- Historical data for both LLM routing and planning optimization

Communication:

Receives: TCP/HTTP connections from Data Processor for both LLM and planning data.

Sends: Query results to AI Optimizer and Dashboard API for unified analytics.

j. Prometheus (Monitoring System)
Functionality: A time-series database and monitoring system. It continuously scrapes metrics from configured targets (e.g., Kubernetes pods running Mock Backend GPU services via cAdvisor metrics).

Communication:

Scrapes: HTTP endpoints of Mock Backend GPU 1/2 (and other Kubernetes components) to collect metrics.

Serves: HTTP API queries from Data Processor.

k. AI Optimizer (Go Lang Service) [ENHANCED] - Intelligent Routing & Planning Component
Functionality: Enhanced analytics and routing engine supporting both LLM optimization and planning-aware decisions.

LLM Optimization:
- Periodically runs optimization cycles for backend performance analysis
- Fetches historical inference logs from ClickHouse
- Analyzes backend performance metrics and real-time latency
- Makes routing decisions for individual LLM requests

Planning Integration:
- Provides cost-optimized LLM routing for planning task execution
- Considers workflow-level optimization when routing planning-driven requests
- Supports planning-specific routing policies and preferences
- Integrates with Planning Service for task-aware routing decisions

Exposes /route endpoint for both Proxy Gateway and Planning Service routing requests.

Communication:

Queries ClickHouse: Fetches historical data for its internal analysis.

Queries Redis: Fetches live data for its internal analysis.

l. Dashboard API (Go Lang Service) [ENHANCED] - Unified Frontend Backend
Functionality: Enhanced backend serving both LLM optimization and planning analytics to the React dashboard.

API Endpoints:
- Traditional: /api/inference_summary, /api/time_series_summary for LLM analytics
- Planning: /api/planning_metrics, /api/goal_analytics, /api/workflow_performance for planning insights

Data Sources:
- Queries ClickHouse for both LLM and planning historical data
- Queries Redis for real-time metrics including planning execution status

Communication:

Receives: HTTP requests for both LLM and planning dashboard data.

Queries ClickHouse: Historical data for LLM optimization and planning analytics.

Queries Redis: Live metrics for both LLM routing and planning execution.

m. React App (Frontend UI) [ENHANCED]
Functionality: Unified user interface supporting both LLM optimization and autonomous planning workflows.

LLM Analytics Features:
- View aggregated inference summaries and trends
- Visualize metrics (requests, latency, tokens, cost)
- Apply filters and manage routing policies/model profiles

Planning Dashboard Features:
- Goal creation and management interface
- Real-time workflow execution monitoring
- Task dependency visualization and progress tracking
- Planning effectiveness metrics and cost analysis
- Workflow templates and success rate analytics

Communication: Makes API calls for both LLM optimization and planning data through the unified API Gateway.

n. Kafka Topic Creator (Utility) [ENHANCED]
Functionality: Enhanced utility ensuring both LLM and planning-related Kafka topics are created.

Topics Created:
- inference-logs: For traditional LLM request logging
- planning-events: For goal creation, task execution, and workflow completion events
- planning-metrics: For planning performance and analytics data

Communication:

Communicates with Kafka: Creates topics and sends test messages for both LLM and planning workflows.




How the Enhanced Routing Works:

The system now supports TWO types of routing:

1. DIRECT LLM ROUTING: Traditional model routing for individual requests
2. PLANNING-AWARE ROUTING: Intelligent routing for autonomous workflow tasks

DIRECT LLM ROUTING:
The proxy-gateway determines whether to route requests to mock backends or real external models based on the BackendURL defined within the ModelProfile selected for a given request.

PLANNING-AWARE ROUTING:
The Planning Service routes task-specific LLM calls through the AI Optimizer, which considers:
- Task type and complexity
- Workflow-level cost optimization
- Planning-specific routing policies
- Resource allocation across the entire goal execution

Here's a breakdown of the routing logic in proxy-gateway/main.go and how this distinction is made:

The selectBackend Function: The Brain of Routing
The selectBackend function is where the core routing decision is made. It follows a specific hierarchy:

Policy Evaluation (Highest Priority):

The proxy-gateway first iterates through all configured policies, sorted by their Priority (higher priority evaluated first).
It checks if any ROUTE policy's Criteria match the incoming request (e.g., model_requested, client_ip, data_sensitivity, prompt_length).
If a ROUTE policy matches, the policy.BackendID is used to look up a corresponding ModelProfile from its in-memory cache.
Fallback 1: Requested Model Name or Alias:

If no explicit ROUTE policy matches, the proxy then attempts to find a ModelProfile whose Name or Aliases directly match the model specified in the incoming LLM request's body (e.g., "gpt-4o-mini", "gemini-flash").
Fallback 2: AI Optimizer's Optimal Backend:

If neither policies nor a direct model name/alias match, the proxy-gateway checks Redis for the REDIS_OPTIMAL_BACKEND_KEY ("routing:optimal_backend_id"). The ai-optimizer service is responsible for publishing the ID of the currently "optimal" backend to this key.
If an optimal backend ID is found, the proxy retrieves its ModelProfile.
Fallback 3: First Available Model Profile:

As a last resort, if none of the above criteria yield a backend, the proxy-gateway will simply pick the first ModelProfile it finds in its cache. This serves as a safety net to ensure requests are always routed if possible.
How BackendURL Dictates the Target
Once a ModelProfile is selected by any of the above mechanisms, the SendRequestToBackend function is called. This function uses the BackendURL field from the chosen ModelProfile to construct the actual HTTP request that is sent to the LLM.

Routing to Mock Backends:
Your mock backends (e.g., mock-backend-gpu1, mock-google, mock-anthropic, mock-openai) are typically deployed within your Kubernetes cluster. Their ModelProfiles will have BackendURLs pointing to their internal Kubernetes service names and ports.

Example: http://mock-backend-gpu1:5001/predict or http://mock-openai:5003/v1/chat/completions
Routing to Real External Models:
The ModelProfiles for real OpenAI, Google, or Anthropic models will have BackendURLs pointing to their public internet endpoints.

Example: https://api.openai.com/v1/chat/completions
Example: https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent
Example: https://api.anthropic.com/v1/messages
In Summary:
The routing decision is a cascade:

The proxy-gateway figures out which ModelProfile to use based on policies, requested model name, or the AI optimizer's recommendation.
Once a ModelProfile is chosen, the proxy-gateway simply uses the BackendURL specified within that ModelProfile to send the request.
So, if a ModelProfile's BackendURL is set to an internal Kubernetes service address, the request goes to your mock. If it's set to an external API endpoint, it goes to the real provider. The choice between mock and real is entirely driven by how you define and manage the BackendURLs in your ModelProfiles (which are stored in Redis).






Policy Form Fields Explained
This section details the fields used when defining or editing a routing policy.

Name

Purpose: A human-readable identifier for the policy. This should be concise and descriptive.

Example: "High-Priority GPT-4 Requests," "Cost-Optimized Llama-2 Routing," "Block Malicious IPs."

Description

Purpose: Provides more detailed information about the policy's intent, the specific scenarios it covers, or any caveats. This helps other team members understand its function without diving into the code.

Example: "Routes all production GPT-4 large model requests to dedicated GPU backends to minimize latency," or "Denies requests from known problematic IP ranges identified by security."

Action

Purpose: Defines what the router should do when an incoming inference request matches the policy's Criteria. This is the core outcome of the policy.

Possible Values:

ROUTE_TO_BACKEND: Send the request to the Backend ID specified in this policy.

REJECT: Block the request entirely (e.g., return a 403 Forbidden or 429 Too Many Requests).

OPTIMIZE_COST: Dynamically select the cheapest available backend from a pool of Model Profiles that meet the request's needs.

OPTIMIZE_LATENCY: Dynamically select the fastest available backend.

LOG_ONLY: Process the request as usual but specifically log its details for analysis without altering routing.

Example: "ROUTE_TO_BACKEND," "REJECT," "OPTIMIZE_COST."

Backend ID

Purpose: If the Action is set to ROUTE_TO_BACKEND, this field specifies the exact ID of the target model backend where the inference request should be sent. This ID would typically correspond to the id of one of your Model Profiles.

Example: "mock-backend-gpu1," "azure-openai-us-east," "on-prem-cpu-cluster."

Priority

Purpose: Determines the order in which policies are evaluated. When multiple policies could potentially match an incoming request, the policy with the higher priority (typically a lower number, like 1 being highest priority) is applied first.

Example: 1 (highest), 10, 100 (lowest). You'd want a "block malicious IPs" policy to have a higher priority than a general routing policy.

Rules (JSON String)

Purpose: This field is for more complex or custom routing logic that might not be easily expressed with simple key-value Criteria. By storing it as a JSON string, your backend can parse and interpret it for advanced scenarios.

Example: You might store a complex conditional expression, a weighted routing configuration, or parameters for A/B testing different backends.

Example Value: {"min_batch_size": 2, "max_concurrency": 5} or {"custom_function": "evaluate_user_tier"}.

Criteria (Repeating Group: Field, Operator, Value)

Purpose: These define the conditions that an incoming inference request must meet for the policy to be applied. A policy can have multiple criteria, which are usually evaluated with an "AND" logic (all criteria must be true).

Field: The name of the attribute or property of the incoming request you want to evaluate.

Example: "user_id," "model_name," "input_token_count," "origin_ip."

Operator: How you want to compare the Field to the Value.

Example: "=", "!=", ">", "<", "CONTAINS", "STARTS_WITH."

Value: The specific value you are comparing the Field against.

Example: "premium-user," "gpt-4-turbo," "1000," "***********."

Remove (Button for Criteria)

Purpose: Allows the user to remove a specific criterion from the policy.

This architecture provides a robust, scalable, and observable system for dynamic AI inference routing and performance analytics.
