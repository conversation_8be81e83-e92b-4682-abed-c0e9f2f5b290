# AI Operations Hub - Edition Deployment Guide

This guide explains how to deploy both Standard and Enterprise editions without conflicts.

## 🚨 Problem Summary

The original deployment had conflicts between Standard and Enterprise editions:

1. **Conflicting Ingress Rules**: Both editions tried to use the same API paths
2. **Missing Services**: Standard edition frontend called enterprise-only APIs
3. **503 Service Unavailable**: Frontend received errors for unavailable services
4. **Namespace Conflicts**: Services in different namespaces couldn't communicate properly

## ✅ Solution Overview

The solution implements:

1. **Edition-Specific Routing**: Each edition uses distinct URL prefixes
2. **Graceful Fallbacks**: Standard edition returns helpful messages for enterprise features
3. **Namespace Isolation**: Each edition runs in its own namespace
4. **Smart Frontend**: Frontend detects edition and adjusts API calls accordingly
5. **Cross-Namespace Communication**: Proxy services enable ingress routing across namespaces
6. **Static Asset Handling**: Proper rewrite rules for CSS/JS assets in standard edition

## 🏗️ Architecture

### Standard Edition
- **Namespace**: `standard-edition`
- **URL Prefix**: `/standard`
- **Services**: proxy-gateway, dashboard-api, policy-manager, redis, frontend
- **Features**: Basic AI optimization, cost tracking, simple PromptOps

### Enterprise Edition  
- **Namespace**: `default`
- **URL Prefix**: `/enterprise`
- **Services**: All standard services + integration, planning, evaluation, multi-agent
- **Features**: Full feature set including GitOps, advanced analytics, governance

## 📁 Key Files Modified

### 1. Ingress Configuration
- `k8s/standard-edition-api-ingress.yaml` - Routes `/standard/*` to standard services
- `k8s/enterprise-api-ingress.yaml` - Routes `/enterprise/*` to enterprise services

### 2. Frontend Configuration
- `k8s/standard-edition/frontend-standard.yaml` - Standard edition nginx config
- `k8s/frontend/src/components/PromptOpsDashboard.jsx` - Edition-aware API calls
- `k8s/frontend/src/hooks/usePromptOps.js` - Graceful fallback handling

### 3. Deployment Scripts
- `k8s/standard-edition/deploy-standard.sh` - Updated to include fallback services
- `k8s/scripts/validate-edition-deployment.sh` - Validation and conflict detection

## 🚀 Deployment Instructions

### Deploy Standard Edition Only

```bash
# Deploy standard edition
./deploy.sh -e standard

# Validate deployment
./k8s/scripts/validate-edition-deployment.sh

# Access at: https://scale-llm.com/standard
```

### Deploy Enterprise Edition Only

```bash
# Deploy enterprise edition  
./deploy.sh -e enterprise

# Validate deployment
./k8s/scripts/validate-edition-deployment.sh

# Access at: https://scale-llm.com/enterprise
```

### Deploy Both Editions (Coexistence)

```bash
# Deploy enterprise edition first
./deploy.sh -e enterprise

# Deploy standard edition
./deploy.sh -e standard

# Validate both deployments
./k8s/scripts/validate-edition-deployment.sh

# Access:
# - Standard: https://scale-llm.com/standard  
# - Enterprise: https://scale-llm.com/enterprise
```

## 🔧 Technical Details

### API Routing Strategy

#### Standard Edition (`/standard/api/*`)
```
/standard/api/prompts        → policy-manager-standard
/standard/api/policies       → policy-manager-standard  
/standard/api/model-profiles → policy-manager-standard
/standard/api/integration/*  → fallback service (returns unavailable message)
/standard/api/planning/*     → fallback service (returns unavailable message)
/standard/api/*              → proxy-gateway-standard
```

#### Enterprise Edition (`/enterprise/api/*`)
```
/enterprise/api/prompts      → dashboard-api
/enterprise/api/integration/* → integration-service
/enterprise/api/planning/*   → planning-service
/enterprise/api/*            → proxy-gateway
```

### Fallback Service

The standard edition includes a fallback service that returns JSON responses for enterprise-only APIs:

```json
{
  "status": "unavailable",
  "message": "This feature is available in Enterprise Edition", 
  "edition": "standard"
}
```

### Frontend Edition Detection

The frontend detects the edition using:
1. Environment variables (`REACT_APP_EDITION`, `VITE_APP_EDITION`)
2. URL path analysis (`window.location.pathname.startsWith('/standard')`)
3. API response inspection (checking for `edition: "standard"` in responses)

## 🧪 Testing & Validation

### Manual Testing

```bash
# Test standard edition
curl https://scale-llm.com/standard/health
curl https://scale-llm.com/standard/api/integration/gitops/status

# Test enterprise edition  
curl https://scale-llm.com/enterprise/health
curl https://scale-llm.com/enterprise/api/integration/gitops/status
```

### Automated Validation

```bash
# Run comprehensive validation
./k8s/scripts/validate-edition-deployment.sh
```

## 🐛 Troubleshooting

### Common Issues

1. **503 Service Unavailable**
   - Check if services are running: `kubectl get pods -n standard-edition`
   - Verify ingress rules: `kubectl get ingress --all-namespaces`

2. **Frontend Shows Enterprise Features in Standard**
   - Check environment variables in deployment
   - Verify frontend build used correct edition settings

3. **API Calls to Wrong Endpoints**
   - Check browser network tab for actual URLs being called
   - Verify `REACT_APP_API_BASE_URL` environment variable

### Debug Commands

```bash
# Check standard edition services
kubectl get all -n standard-edition

# Check enterprise edition services  
kubectl get all -n default -l edition=enterprise

# Check ingress rules
kubectl get ingress --all-namespaces -o wide

# Check logs
kubectl logs -f deployment/frontend-standard -n standard-edition
kubectl logs -f deployment/dashboard-api-standard -n standard-edition
```

## 📊 Monitoring

### Health Checks

- Standard: `https://scale-llm.com/standard/health`
- Enterprise: `https://scale-llm.com/enterprise/health`

### Service Status

```bash
# Standard edition status
kubectl get deployments -n standard-edition

# Enterprise edition status
kubectl get deployments -n default -l edition=enterprise
```

## 🔄 Switching Between Editions

### From Standard to Enterprise

```bash
# Keep standard running, deploy enterprise
./deploy.sh -e enterprise

# Both will be available at their respective URLs
```

### From Enterprise to Standard Only

```bash
# Remove enterprise edition
kubectl delete namespace default --cascade=foreground

# Deploy standard edition
./deploy.sh -e standard
```

## 📝 Next Steps

1. Test both editions thoroughly
2. Update API keys in both namespaces
3. Configure monitoring and alerting
4. Set up backup procedures for both editions
5. Document any custom configurations

## 🆘 Support

If you encounter issues:

1. Run the validation script: `./k8s/scripts/validate-edition-deployment.sh`
2. Check the troubleshooting section above
3. Review logs from affected services
4. Verify ingress and service configurations
