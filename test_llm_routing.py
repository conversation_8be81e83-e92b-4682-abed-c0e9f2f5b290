#!/usr/bin/env python3
"""
Simple diagnostic tool to test LLM routing through the proxy-gateway
"""

import requests
import json
import time

# Configuration
BASE_URL = "https://scale-llm.com"
TEST_PROMPT = "Summarize the provided content for a technical audience, highlighting key findings: This is a test prompt for debugging."

def test_llm_request(model="gpt-3.5-turbo"):
    """Test a direct LLM request through the proxy-gateway"""
    
    url = f"{BASE_URL}/v1/chat/completions"
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user", 
                "content": TEST_PROMPT
            }
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    print(f"Testing LLM request to {url}")
    print(f"Model: {model}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print("-" * 50)
    
    try:
        start_time = time.time()
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        end_time = time.time()
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Time: {end_time - start_time:.2f}s")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"Response: {json.dumps(response_data, indent=2)}")
                return True
            except json.JSONDecodeError:
                print(f"Response Body (not JSON): {response.text}")
                return False
        else:
            print(f"Error Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("Request timed out after 30 seconds")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"Connection error: {e}")
        return False
    except Exception as e:
        print(f"Unexpected error: {e}")
        return False

def test_health_endpoints():
    """Test health endpoints of various services"""
    
    endpoints = [
        "/health",
        "/api/summary",
        "/api/policies",
        "/api/model_profiles"
    ]
    
    print("Testing health endpoints...")
    print("=" * 50)
    
    for endpoint in endpoints:
        url = f"{BASE_URL}{endpoint}"
        try:
            response = requests.get(url, timeout=10)
            print(f"{endpoint}: {response.status_code}")
            if response.status_code != 200:
                print(f"  Error: {response.text[:200]}")
        except Exception as e:
            print(f"{endpoint}: ERROR - {e}")
    
    print("=" * 50)

def test_model_profiles():
    """Test model profiles endpoint"""
    
    url = f"{BASE_URL}/api/model_profiles"
    
    print("Testing model profiles...")
    print("-" * 50)
    
    try:
        response = requests.get(url, timeout=10)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            profiles = response.json()
            print(f"Found {len(profiles)} model profiles:")
            for profile in profiles:
                print(f"  - {profile.get('id', 'unknown')} ({profile.get('name', 'unknown')})")
                print(f"    Backend: {profile.get('url', 'unknown')}")
                print(f"    Status: {profile.get('status', 'unknown')}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error testing model profiles: {e}")
    
    print("-" * 50)

if __name__ == "__main__":
    print("LLM Routing Diagnostic Tool")
    print("=" * 50)
    
    # Test health endpoints first
    test_health_endpoints()
    
    # Test model profiles
    test_model_profiles()
    
    # Test LLM requests with different models
    models_to_test = [
        "gpt-3.5-turbo",
        "gpt-4",
        "claude-3-sonnet",
        "gemini-pro"
    ]
    
    for model in models_to_test:
        print(f"\nTesting model: {model}")
        print("=" * 50)
        success = test_llm_request(model)
        if success:
            print("✅ SUCCESS")
        else:
            print("❌ FAILED")
        print()
