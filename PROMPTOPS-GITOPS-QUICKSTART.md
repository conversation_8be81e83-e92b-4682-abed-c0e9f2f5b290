# PromptOps GitOps Quick Start Guide

🚀 **Your GitOps integration is ready!** This guide will help you deploy and configure the PromptOps GitOps integration with your repository: `https://github.com/datlaphani/ai-cost-performance-optimizer.git`

## 🎯 What You Get

- **Version-controlled prompts** stored in your Git repository
- **Automated deployments** when you push prompt changes
- **Collaborative workflows** with pull requests and reviews
- **Rollback capabilities** using Git history
- **GitOps dashboard** in your PromptOps interface

## 🚀 Quick Deployment

### Step 1: Run the Setup Script

```bash
./scripts/setup-promptops-gitops.sh
```

This script will:
- ✅ Check prerequisites (kubectl, gcloud)
- 🔐 Set up Git credentials automatically (using your GitHub token)
- 🚀 Deploy the enhanced integration service
- 🧪 Test the deployment
- 📋 Provide webhook configuration details

### Step 2: Configure GitHub Webhook

1. **Go to your repository settings:**
   ```
   https://github.com/datlaphani/ai-cost-performance-optimizer/settings/hooks
   ```

2. **Click "Add webhook"**

3. **Configure the webhook:**
   - **Payload URL:** `https://scale-llm.com/api/integration/gitops/webhook`
   - **Content type:** `application/json`
   - **Secret:** (provided by setup script)
   - **Events:** Select "Just the push event"

4. **Click "Add webhook"**

### Step 3: Test the Integration

1. **Open your PromptOps dashboard:**
   ```
   https://scale-llm.com
   ```

2. **Navigate to PromptOps tab**

3. **Click the "GitOps" button** in the header

4. **Test the integration:**
   - Click "Sync Repository" to pull latest changes
   - Click "Export to Git" to push current prompts
   - Check the status indicators

## 📁 Repository Structure

Your repository now includes:

```
ai-cost-performance-optimizer/
├── prompts/                          # 🎯 Prompt manifests
│   ├── README.md                     # Documentation
│   └── examples/                     # Example prompts
│       ├── customer-greeting.prompt.json
│       ├── code-review.prompt.json
│       └── data-analysis.prompt.json
├── cloudbuild-prompts.yaml           # ☁️ CI/CD pipeline
├── k8s/integration-service/           # 🔧 Enhanced service
│   ├── integration-service.yaml      # Kubernetes deployment
│   └── git-credentials-secret.yaml   # Secret template
└── scripts/
    └── setup-promptops-gitops.sh     # 🛠️ Setup automation
```

## 🔄 GitOps Workflows

### Development Workflow
1. **Create prompts** in PromptOps dashboard
2. **Test in playground** to ensure they work
3. **Export to Git** using the GitOps button
4. **Review changes** in GitHub
5. **Merge to main** → automatic deployment

### Collaborative Workflow
1. **Create feature branch** for prompt changes
2. **Edit prompt manifests** directly in Git
3. **Create pull request** for review
4. **Merge approved changes** → automatic deployment

### Emergency Rollback
1. **Identify problematic commit** in Git history
2. **Revert the commit** in GitHub
3. **Push revert** → automatic rollback deployment

## 🎛️ Dashboard Features

### GitOps Status Panel
- **Current branch and commit** information
- **Sync status** with repository
- **Last update timestamp**

### GitOps Actions
- **🔄 Sync Repository:** Pull latest changes from Git
- **📤 Export to Git:** Push current prompts to repository
- **📥 Import from Git:** Load prompts from repository
- **📊 View Diff:** Compare service vs Git prompts

### Real-time Monitoring
- **Repository status** indicators
- **Deployment progress** tracking
- **Error notifications** for failed operations

## 🔧 Configuration

### Environment Variables (Already Configured)
```yaml
GIT_REPO_URL: "https://github.com/datlaphani/ai-cost-performance-optimizer.git"
GIT_BRANCH: "main"
CLOUD_BUILD_PROJECT: "silken-zenith-460615-s7"
CLOUD_BUILD_REGION: "us-central1"
```

### Webhook Configuration
- **URL:** `https://scale-llm.com/api/integration/gitops/webhook`
- **Events:** Push events to main branch
- **Security:** HMAC signature verification

## 🧪 Testing Your Setup

### 1. Test Export Functionality
```bash
# In PromptOps dashboard:
# 1. Create a test prompt
# 2. Click "GitOps" → "Export to Git"
# 3. Check your GitHub repository for new files
```

### 2. Test Import Functionality
```bash
# 1. Edit a prompt file in GitHub
# 2. In PromptOps: Click "GitOps" → "Import from Git"
# 3. Verify changes appear in dashboard
```

### 3. Test Webhook Automation
```bash
# 1. Make changes to prompts/ directory in GitHub
# 2. Commit and push to main branch
# 3. Check PromptOps dashboard for automatic updates
```

## 🔍 Troubleshooting

### Check Integration Service Health
```bash
kubectl logs -f deployment/integration-service
```

### Test Webhook Endpoint
```bash
kubectl port-forward service/integration-service 8080:8080
curl http://localhost:8080/health
```

### Verify Git Credentials
```bash
kubectl get secret git-credentials -o yaml
```

### Check GitOps Status
```bash
curl https://scale-llm.com/api/integration/gitops/status
```

## 📚 Next Steps

1. **🎨 Customize prompts** in the `prompts/examples/` directory
2. **👥 Invite team members** to collaborate on prompt development
3. **🔄 Set up branch protection** rules in GitHub
4. **📊 Monitor deployments** using Cloud Build logs
5. **🚀 Scale to production** with environment-specific branches

## 🎉 You're All Set!

Your PromptOps GitOps integration is now live! You can:

- ✅ **Version control** all your prompts
- ✅ **Collaborate** with your team using Git workflows
- ✅ **Automatically deploy** prompt changes
- ✅ **Roll back** problematic changes instantly
- ✅ **Monitor** everything from the dashboard

**Happy prompting!** 🚀

---

**Need help?** Check the full documentation in `docs/promptops-gitops-integration.md` or review the example prompts in `prompts/examples/`.
