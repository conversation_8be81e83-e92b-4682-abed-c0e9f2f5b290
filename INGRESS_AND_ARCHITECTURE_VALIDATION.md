# AI Operations Hub - Ingress & Architecture Validation

This document explains the Kubernetes ingress configuration and architecture validation for both Standard and Enterprise editions.

## 🌐 Ingress Configuration

### **Standard Edition Ingress**
**File**: `k8s/standard-edition-api-ingress.yaml`
**Namespace**: `standard-edition`  
**Domain**: `scale-llm.com`

#### **Routing Rules**
```yaml
# Standard Edition Frontend
/standard/* → frontend-standard:80

# Standard Edition API
/standard/api/* → proxy-gateway-standard:8080

# Standard Edition Dashboard
/standard/dashboard/* → dashboard-api-standard:8081
```

#### **Features**
- ✅ **SSL/TLS**: Automatic HTTPS with Let's Encrypt
- ✅ **Path Rewriting**: `/standard/api/health` → `/health` internally
- ✅ **Regex Support**: Advanced path matching
- ✅ **Timeouts**: 300s proxy timeout for long requests
- ✅ **Body Size**: 50MB max request size

### **Enterprise Edition Ingress**
**File**: `k8s/unified-ingress.yaml` and `k8s/enterprise-frontend-ingress.yaml`
**Namespace**: `default`  
**Domain**: `scale-llm.com`

#### **Routing Rules**
```yaml
# Landing Page (Root)
/ → landing-page:80

# Enterprise Frontend
/enterprise/* → frontend:80
/ (fallback) → frontend:80

# Enterprise API
/api/* → proxy-gateway:8080

# Enterprise Dashboard
/dashboard/* → dashboard-api:8081

# Enterprise Planning
/planning/* → planning-service:8084

# Enterprise Evaluation
/evaluation/* → evaluation-service:8085

# Enterprise Integration
/integration/* → integration-service:8086
```

#### **Features**
- ✅ **SSL/TLS**: Automatic HTTPS with Let's Encrypt
- ✅ **Multi-Service**: Routes to all enterprise services
- ✅ **Landing Page**: Root domain serves marketing site
- ✅ **Fallback Routing**: Unmatched paths go to frontend
- ✅ **Enterprise APIs**: Direct access to all service endpoints

## 🏗️ Architecture Validation

### **Standard Edition Architecture**
**Target Market**: Startups (1-50 employees) and SMBs (50-200 employees)

#### **✅ Included Services**
- **proxy-gateway-standard**: Intelligent routing with usage limits
- **ai-optimizer**: Basic cost optimization (no enterprise features)
- **dashboard-api-standard**: Essential metrics (Redis-only)
- **frontend-standard**: Simplified no-code interface
- **redis**: Caching and session management
- **landing-page**: Marketing site

#### **❌ Excluded Services (Enterprise-Only)**
- **data-processor**: ❌ NOT deployed (requires ClickHouse/Kafka)
- **clickhouse**: ❌ NOT deployed (enterprise analytics only)
- **kafka**: ❌ NOT deployed (enterprise messaging only)
- **planning-service**: ❌ NOT deployed (autonomous planning)
- **evaluation-service**: ❌ NOT deployed (advanced evaluation)
- **integration-service**: ❌ NOT deployed (enterprise integrations)
- **policy-manager**: ❌ NOT deployed (governance features)
- **governance-service**: ❌ NOT deployed (compliance features)
- **multi-agent-orchestrator**: ❌ NOT deployed (multi-agent workflows)

#### **Why data-processor is Excluded**
```go
// data-processor dependencies (from main.go)
_ "github.com/ClickHouse/clickhouse-go/v2" // ClickHouse driver
"github.com/segmentio/kafka-go"            // Kafka client

// Constants showing enterprise dependencies
const (
    kafkaBroker     = "kafka:9092"
    clickhouseHost  = "clickhouse"
    clickhousePort  = 9000
)
```

The data-processor service:
1. **Requires ClickHouse** for analytics data storage
2. **Requires Kafka** for real-time message processing
3. **Processes enterprise analytics** not needed in standard edition
4. **Handles complex workflows** beyond standard edition scope

### **Enterprise Edition Architecture**
**Target Market**: Large organizations (200+ employees)

#### **✅ All Services Included**
- **All Standard Edition services** (with enterprise features enabled)
- **data-processor**: ✅ Deployed (analytics processing)
- **clickhouse**: ✅ Deployed (analytics database)
- **kafka**: ✅ Deployed (message streaming)
- **planning-service**: ✅ Deployed (autonomous planning)
- **evaluation-service**: ✅ Deployed (advanced evaluation)
- **integration-service**: ✅ Deployed (enterprise integrations)
- **policy-manager**: ✅ Deployed (governance)
- **governance-service**: ✅ Deployed (compliance)
- **multi-agent-orchestrator**: ✅ Deployed (multi-agent workflows)

## 🔍 Validation Process

### **Automated Validation**
The `validate-deployment.sh` script now includes:

#### **1. Ingress Validation**
```bash
# Standard Edition
kubectl get ingress standard-edition-ingress -n standard-edition

# Enterprise Edition  
kubectl get ingress comprehensive-ingress
```

#### **2. Architecture Validation**
```bash
# Validates that data-processor is NOT in standard edition
kubectl get deployment data-processor -n standard-edition
# Should return: Error from server (NotFound)

# Validates that data-processor IS in enterprise edition
kubectl get deployment data-processor
# Should return: deployment details
```

#### **3. Service Validation**
- **Standard**: Checks only core services exist
- **Enterprise**: Checks all services including enterprise-specific ones

### **Manual Validation Commands**

#### **Standard Edition**
```bash
# Check ingress
kubectl get ingress -n standard-edition
kubectl describe ingress standard-edition-ingress -n standard-edition

# Verify data-processor is NOT deployed
kubectl get deployment data-processor -n standard-edition
# Expected: Error from server (NotFound)

# Check core services
kubectl get deployments -n standard-edition
# Expected: ai-optimizer, dashboard-api-standard, frontend-standard, proxy-gateway-standard, redis
```

#### **Enterprise Edition**
```bash
# Check ingress
kubectl get ingress
kubectl describe ingress comprehensive-ingress

# Verify data-processor IS deployed
kubectl get deployment data-processor
# Expected: deployment details

# Check all services
kubectl get deployments
# Expected: All enterprise services including data-processor
```

## 🌐 Access Points Validation

### **Standard Edition**
- **Frontend**: https://scale-llm.com/standard ✅
- **API Health**: https://scale-llm.com/standard/api/health ✅
- **Dashboard**: https://scale-llm.com/standard/dashboard/health ✅
- **AI Scores**: https://scale-llm.com/standard/api/model-capabilities ✅

### **Enterprise Edition**
- **Frontend**: https://scale-llm.com ✅
- **Landing Page**: https://scale-llm.com ✅
- **API Health**: https://scale-llm.com/api/health ✅
- **Dashboard**: https://scale-llm.com/dashboard/health ✅
- **Planning**: https://scale-llm.com/api/planning/v1/goals ✅
- **AI Scores**: https://scale-llm.com/api/model-capabilities ✅

## 🚀 Deployment Integration

### **Standard Edition Deployment**
The `deploy-standard.sh` script now:
1. ✅ Deploys core services (no data-processor)
2. ✅ Deploys standard edition ingress
3. ✅ Validates ingress readiness
4. ✅ Excludes enterprise dependencies

### **Enterprise Edition Deployment**
The `New_Autostart.sh` script now:
1. ✅ Deploys all services (including data-processor)
2. ✅ Deploys comprehensive ingress
3. ✅ Validates ingress readiness
4. ✅ Includes all enterprise dependencies

## 🔧 Troubleshooting

### **Common Ingress Issues**

#### **1. Ingress Pending External IP**
```bash
# Check ingress status
kubectl get ingress -A

# Check ingress controller
kubectl get pods -n ingress-nginx

# Check load balancer
kubectl get svc -n ingress-nginx
```

#### **2. SSL Certificate Issues**
```bash
# Check certificate status
kubectl get certificates

# Check cert-manager logs
kubectl logs -n cert-manager deployment/cert-manager
```

#### **3. Path Routing Issues**
```bash
# Test specific paths
curl -v https://scale-llm.com/standard/api/health
curl -v https://scale-llm.com/api/health

# Check ingress annotations
kubectl describe ingress standard-edition-ingress -n standard-edition
```

### **Architecture Validation Issues**

#### **1. data-processor in Standard Edition**
```bash
# If data-processor appears in standard edition (ERROR)
kubectl delete deployment data-processor -n standard-edition

# Verify it's removed
kubectl get deployments -n standard-edition
```

#### **2. Missing Enterprise Services**
```bash
# Check if enterprise services are deployed
kubectl get deployments | grep -E "(planning|evaluation|integration|policy)"

# Redeploy if missing
kubectl apply -f k8s/planning-service/
kubectl apply -f k8s/evaluation-service/
```

## ✅ Success Criteria

### **Standard Edition**
- ✅ Ingress routes `/standard/*` correctly
- ✅ No data-processor deployment
- ✅ No ClickHouse/Kafka dependencies
- ✅ Core services operational
- ✅ AI optimizer scores working

### **Enterprise Edition**
- ✅ Ingress routes all paths correctly
- ✅ data-processor deployed and operational
- ✅ ClickHouse/Kafka operational
- ✅ All enterprise services operational
- ✅ Advanced features accessible

## 🎯 Validation Commands

### **Quick Validation**
```bash
# Standard Edition
./validate-deployment.sh -e standard

# Enterprise Edition
./validate-deployment.sh -e enterprise
```

### **Detailed Validation**
```bash
# With extended timeout
./validate-deployment.sh -e standard -t 600

# Manual checks
kubectl get ingress -A
kubectl get deployments -A
curl -s https://scale-llm.com/standard/api/health
curl -s https://scale-llm.com/api/health
```

Your AI Operations Hub now has robust ingress configuration and proper architecture validation for both editions! 🎉
