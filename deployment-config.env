# AI Operations Hub - Deployment Configuration
# This file contains environment variables for deployment customization

# =============================================================================
# GCP Configuration
# =============================================================================
# Google Cloud Project ID (REQUIRED - update this!)
GCP_PROJECT_ID=your-gcp-project-id

# Google Cloud Region
GCP_REGION=us-central1

# Artifact Registry Repository Name
ARTIFACT_REGISTRY_REPO=ai-operations-hub

# GKE Cluster Name
GKE_CLUSTER_NAME=ai-optimizer-cluster

# =============================================================================
# Build Configuration
# =============================================================================
# Set to "true" to enable building specific services, "false" to skip

# Core Services (required for both editions)
BUILD_PROXY_GATEWAY=true
BUILD_AI_OPTIMIZER=true
BUILD_DASHBOARD_API=true
BUILD_FRONTEND=true
BUILD_LANDING_PAGE=true

# Enterprise Services (only for enterprise edition)
BUILD_DATA_PROCESSOR=true
BUILD_POLICY_MANAGER=true
BUILD_EVALUATION_SERVICE=true
BUILD_INTEGRATION_SERVICE=true
BUILD_PLANNING_SERVICE=true
BUILD_GOVERNANCE_SERVICE=true
BUILD_MULTI_AGENT_ORCHESTRATOR=true
BUILD_BIAS_DETECTION_SERVICE=true
BUILD_EXPLAINABILITY_SERVICE=true
BUILD_ROBUSTNESS_TESTING_SERVICE=true
BUILD_COMPLIANCE_SERVICE=true

# =============================================================================
# Deployment Configuration
# =============================================================================
# Set to "true" to enable deploying specific services, "false" to skip

# Core Deployments
DEPLOY_LANDING_PAGE=true
DEPLOY_FRONTEND=true
DEPLOY_REDIS=true

# Enterprise Deployments
DEPLOY_CLICKHOUSE=true
DEPLOY_KAFKA=true
DEPLOY_POLICY_MANAGER=true
DEPLOY_GOVERNANCE_SERVICE=true
DEPLOY_INTEGRATION_SERVICE=true

# =============================================================================
# Feature Flags
# =============================================================================
# Enable/disable specific features

# AI Optimizer Features
ENABLE_AI_OPTIMIZER_SCORES=true
ENABLE_INTELLIGENT_ROUTING=true
ENABLE_COST_OPTIMIZATION=true

# Frontend Features
ENABLE_CHAT_INTERFACE=true
ENABLE_PLANNING_DASHBOARD=true
ENABLE_PROMPTOPS=true
ENABLE_MULTI_AGENT=true

# Enterprise Features (ignored in standard edition)
ENABLE_GOVERNANCE=true
ENABLE_COMPLIANCE=true
ENABLE_ADVANCED_ANALYTICS=true

# =============================================================================
# API Keys Configuration
# =============================================================================
# These will be created as Kubernetes secrets
# Update these with your actual API keys after deployment

# LLM Provider API Keys (REQUIRED)
OPENAI_API_KEY=your-valid-openai-api-key-here
GOOGLE_API_KEY=AIzaSyBzG0Wdhtm44BPP4Htrt739oZyNBXFZ46I
ANTHROPIC_API_KEY=************************************************************************************************************
COHERE_API_KEY=h7u0El3P53rwzUkpPqS4CPwd0wy46NTRIK2td6Gc
HUGGINGFACE_API_KEY=*************************************
MISTRAL_API_KEY=ytgArPUhw6EHkn4lXCXycGucFCsJz1Ym
GROK_API_KEY=************************************************************************************

# GitHub Token for GitOps (Enterprise only)
GITHUB_TOKEN=your-github-token-here

# =============================================================================
# Networking Configuration
# =============================================================================
# Domain and SSL configuration

# Primary domain
DOMAIN=scale-llm.com

# SSL/TLS Configuration
ENABLE_SSL=true
SSL_CERT_SECRET=scale-llm-tls

# Ingress Configuration
INGRESS_CLASS=nginx

# =============================================================================
# Resource Configuration
# =============================================================================
# Resource limits and requests

# Standard Edition Resources (lightweight)
STANDARD_CPU_REQUEST=200m
STANDARD_MEMORY_REQUEST=256Mi
STANDARD_CPU_LIMIT=500m
STANDARD_MEMORY_LIMIT=512Mi

# Enterprise Edition Resources (production-ready)
ENTERPRISE_CPU_REQUEST=500m
ENTERPRISE_MEMORY_REQUEST=512Mi
ENTERPRISE_CPU_LIMIT=2000m
ENTERPRISE_MEMORY_LIMIT=2Gi

# =============================================================================
# Monitoring and Logging
# =============================================================================
# Observability configuration

ENABLE_PROMETHEUS=true
ENABLE_GRAFANA=true
ENABLE_LOGGING=true
LOG_LEVEL=info

# =============================================================================
# Database Configuration
# =============================================================================
# Database settings for enterprise edition

# ClickHouse Configuration
CLICKHOUSE_HOST=clickhouse
CLICKHOUSE_PORT=8123
CLICKHOUSE_DATABASE=ai_operations_hub

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=""

# =============================================================================
# Edition-Specific Overrides
# =============================================================================
# These settings are automatically applied based on edition

# Standard Edition Overrides (applied automatically)
# - Disables enterprise services
# - Reduces resource requirements
# - Simplifies configuration

# Enterprise Edition Overrides (applied automatically)
# - Enables all services
# - Production resource limits
# - Full feature set

# =============================================================================
# Advanced Configuration
# =============================================================================
# Advanced settings for power users

# Deployment Strategy
DEPLOYMENT_STRATEGY=RollingUpdate
MAX_UNAVAILABLE=1
MAX_SURGE=1

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=30s
HEALTH_CHECK_INTERVAL=10s
HEALTH_CHECK_RETRIES=3

# Scaling Configuration
MIN_REPLICAS=1
MAX_REPLICAS=10
TARGET_CPU_UTILIZATION=70

# =============================================================================
# Development Configuration
# =============================================================================
# Settings for development and testing

# Development Mode (enables debug features)
DEVELOPMENT_MODE=false

# Skip certain validations in development
SKIP_VALIDATIONS=false

# Enable verbose logging
VERBOSE_LOGGING=false

# Mock external services (for testing)
MOCK_EXTERNAL_SERVICES=false
