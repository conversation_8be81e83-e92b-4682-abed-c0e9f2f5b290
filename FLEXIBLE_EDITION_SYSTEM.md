# AI Operations Hub - Flexible Edition System

## 🔒 **Zero Risk to Your Enterprise Product**

Your enterprise edition remains **completely untouched** and safe. The startup edition uses:
- ✅ **Separate Dockerfiles**: `Dockerfile.startup` vs `Dockerfile` 
- ✅ **Separate Build Configs**: `cloudbuild-startup.yaml` vs `cloudbuild.yaml`
- ✅ **Separate Build Flags**: `_BUILD_*_STARTUP` vs `_BUILD_*`
- ✅ **Separate Namespaces**: `startup-edition` vs `default`
- ✅ **Separate Images**: `*-startup:latest` vs `:latest`

## 🎛️ **Your Existing Build System (Preserved)**

### Enterprise Edition (Unchanged)
```bash
# Your existing enterprise build (exactly as before)
gcloud builds submit --config=cloudbuild.yaml

# Your existing selective builds still work
gcloud builds submit --config=cloudbuild.yaml \
  --substitutions=_BUILD_FRONTEND=false,_BUILD_AI_OPTIMIZER=true
```

**Available Enterprise Flags** (all preserved):
- `_BUILD_PROXY_GATEWAY: 'true'`
- `_BUILD_DATA_PROCESSOR: 'true'`
- `_BUILD_KAFKA_TOPIC_CREATOR: 'true'`
- `_BUILD_DASHBOARD_API: 'true'`
- `_BUILD_POLICY_MANAGER: 'true'`
- `_BUILD_AI_OPTIMIZER: 'true'`
- `_BUILD_FRONTEND: 'true'`
- `_BUILD_EVALUATION_SERVICE: 'true'`
- `_BUILD_GOVERNANCE_SERVICE: 'true'`
- `_BUILD_INTEGRATION_SERVICE: 'true'`
- `_BUILD_PLANNING_SERVICE: 'true'`
- `_BUILD_MULTI_AGENT_ORCHESTRATOR: 'true'`
- `_BUILD_BIAS_DETECTION_SERVICE: 'true'`
- `_BUILD_EXPLAINABILITY_SERVICE: 'true'`
- `_BUILD_ROBUSTNESS_TESTING_SERVICE: 'true'`
- `_BUILD_COMPLIANCE_SERVICE: 'true'`

## 🚀 **New Startup Edition System**

### Startup Edition (New, Separate)
```bash
# Build complete startup edition
gcloud builds submit --config=cloudbuild-startup.yaml

# Build only specific startup services
gcloud builds submit --config=cloudbuild-startup.yaml \
  --substitutions=_BUILD_FRONTEND_STARTUP=true,_BUILD_PROXY_GATEWAY_STARTUP=false

# Build without deploying (just create images)
gcloud builds submit --config=cloudbuild-startup.yaml \
  --substitutions=_DEPLOY_STARTUP_EDITION=false
```

**Available Startup Flags** (new, separate):
- `_BUILD_PROXY_GATEWAY_STARTUP: 'true'`
- `_BUILD_AI_OPTIMIZER_STARTUP: 'true'`
- `_BUILD_DASHBOARD_API_STARTUP: 'true'`
- `_BUILD_FRONTEND_STARTUP: 'true'`
- `_DEPLOY_STARTUP_EDITION: 'true'`

## 🏗️ **Flexible Build Examples**

### 1. **Enterprise Only** (Your Current Workflow)
```bash
# Build everything for enterprise
./build-editions.sh enterprise

# Or with specific services
gcloud builds submit --config=cloudbuild.yaml \
  --substitutions=_BUILD_FRONTEND=true,_BUILD_AI_OPTIMIZER=true
```

### 2. **Startup Only**
```bash
# Build everything for startup
./build-editions.sh startup

# Or with specific services
gcloud builds submit --config=cloudbuild-startup.yaml \
  --substitutions=_BUILD_FRONTEND_STARTUP=true
```

### 3. **Both Editions**
```bash
# Build both (completely independent)
./build-editions.sh both
```

### 4. **Development Scenarios**
```bash
# Test frontend changes in startup only
gcloud builds submit --config=cloudbuild-startup.yaml \
  --substitutions=_BUILD_FRONTEND_STARTUP=true,_BUILD_PROXY_GATEWAY_STARTUP=false,_BUILD_AI_OPTIMIZER_STARTUP=false,_BUILD_DASHBOARD_API_STARTUP=false

# Update enterprise AI optimizer only
gcloud builds submit --config=cloudbuild.yaml \
  --substitutions=_BUILD_AI_OPTIMIZER=true,_BUILD_FRONTEND=false,_BUILD_PROXY_GATEWAY=false
```

## 📁 **File Structure**

```
k8s/
├── frontend/
│   ├── Dockerfile              # Enterprise (your original)
│   ├── Dockerfile.startup      # Startup (new, separate)
│   └── nginx-startup.conf      # Startup nginx config
├── startup-edition/            # All startup manifests
│   ├── deploy-startup.sh
│   ├── proxy-gateway-startup.yaml
│   ├── ai-optimizer-startup.yaml
│   ├── dashboard-api-startup.yaml
│   └── frontend-startup.yaml
cloudbuild.yaml                 # Enterprise (your original)
cloudbuild-startup.yaml         # Startup (new, separate)
build-editions.sh               # Flexible builder script
```

## 🌐 **Access Points**

- **Enterprise Edition**: `https://scale-llm.com` (unchanged)
- **Startup Edition**: `https://scale-llm.com/startups` (new)

## 🔄 **Development Workflow**

### For Enterprise Development (Unchanged)
```bash
# Your existing workflow continues exactly as before
git checkout main
# Make enterprise changes
gcloud builds submit --config=cloudbuild.yaml
```

### For Startup Development
```bash
git checkout startup-edition
# Make startup changes
gcloud builds submit --config=cloudbuild-startup.yaml
```

### For Shared Core Changes
```bash
git checkout main
# Make core improvements (affects both)
git commit -m "Core: Improve proxy gateway performance"

# Apply to startup edition
git checkout startup-edition
git cherry-pick <commit-hash>
```

## ✅ **Safety Guarantees**

1. **No Cross-Contamination**: Startup builds cannot affect enterprise
2. **Separate Images**: Different image names prevent conflicts
3. **Separate Configs**: Independent build and deploy configurations
4. **Separate Namespaces**: Kubernetes isolation
5. **Rollback Safety**: Can disable startup edition anytime

## 🎯 **Next Steps**

1. **Test the system**: `./build-editions.sh startup`
2. **Verify enterprise unchanged**: `./build-editions.sh enterprise`
3. **Use selective builds**: Customize with build flags as needed

Your enterprise product is **100% safe** and you can now flexibly run both editions!
