# AI Operations Hub - Deployment Guide

This guide provides comprehensive instructions for deploying the AI Operations Hub in both Standard and Enterprise editions.

## 🚀 Quick Start

### Option 1: Interactive Deployment (Recommended)
```bash
./quick-deploy.sh
```
This interactive script will guide you through the deployment process with a user-friendly interface.

### Option 2: Direct Deployment
```bash
# Deploy Standard Edition
./deploy.sh -e standard

# Deploy Enterprise Edition  
./deploy.sh -e enterprise
```

## 📋 Prerequisites

### Required Tools
- **Google Cloud SDK** (`gcloud`) - [Install Guide](https://cloud.google.com/sdk/docs/install)
- **kubectl** - [Install Guide](https://kubernetes.io/docs/tasks/tools/)
- **Docker** (for local builds) - [Install Guide](https://docs.docker.com/get-docker/)

### GCP Setup
1. **Create or select a GCP project**
2. **Enable required APIs**:
   ```bash
   gcloud services enable container.googleapis.com
   gcloud services enable cloudbuild.googleapis.com
   gcloud services enable artifactregistry.googleapis.com
   ```
3. **Create GKE cluster** (if not exists):
   ```bash
   gcloud container clusters create ai-optimizer-cluster \
     --zone=us-central1-a \
     --num-nodes=3 \
     --machine-type=e2-standard-4
   ```
4. **Configure kubectl**:
   ```bash
   gcloud container clusters get-credentials ai-optimizer-cluster --zone=us-central1-a
   ```

## 🎯 Deployment Options

### Standard Edition
**Perfect for startups and SMBs**

**Features:**
- ✅ AI Chat Interface with Live Optimizer Scores
- ✅ Intelligent Model Routing & Selection
- ✅ Cost Optimization Dashboard
- ✅ Basic Analytics & Monitoring
- ✅ Landing Page
- ✅ Redis Caching

**Resources:** ~2GB RAM, 2 CPU cores  
**Cost:** ~$50-100/month

**Deploy:**
```bash
./deploy.sh -e standard
```

### Enterprise Edition
**Designed for large organizations**

**Features:**
- ✅ Everything in Standard Edition
- ✅ Advanced Planning & Task Decomposition
- ✅ Multi-Agent Orchestration
- ✅ PromptOps with A/B Testing
- ✅ Governance & Compliance
- ✅ Advanced Analytics & Reporting
- ✅ GitOps Integration
- ✅ Synthetic Data Generation
- ✅ Responsible AI Features

**Resources:** ~8GB RAM, 4 CPU cores  
**Cost:** ~$200-500/month

**Deploy:**
```bash
./deploy.sh -e enterprise
```

## 🔧 Advanced Deployment Options

### Build Only (No Deployment)
```bash
./deploy.sh -e standard -b    # Build standard edition images
./deploy.sh -e enterprise -b  # Build enterprise edition images
```

### Deploy Only (Skip Build)
```bash
./deploy.sh -e standard -d    # Deploy standard (assumes images exist)
./deploy.sh -e enterprise -d  # Deploy enterprise (assumes images exist)
```

### Skip Landing Page
```bash
./deploy.sh -e standard -s    # Deploy without landing page
```

### Dry Run (See What Would Be Deployed)
```bash
./deploy.sh -e standard -n    # Show deployment plan without executing
```

### Verbose Output
```bash
./deploy.sh -e standard -v    # Enable detailed logging
```

## 📁 File Structure

```
ai-cost-performance-optimizer/
├── deploy.sh                    # Main deployment script
├── quick-deploy.sh             # Interactive deployment
├── deployment-config.env       # Configuration file
├── cloudbuild.yaml            # Enterprise Cloud Build config
├── cloudbuild-standard.yaml   # Standard Cloud Build config
├── k8s/
│   ├── New_Autostart.sh       # Enterprise deployment script
│   └── standard-edition/
│       └── deploy-standard.sh # Standard deployment script
```

## 🔑 API Keys Configuration

After deployment, update your API keys:

### Standard Edition
```bash
kubectl edit secret llm-api-keys -n standard-edition
```

### Enterprise Edition
```bash
kubectl edit secret llm-api-keys
```

**Required API Keys:**
- `openai-api-key`: OpenAI API key
- `google-api-key`: Google AI API key  
- `anthropic-api-key`: Anthropic API key
- `cohere-api-key`: Cohere API key
- `huggingface-api-key`: Hugging Face API key
- `mistral-api-key`: Mistral API key
- `grok-api-key`: Grok API key

**Enterprise Only:**
- `github-token`: GitHub token for GitOps

## 🌐 Access Points

### Standard Edition
- **Frontend**: https://scale-llm.com/standard
- **API Health**: https://scale-llm.com/standard/api/health
- **Dashboard**: https://scale-llm.com/standard/dashboard/health

### Enterprise Edition
- **Frontend**: https://scale-llm.com
- **API Health**: https://scale-llm.com/api/health
- **Dashboard**: https://scale-llm.com/dashboard/health
- **Planning**: https://scale-llm.com/api/planning/v1/goals

### Both Editions
- **Landing Page**: https://scale-llm.com

## 🔍 Monitoring & Troubleshooting

### Check Deployment Status
```bash
# Standard Edition
kubectl get pods -n standard-edition
kubectl get services -n standard-edition

# Enterprise Edition
kubectl get pods
kubectl get services
```

### View Logs
```bash
# Standard Edition
kubectl logs -f deployment/ai-optimizer -n standard-edition
kubectl logs -f deployment/frontend -n standard-edition

# Enterprise Edition
kubectl logs -f deployment/ai-optimizer
kubectl logs -f deployment/frontend
```

### Common Issues

**1. Images Not Found**
- Ensure Cloud Build completed successfully
- Check Artifact Registry for images
- Verify project ID and region settings

**2. Pods Stuck in Pending**
- Check cluster resources: `kubectl describe nodes`
- Verify resource requests in deployment manifests

**3. Services Not Accessible**
- Check ingress configuration: `kubectl get ingress`
- Verify DNS settings and SSL certificates

## 🔄 Updates & Maintenance

### Update Deployment
```bash
# Rebuild and redeploy
./deploy.sh -e standard

# Deploy only (if images are already updated)
./deploy.sh -e standard -d
```

### Scale Services
```bash
# Scale specific deployment
kubectl scale deployment ai-optimizer --replicas=3

# Auto-scaling (Enterprise)
kubectl autoscale deployment ai-optimizer --min=1 --max=10 --cpu-percent=70
```

## 🛡️ Security Considerations

1. **API Keys**: Store securely in Kubernetes secrets
2. **Network Policies**: Implement network segmentation
3. **RBAC**: Configure role-based access control
4. **SSL/TLS**: Enable HTTPS for all endpoints
5. **Monitoring**: Set up security monitoring and alerts

## 📊 Performance Optimization

### Standard Edition
- Monitor resource usage and scale as needed
- Optimize Redis configuration for caching
- Use CDN for static assets

### Enterprise Edition
- Configure ClickHouse for optimal query performance
- Set up proper indexing for time-series data
- Implement caching strategies for frequently accessed data

## 🆘 Support

For deployment issues:
1. Check the troubleshooting section above
2. Review logs for error messages
3. Verify all prerequisites are met
4. Ensure API keys are correctly configured

## 🎉 Success!

Once deployed, your AI Operations Hub will provide:
- **Intelligent AI model routing** with live performance scores
- **Cost optimization** through smart model selection
- **User-friendly chat interface** with model comparison
- **Real-time analytics** and monitoring
- **Scalable architecture** for growing needs

Your AI Operations Hub is now ready to optimize your AI workloads! 🚀
