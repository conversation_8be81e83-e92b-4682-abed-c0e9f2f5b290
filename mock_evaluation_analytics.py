#!/usr/bin/env python3
"""
Mock Evaluation Analytics Service
Provides mock data for evaluation analytics endpoints until the real service is updated
"""

from flask import Flask, jsonify
import json
from datetime import datetime, timedelta
import random

app = Flask(__name__)

# Mock data
MOCK_ANALYTICS = [
    {
        "model_id": "gpt-3.5-turbo",
        "task_type": "chat_completion",
        "total_evaluations": 150,
        "average_score": 0.85,
        "pass_rate": 0.92,
        "trend_direction": "improving",
        "last_evaluated": (datetime.now() - timedelta(hours=1)).isoformat(),
        "detailed_breakdown": {
            "accuracy": 0.88,
            "fluency": 0.90,
            "relevance": 0.82,
            "safety": 0.95
        }
    },
    {
        "model_id": "gpt-4",
        "task_type": "chat_completion", 
        "total_evaluations": 89,
        "average_score": 0.92,
        "pass_rate": 0.96,
        "trend_direction": "stable",
        "last_evaluated": (datetime.now() - timedelta(minutes=30)).isoformat(),
        "detailed_breakdown": {
            "accuracy": 0.94,
            "fluency": 0.93,
            "relevance": 0.89,
            "safety": 0.97
        }
    },
    {
        "model_id": "claude-3-haiku",
        "task_type": "summarization",
        "total_evaluations": 67,
        "average_score": 0.78,
        "pass_rate": 0.84,
        "trend_direction": "declining",
        "last_evaluated": (datetime.now() - timedelta(hours=2)).isoformat(),
        "detailed_breakdown": {
            "accuracy": 0.76,
            "fluency": 0.82,
            "relevance": 0.75,
            "safety": 0.89
        }
    }
]

MOCK_ALERTS = [
    {
        "id": "alert-1",
        "model_id": "claude-3-haiku",
        "task_type": "summarization",
        "alert_type": "low_score",
        "severity": "medium",
        "message": "Model claude-3-haiku has low average score 0.58 for summarization tasks",
        "threshold": 0.60,
        "actual_value": 0.58,
        "created_at": (datetime.now() - timedelta(hours=2)).isoformat(),
        "status": "active",
        "actions": ["review_model_configuration", "consider_model_switch", "optimize_prompts"]
    },
    {
        "id": "alert-2", 
        "model_id": "gpt-3.5-turbo",
        "task_type": "code_generation",
        "alert_type": "declining_trend",
        "severity": "high",
        "message": "Model gpt-3.5-turbo performance is declining for code generation tasks",
        "threshold": 0.05,
        "actual_value": -0.08,
        "created_at": (datetime.now() - timedelta(minutes=45)).isoformat(),
        "status": "active",
        "actions": ["analyze_failing_prompts", "optimize_prompt_templates", "run_a_b_tests"]
    }
]

MOCK_RECOMMENDATIONS = [
    {
        "id": "rec-1",
        "type": "model_switch",
        "model_id": "claude-3-haiku",
        "task_type": "summarization",
        "priority": "high",
        "description": "Consider switching from claude-3-haiku to gpt-4 for summarization tasks to improve performance",
        "expected_gain": 0.15,
        "confidence": 0.85,
        "created_at": (datetime.now() - timedelta(hours=1)).isoformat(),
        "status": "pending",
        "actions": ["evaluate_alternative_models", "run_comparative_tests", "update_routing_policy"]
    },
    {
        "id": "rec-2",
        "type": "prompt_optimization", 
        "model_id": "gpt-3.5-turbo",
        "task_type": "code_generation",
        "priority": "medium",
        "description": "Optimize prompts for gpt-3.5-turbo code generation to address declining performance",
        "expected_gain": 0.12,
        "confidence": 0.78,
        "created_at": (datetime.now() - timedelta(minutes=30)).isoformat(),
        "status": "pending",
        "actions": ["analyze_failing_prompts", "optimize_prompt_templates", "run_a_b_tests"]
    }
]

@app.route('/analytics/models', methods=['GET'])
def get_model_analytics():
    """Return mock model analytics"""
    return jsonify(MOCK_ANALYTICS)

@app.route('/analytics/models/<model_id>', methods=['GET'])
def get_model_analytics_by_id(model_id):
    """Return mock analytics for specific model"""
    filtered = [a for a in MOCK_ANALYTICS if a['model_id'] == model_id]
    return jsonify(filtered)

@app.route('/analytics/trends', methods=['GET'])
def get_trend_analytics():
    """Return mock trend data"""
    trends = []
    for i in range(7):  # Last 7 days
        date = datetime.now() - timedelta(days=i)
        for model in ["gpt-3.5-turbo", "gpt-4", "claude-3-haiku"]:
            trends.append({
                "model_id": model,
                "task_type": "chat_completion",
                "date": date.strftime("%Y-%m-%d"),
                "avg_score": round(0.7 + random.random() * 0.3, 2),
                "evaluations": random.randint(10, 50)
            })
    return jsonify(trends)

@app.route('/analytics/alerts', methods=['GET'])
def get_active_alerts():
    """Return mock active alerts"""
    return jsonify(MOCK_ALERTS)

@app.route('/analytics/recommendations', methods=['GET'])
def get_recommendations():
    """Return mock recommendations"""
    return jsonify(MOCK_RECOMMENDATIONS)

@app.route('/actions/acknowledge-alert/<alert_id>', methods=['POST'])
def acknowledge_alert(alert_id):
    """Mock acknowledge alert"""
    return jsonify({"status": "acknowledged", "alert_id": alert_id})

@app.route('/actions/apply-recommendation/<rec_id>', methods=['POST'])
def apply_recommendation(rec_id):
    """Mock apply recommendation"""
    return jsonify({"status": "applied", "recommendation_id": rec_id})

@app.route('/actions/trigger-optimization', methods=['POST'])
def trigger_optimization():
    """Mock trigger optimization"""
    return jsonify({"status": "optimization_triggered"})

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "service": "mock-evaluation-analytics"})

if __name__ == '__main__':
    print("🚀 Starting Mock Evaluation Analytics Service")
    print("📊 Providing mock data for evaluation analytics endpoints")
    app.run(host='0.0.0.0', port=8087, debug=True)
