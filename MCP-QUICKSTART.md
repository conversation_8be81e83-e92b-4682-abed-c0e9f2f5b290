# 🚀 AI Operations Hub MCP Quick Start Guide

Get up and running with Model Context Protocol (MCP) integration in under 5 minutes!

## What You'll Get

✅ **Universal AI Tool Access**: Connect Claude Desktop, IDEs, and custom apps to AI Operations Hub  
✅ **Intelligent LLM Routing**: Automatic cost and performance optimization for your prompts  
✅ **Autonomous Planning**: Goal decomposition and execution through MCP tools  
✅ **Quality Evaluation**: Real-time LLM response assessment and bias detection  
✅ **GitOps Integration**: Prompt deployment and management via MCP workflows  

## 🎯 Quick Setup Options

### Option 1: Claude Desktop Integration (Recommended)

**Perfect for**: Content creators, researchers, developers who want AI Operations Hub tools in Claude Desktop

1. **Install Claude Desktop** (if not already installed)
2. **Configure MCP Server**:
   ```json
   {
     "mcpServers": {
       "ai-operations-hub": {
         "command": "npx",
         "args": ["@modelcontextprotocol/server-websocket", "wss://scale-llm.com/mcp/connect"],
         "env": {
           "X-User-ID": "your-user-id"
         }
       }
     }
   }
   ```
3. **Restart Claude <PERSON>**
4. **Start using AI Operations Hub tools** in your conversations!

### Option 2: Development Integration

**Perfect for**: Developers building AI applications

```javascript
const WebSocket = require('ws');

const ws = new WebSocket('wss://scale-llm.com/mcp/connect', {
  headers: { 'X-User-ID': 'your-user-id' }
});

ws.on('open', () => {
  // Initialize MCP connection
  ws.send(JSON.stringify({
    jsonrpc: "2.0",
    id: 1,
    method: "initialize",
    params: {
      protocolVersion: "2025-06-18",
      capabilities: { tools: {}, resources: {} },
      clientInfo: { name: "my-app", version: "1.0.0" }
    }
  }));
});
```

### Option 3: MCP Inspector (Testing)

**Perfect for**: Testing and debugging MCP connections

```bash
npm install -g @modelcontextprotocol/inspector
mcp-inspector wss://scale-llm.com/mcp/connect
```

## 🛠️ Available Tools

### AI Optimizer Tools
- `optimize_llm_routing`: Get the best LLM for your prompt
- `get_cost_analysis`: Compare costs across different models
- `analyze_prompt`: Understand prompt complexity and requirements

### Planning Tools
- `create_goal`: Create autonomous goals for execution
- `decompose_goal`: Break down complex goals into tasks
- `execute_plan`: Run automated workflows
- `monitor_execution`: Track progress in real-time

### Evaluation Tools
- `evaluate_llm_response`: Comprehensive response quality assessment
- `run_quality_assessment`: Multi-dimensional quality analysis
- `perform_bias_detection`: Automated bias detection
- `generate_evaluation_report`: Detailed evaluation reports

### Integration Tools
- `deploy_prompts`: GitOps-based prompt deployment
- `sync_repository`: Git repository synchronization
- `validate_prompts`: Prompt configuration validation
- `export_configurations`: System configuration export

## 💡 Example Use Cases

### 1. Smart LLM Selection in Claude Desktop

```
User: I need to analyze this complex financial data. Can you help me choose the best LLM for this task?

Claude: I'll use the AI Operations Hub to find the optimal LLM for your financial analysis task.

[Uses optimize_llm_routing tool]

Based on the analysis, I recommend using GPT-4 for this task because:
- High accuracy for financial data analysis (95% confidence)
- Cost-effective for this complexity level ($0.03 estimated)
- Best performance for numerical reasoning tasks

Would you like me to proceed with the analysis using this model?
```

### 2. Autonomous Goal Execution

```
User: I want to create a comprehensive market research report for renewable energy trends.

Claude: I'll create an autonomous goal to handle this complex research task.

[Uses create_goal and execute_plan tools]

Goal created successfully! I've broken this down into:
1. Data collection from multiple sources
2. Trend analysis and visualization
3. Competitive landscape assessment
4. Future predictions and recommendations
5. Report compilation and formatting

The system is now executing these tasks autonomously. I'll monitor progress and provide updates.
```

### 3. Quality Assurance Workflow

```
User: Please evaluate this LLM response for bias and quality issues.

Claude: I'll run a comprehensive evaluation using the AI Operations Hub's assessment tools.

[Uses evaluate_llm_response and perform_bias_detection tools]

Evaluation Results:
- Overall Quality: 8.5/10 (Very Good)
- Bias Detection: Low risk detected
- Recommendations: Consider adding more diverse perspectives
- Confidence: 92%

The response meets quality standards with minor suggestions for improvement.
```

## 🔧 Configuration Options

### Authentication
All MCP connections require user authentication:
```javascript
const headers = {
  'X-User-ID': 'your-user-id'  // Required for all connections
};
```

### Connection URLs
- **Production**: `wss://scale-llm.com/mcp/connect`
- **Development**: `ws://localhost:8080/mcp/connect`

### Rate Limits
- 100 requests per minute per user
- 1000 requests per hour per user

## 📊 Monitoring & Management

### Web Dashboard
Access the MCP Management dashboard at:
**https://scale-llm.com** → **MCP Management** tab

Features:
- View connected clients
- Monitor server status
- Manage configurations
- View analytics and usage

### Health Check
```bash
curl https://scale-llm.com/mcp/status
```

### Logs
```bash
# View MCP activity logs
kubectl logs -f deployment/proxy-gateway -n default | grep MCP
```

## 🚨 Troubleshooting

### Common Issues

**❌ Connection Failed**
```
Error: WebSocket connection failed
```
**✅ Solution**: Check authentication headers and network connectivity

**❌ Tool Not Found**
```
Error: Method not found
```
**✅ Solution**: Verify tool name and check available tools with `tools/list`

**❌ Permission Denied**
```
Error: Insufficient permissions
```
**✅ Solution**: Verify user ID and check user permissions

### Getting Help

1. **Check Status**: Visit https://scale-llm.com/mcp/status
2. **View Logs**: Check the MCP Management dashboard
3. **Test Connection**: Use MCP Inspector for debugging
4. **Documentation**: See [MCP Integration Guide](./docs/mcp_integration_guide.md)

## 🎉 What's Next?

1. **Explore Tools**: Try different MCP tools to understand capabilities
2. **Build Workflows**: Combine multiple tools for complex workflows
3. **Monitor Usage**: Use the dashboard to track performance
4. **Custom Integration**: Build your own MCP clients

## 📚 Additional Resources

- **[Complete Integration Guide](./docs/mcp_integration_guide.md)**: Detailed technical documentation
- **[Examples & Tutorials](./docs/mcp_examples.md)**: Code examples and use cases
- **[Development Roadmap](./docs/development_roadmap.md)**: Platform features and future plans

---

**Ready to get started?** Choose your integration option above and start building with AI Operations Hub's MCP capabilities! 🚀

For questions or support, check the troubleshooting section or review the comprehensive documentation.
