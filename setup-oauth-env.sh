#!/bin/bash

# Google OAuth Setup Script
# Replace the values below with your actual Google OAuth credentials

echo "Setting up Google OAuth environment variables..."

# Replace these with your actual values from Google Cloud Console
export GOOGLE_CLIENT_ID="your-google-client-id-here.apps.googleusercontent.com"
export GOOGLE_CLIENT_SECRET="your-google-client-secret-here"

# Generate a secure JWT secret (or use your own)
export JWT_SECRET=$(openssl rand -base64 32)

# Set admin emails (replace with your actual admin emails)
export ADMIN_EMAILS="<EMAIL>,<EMAIL>"

# Optional: Set ClickHouse password if not already set
export CLICKHOUSE_PASSWORD="your-secure-clickhouse-password"

echo "Environment variables set:"
echo "GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}"
echo "GOOGLE_CLIENT_SECRET: [HIDDEN]"
echo "JWT_SECRET: [HIDDEN]"
echo "ADMIN_EMAILS: ${ADMIN_EMAILS}"

echo ""
echo "Next steps:"
echo "1. Update the values above with your actual Google OAuth credentials"
echo "2. Run: source setup-oauth-env.sh"
echo "3. Run: ./k8s/scripts/setup-auth-secrets.sh"
echo "4. Deploy your services"

# Uncomment the line below to automatically run the setup script
# ./k8s/scripts/setup-auth-secrets.sh
