apiVersion: v1
kind: Service
metadata:
  name: frontend-standard-external
  namespace: default
spec:
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP

---
apiVersion: v1
kind: Endpoints
metadata:
  name: frontend-standard-external
  namespace: default
subsets:
- addresses:
  - ip: **************  # ClusterIP of frontend-standard service
  ports:
  - port: 80
    protocol: TCP

---
apiVersion: v1
kind: Service
metadata:
  name: proxy-gateway-standard-external
  namespace: default
spec:
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP

---
apiVersion: v1
kind: Endpoints
metadata:
  name: proxy-gateway-standard-external
  namespace: default
subsets:
- addresses:
  - ip: *************  # ClusterIP of proxy-gateway-standard service
  ports:
  - port: 8080
    protocol: TCP

---
apiVersion: v1
kind: Service
metadata:
  name: dashboard-api-standard-external
  namespace: default
spec:
  ports:
  - port: 8081
    targetPort: 8081
    protocol: TCP

---
apiVersion: v1
kind: Endpoints
metadata:
  name: dashboard-api-standard-external
  namespace: default
subsets:
- addresses:
  - ip: **************  # ClusterIP of dashboard-api-standard service
  ports:
  - port: 8081
    protocol: TCP

---
# Landing page external service (for root path routing)
apiVersion: v1
kind: Service
metadata:
  name: landing-page
  namespace: default
spec:
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP

---
apiVersion: v1
kind: Endpoints
metadata:
  name: landing-page
  namespace: default
subsets:
- addresses:
  - ip: **************  # ClusterIP of landing-page service
  ports:
  - port: 80
    protocol: TCP
