package main

import (
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/rs/cors"
)

// BiasMetrics represents bias detection metrics for a model or agent
type BiasMetrics struct {
	ID                     string                  `json:"id"`
	ModelID                string                  `json:"model_id"`
	AgentID                string                  `json:"agent_id,omitempty"`
	Timestamp              time.Time               `json:"timestamp"`
	DisparateImpact        float64                 `json:"disparate_impact"`
	EqualOpportunity       float64                 `json:"equal_opportunity"`
	DemographicParity      float64                 `json:"demographic_parity"`
	EqualizationOdds       float64                 `json:"equalization_odds"`
	CalibrationScore       float64                 `json:"calibration_score"`
	FairnessStatus         string                  `json:"fairness_status"` // "pass", "warning", "fail"
	ProtectedAttributes    []string                `json:"protected_attributes"`
	BiasDetectionResults   map[string]interface{}  `json:"bias_detection_results"`
	RemediationSuggestions []RemediationSuggestion `json:"remediation_suggestions"`
	CreatedAt              time.Time               `json:"created_at"`
	UpdatedAt              time.Time               `json:"updated_at"`
}

// RemediationSuggestion provides actionable bias mitigation recommendations
type RemediationSuggestion struct {
	Type        string  `json:"type"`     // "reweighting", "preprocessing", "postprocessing", "model_adjustment"
	Priority    string  `json:"priority"` // "high", "medium", "low"
	Description string  `json:"description"`
	Impact      float64 `json:"impact"` // Expected improvement score
	Effort      string  `json:"effort"` // "low", "medium", "high"
	Method      string  `json:"method"` // Specific technique name
}

// BiasAuditRequest represents a request for bias auditing
type BiasAuditRequest struct {
	ModelID             string                 `json:"model_id"`
	AgentID             string                 `json:"agent_id,omitempty"`
	DatasetPath         string                 `json:"dataset_path,omitempty"`
	ProtectedAttributes []string               `json:"protected_attributes"`
	TargetVariable      string                 `json:"target_variable"`
	AuditType           string                 `json:"audit_type"` // "fairlearn", "aif360", "comprehensive"
	Configuration       map[string]interface{} `json:"configuration,omitempty"`
}

// BiasDetectionService manages bias detection and mitigation
type BiasDetectionService struct {
	metrics map[string]BiasMetrics
	mu      sync.RWMutex
}

// NewBiasDetectionService creates a new bias detection service
func NewBiasDetectionService() *BiasDetectionService {
	service := &BiasDetectionService{
		metrics: make(map[string]BiasMetrics),
	}

	// Initialize with some sample data
	service.initializeSampleData()

	return service
}

// initializeSampleData creates sample bias metrics for demonstration
func (bds *BiasDetectionService) initializeSampleData() {
	sampleMetrics := []BiasMetrics{
		{
			ID:                  "bias-001",
			ModelID:             "gpt-4",
			Timestamp:           time.Now().Add(-1 * time.Hour),
			DisparateImpact:     1.15,
			EqualOpportunity:    0.92,
			DemographicParity:   0.88,
			EqualizationOdds:    0.90,
			CalibrationScore:    0.94,
			FairnessStatus:      "warning",
			ProtectedAttributes: []string{"gender", "race", "age"},
			BiasDetectionResults: map[string]interface{}{
				"gender_bias_score":   0.15,
				"racial_bias_score":   0.12,
				"age_bias_score":      0.08,
				"intersectional_bias": 0.18,
			},
			RemediationSuggestions: []RemediationSuggestion{
				{
					Type:        "preprocessing",
					Priority:    "high",
					Description: "Apply reweighting to balance gender representation in training data",
					Impact:      0.25,
					Effort:      "medium",
					Method:      "fairlearn_reweighting",
				},
				{
					Type:        "postprocessing",
					Priority:    "medium",
					Description: "Implement threshold optimization for equalized odds",
					Impact:      0.18,
					Effort:      "low",
					Method:      "threshold_optimization",
				},
			},
			CreatedAt: time.Now().Add(-2 * time.Hour),
			UpdatedAt: time.Now().Add(-1 * time.Hour),
		},
		{
			ID:                  "bias-002",
			ModelID:             "claude-3",
			AgentID:             "agent-planning-001",
			Timestamp:           time.Now().Add(-30 * time.Minute),
			DisparateImpact:     1.05,
			EqualOpportunity:    0.96,
			DemographicParity:   0.94,
			EqualizationOdds:    0.95,
			CalibrationScore:    0.97,
			FairnessStatus:      "pass",
			ProtectedAttributes: []string{"gender", "ethnicity"},
			BiasDetectionResults: map[string]interface{}{
				"gender_bias_score":    0.05,
				"ethnicity_bias_score": 0.04,
				"overall_fairness":     0.96,
			},
			RemediationSuggestions: []RemediationSuggestion{
				{
					Type:        "monitoring",
					Priority:    "low",
					Description: "Continue monitoring for bias drift over time",
					Impact:      0.02,
					Effort:      "low",
					Method:      "continuous_monitoring",
				},
			},
			CreatedAt: time.Now().Add(-1 * time.Hour),
			UpdatedAt: time.Now().Add(-30 * time.Minute),
		},
	}

	for _, metric := range sampleMetrics {
		bds.metrics[metric.ID] = metric
	}
}

// CreateBiasAudit creates a new bias audit
func (bds *BiasDetectionService) CreateBiasAudit(w http.ResponseWriter, r *http.Request) {
	var request BiasAuditRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Simulate bias detection process
	metrics := bds.simulateBiasDetection(request)

	bds.mu.Lock()
	bds.metrics[metrics.ID] = metrics
	bds.mu.Unlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(metrics)

	log.Printf("Created bias audit %s for model %s", metrics.ID, request.ModelID)
}

// simulateBiasDetection simulates the bias detection process
func (bds *BiasDetectionService) simulateBiasDetection(request BiasAuditRequest) BiasMetrics {
	// Simulate bias metrics calculation
	// In a real implementation, this would integrate with AIF360/Fairlearn

	disparateImpact := 0.9 + (0.4 * math.Sin(float64(time.Now().Unix())))
	if disparateImpact < 0.8 {
		disparateImpact = 0.8
	}
	if disparateImpact > 1.3 {
		disparateImpact = 1.3
	}

	equalOpportunity := 0.85 + (0.15 * math.Cos(float64(time.Now().Unix())))
	demographicParity := 0.80 + (0.20 * math.Sin(float64(time.Now().Unix()+100)))

	// Determine fairness status
	status := "pass"
	if disparateImpact > 1.2 || disparateImpact < 0.8 || equalOpportunity < 0.9 {
		status = "warning"
	}
	if disparateImpact > 1.3 || disparateImpact < 0.7 || equalOpportunity < 0.8 {
		status = "fail"
	}

	// Generate remediation suggestions based on metrics
	suggestions := bds.generateRemediationSuggestions(disparateImpact, equalOpportunity, status)

	return BiasMetrics{
		ID:                  uuid.New().String(),
		ModelID:             request.ModelID,
		AgentID:             request.AgentID,
		Timestamp:           time.Now(),
		DisparateImpact:     disparateImpact,
		EqualOpportunity:    equalOpportunity,
		DemographicParity:   demographicParity,
		EqualizationOdds:    (equalOpportunity + demographicParity) / 2,
		CalibrationScore:    0.90 + (0.10 * math.Cos(float64(time.Now().Unix()+200))),
		FairnessStatus:      status,
		ProtectedAttributes: request.ProtectedAttributes,
		BiasDetectionResults: map[string]interface{}{
			"audit_type": request.AuditType,
			"bias_score": 1.0 - equalOpportunity,
			"fairness_metrics": map[string]float64{
				"disparate_impact":   disparateImpact,
				"equal_opportunity":  equalOpportunity,
				"demographic_parity": demographicParity,
			},
		},
		RemediationSuggestions: suggestions,
		CreatedAt:              time.Now(),
		UpdatedAt:              time.Now(),
	}
}

// generateRemediationSuggestions creates appropriate remediation suggestions
func (bds *BiasDetectionService) generateRemediationSuggestions(disparateImpact, equalOpportunity float64, status string) []RemediationSuggestion {
	var suggestions []RemediationSuggestion

	if status == "fail" || status == "warning" {
		if disparateImpact > 1.2 || disparateImpact < 0.8 {
			suggestions = append(suggestions, RemediationSuggestion{
				Type:        "preprocessing",
				Priority:    "high",
				Description: "Apply data reweighting to address disparate impact",
				Impact:      0.30,
				Effort:      "medium",
				Method:      "aif360_reweighting",
			})
		}

		if equalOpportunity < 0.9 {
			suggestions = append(suggestions, RemediationSuggestion{
				Type:        "postprocessing",
				Priority:    "high",
				Description: "Implement equalized odds postprocessing",
				Impact:      0.25,
				Effort:      "low",
				Method:      "equalized_odds_postprocessing",
			})
		}

		suggestions = append(suggestions, RemediationSuggestion{
			Type:        "model_adjustment",
			Priority:    "medium",
			Description: "Consider adversarial debiasing during model training",
			Impact:      0.35,
			Effort:      "high",
			Method:      "adversarial_debiasing",
		})
	} else {
		suggestions = append(suggestions, RemediationSuggestion{
			Type:        "monitoring",
			Priority:    "low",
			Description: "Continue regular bias monitoring",
			Impact:      0.05,
			Effort:      "low",
			Method:      "continuous_monitoring",
		})
	}

	return suggestions
}

// GetBiasMetrics retrieves bias metrics
func (bds *BiasDetectionService) GetBiasMetrics(w http.ResponseWriter, r *http.Request) {
	bds.mu.RLock()
	defer bds.mu.RUnlock()

	vars := mux.Vars(r)
	id := vars["id"]

	if id != "" {
		if metric, exists := bds.metrics[id]; exists {
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(metric)
			return
		}
		http.Error(w, "Bias metrics not found", http.StatusNotFound)
		return
	}

	// Return all metrics
	var allMetrics []BiasMetrics
	for _, metric := range bds.metrics {
		allMetrics = append(allMetrics, metric)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(allMetrics)
}

// GetBiasMetricsByModel retrieves bias metrics for a specific model
func (bds *BiasDetectionService) GetBiasMetricsByModel(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	modelID := vars["modelId"]

	bds.mu.RLock()
	defer bds.mu.RUnlock()

	var modelMetrics []BiasMetrics
	for _, metric := range bds.metrics {
		if metric.ModelID == modelID {
			modelMetrics = append(modelMetrics, metric)
		}
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(modelMetrics)
}

// HealthCheck provides service health status
func (bds *BiasDetectionService) HealthCheck(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":        "healthy",
		"service":       "bias-detection-service",
		"timestamp":     time.Now(),
		"metrics_count": len(bds.metrics),
	})
}

func main() {
	service := NewBiasDetectionService()

	r := mux.NewRouter()

	// API routes
	r.HandleFunc("/health", service.HealthCheck).Methods("GET")
	r.HandleFunc("/bias-audits", service.CreateBiasAudit).Methods("POST")
	r.HandleFunc("/bias-metrics", service.GetBiasMetrics).Methods("GET")
	r.HandleFunc("/bias-metrics/{id}", service.GetBiasMetrics).Methods("GET")
	r.HandleFunc("/bias-metrics/model/{modelId}", service.GetBiasMetricsByModel).Methods("GET")

	// Enable CORS
	c := cors.New(cors.Options{
		AllowedOrigins: []string{"*"},
		AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders: []string{"*"},
	})

	handler := c.Handler(r)

	port := ":8084"
	fmt.Printf("Bias Detection Service starting on port %s...\n", port)
	log.Fatal(http.ListenAndServe(port, handler))
}
