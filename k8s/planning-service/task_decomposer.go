package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/google/uuid"
)

// TaskDecomposer handles breaking down goals into executable tasks
type TaskDecomposer struct {
	llmClient       LLMClient
	templateLibrary *TaskTemplateLibrary
}

// TaskTemplate represents a reusable template for common task patterns
type TaskTemplate struct {
	ID            string                 `json:"id"`
	Name          string                 `json:"name"`
	Description   string                 `json:"description"`
	Category      string                 `json:"category"`
	TaskType      TaskType               `json:"task_type"`
	Parameters    map[string]interface{} `json:"parameters"`
	Tools         []string               `json:"tools"`
	Complexity    string                 `json:"complexity"`
	EstimatedTime time.Duration          `json:"estimated_time"`
	EstimatedCost float64                `json:"estimated_cost"`
}

// TaskTemplateLibrary manages a collection of task templates
type TaskTemplateLibrary struct {
	templates map[string]TaskTemplate
}

// NewTaskDecomposer creates a new task decomposer instance
func NewTaskDecomposer(llmClient LLMClient) *TaskDecomposer {
	return &TaskDecomposer{
		llmClient:       llmClient,
		templateLibrary: NewTaskTemplateLibrary(),
	}
}

// NewTaskTemplateLibrary creates a new task template library with default templates
func NewTaskTemplateLibrary() *TaskTemplateLibrary {
	library := &TaskTemplateLibrary{
		templates: make(map[string]TaskTemplate),
	}

	// Load default templates
	library.loadDefaultTemplates()

	return library
}

// loadDefaultTemplates loads predefined task templates
func (tl *TaskTemplateLibrary) loadDefaultTemplates() {
	// Data analysis templates
	tl.addTemplate(TaskTemplate{
		ID:            "data_analysis_query",
		Name:          "Data Query and Analysis",
		Description:   "Query data source and perform basic analysis",
		Category:      "data_analysis",
		TaskType:      TaskTypeDataQuery,
		Parameters:    map[string]interface{}{"query_type": "analytical"},
		Tools:         []string{"database", "analytics_engine"},
		Complexity:    "medium",
		EstimatedTime: 30 * time.Minute,
		EstimatedCost: 0.25,
	})

	// Content creation templates
	tl.addTemplate(TaskTemplate{
		ID:            "content_generation",
		Name:          "Generate Content",
		Description:   "Generate text content based on requirements",
		Category:      "content_creation",
		TaskType:      TaskTypeLLMCall,
		Parameters:    map[string]interface{}{"content_type": "text"},
		Tools:         []string{"llm"},
		Complexity:    "low",
		EstimatedTime: 15 * time.Minute,
		EstimatedCost: 0.15,
	})

	// Research templates
	tl.addTemplate(TaskTemplate{
		ID:            "information_gathering",
		Name:          "Gather Information",
		Description:   "Research and collect relevant information",
		Category:      "research",
		TaskType:      TaskTypeAPICall,
		Parameters:    map[string]interface{}{"search_type": "comprehensive"},
		Tools:         []string{"search_api", "web_scraper"},
		Complexity:    "medium",
		EstimatedTime: 45 * time.Minute,
		EstimatedCost: 0.30,
	})

	// Automation templates
	tl.addTemplate(TaskTemplate{
		ID:            "workflow_automation",
		Name:          "Automate Workflow Step",
		Description:   "Execute automated workflow step",
		Category:      "automation",
		TaskType:      TaskTypeAPICall,
		Parameters:    map[string]interface{}{"automation_type": "workflow"},
		Tools:         []string{"automation_engine", "api_client"},
		Complexity:    "high",
		EstimatedTime: 60 * time.Minute,
		EstimatedCost: 0.40,
	})
}

// addTemplate adds a template to the library
func (tl *TaskTemplateLibrary) addTemplate(template TaskTemplate) {
	tl.templates[template.ID] = template
}

// getTemplatesByCategory returns templates for a specific category
func (tl *TaskTemplateLibrary) getTemplatesByCategory(category string) []TaskTemplate {
	var templates []TaskTemplate
	for _, template := range tl.templates {
		if template.Category == category {
			templates = append(templates, template)
		}
	}
	return templates
}

// buildRefinementPrompt creates a prompt for refining template-based plans
func (td *TaskDecomposer) buildRefinementPrompt(goal Goal, tasks []Task, dependencies []Dependency) string {
	tasksJSON, _ := json.MarshalIndent(tasks, "", "  ")
	depsJSON, _ := json.MarshalIndent(dependencies, "", "  ")

	return fmt.Sprintf(`
Review and refine the following task plan for the given goal. Suggest improvements,
additional tasks, or modifications to better achieve the goal.

Goal: %s

Current Tasks:
%s

Current Dependencies:
%s

Provide refinements in JSON format with:
{
  "modifications": [
    {
      "task_id": "task_id_to_modify",
      "changes": {"field": "new_value"}
    }
  ],
  "additional_tasks": [
    // New tasks to add
  ],
  "additional_dependencies": [
    // New dependencies to add
  ]
}

Focus on improving task efficiency, adding missing steps, and optimizing the workflow.
Return only the JSON, no additional text.`,
		goal.Description, string(tasksJSON), string(depsJSON))
}

// applyRefinements applies LLM-suggested refinements to the plan
func (td *TaskDecomposer) applyRefinements(_ string, tasks []Task, dependencies []Dependency) ([]Task, []Dependency, error) {
	// For now, return the original plan
	// This would be implemented to parse and apply the refinements
	return tasks, dependencies, nil
}

// DecomposeTasks breaks down a goal into executable tasks
func (td *TaskDecomposer) DecomposeTasks(ctx context.Context, goal Goal) (*Plan, error) {
	log.Printf("Starting task decomposition for goal: %s", goal.ID)

	// Create plan structure
	plan := &Plan{
		ID:          uuid.New().String(),
		GoalID:      goal.ID,
		Name:        fmt.Sprintf("Plan for: %s", truncateString(goal.Description, 50)),
		Description: fmt.Sprintf("Execution plan to achieve goal: %s", goal.Description),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Status:      PlanStatusDraft,
		Version:     1,
		Metadata:    make(map[string]interface{}),
	}

	// Determine decomposition strategy based on goal complexity
	strategy := td.selectDecompositionStrategy(goal)
	plan.Metadata["decomposition_strategy"] = strategy

	var tasks []Task
	var dependencies []Dependency
	var err error

	switch strategy {
	case "template_based":
		tasks, dependencies, err = td.templateBasedDecomposition(ctx, goal, plan.ID)
	case "llm_assisted":
		tasks, dependencies, err = td.llmAssistedDecomposition(ctx, goal, plan.ID)
	case "hybrid":
		tasks, dependencies, err = td.hybridDecomposition(ctx, goal, plan.ID)
	default:
		tasks, dependencies, err = td.llmAssistedDecomposition(ctx, goal, plan.ID)
	}

	if err != nil {
		return nil, fmt.Errorf("task decomposition failed: %w", err)
	}

	plan.Tasks = tasks
	plan.Dependencies = dependencies

	// Calculate estimates
	td.calculatePlanEstimates(plan)

	// Validate the plan
	if err := td.validatePlan(plan, goal); err != nil {
		return nil, fmt.Errorf("plan validation failed: %w", err)
	}

	log.Printf("Task decomposition completed. Generated %d tasks with %d dependencies",
		len(tasks), len(dependencies))

	return plan, nil
}

// selectDecompositionStrategy determines the best strategy for decomposing the goal
func (td *TaskDecomposer) selectDecompositionStrategy(goal Goal) string {
	complexity := goal.Metadata["complexity"]
	classification := goal.Metadata["classification"]

	// Use template-based for simple, well-known patterns
	if complexity == "low" && td.hasMatchingTemplate(classification) {
		return "template_based"
	}

	// Use hybrid for medium complexity
	if complexity == "medium" {
		return "hybrid"
	}

	// Use LLM-assisted for complex or novel goals
	return "llm_assisted"
}

// hasMatchingTemplate checks if there are suitable templates for the goal
func (td *TaskDecomposer) hasMatchingTemplate(classification interface{}) bool {
	if classification == nil {
		return false
	}

	classStr, ok := classification.(string)
	if !ok {
		return false
	}

	for _, template := range td.templateLibrary.templates {
		if template.Category == classStr {
			return true
		}
	}
	return false
}

// templateBasedDecomposition uses predefined templates to create tasks
func (td *TaskDecomposer) templateBasedDecomposition(_ context.Context, goal Goal, planID string) ([]Task, []Dependency, error) {
	classification := goal.Metadata["classification"]
	if classification == nil {
		return nil, nil, fmt.Errorf("goal classification required for template-based decomposition")
	}

	classStr := classification.(string)
	templates := td.templateLibrary.getTemplatesByCategory(classStr)

	if len(templates) == 0 {
		return nil, nil, fmt.Errorf("no templates found for category: %s", classStr)
	}

	var tasks []Task
	var dependencies []Dependency

	// Create tasks from templates
	for i, template := range templates {
		task := Task{
			ID:            uuid.New().String(),
			PlanID:        planID,
			Name:          template.Name,
			Description:   template.Description,
			Type:          template.TaskType,
			Parameters:    template.Parameters,
			RequiredTools: template.Tools,
			EstimatedCost: template.EstimatedCost,
			EstimatedTime: template.EstimatedTime,
			MaxRetries:    3,
			Status:        TaskStatusPending,
			CreatedAt:     time.Now(),
			Metadata:      make(map[string]interface{}),
		}

		// Customize task based on goal specifics
		td.customizeTaskFromGoal(&task, goal)

		tasks = append(tasks, task)

		// Create sequential dependencies for template-based tasks
		if i > 0 {
			dependency := Dependency{
				ID:          uuid.New().String(),
				FromTaskID:  tasks[i-1].ID,
				ToTaskID:    task.ID,
				Type:        "finish_to_start",
				Description: fmt.Sprintf("Task %s must complete before %s", tasks[i-1].Name, task.Name),
			}
			dependencies = append(dependencies, dependency)
		}
	}

	return tasks, dependencies, nil
}

// llmAssistedDecomposition uses LLM to create a custom task breakdown
func (td *TaskDecomposer) llmAssistedDecomposition(ctx context.Context, goal Goal, planID string) ([]Task, []Dependency, error) {
	prompt := td.buildDecompositionPrompt(goal)

	response, err := td.llmClient.GenerateResponse(ctx, prompt, "gpt-4", map[string]interface{}{
		"temperature": 0.3,
		"max_tokens":  3000,
	})
	if err != nil {
		return nil, nil, fmt.Errorf("LLM decomposition failed: %w", err)
	}

	// Parse the LLM response
	planData, err := td.parseLLMPlanResponse(response)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse LLM response: %w", err)
	}

	// Convert to Task and Dependency objects
	tasks := make([]Task, len(planData.Tasks))
	for i, taskData := range planData.Tasks {
		tasks[i] = Task{
			ID:            uuid.New().String(),
			PlanID:        planID,
			Name:          taskData.Name,
			Description:   taskData.Description,
			Type:          TaskType(taskData.Type),
			Parameters:    taskData.Parameters,
			RequiredTools: taskData.Tools,
			EstimatedCost: taskData.EstimatedCost,
			EstimatedTime: time.Duration(taskData.EstimatedMinutes) * time.Minute,
			MaxRetries:    3,
			Status:        TaskStatusPending,
			CreatedAt:     time.Now(),
			Metadata:      make(map[string]interface{}),
		}
	}

	dependencies := make([]Dependency, len(planData.Dependencies))
	for i, depData := range planData.Dependencies {
		dependencies[i] = Dependency{
			ID:          uuid.New().String(),
			FromTaskID:  tasks[depData.FromIndex].ID,
			ToTaskID:    tasks[depData.ToIndex].ID,
			Type:        depData.Type,
			Description: depData.Description,
		}
	}

	return tasks, dependencies, nil
}

// hybridDecomposition combines templates with LLM customization
func (td *TaskDecomposer) hybridDecomposition(ctx context.Context, goal Goal, planID string) ([]Task, []Dependency, error) {
	// Start with template-based decomposition
	tasks, dependencies, err := td.templateBasedDecomposition(ctx, goal, planID)
	if err != nil {
		// Fall back to LLM-assisted if templates fail
		return td.llmAssistedDecomposition(ctx, goal, planID)
	}

	// Use LLM to refine and customize the template-based plan
	refinementPrompt := td.buildRefinementPrompt(goal, tasks, dependencies)

	response, err := td.llmClient.GenerateResponse(ctx, refinementPrompt, "gpt-4", map[string]interface{}{
		"temperature": 0.2,
		"max_tokens":  2000,
	})
	if err != nil {
		log.Printf("LLM refinement failed, using template-based plan: %v", err)
		return tasks, dependencies, nil
	}

	// Apply refinements if parsing succeeds
	refinedTasks, refinedDeps, err := td.applyRefinements(response, tasks, dependencies)
	if err != nil {
		log.Printf("Failed to apply refinements, using template-based plan: %v", err)
		return tasks, dependencies, nil
	}

	return refinedTasks, refinedDeps, nil
}

// LLMPlanData represents the structure expected from LLM planning responses
type LLMPlanData struct {
	Tasks        []LLMTaskData       `json:"tasks"`
	Dependencies []LLMDependencyData `json:"dependencies"`
}

type LLMTaskData struct {
	Name             string                 `json:"name"`
	Description      string                 `json:"description"`
	Type             string                 `json:"type"`
	Parameters       map[string]interface{} `json:"parameters"`
	Tools            []string               `json:"tools"`
	EstimatedMinutes int                    `json:"estimated_minutes"`
	EstimatedCost    float64                `json:"estimated_cost"`
}

type LLMDependencyData struct {
	FromIndex   int    `json:"from_index"`
	ToIndex     int    `json:"to_index"`
	Type        string `json:"type"`
	Description string `json:"description"`
}

// buildDecompositionPrompt creates a prompt for LLM-assisted task decomposition
func (td *TaskDecomposer) buildDecompositionPrompt(goal Goal) string {
	return fmt.Sprintf(`
You are an expert task planner. Break down the following goal into specific, executable tasks.

Goal: %s

Success Criteria:
%s

Constraints:
%s

Trust Criteria:
%s

Context:
%s

Create a detailed execution plan with the following JSON structure:
{
  "tasks": [
    {
      "name": "Task name",
      "description": "Detailed description",
      "type": "llm_call|data_query|api_call|analysis|validation|aggregation|transform|decision",
      "parameters": {"key": "value"},
      "tools": ["tool1", "tool2"],
      "estimated_minutes": 30,
      "estimated_cost": 0.50
    }
  ],
  "dependencies": [
    {
      "from_index": 0,
      "to_index": 1,
      "type": "finish_to_start",
      "description": "Task 0 must complete before Task 1"
    }
  ]
}

Guidelines:
- Break complex tasks into smaller, manageable subtasks
- Each task should be specific and actionable
- Consider dependencies between tasks
- Estimate realistic time and cost for each task
- Use appropriate task types and tools

Return only the JSON, no additional text.`,
		goal.Description,
		td.formatSuccessCriteria(goal.SuccessCriteria),
		td.formatConstraints(goal.Constraints),
		td.formatTrustCriteria(goal.Trust),
		td.formatContext(goal.Context))
}

// formatSuccessCriteria formats success criteria for the prompt
func (td *TaskDecomposer) formatTrustCriteria(criteria *TrustCriteria) string {
	if criteria == nil {
		return "No specific trust criteria defined."
	}

	var parts []string
	if criteria.MinFairnessScore != nil {
		parts = append(parts, fmt.Sprintf("Minimum fairness score: %.2f", *criteria.MinFairnessScore))
	}
	if criteria.MinRobustnessScore != nil {
		parts = append(parts, fmt.Sprintf("Minimum robustness score: %.2f", *criteria.MinRobustnessScore))
	}
	if criteria.RequireLIMEExplainability != nil && *criteria.RequireLIMEExplainability {
		parts = append(parts, "LIME explainability is required")
	}
	if criteria.RequireSHAPExplainability != nil && *criteria.RequireSHAPExplainability {
		parts = append(parts, "SHAP explainability is required")
	}
	if criteria.EthicalGuardrailsEnforced != nil && *criteria.EthicalGuardrailsEnforced {
		parts = append(parts, "Ethical guardrails must be enforced")
	}

	if len(parts) == 0 {
		return "No specific trust criteria defined."
	}
	return strings.Join(parts, "\n")
}

func (td *TaskDecomposer) formatSuccessCriteria(criteria []SuccessCriterion) string {
	if len(criteria) == 0 {
		return "No specific criteria defined"
	}

	var formatted []string
	for _, c := range criteria {
		formatted = append(formatted, fmt.Sprintf("- %s", c.Description))
	}
	return strings.Join(formatted, "\n")
}

// formatConstraints formats constraints for the prompt
func (td *TaskDecomposer) formatConstraints(constraints []Constraint) string {
	if len(constraints) == 0 {
		return "No specific constraints"
	}

	var formatted []string
	for _, c := range constraints {
		formatted = append(formatted, fmt.Sprintf("- %s: %s", c.Type, c.Description))
	}
	return strings.Join(formatted, "\n")
}

// formatContext formats context information for the prompt
func (td *TaskDecomposer) formatContext(context map[string]interface{}) string {
	if len(context) == 0 {
		return "No additional context"
	}

	var formatted []string
	for k, v := range context {
		formatted = append(formatted, fmt.Sprintf("- %s: %v", k, v))
	}
	return strings.Join(formatted, "\n")
}

// parseLLMPlanResponse parses the JSON response from LLM
func (td *TaskDecomposer) parseLLMPlanResponse(response string) (*LLMPlanData, error) {
	// Clean the response to extract JSON
	// Clean the response to better handle cases where the LLM might return JSON within a markdown block.
	response = strings.TrimSpace(response)
	if strings.HasPrefix(response, "```json") {
		response = strings.TrimPrefix(response, "```json")
		response = strings.TrimSuffix(response, "```")
	}

	var planData LLMPlanData
	if err := json.Unmarshal([]byte(response), &planData); err != nil {
		return nil, fmt.Errorf("failed to parse LLM response JSON: %w. Response: %s", err, response)
	}

	return &planData, nil
}

// customizeTaskFromGoal customizes a template task based on goal specifics
func (td *TaskDecomposer) customizeTaskFromGoal(task *Task, goal Goal) {
	// Add goal-specific parameters
	if task.Parameters == nil {
		task.Parameters = make(map[string]interface{})
	}

	task.Parameters["goal_id"] = goal.ID
	task.Parameters["goal_description"] = goal.Description

	// Adjust based on goal context
	if goal.Context != nil {
		for k, v := range goal.Context {
			task.Parameters[fmt.Sprintf("context_%s", k)] = v
		}
	}

	// Store goal constraints in task metadata
	if task.Metadata == nil {
		task.Metadata = make(map[string]interface{})
	}
	task.Metadata["goal_constraints"] = goal.Constraints
	task.Metadata["goal_trust_criteria"] = goal.Trust
}

// calculatePlanEstimates calculates total cost and time estimates for the plan
func (td *TaskDecomposer) calculatePlanEstimates(plan *Plan) {
	var totalCost float64
	var totalTime time.Duration

	for _, task := range plan.Tasks {
		totalCost += task.EstimatedCost
		totalTime += task.EstimatedTime
	}

	plan.EstimatedCost = totalCost
	plan.EstimatedTime = totalTime
}

// validatePlan validates the generated plan against the goal
func (td *TaskDecomposer) validatePlan(plan *Plan, goal Goal) error {
	if len(plan.Tasks) == 0 {
		return fmt.Errorf("plan must contain at least one task")
	}

	// Check for circular dependencies
	if td.hasCircularDependencies(plan.Dependencies) {
		return fmt.Errorf("plan contains circular dependencies")
	}

	// Validate against constraints
	for _, constraint := range goal.Constraints {
		if err := td.validateConstraint(plan, constraint); err != nil {
			return fmt.Errorf("plan violates constraint: %w", err)
		}
	}

	return nil
}

// hasCircularDependencies checks for circular dependencies in the plan
func (td *TaskDecomposer) hasCircularDependencies(dependencies []Dependency) bool {
	// Build adjacency list
	graph := make(map[string][]string)
	for _, dep := range dependencies {
		graph[dep.FromTaskID] = append(graph[dep.FromTaskID], dep.ToTaskID)
	}

	// DFS to detect cycles
	visited := make(map[string]bool)
	recStack := make(map[string]bool)

	for node := range graph {
		if !visited[node] {
			if td.hasCycleDFS(node, graph, visited, recStack) {
				return true
			}
		}
	}

	return false
}

// hasCycleDFS performs DFS to detect cycles
func (td *TaskDecomposer) hasCycleDFS(node string, graph map[string][]string, visited, recStack map[string]bool) bool {
	visited[node] = true
	recStack[node] = true

	for _, neighbor := range graph[node] {
		if !visited[neighbor] {
			if td.hasCycleDFS(neighbor, graph, visited, recStack) {
				return true
			}
		} else if recStack[neighbor] {
			return true
		}
	}

	recStack[node] = false
	return false
}

// validateConstraint validates a plan against a specific constraint
func (td *TaskDecomposer) validateConstraint(plan *Plan, constraint Constraint) error {
	switch constraint.Type {
	case "cost":
		if limit, ok := constraint.Limit.(float64); ok {
			if plan.EstimatedCost > limit {
				return fmt.Errorf("estimated cost %.2f exceeds limit %.2f", plan.EstimatedCost, limit)
			}
		}
	case "time":
		if limitStr, ok := constraint.Limit.(string); ok {
			if limit, err := time.ParseDuration(limitStr); err == nil {
				if plan.EstimatedTime > limit {
					return fmt.Errorf("estimated time %v exceeds limit %v", plan.EstimatedTime, limit)
				}
			}
		}
	}
	return nil
}

// truncateString truncates a string to the specified length
func truncateString(s string, length int) string {
	if len(s) <= length {
		return s
	}
	return s[:length] + "..."
}
