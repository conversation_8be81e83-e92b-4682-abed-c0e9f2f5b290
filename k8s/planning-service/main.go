package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"ai-optimizer/aigen"

	"github.com/gorilla/mux"
	"github.com/rs/cors"
)

// PlanningService is the main service struct
type PlanningService struct {
	goalParser      *GoalParser
	taskDecomposer  *TaskDecomposer
	executionEngine *ExecutionEngine
	stateManager    *StateManager
	llmClient       LLMClient
	port            string
}

// LLMClient defines the interface for a large language model client

// NewPlanningService creates a new planning service
func NewPlanningService() *PlanningService {
	// Get configuration from environment
	port := getEnv("PORT", "8080")
	// Use proxy-gateway instead of direct AI optimizer connection
	proxyGatewayURL := getEnv("PROXY_GATEWAY_URL", "http://proxy-gateway:8080")
	redisAddr := getEnv("REDIS_ADDR", "redis:6379")

	// Create LLM client that connects to proxy-gateway
	llmClient := aigen.NewAIOptimizerLLMClient(proxyGatewayURL)
	// if err := llmClient.HealthCheck(); err != nil {
	// 	log.Fatalf("AI Optimizer health check failed: %v", err)
	// }

	// Create state manager with Redis storage
	storage, err := NewRedisStateStorage(redisAddr)
	if err != nil {
		log.Fatalf("Failed to create Redis state storage: %v", err)
	}
	stateManager := NewStateManager(storage)

	// Create components
	goalParser := NewGoalParser(llmClient)
	taskDecomposer := NewTaskDecomposer(llmClient)
	executionEngine := NewExecutionEngine(llmClient, stateManager)

	return &PlanningService{
		goalParser:      goalParser,
		taskDecomposer:  taskDecomposer,
		executionEngine: executionEngine,
		stateManager:    stateManager,
		llmClient:       llmClient,
		port:            port,
	}
}

// Start starts the planning service
func (ps *PlanningService) Start() error {
	router := mux.NewRouter()

	// API routes
	api := router.PathPrefix("/v1").Subrouter()

	// Goal management endpoints
	api.HandleFunc("/goals", ps.createGoal).Methods("POST")
	api.HandleFunc("/goals/{id}", ps.getGoal).Methods("GET")
	api.HandleFunc("/goals/{id}", ps.updateGoal).Methods("PUT")
	api.HandleFunc("/goals/{id}", ps.deleteGoal).Methods("DELETE")
	api.HandleFunc("/goals", ps.listGoals).Methods("GET")

	// Plan management endpoints
	api.HandleFunc("/goals/{id}/plan", ps.createPlan).Methods("POST")
	api.HandleFunc("/goals/{id}/plan", ps.getPlan).Methods("GET")
	api.HandleFunc("/goals/{id}/replan", ps.replanGoal).Methods("POST")

	// Execution endpoints
	api.HandleFunc("/goals/{id}/execute", ps.executeGoal).Methods("POST")
	api.HandleFunc("/goals/{id}/status", ps.getExecutionStatus).Methods("GET")
	api.HandleFunc("/goals/{id}/results", ps.getExecutionResults).Methods("GET")

	// Task and context endpoints
	api.HandleFunc("/goals/{id}/tasks", ps.getGoalTasks).Methods("GET")
	api.HandleFunc("/goals/{id}/context", ps.getExecutionContext).Methods("GET")

	// Templates endpoint
	api.HandleFunc("/templates", ps.getTemplates).Methods("GET")

	// Health check
	router.HandleFunc("/health", ps.healthCheck).Methods("GET")

	// Initialize MCP Server
	InitializeMCPPlanningServer(ps)
	router.HandleFunc("/mcp", mcpPlanningServer.HandleMCPConnection)

	// Setup CORS
	c := cors.New(cors.Options{
		AllowedOrigins: []string{"*"},
		AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders: []string{"*"},
	})

	handler := c.Handler(router)

	// Create server
	server := &http.Server{
		Addr:         ":" + ps.port,
		Handler:      handler,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Start server in goroutine
	go func() {
		log.Printf("Planning service starting on port %s", ps.port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Server failed to start: %v", err)
		}
	}()

	// Wait for interrupt signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Planning service shutting down...")

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
		return err
	}

	// Close state manager
	if err := ps.stateManager.Close(); err != nil {
		log.Printf("Error closing state manager: %v", err)
	}

	log.Println("Planning service stopped")
	return nil
}

// HTTP Handlers

// createGoal handles POST /v1/goals
func (ps *PlanningService) createGoal(w http.ResponseWriter, r *http.Request) {
	var request GoalRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Extract user ID from headers or context
	userID := r.Header.Get("X-User-ID")
	if userID == "" {
		userID = "anonymous" // Default user
	}

	// Parse the goal
	goal, err := ps.goalParser.ParseGoal(r.Context(), request, userID)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to parse goal: %v", err), http.StatusBadRequest)
		return
	}

	// Save the goal
	if err := ps.stateManager.SaveGoal(*goal); err != nil {
		http.Error(w, fmt.Sprintf("Failed to save goal: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(goal)
}

// getGoal handles GET /v1/goals/{id}
func (ps *PlanningService) getGoal(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	goalID := vars["id"]

	goal, err := ps.stateManager.GetGoal(goalID)
	if err != nil {
		http.Error(w, fmt.Sprintf("Goal not found: %v", err), http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(goal)
}

// updateGoal handles PUT /v1/goals/{id}
func (ps *PlanningService) updateGoal(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	goalID := vars["id"]

	// Get existing goal
	goal, err := ps.stateManager.GetGoal(goalID)
	if err != nil {
		http.Error(w, fmt.Sprintf("Goal not found: %v", err), http.StatusNotFound)
		return
	}

	// Parse update request
	var updateRequest GoalRequest
	if err := json.NewDecoder(r.Body).Decode(&updateRequest); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Update goal fields
	if updateRequest.Description != "" {
		goal.Description = updateRequest.Description
	}
	if updateRequest.Priority != 0 {
		goal.Priority = updateRequest.Priority
	}
	if updateRequest.Deadline != nil {
		goal.Deadline = updateRequest.Deadline
	}
	if len(updateRequest.SuccessCriteria) > 0 {
		goal.SuccessCriteria = updateRequest.SuccessCriteria
	}
	if len(updateRequest.Constraints) > 0 {
		goal.Constraints = updateRequest.Constraints
	}

	goal.UpdatedAt = time.Now()

	// Save updated goal
	if err := ps.stateManager.UpdateGoal(*goal); err != nil {
		http.Error(w, fmt.Sprintf("Failed to update goal: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(goal)
}

// deleteGoal handles DELETE /v1/goals/{id}
func (ps *PlanningService) deleteGoal(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	goalID := vars["id"]

	// Delete the goal
	if err := ps.stateManager.DeleteGoal(goalID); err != nil {
		http.Error(w, fmt.Sprintf("Failed to delete goal: %v", err), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

// listGoals handles GET /v1/goals
func (ps *PlanningService) listGoals(w http.ResponseWriter, r *http.Request) {
	userID := r.Header.Get("X-User-ID")
	if userID == "" {
		userID = "anonymous"
	}

	status := GoalStatus(r.URL.Query().Get("status"))

	goals, err := ps.stateManager.ListGoals(userID, status)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to list goals: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(goals)
}

// createPlan handles POST /v1/goals/{id}/plan
func (ps *PlanningService) createPlan(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	goalID := vars["id"]

	// Get the goal
	goal, err := ps.stateManager.GetGoal(goalID)
	if err != nil {
		http.Error(w, fmt.Sprintf("Goal not found: %v", err), http.StatusNotFound)
		return
	}

	// Update goal status to planning
	goal.Status = GoalStatusPlanning
	ps.stateManager.UpdateGoal(*goal)

	// Decompose tasks
	plan, err := ps.taskDecomposer.DecomposeTasks(r.Context(), *goal)
	if err != nil {
		goal.Status = GoalStatusFailed
		ps.stateManager.UpdateGoal(*goal)
		http.Error(w, fmt.Sprintf("Failed to create plan: %v", err), http.StatusInternalServerError)
		return
	}

	// Save the plan
	if err := ps.stateManager.SavePlan(*plan); err != nil {
		http.Error(w, fmt.Sprintf("Failed to save plan: %v", err), http.StatusInternalServerError)
		return
	}

	// Update goal status
	goal.Status = GoalStatusPending
	ps.stateManager.UpdateGoal(*goal)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(plan)
}

// getPlan handles GET /v1/goals/{id}/plan
func (ps *PlanningService) getPlan(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	goalID := vars["id"]

	plans, err := ps.stateManager.ListPlans(goalID)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get plans: %v", err), http.StatusInternalServerError)
		return
	}

	if len(plans) == 0 {
		http.Error(w, "No plan found for goal", http.StatusNotFound)
		return
	}

	// Return the latest plan
	latestPlan := plans[0]
	for _, plan := range plans {
		if plan.CreatedAt.After(latestPlan.CreatedAt) {
			latestPlan = plan
		}
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(latestPlan)
}

// executeGoal handles POST /v1/goals/{id}/execute
func (ps *PlanningService) executeGoal(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	goalID := vars["id"]

	// Get the goal
	goal, err := ps.stateManager.GetGoal(goalID)
	if err != nil {
		http.Error(w, fmt.Sprintf("Goal not found: %v", err), http.StatusNotFound)
		return
	}

	// Get the latest plan
	plans, err := ps.stateManager.ListPlans(goalID)
	if err != nil || len(plans) == 0 {
		http.Error(w, "No plan found for goal", http.StatusNotFound)
		return
	}

	latestPlan := plans[0]
	for _, plan := range plans {
		if plan.CreatedAt.After(latestPlan.CreatedAt) {
			latestPlan = plan
		}
	}

	// Parse execution options
	var options ExecutionOptions
	if err := json.NewDecoder(r.Body).Decode(&options); err != nil {
		// Use default options if parsing fails
		options = ExecutionOptions{
			MaxConcurrency: 5,
			Timeout:        30 * time.Minute,
			RetryPolicy: RetryPolicy{
				MaxRetries:    3,
				InitialDelay:  time.Second,
				BackoffFactor: 2.0,
				MaxDelay:      30 * time.Second,
			},
		}
	}

	// Update goal status
	goal.Status = GoalStatusExecuting
	ps.stateManager.UpdateGoal(*goal)

	// Execute plan asynchronously
	go func() {
		err := ps.executionEngine.ExecutePlan(context.Background(), latestPlan, options)
		if err != nil {
			log.Printf("Plan execution failed for goal %s: %v", goalID, err)
			goal.Status = GoalStatusFailed
		} else {
			goal.Status = GoalStatusCompleted
		}
		ps.stateManager.UpdateGoal(*goal)
	}()

	// Return immediate response
	response := map[string]interface{}{
		"message":   "Execution started",
		"goal_id":   goalID,
		"plan_id":   latestPlan.ID,
		"status":    "executing",
		"timestamp": time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// getExecutionStatus handles GET /v1/goals/{id}/status
func (ps *PlanningService) getExecutionStatus(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	goalID := vars["id"]

	goal, err := ps.stateManager.GetGoal(goalID)
	if err != nil {
		http.Error(w, fmt.Sprintf("Goal not found: %v", err), http.StatusNotFound)
		return
	}

	response := map[string]interface{}{
		"goal_id":     goalID,
		"status":      goal.Status,
		"updated_at":  goal.UpdatedAt.Format(time.RFC3339),
		"description": goal.Description,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// getExecutionResults handles GET /v1/goals/{id}/results
func (ps *PlanningService) getExecutionResults(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	goalID := vars["id"]

	// Get plans for the goal
	plans, err := ps.stateManager.ListPlans(goalID)
	if err != nil || len(plans) == 0 {
		http.Error(w, "No plan found for goal", http.StatusNotFound)
		return
	}

	// Get the latest plan
	latestPlan := plans[0]
	for _, plan := range plans {
		if plan.CreatedAt.After(latestPlan.CreatedAt) {
			latestPlan = plan
		}
	}

	// Try to get execution context
	// This is a simplified approach - in practice, you'd track execution context IDs
	response := map[string]interface{}{
		"goal_id":   goalID,
		"plan_id":   latestPlan.ID,
		"plan":      latestPlan,
		"timestamp": time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// getGoalTasks handles GET /v1/goals/{id}/tasks
func (ps *PlanningService) getGoalTasks(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	goalID := vars["id"]

	plans, err := ps.stateManager.ListPlans(goalID)
	if err != nil || len(plans) == 0 {
		http.Error(w, "No plan found for goal", http.StatusNotFound)
		return
	}

	// Get the latest plan
	latestPlan := plans[0]
	for _, plan := range plans {
		if plan.CreatedAt.After(latestPlan.CreatedAt) {
			latestPlan = plan
		}
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(latestPlan.Tasks)
}

// getExecutionContext handles GET /v1/goals/{id}/context
func (ps *PlanningService) getExecutionContext(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	goalID := vars["id"]

	// This is simplified - in practice, you'd maintain a mapping of goals to execution contexts
	response := map[string]interface{}{
		"goal_id":   goalID,
		"message":   "Execution context retrieval not fully implemented",
		"timestamp": time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// replanGoal handles POST /v1/goals/{id}/replan
func (ps *PlanningService) replanGoal(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	goalID := vars["id"]

	// Get the goal
	goal, err := ps.stateManager.GetGoal(goalID)
	if err != nil {
		http.Error(w, fmt.Sprintf("Goal not found: %v", err), http.StatusNotFound)
		return
	}

	// Create new plan
	goal.Status = GoalStatusPlanning
	ps.stateManager.UpdateGoal(*goal)

	plan, err := ps.taskDecomposer.DecomposeTasks(r.Context(), *goal)
	if err != nil {
		goal.Status = GoalStatusFailed
		ps.stateManager.UpdateGoal(*goal)
		http.Error(w, fmt.Sprintf("Failed to create new plan: %v", err), http.StatusInternalServerError)
		return
	}

	// Increment version
	plan.Version = plan.Version + 1

	// Save the new plan
	if err := ps.stateManager.SavePlan(*plan); err != nil {
		http.Error(w, fmt.Sprintf("Failed to save new plan: %v", err), http.StatusInternalServerError)
		return
	}

	goal.Status = GoalStatusPending
	ps.stateManager.UpdateGoal(*goal)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(plan)
}

// getTemplates handles GET /v1/templates
func (ps *PlanningService) getTemplates(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	// Return predefined templates for common goal types
	templates := []map[string]interface{}{
		{
			"id":          "data-analysis",
			"name":        "Data Analysis",
			"description": "Analyze datasets and generate insights",
			"category":    "analytics",
			"template": map[string]interface{}{
				"goal_type": "data_analysis",
				"steps": []string{
					"Load and validate data",
					"Perform exploratory data analysis",
					"Generate statistical summaries",
					"Create visualizations",
					"Extract key insights",
				},
			},
		},
		{
			"id":          "content-generation",
			"name":        "Content Generation",
			"description": "Generate various types of content",
			"category":    "content",
			"template": map[string]interface{}{
				"goal_type": "content_generation",
				"steps": []string{
					"Understand content requirements",
					"Research topic and gather information",
					"Create content outline",
					"Generate content",
					"Review and refine",
				},
			},
		},
		{
			"id":          "code-development",
			"name":        "Code Development",
			"description": "Develop and implement software solutions",
			"category":    "development",
			"template": map[string]interface{}{
				"goal_type": "code_development",
				"steps": []string{
					"Analyze requirements",
					"Design solution architecture",
					"Implement core functionality",
					"Write tests",
					"Document and deploy",
				},
			},
		},
		{
			"id":          "research-synthesis",
			"name":        "Research & Synthesis",
			"description": "Research topics and synthesize findings",
			"category":    "research",
			"template": map[string]interface{}{
				"goal_type": "research_synthesis",
				"steps": []string{
					"Define research scope",
					"Gather relevant sources",
					"Analyze and categorize information",
					"Synthesize findings",
					"Present conclusions",
				},
			},
		},
	}

	json.NewEncoder(w).Encode(templates)
}

// healthCheck handles GET /health
func (ps *PlanningService) healthCheck(w http.ResponseWriter, r *http.Request) {
	response := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now().Format(time.RFC3339),
		"service":   "planning-service",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// getEnv gets an environment variable with a default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// main function
func main() {
	service := NewPlanningService()
	if err := service.Start(); err != nil {
		log.Fatalf("Failed to start planning service: %v", err)
	}
}
