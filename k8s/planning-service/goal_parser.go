package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
)

// LLMClient interface for interacting with LLM services

// GoalParser handles parsing and validation of user goals
type GoalParser struct {
	llmClient LLMClient
}

// NewGoalParser creates a new goal parser instance
func NewGoalParser(llmClient LLMClient) *GoalParser {
	return &GoalParser{
		llmClient: llmClient,
	}
}

// ParseGoal parses a natural language goal description into a structured Goal object
func (gp *GoalParser) ParseGoal(ctx context.Context, request GoalRequest, userID string) (*Goal, error) {
	// Generate unique ID
	goalID := uuid.New().String()

	// Create base goal object
	goal := &Goal{
		ID:          goalID,
		UserID:      userID,
		Description: strings.TrimSpace(request.Description),
		Priority:    request.Priority,
		Deadline:    request.Deadline,
		Context:     request.Context,
		Trust:       request.Trust,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Status:      GoalStatusPending,
		Metadata:    request.Metadata,
	}

	// Set default priority if not specified
	if goal.Priority == 0 {
		goal.Priority = 5 // Medium priority
	}

	// Initialize context if nil
	if goal.Context == nil {
		goal.Context = make(map[string]interface{})
	}

	// Initialize metadata if nil
	if goal.Metadata == nil {
		goal.Metadata = make(map[string]interface{})
	}

	// Parse success criteria from request or extract from description
	if len(request.SuccessCriteria) > 0 {
		goal.SuccessCriteria = request.SuccessCriteria
	} else {
		criteria, err := gp.extractSuccessCriteria(ctx, goal.Description)
		if err != nil {
			log.Printf("Warning: Failed to extract success criteria: %v", err)
			// Continue with empty criteria - can be added later
		} else {
			goal.SuccessCriteria = criteria
		}
	}

	// Parse constraints from request or extract from description
	if len(request.Constraints) > 0 {
		goal.Constraints = request.Constraints
	} else {
		constraints, err := gp.extractConstraints(ctx, goal.Description)
		if err != nil {
			log.Printf("Warning: Failed to extract constraints: %v", err)
			// Continue with empty constraints
		} else {
			goal.Constraints = constraints
		}
	}

	// Validate the goal
	if err := gp.validateGoal(goal); err != nil {
		return nil, fmt.Errorf("goal validation failed: %w", err)
	}

	// Enrich goal with additional context
	if err := gp.enrichGoal(ctx, goal); err != nil {
		log.Printf("Warning: Failed to enrich goal: %v", err)
		// Continue without enrichment
	}

	return goal, nil
}

// extractSuccessCriteria uses LLM to extract success criteria from goal description
func (gp *GoalParser) extractSuccessCriteria(ctx context.Context, description string) ([]SuccessCriterion, error) {
	prompt := fmt.Sprintf(`
Analyze the following goal description and extract specific, measurable success criteria.
Return the criteria as a JSON array of objects with the following structure:
{
  "description": "Clear description of the criterion",
  "metric": "The measurable aspect (e.g., 'accuracy', 'count', 'percentage', 'time')",
  "target": "The target value (number, string, or boolean)",
  "operator": "Comparison operator ('>=', '<=', '==', '!=', 'contains', 'matches')",
  "weight": "Importance weight between 0.0 and 1.0",
  "required": "Boolean indicating if this criterion is mandatory"
}

Goal Description: %s

Focus on extracting concrete, measurable criteria. If no specific criteria are mentioned, 
suggest reasonable default criteria based on the goal type.

Return only the JSON array, no additional text.`, description)

	response, err := gp.llmClient.GenerateResponse(ctx, prompt, "gpt-4", map[string]interface{}{
		"temperature": 0.3,
		"max_tokens":  1000,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to generate success criteria: %w", err)
	}

	var criteria []SuccessCriterion
	if err := json.Unmarshal([]byte(response), &criteria); err != nil {
		// If JSON parsing fails, try to extract from text
		criteria = gp.parseSuccessCriteriaFromText(description)
	}

	// Assign IDs to criteria
	for i := range criteria {
		criteria[i].ID = uuid.New().String()
	}

	return criteria, nil
}

// extractConstraints uses LLM to extract constraints from goal description
func (gp *GoalParser) extractConstraints(ctx context.Context, description string) ([]Constraint, error) {
	prompt := fmt.Sprintf(`
Analyze the following goal description and extract any constraints or limitations.
Return the constraints as a JSON array of objects with the following structure:
{
  "type": "Type of constraint ('cost', 'time', 'quality', 'resource', 'compliance')",
  "description": "Clear description of the constraint",
  "limit": "The limit value (number, string, or boolean)",
  "operator": "Comparison operator ('<=', '>=', '==', '!=', 'contains')",
  "severity": "Constraint severity ('hard', 'soft', 'preference')"
}

Goal Description: %s

Look for mentions of budgets, deadlines, quality requirements, resource limitations, 
compliance needs, etc. If no constraints are explicitly mentioned, suggest reasonable 
default constraints based on the goal type.

Return only the JSON array, no additional text.`, description)

	response, err := gp.llmClient.GenerateResponse(ctx, prompt, "gpt-4", map[string]interface{}{
		"temperature": 0.3,
		"max_tokens":  1000,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to generate constraints: %w", err)
	}

	var constraints []Constraint
	if err := json.Unmarshal([]byte(response), &constraints); err != nil {
		// If JSON parsing fails, try to extract from text
		constraints = gp.parseConstraintsFromText(description)
	}

	// Assign IDs to constraints
	for i := range constraints {
		constraints[i].ID = uuid.New().String()
	}

	return constraints, nil
}

// parseSuccessCriteriaFromText extracts success criteria using regex patterns
func (gp *GoalParser) parseSuccessCriteriaFromText(description string) []SuccessCriterion {
	var criteria []SuccessCriterion

	// Pattern for accuracy/percentage requirements
	accuracyPattern := regexp.MustCompile(`(?i)(accuracy|precision|success rate).*?(\d+)%`)
	if matches := accuracyPattern.FindStringSubmatch(description); len(matches) >= 3 {
		if percentage, err := strconv.ParseFloat(matches[2], 64); err == nil {
			criteria = append(criteria, SuccessCriterion{
				ID:          uuid.New().String(),
				Description: fmt.Sprintf("Achieve %s of at least %s%%", matches[1], matches[2]),
				Metric:      "accuracy",
				Target:      percentage / 100.0,
				Operator:    ">=",
				Weight:      0.8,
				Required:    true,
			})
		}
	}

	// Pattern for count requirements
	countPattern := regexp.MustCompile(`(?i)(generate|create|produce).*?(\d+).*?(items?|responses?|results?)`)
	if matches := countPattern.FindStringSubmatch(description); len(matches) >= 3 {
		if count, err := strconv.Atoi(matches[2]); err == nil {
			criteria = append(criteria, SuccessCriterion{
				ID:          uuid.New().String(),
				Description: fmt.Sprintf("Generate at least %d %s", count, matches[3]),
				Metric:      "count",
				Target:      count,
				Operator:    ">=",
				Weight:      0.9,
				Required:    true,
			})
		}
	}

	// Default criterion if none found
	if len(criteria) == 0 {
		criteria = append(criteria, SuccessCriterion{
			ID:          uuid.New().String(),
			Description: "Task completed successfully",
			Metric:      "completion",
			Target:      true,
			Operator:    "==",
			Weight:      1.0,
			Required:    true,
		})
	}

	return criteria
}

// parseConstraintsFromText extracts constraints using regex patterns
func (gp *GoalParser) parseConstraintsFromText(description string) []Constraint {
	var constraints []Constraint

	// Pattern for cost constraints
	costPattern := regexp.MustCompile(`(?i)(budget|cost).*?\$?(\d+(?:\.\d+)?)`)
	if matches := costPattern.FindStringSubmatch(description); len(matches) >= 3 {
		if cost, err := strconv.ParseFloat(matches[2], 64); err == nil {
			constraints = append(constraints, Constraint{
				ID:          uuid.New().String(),
				Type:        "cost",
				Description: fmt.Sprintf("Stay within budget of $%.2f", cost),
				Limit:       cost,
				Operator:    "<=",
				Severity:    "hard",
			})
		}
	}

	// Pattern for time constraints
	timePattern := regexp.MustCompile(`(?i)(within|in).*?(\d+).*?(minutes?|hours?|days?)`)
	if matches := timePattern.FindStringSubmatch(description); len(matches) >= 4 {
		if duration, err := strconv.Atoi(matches[2]); err == nil {
			var timeLimit time.Duration
			switch strings.ToLower(matches[3]) {
			case "minute", "minutes":
				timeLimit = time.Duration(duration) * time.Minute
			case "hour", "hours":
				timeLimit = time.Duration(duration) * time.Hour
			case "day", "days":
				timeLimit = time.Duration(duration) * 24 * time.Hour
			}

			constraints = append(constraints, Constraint{
				ID:          uuid.New().String(),
				Type:        "time",
				Description: fmt.Sprintf("Complete within %d %s", duration, matches[3]),
				Limit:       timeLimit.String(),
				Operator:    "<=",
				Severity:    "hard",
			})
		}
	}

	return constraints
}

// validateGoal validates the goal structure and content
func (gp *GoalParser) validateGoal(goal *Goal) error {
	if goal.Description == "" {
		return fmt.Errorf("goal description cannot be empty")
	}

	if len(goal.Description) < 10 {
		return fmt.Errorf("goal description too short (minimum 10 characters)")
	}

	if len(goal.Description) > 5000 {
		return fmt.Errorf("goal description too long (maximum 5000 characters)")
	}

	if goal.Priority < 1 || goal.Priority > 10 {
		return fmt.Errorf("priority must be between 1 and 10")
	}

	// Validate success criteria
	for _, criterion := range goal.SuccessCriteria {
		if criterion.Description == "" {
			return fmt.Errorf("success criterion description cannot be empty")
		}
		if criterion.Weight < 0 || criterion.Weight > 1 {
			return fmt.Errorf("success criterion weight must be between 0 and 1")
		}
	}

	// Validate constraints
	for _, constraint := range goal.Constraints {
		if constraint.Type == "" {
			return fmt.Errorf("constraint type cannot be empty")
		}
		if constraint.Severity != "hard" && constraint.Severity != "soft" && constraint.Severity != "preference" {
			return fmt.Errorf("constraint severity must be 'hard', 'soft', or 'preference'")
		}
	}

	return nil
}

// enrichGoal adds additional context and metadata to the goal
func (gp *GoalParser) enrichGoal(ctx context.Context, goal *Goal) error {
	// Add goal classification
	classification, err := gp.classifyGoal(ctx, goal.Description)
	if err == nil {
		goal.Metadata["classification"] = classification
	}

	// Add complexity estimation
	complexity := gp.estimateComplexity(goal)
	goal.Metadata["complexity"] = complexity

	// Add estimated duration if not already set
	if goal.Deadline == nil {
		estimatedDuration := gp.estimateDuration(goal)
		goal.Metadata["estimated_duration"] = estimatedDuration.String()
	}

	return nil
}

// classifyGoal classifies the goal into categories
func (gp *GoalParser) classifyGoal(ctx context.Context, description string) (string, error) {
	prompt := fmt.Sprintf(`
Classify the following goal into one of these categories:
- data_analysis: Goals involving data processing, analysis, or insights
- content_creation: Goals involving generating text, images, or other content
- automation: Goals involving automating processes or workflows
- research: Goals involving information gathering or research
- decision_support: Goals involving analysis to support decision making
- customer_service: Goals involving customer interaction or support
- monitoring: Goals involving tracking or monitoring systems/metrics
- optimization: Goals involving improving efficiency or performance

Goal: %s

Return only the category name, no additional text.`, description)

	response, err := gp.llmClient.GenerateResponse(ctx, prompt, "gpt-3.5-turbo", map[string]interface{}{
		"temperature": 0.1,
		"max_tokens":  50,
	})
	if err != nil {
		return "", err
	}

	return strings.TrimSpace(response), nil
}

// estimateComplexity estimates the complexity of the goal
func (gp *GoalParser) estimateComplexity(goal *Goal) string {
	score := 0

	// Base complexity from description length
	if len(goal.Description) > 500 {
		score += 2
	} else if len(goal.Description) > 200 {
		score += 1
	}

	// Complexity from number of success criteria
	score += len(goal.SuccessCriteria)

	// Complexity from number of constraints
	score += len(goal.Constraints)

	// Complexity from context richness
	score += len(goal.Context) / 3

	if score <= 3 {
		return "low"
	} else if score <= 7 {
		return "medium"
	} else {
		return "high"
	}
}

// estimateDuration estimates how long the goal might take to complete
func (gp *GoalParser) estimateDuration(goal *Goal) time.Duration {
	complexity := goal.Metadata["complexity"]

	baseDuration := time.Hour // Default 1 hour

	switch complexity {
	case "low":
		baseDuration = 30 * time.Minute
	case "medium":
		baseDuration = 2 * time.Hour
	case "high":
		baseDuration = 8 * time.Hour
	}

	// Adjust based on number of success criteria and constraints
	multiplier := 1.0 + float64(len(goal.SuccessCriteria)+len(goal.Constraints))*0.2

	return time.Duration(float64(baseDuration) * multiplier)
}
