package aigen

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// AIClient defines the interface for an AI client
type AIClient interface {
	GenerateResponse(ctx context.Context, prompt string, model string, options map[string]interface{}) (string, error)
	HealthCheck() error
}

// AIOptimizer<PERSON>MClient implements LLMClient by calling the AI Optimizer service
type AIOptimizerLLMClient struct {
	OptimizerURL string
	HttpClient   *http.Client
}

// NewAIOptimizerLLMClient creates a new AI Optimizer LLM client
func NewAIOptimizerLLMClient(proxyGatewayURL string) *AIOptimizerLLMClient {
	return &AIOptimizerLLMClient{
		OptimizerURL: proxyGatewayURL, // URL is now the proxy-gateway
		HttpClient:   &http.Client{Timeout: 60 * time.Second},
	}
}

// GenerateResponse implements the LLMClient interface by calling the proxy-gateway
func (c *AIOptimizerLLMClient) GenerateResponse(ctx context.Context, prompt string, model string, options map[string]interface{}) (string, error) {
	// Construct a request similar to what an OpenAI client would send
	requestBody := map[string]interface{}{
		"model": model,
		"messages": []map[string]string{
			{"role": "user", "content": prompt},
		},
	}

	// Add any extra options to the request body
	for key, value := range options {
		requestBody[key] = value
	}

	requestBytes, err := json.Marshal(requestBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request to proxy-gateway: %w", err)
	}

	// The request is sent to the `/v1/chat/completions` endpoint of the proxy-gateway.
	// Use url.JoinPath for robust URL construction.
	targetURL, err := url.JoinPath(c.OptimizerURL, "/v1/chat/completions")
	if err != nil {
		return "", fmt.Errorf("failed to create target URL: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(string(requestBytes)))
	if err != nil {
		return "", fmt.Errorf("failed to create request to proxy-gateway: %w", err)
	}

	// Set required headers
	req.Header.Set("Content-Type", "application/json")
	// Add any other necessary headers for the gateway (e.g., authentication) here

	resp, err := c.HttpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request to proxy-gateway: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("proxy-gateway returned status %d", resp.StatusCode)
	}

	// The proxy-gateway's response should be in OpenAI format.
	var openAIResponse struct {
		Choices []struct {
			Message struct {
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
		Error struct {
			Message string `json:"message"`
		} `json:"error"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&openAIResponse); err != nil {
		return "", fmt.Errorf("failed to decode response from proxy-gateway: %w", err)
	}

	if openAIResponse.Error.Message != "" {
		return "", fmt.Errorf("proxy-gateway returned an error: %s", openAIResponse.Error.Message)
	}

	if len(openAIResponse.Choices) > 0 {
		return openAIResponse.Choices[0].Message.Content, nil
	}

	return "", fmt.Errorf("no response text found in proxy-gateway response")
}

// HealthCheck is no longer valid as we are not directly calling the ai-optimizer
func (c *AIOptimizerLLMClient) HealthCheck() error {
	// This check is no longer relevant as we are going through the proxy-gateway.
	// We assume the gateway is responsible for its own health checks.
	return nil
}
