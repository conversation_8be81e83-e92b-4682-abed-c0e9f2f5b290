package main

import (
	"context"
	"fmt"
	"log"
	"math"
	"strings"
	"sync"
	"time"
)

// ExecutionEngine orchestrates the execution of plans and tasks
type ExecutionEngine struct {
	llmClient     LLMClient
	stateManager  *StateManager
	taskExecutors map[TaskType]TaskExecutor
	maxConcurrent int
	semaphore     chan struct{}
	mu            sync.RWMutex
}

// TaskExecutor interface for executing different types of tasks
type TaskExecutor interface {
	Execute(ctx context.Context, task Task, context ExecutionContext) (*TaskResult, error)
	CanExecute(taskType TaskType) bool
	EstimateResources(task Task) ResourceRequirement
}

// ResourceRequirement represents the resources needed for task execution
type ResourceRequirement struct {
	CPU    float64       `json:"cpu"`
	Memory int64         `json:"memory"`
	Cost   float64       `json:"cost"`
	Time   time.Duration `json:"time"`
}

// ExecutionOptions contains options for plan execution
type ExecutionOptions struct {
	MaxConcurrency int                    `json:"max_concurrency"`
	Timeout        time.Duration          `json:"timeout"`
	RetryPolicy    RetryPolicy            `json:"retry_policy"`
	Preferences    map[string]interface{} `json:"preferences"`
}

// RetryPolicy defines how failed tasks should be retried
type RetryPolicy struct {
	MaxRetries    int           `json:"max_retries"`
	InitialDelay  time.Duration `json:"initial_delay"`
	BackoffFactor float64       `json:"backoff_factor"`
	MaxDelay      time.Duration `json:"max_delay"`
}

// NewExecutionEngine creates a new execution engine
func NewExecutionEngine(llmClient LLMClient, stateManager *StateManager) *ExecutionEngine {
	engine := &ExecutionEngine{
		llmClient:     llmClient,
		stateManager:  stateManager,
		taskExecutors: make(map[TaskType]TaskExecutor),
		maxConcurrent: 10, // Default concurrency limit
		semaphore:     make(chan struct{}, 10),
	}

	// Register default task executors
	engine.registerDefaultExecutors()

	return engine
}

// registerDefaultExecutors registers the default task executors
func (ee *ExecutionEngine) registerDefaultExecutors() {
	ee.RegisterExecutor(NewLLMTaskExecutor(ee.llmClient))
	ee.RegisterExecutor(NewDataQueryExecutor())
	ee.RegisterExecutor(NewAPICallExecutor())
	ee.RegisterExecutor(NewAnalysisExecutor(ee.llmClient))
	ee.RegisterExecutor(NewValidationExecutor())
	ee.RegisterExecutor(NewAggregationExecutor())
}

// RegisterExecutor registers a task executor for specific task types
func (ee *ExecutionEngine) RegisterExecutor(executor TaskExecutor) {
	ee.mu.Lock()
	defer ee.mu.Unlock()

	// Register executor for all task types it can handle
	for _, taskType := range []TaskType{
		TaskTypeLLMCall, TaskTypeDataQuery, TaskTypeAPICall,
		TaskTypeAnalysis, TaskTypeValidation, TaskTypeAggregation,
		TaskTypeTransform, TaskTypeDecision,
	} {
		if executor.CanExecute(taskType) {
			ee.taskExecutors[taskType] = executor
		}
	}
}

// ExecutePlan executes a complete plan
func (ee *ExecutionEngine) ExecutePlan(ctx context.Context, plan Plan, options ExecutionOptions) error {
	log.Printf("Starting execution of plan: %s", plan.ID)

	// Create execution context
	execCtx, err := ee.stateManager.CreateExecutionContext(plan.GoalID, plan.ID)
	if err != nil {
		return fmt.Errorf("failed to create execution context: %w", err)
	}

	// Load the goal to access trust criteria
	goal, err := ee.stateManager.GetGoal(plan.GoalID)
	if err != nil {
		return fmt.Errorf("failed to retrieve goal %s for execution: %w", plan.GoalID, err)
	}

	// Update plan status
	plan.Status = PlanStatusExecuting
	if err := ee.stateManager.UpdatePlan(plan); err != nil {
		log.Printf("Warning: Failed to update plan status: %v", err)
	}

	// Build task dependency graph
	taskGraph := ee.buildTaskGraph(plan.Tasks, plan.Dependencies)

	// Execute tasks according to dependencies
	err = ee.executeTaskGraph(ctx, taskGraph, execCtx, goal, options)

	// Update final plan status
	if err != nil {
		plan.Status = PlanStatusFailed
		execCtx.CurrentState = ExecutionStateFailed
	} else {
		plan.Status = PlanStatusCompleted
		execCtx.CurrentState = ExecutionStateCompleted
	}

	// Save final state
	if updateErr := ee.stateManager.UpdatePlan(plan); updateErr != nil {
		log.Printf("Warning: Failed to update final plan status: %v", updateErr)
	}
	if updateErr := ee.stateManager.UpdateExecutionContext(*execCtx); updateErr != nil {
		log.Printf("Warning: Failed to update final execution context: %v", updateErr)
	}

	log.Printf("Plan execution completed: %s (success: %t)", plan.ID, err == nil)
	return err
}

// TaskNode represents a node in the task dependency graph
type TaskNode struct {
	Task         Task
	Dependencies []*TaskNode
	Dependents   []*TaskNode
	Status       TaskStatus
	Result       *TaskResult
}

// buildTaskGraph builds a dependency graph from tasks and dependencies
func (ee *ExecutionEngine) buildTaskGraph(tasks []Task, dependencies []Dependency) map[string]*TaskNode {
	// Create nodes for all tasks
	nodes := make(map[string]*TaskNode)
	for _, task := range tasks {
		nodes[task.ID] = &TaskNode{
			Task:         task,
			Dependencies: make([]*TaskNode, 0),
			Dependents:   make([]*TaskNode, 0),
			Status:       task.Status,
		}
	}

	// Build dependency relationships
	for _, dep := range dependencies {
		fromNode := nodes[dep.FromTaskID]
		toNode := nodes[dep.ToTaskID]

		if fromNode != nil && toNode != nil {
			toNode.Dependencies = append(toNode.Dependencies, fromNode)
			fromNode.Dependents = append(fromNode.Dependents, toNode)
		}
	}

	return nodes
}

// executeTaskGraph executes tasks in the dependency graph
func (ee *ExecutionEngine) executeTaskGraph(ctx context.Context, taskGraph map[string]*TaskNode,
	execCtx *ExecutionContext, goal *Goal, options ExecutionOptions) error {

	// Find ready tasks (no dependencies or all dependencies completed)
	readyTasks := ee.findReadyTasks(taskGraph)

	// Track completion
	completed := make(map[string]bool)
	failed := make(map[string]bool)

	// Execute tasks in waves
	for len(readyTasks) > 0 {
		// Execute current wave of ready tasks
		err := ee.executeTaskWave(ctx, readyTasks, execCtx, goal.Trust, options)
		if err != nil {
			log.Printf("Task wave execution encountered an error: %v", err)
		}

		// Update completion status
		for _, node := range readyTasks {
			if node.Result != nil && node.Result.Success {
				completed[node.Task.ID] = true
				node.Status = TaskStatusCompleted
			} else {
				failed[node.Task.ID] = true
				node.Status = TaskStatusFailed
			}
		}

		// Find next wave of ready tasks
		readyTasks = ee.findReadyTasks(taskGraph)

		// Remove already processed tasks
		var nextWave []*TaskNode
		for _, node := range readyTasks {
			if !completed[node.Task.ID] && !failed[node.Task.ID] {
				// Check if all dependencies are completed
				allDepsCompleted := true
				for _, dep := range node.Dependencies {
					if !completed[dep.Task.ID] {
						allDepsCompleted = false
						break
					}
				}
				if allDepsCompleted {
					nextWave = append(nextWave, node)
				}
			}
		}
		readyTasks = nextWave

		// Check for deadlock (no progress possible)
		if len(readyTasks) == 0 {
			remaining := 0
			for _, node := range taskGraph {
				if !completed[node.Task.ID] && !failed[node.Task.ID] {
					remaining++
				}
			}
			if remaining > 0 {
				return fmt.Errorf("execution deadlock: %d tasks remaining but none are ready", remaining)
			}
		}
	}

	// Check if any critical tasks failed
	for taskID, hasFailed := range failed {
		if hasFailed {
			node := taskGraph[taskID]
			if node != nil && ee.isTaskCritical(node.Task) {
				return fmt.Errorf("critical task failed: %s", taskID)
			}
		}
	}

	return nil
}

// findReadyTasks finds tasks that are ready to execute
func (ee *ExecutionEngine) findReadyTasks(taskGraph map[string]*TaskNode) []*TaskNode {
	var ready []*TaskNode

	for _, node := range taskGraph {
		if node.Status == TaskStatusPending || node.Status == TaskStatusReady {
			// Check if all dependencies are completed
			allDepsCompleted := true
			for _, dep := range node.Dependencies {
				if dep.Status != TaskStatusCompleted {
					allDepsCompleted = false
					break
				}
			}

			if allDepsCompleted {
				node.Status = TaskStatusReady
				ready = append(ready, node)
			}
		}
	}

	return ready
}

// executeTaskWave executes a wave of ready tasks concurrently
func (ee *ExecutionEngine) executeTaskWave(ctx context.Context, tasks []*TaskNode,
	execCtx *ExecutionContext, trustCriteria *TrustCriteria, options ExecutionOptions) error {

	if len(tasks) == 0 {
		return nil
	}

	// Limit concurrency
	maxConcurrent := options.MaxConcurrency
	if maxConcurrent <= 0 {
		maxConcurrent = ee.maxConcurrent
	}

	// Create semaphore for this wave
	sem := make(chan struct{}, maxConcurrent)

	// Execute tasks concurrently
	var wg sync.WaitGroup
	errors := make(chan error, len(tasks))

	for _, node := range tasks {
		wg.Add(1)
		go func(n *TaskNode) {
			defer wg.Done()

			// Acquire semaphore
			sem <- struct{}{}
			defer func() { <-sem }()

			// Check trust criteria before execution
			if err := ee.validateTaskTrust(n.Task, trustCriteria); err != nil {
				// Mark as failed and skip execution
				n.Status = TaskStatusFailed
				n.Result = &TaskResult{
					TaskID:  n.Task.ID,
					Success: false,
					Error:   fmt.Sprintf("Trust validation failed: %v", err),
				}
				log.Printf("Task %s failed trust validation: %v", n.Task.ID, err)
				return // Exit goroutine for this task
			}

			// Execute task
			result, err := ee.executeTask(ctx, n.Task, *execCtx, options)
			if err != nil {
				errors <- fmt.Errorf("task %s execution failed: %w", n.Task.ID, err)
				n.Status = TaskStatusFailed // Ensure node status is updated on error
				n.Result = result           // Store partial/error result
				return
			}

			// Store result
			n.Result = result

			// Update execution context with result
			ee.stateManager.UpdateTaskResult(execCtx, n.Task.ID, *result)

		}(node)
	}

	// Wait for all tasks to complete
	wg.Wait()
	close(errors)

	// Check for errors
	var firstError error
	for err := range errors {
		if firstError == nil {
			firstError = err
		}
		log.Printf("Task execution error: %v", err)
	}

	return firstError
}

// executeTask executes a single task
func (ee *ExecutionEngine) executeTask(ctx context.Context, task Task,
	execCtx ExecutionContext, options ExecutionOptions) (*TaskResult, error) {

	log.Printf("Executing task: %s (%s)", task.Name, task.Type)

	// Find appropriate executor
	ee.mu.RLock()
	executor, exists := ee.taskExecutors[task.Type]
	ee.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("no executor found for task type: %s", task.Type)
	}

	// Update task status
	task.Status = TaskStatusExecuting
	startTime := time.Now()
	task.StartedAt = &startTime

	// Execute with retry logic
	var result *TaskResult
	var err error

	retryPolicy := options.RetryPolicy
	if retryPolicy.MaxRetries == 0 {
		retryPolicy = RetryPolicy{
			MaxRetries:    3,
			InitialDelay:  time.Second,
			BackoffFactor: 2.0,
			MaxDelay:      30 * time.Second,
		}
	}

	for attempt := 0; attempt <= retryPolicy.MaxRetries; attempt++ {
		if attempt > 0 {
			// Calculate delay with exponential backoff
			delay := time.Duration(float64(retryPolicy.InitialDelay) *
				math.Pow(retryPolicy.BackoffFactor, float64(attempt-1)))
			if delay > retryPolicy.MaxDelay {
				delay = retryPolicy.MaxDelay
			}

			log.Printf("Retrying task %s (attempt %d/%d) after %v",
				task.ID, attempt+1, retryPolicy.MaxRetries+1, delay)
			time.Sleep(delay)
		}

		result, err = executor.Execute(ctx, task, execCtx)
		if err == nil && result != nil && result.Success {
			break // Success
		}

		if attempt < retryPolicy.MaxRetries {
			log.Printf("Task %s failed (attempt %d), will retry: %v", task.ID, attempt+1, err)
		}
	}

	// Update task completion
	completedAt := time.Now()
	task.CompletedAt = &completedAt

	if err != nil || result == nil || !result.Success {
		task.Status = TaskStatusFailed
		if result == nil {
			result = &TaskResult{
				TaskID:      task.ID,
				Success:     false,
				Error:       fmt.Sprintf("Task execution failed: %v", err),
				Duration:    time.Since(startTime),
				CompletedAt: completedAt,
			}
		}
	} else {
		task.Status = TaskStatusCompleted
		result.Duration = time.Since(startTime)
		result.CompletedAt = completedAt
	}

	return result, nil
}

// isTaskCritical determines if a task is critical for plan success
func (ee *ExecutionEngine) isTaskCritical(_ Task) bool {
	// For now, consider all tasks critical
	// This could be enhanced to check task metadata or dependencies
	return true
}

// validateTaskTrust checks a task against the goal's trust criteria before execution.
func (ee *ExecutionEngine) validateTaskTrust(task Task, criteria *TrustCriteria) error {
	if criteria == nil {
		return nil // No criteria to validate against
	}

	// Example: Ethical Guardrail for content generation
	// This is a placeholder for a more sophisticated check, potentially calling the Governance Service.
	if criteria.EthicalGuardrailsEnforced != nil && *criteria.EthicalGuardrailsEnforced {
		if task.Type == TaskTypeLLMCall {
			if prompt, ok := task.Parameters["prompt"].(string); ok {
				// Simple check for harmful content keywords. In reality, this would be a call
				// to a content moderation API or a more advanced classifier.
				harmfulKeywords := []string{"hate speech", "build a bomb", "illegal activities"}
				for _, keyword := range harmfulKeywords {
					if strings.Contains(strings.ToLower(prompt), keyword) {
						return fmt.Errorf("task prompt contains potentially harmful content ('%s')", keyword)
					}
				}
			}
		}
	}

	// Other trust criteria like fairness or robustness would be checked here,
	// likely by inspecting task metadata that indicates the properties of the
	// tool/agent intended to be used (once that info is available at this stage).
	// For now, these are placeholders.
	log.Printf("Task %s passed trust validation.", task.ID)

	return nil
}
