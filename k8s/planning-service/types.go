package main

import (
	"time"
)

// GoalStatus represents the current status of a goal
type GoalStatus string

const (
	GoalStatusPending   GoalStatus = "pending"
	GoalStatusPlanning  GoalStatus = "planning"
	GoalStatusExecuting GoalStatus = "executing"
	GoalStatusCompleted GoalStatus = "completed"
	GoalStatusFailed    GoalStatus = "failed"
	GoalStatusCancelled GoalStatus = "cancelled"
	GoalStatusPaused    GoalStatus = "paused"
)

// PlanStatus represents the current status of a plan
type PlanStatus string

const (
	PlanStatusDraft     PlanStatus = "draft"
	PlanStatusApproved  PlanStatus = "approved"
	PlanStatusExecuting PlanStatus = "executing"
	PlanStatusCompleted PlanStatus = "completed"
	PlanStatusFailed    PlanStatus = "failed"
	PlanStatusObsolete  PlanStatus = "obsolete"
)

// TaskStatus represents the current status of a task
type TaskStatus string

const (
	TaskStatusPending   TaskStatus = "pending"
	TaskStatusReady     TaskStatus = "ready"
	TaskStatusExecuting TaskStatus = "executing"
	TaskStatusCompleted TaskStatus = "completed"
	TaskStatusFailed    TaskStatus = "failed"
	TaskStatusSkipped   TaskStatus = "skipped"
	TaskStatusRetrying  TaskStatus = "retrying"
)

// TaskType represents different types of tasks
type TaskType string

const (
	TaskTypeLLMCall     TaskType = "llm_call"
	TaskTypeDataQuery   TaskType = "data_query"
	TaskTypeAPICall     TaskType = "api_call"
	TaskTypeAnalysis    TaskType = "analysis"
	TaskTypeValidation  TaskType = "validation"
	TaskTypeAggregation TaskType = "aggregation"
	TaskTypeTransform   TaskType = "transform"
	TaskTypeDecision    TaskType = "decision"
)

// ExecutionState represents the current state of execution
type ExecutionState string

const (
	ExecutionStateInitializing ExecutionState = "initializing"
	ExecutionStateRunning      ExecutionState = "running"
	ExecutionStatePaused       ExecutionState = "paused"
	ExecutionStateCompleted    ExecutionState = "completed"
	ExecutionStateFailed       ExecutionState = "failed"
	ExecutionStateCancelled    ExecutionState = "cancelled"
)

// Goal represents a high-level objective submitted by a user
type Goal struct {
	ID              string                 `json:"id"`
	UserID          string                 `json:"user_id"`
	Description     string                 `json:"description"`
	SuccessCriteria []SuccessCriterion     `json:"success_criteria"`
	Constraints     []Constraint           `json:"constraints"`
	Trust           *TrustCriteria         `json:"trust,omitempty"`
	Context         map[string]interface{} `json:"context"`
	Priority        int                    `json:"priority"`
	Deadline        *time.Time             `json:"deadline,omitempty"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
	Status          GoalStatus             `json:"status"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// SuccessCriterion defines what constitutes success for a goal
type SuccessCriterion struct {
	ID          string      `json:"id"`
	Description string      `json:"description"`
	Metric      string      `json:"metric"`
	Target      interface{} `json:"target"`
	Operator    string      `json:"operator"` // ">=", "==", "contains", "matches", etc.
	Weight      float64     `json:"weight"`   // Importance weight (0.0 to 1.0)
	Required    bool        `json:"required"` // Whether this criterion is mandatory
}

// Constraint defines limitations or requirements for goal execution
type Constraint struct {
	ID          string      `json:"id"`
	Type        string      `json:"type"` // "cost", "time", "quality", "resource", "compliance"
	Description string      `json:"description"`
	Limit       interface{} `json:"limit"`
	Operator    string      `json:"operator"` // "<=", ">=", "==", "!=", etc.
	Severity    string      `json:"severity"` // "hard", "soft", "preference"
}

// Plan represents an execution plan for achieving a goal
type Plan struct {
	ID            string                 `json:"id"`
	GoalID        string                 `json:"goal_id"`
	Name          string                 `json:"name"`
	Description   string                 `json:"description"`
	Tasks         []Task                 `json:"tasks"`
	Dependencies  []Dependency           `json:"dependencies"`
	EstimatedCost float64                `json:"estimated_cost"`
	EstimatedTime time.Duration          `json:"estimated_time"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
	Status        PlanStatus             `json:"status"`
	Version       int                    `json:"version"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// Task represents an individual executable unit within a plan
type Task struct {
	ID            string                 `json:"id"`
	PlanID        string                 `json:"plan_id"`
	Name          string                 `json:"name"`
	Description   string                 `json:"description"`
	Type          TaskType               `json:"type"`
	Parameters    map[string]interface{} `json:"parameters"`
	RequiredTools []string               `json:"required_tools"`
	EstimatedCost float64                `json:"estimated_cost"`
	EstimatedTime time.Duration          `json:"estimated_time"`
	MaxRetries    int                    `json:"max_retries"`
	RetryCount    int                    `json:"retry_count"`
	Status        TaskStatus             `json:"status"`
	Result        *TaskResult            `json:"result,omitempty"`
	CreatedAt     time.Time              `json:"created_at"`
	StartedAt     *time.Time             `json:"started_at,omitempty"`
	CompletedAt   *time.Time             `json:"completed_at,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// Dependency represents a dependency relationship between tasks
type Dependency struct {
	ID          string `json:"id"`
	FromTaskID  string `json:"from_task_id"`
	ToTaskID    string `json:"to_task_id"`
	Type        string `json:"type"`      // "finish_to_start", "start_to_start", "finish_to_finish"
	Condition   string `json:"condition"` // Optional condition for dependency
	Description string `json:"description"`
}

// TaskResult represents the outcome of task execution
type TaskResult struct {
	TaskID      string                 `json:"task_id"`
	Success     bool                   `json:"success"`
	Output      interface{}            `json:"output"`
	Metadata    map[string]interface{} `json:"metadata"`
	Cost        float64                `json:"cost"`
	Duration    time.Duration          `json:"duration"`
	Error       string                 `json:"error,omitempty"`
	CompletedAt time.Time              `json:"completed_at"`
	Quality     *QualityMetrics        `json:"quality,omitempty"`
}

// QualityMetrics represents quality assessment of task results
type QualityMetrics struct {
	Accuracy     float64 `json:"accuracy"`
	Relevance    float64 `json:"relevance"`
	Completeness float64 `json:"completeness"`
	Coherence    float64 `json:"coherence"`
	Overall      float64 `json:"overall"`
}

// ExecutionContext maintains state and context during plan execution
type ExecutionContext struct {
	ID           string                 `json:"id"`
	GoalID       string                 `json:"goal_id"`
	PlanID       string                 `json:"plan_id"`
	CurrentState ExecutionState         `json:"current_state"`
	Variables    map[string]interface{} `json:"variables"`
	Results      map[string]TaskResult  `json:"results"`
	Metadata     map[string]interface{} `json:"metadata"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
}

// GoalRequest represents a request to create a new goal
type GoalRequest struct {
	Description     string                 `json:"description"`
	SuccessCriteria []SuccessCriterion     `json:"success_criteria,omitempty"`
	Constraints     []Constraint           `json:"constraints,omitempty"`
	Trust           *TrustCriteria         `json:"trust,omitempty"`
	Context         map[string]interface{} `json:"context,omitempty"`
	Priority        int                    `json:"priority,omitempty"`
	Deadline        *time.Time             `json:"deadline,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// PlanningRequest represents a request for task decomposition
type PlanningRequest struct {
	GoalID      string                 `json:"goal_id"`
	Goal        Goal                   `json:"goal"`
	Context     map[string]interface{} `json:"context,omitempty"`
	Preferences map[string]interface{} `json:"preferences,omitempty"`
}

// PlanningResponse represents the response from task decomposition
type PlanningResponse struct {
	Plan         Plan    `json:"plan"`
	Confidence   float64 `json:"confidence"`
	Reasoning    string  `json:"reasoning"`
	Alternatives []Plan  `json:"alternatives,omitempty"`
}

// ExecutionRequest represents a request to execute a plan
type ExecutionRequest struct {
	PlanID  string                 `json:"plan_id"`
	Context map[string]interface{} `json:"context,omitempty"`
	Options map[string]interface{} `json:"options,omitempty"`
}

// ProgressUpdate represents a progress update during execution
type ProgressUpdate struct {
	GoalID                 string        `json:"goal_id"`
	PlanID                 string        `json:"plan_id"`
	TaskID                 string        `json:"task_id,omitempty"`
	Status                 string        `json:"status"`
	Progress               float64       `json:"progress"` // 0.0 to 1.0
	Message                string        `json:"message"`
	EstimatedTimeRemaining time.Duration `json:"estimated_time_remaining"`
	Timestamp              time.Time     `json:"timestamp"`
}

// TrustCriteria defines trust-related requirements for goal execution.
type TrustCriteria struct {
	MinFairnessScore          *float64 `json:"min_fairness_score,omitempty"`
	MinRobustnessScore        *float64 `json:"min_robustness_score,omitempty"`
	RequireLIMEExplainability *bool    `json:"require_lime_explainability,omitempty"`
	RequireSHAPExplainability *bool    `json:"require_shap_explainability,omitempty"`
	EthicalGuardrailsEnforced *bool    `json:"ethical_guardrails_enforced,omitempty"`
}
