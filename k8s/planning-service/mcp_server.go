package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gorilla/websocket"
)

// MCPPlanningServer provides MCP server capabilities for the Planning Service
type MCPPlanningServer struct {
	upgrader        websocket.Upgrader
	planningService *PlanningService
}

// MCPRequest represents an incoming MCP JSON-RPC request
type MCPRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      interface{} `json:"id"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params,omitempty"`
}

// MCPResponse represents an outgoing MCP JSON-RPC response
type MCPResponse struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      interface{} `json:"id"`
	Result  interface{} `json:"result,omitempty"`
	Error   *MCPError   `json:"error,omitempty"`
}

// MCPError represents an MCP error
type MCPError struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// MCPTool represents an MCP tool definition
type MCPTool struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	InputSchema interface{} `json:"inputSchema"`
}

// MCPPrompt represents an MCP prompt template
type MCPPrompt struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Arguments   interface{} `json:"arguments,omitempty"`
}

// MCPResource represents an MCP resource definition
type MCPResource struct {
	URI         string `json:"uri"`
	Name        string `json:"name"`
	Description string `json:"description"`
	MimeType    string `json:"mimeType,omitempty"`
}

// NewMCPPlanningServer creates a new MCP server instance for Planning Service
func NewMCPPlanningServer(ps *PlanningService) *MCPPlanningServer {
	return &MCPPlanningServer{
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// Allow connections from any origin for now
				// In production, implement proper origin checking
				return true
			},
		},
		planningService: ps,
	}
}

// HandleMCPConnection handles MCP WebSocket connections
func (s *MCPPlanningServer) HandleMCPConnection(w http.ResponseWriter, r *http.Request) {
	conn, err := s.upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("Planning MCP Server: Failed to upgrade to WebSocket: %v", err)
		return
	}
	defer conn.Close()

	log.Printf("Planning MCP Server: New client connected")

	// Handle MCP protocol initialization
	if err := s.handleInitialization(conn); err != nil {
		log.Printf("Planning MCP Server: Initialization failed: %v", err)
		return
	}

	// Handle incoming messages
	for {
		var req MCPRequest
		if err := conn.ReadJSON(&req); err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("Planning MCP Server: WebSocket error: %v", err)
			}
			break
		}

		response := s.handleMCPRequest(req)
		if err := conn.WriteJSON(response); err != nil {
			log.Printf("Planning MCP Server: Error sending response: %v", err)
			break
		}
	}

	log.Printf("Planning MCP Server: Client disconnected")
}

// handleInitialization handles the MCP initialization handshake
func (s *MCPPlanningServer) handleInitialization(conn *websocket.Conn) error {
	// Send server capabilities
	capabilities := map[string]interface{}{
		"tools": map[string]interface{}{
			"listChanged": true,
		},
		"prompts": map[string]interface{}{
			"listChanged": true,
		},
		"resources": map[string]interface{}{
			"subscribe":   true,
			"listChanged": true,
		},
	}

	initResponse := MCPResponse{
		JSONRPC: "2.0",
		ID:      "init",
		Result: map[string]interface{}{
			"protocolVersion": "2025-06-18",
			"capabilities":    capabilities,
			"serverInfo": map[string]interface{}{
				"name":    "planning-service-mcp-server",
				"version": "1.0.0",
			},
		},
	}

	return conn.WriteJSON(initResponse)
}

// handleMCPRequest processes incoming MCP requests
func (s *MCPPlanningServer) handleMCPRequest(req MCPRequest) MCPResponse {
	switch req.Method {
	case "tools/list":
		return s.handleToolsList(req)
	case "tools/call":
		return s.handleToolCall(req)
	case "prompts/list":
		return s.handlePromptsList(req)
	case "prompts/get":
		return s.handlePromptGet(req)
	case "resources/list":
		return s.handleResourcesList(req)
	case "resources/read":
		return s.handleResourceRead(req)
	default:
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32601,
				Message: "Method not found",
			},
		}
	}
}

// handleToolsList returns the list of available tools
func (s *MCPPlanningServer) handleToolsList(req MCPRequest) MCPResponse {
	tools := []MCPTool{
		{
			Name:        "create_goal",
			Description: "Create a new autonomous goal for execution",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"description": map[string]interface{}{
						"type":        "string",
						"description": "High-level goal description in natural language",
					},
					"priority": map[string]interface{}{
						"type":        "integer",
						"description": "Goal priority (1-10, higher is more urgent)",
						"minimum":     1,
						"maximum":     10,
					},
					"constraints": map[string]interface{}{
						"type":        "array",
						"description": "Constraints and limitations for goal execution",
						"items": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"type":        map[string]interface{}{"type": "string"},
								"description": map[string]interface{}{"type": "string"},
								"limit":       map[string]interface{}{"type": "string"},
							},
						},
					},
					"user_id": map[string]interface{}{
						"type":        "string",
						"description": "User ID for goal ownership",
					},
				},
				"required": []string{"description"},
			},
		},
		{
			Name:        "decompose_goal",
			Description: "Break down a high-level goal into executable tasks",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"goal_id": map[string]interface{}{
						"type":        "string",
						"description": "ID of the goal to decompose",
					},
				},
				"required": []string{"goal_id"},
			},
		},
		{
			Name:        "execute_plan",
			Description: "Execute a planned task sequence",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"goal_id": map[string]interface{}{
						"type":        "string",
						"description": "ID of the goal to execute",
					},
					"dry_run": map[string]interface{}{
						"type":        "boolean",
						"description": "Whether to perform a dry run without actual execution",
						"default":     false,
					},
				},
				"required": []string{"goal_id"},
			},
		},
		{
			Name:        "monitor_execution",
			Description: "Track goal execution progress and status",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"goal_id": map[string]interface{}{
						"type":        "string",
						"description": "ID of the goal to monitor",
					},
				},
				"required": []string{"goal_id"},
			},
		},
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      req.ID,
		Result: map[string]interface{}{
			"tools": tools,
		},
	}
}

// handleToolCall executes a tool call
func (s *MCPPlanningServer) handleToolCall(req MCPRequest) MCPResponse {
	params, ok := req.Params.(map[string]interface{})
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "Invalid params",
			},
		}
	}

	toolName, ok := params["name"].(string)
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "Tool name required",
			},
		}
	}

	arguments, _ := params["arguments"].(map[string]interface{})

	switch toolName {
	case "create_goal":
		return s.executeCreateGoal(req.ID, arguments)
	case "decompose_goal":
		return s.executeDecomposeGoal(req.ID, arguments)
	case "execute_plan":
		return s.executeExecutePlan(req.ID, arguments)
	case "monitor_execution":
		return s.executeMonitorExecution(req.ID, arguments)
	default:
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32601,
				Message: "Tool not found",
			},
		}
	}
}

// Helper function to get string from map
func getString(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}

// Helper function to get int from map
func getInt(m map[string]interface{}, key string) int {
	if val, ok := m[key].(float64); ok {
		return int(val)
	}
	return 0
}

// Helper function to get bool from map
func getBool(m map[string]interface{}, key string) bool {
	if val, ok := m[key].(bool); ok {
		return val
	}
	return false
}

// executeCreateGoal implements the create_goal tool
func (s *MCPPlanningServer) executeCreateGoal(id interface{}, args map[string]interface{}) MCPResponse {
	description := getString(args, "description")
	if description == "" {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32602,
				Message: "Description is required",
			},
		}
	}

	// Create goal request
	goalReq := GoalRequest{
		Description: description,
		Priority:    getInt(args, "priority"),
		Context:     make(map[string]interface{}),
	}

	// Set default priority if not provided
	if goalReq.Priority == 0 {
		goalReq.Priority = 5
	}

	// Handle constraints if provided
	if constraintsData, ok := args["constraints"].([]interface{}); ok {
		constraints := make([]Constraint, 0, len(constraintsData))
		for _, c := range constraintsData {
			if constraintMap, ok := c.(map[string]interface{}); ok {
				constraint := Constraint{
					Type:        getString(constraintMap, "type"),
					Description: getString(constraintMap, "description"),
					Limit:       getString(constraintMap, "limit"),
				}
				constraints = append(constraints, constraint)
			}
		}
		goalReq.Constraints = constraints
	}

	// Set user ID
	userID := getString(args, "user_id")
	if userID == "" {
		userID = "mcp-client"
	}

	// Create the goal using the planning service's goal parser
	goal, err := s.planningService.goalParser.ParseGoal(context.Background(), goalReq, userID)
	if err != nil {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32603,
				Message: fmt.Sprintf("Failed to create goal: %v", err),
			},
		}
	}

	// Store the goal
	if err := s.planningService.stateManager.SaveGoal(*goal); err != nil {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32603,
				Message: fmt.Sprintf("Failed to store goal: %v", err),
			},
		}
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": fmt.Sprintf("Goal created successfully!\nID: %s\nDescription: %s\nPriority: %d\nStatus: %s",
						goal.ID, goal.Description, goal.Priority, goal.Status),
				},
			},
			"isError": false,
		},
	}
}

// executeDecomposeGoal implements the decompose_goal tool
func (s *MCPPlanningServer) executeDecomposeGoal(id interface{}, args map[string]interface{}) MCPResponse {
	goalID := getString(args, "goal_id")
	if goalID == "" {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32602,
				Message: "Goal ID is required",
			},
		}
	}

	// Get the goal
	goal, err := s.planningService.stateManager.GetGoal(goalID)
	if err != nil {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32603,
				Message: fmt.Sprintf("Goal not found: %v", err),
			},
		}
	}

	// Update goal status to planning
	goal.Status = "planning"
	s.planningService.stateManager.SaveGoal(*goal)

	// Decompose tasks
	plan, err := s.planningService.taskDecomposer.DecomposeTasks(context.Background(), *goal)
	if err != nil {
		goal.Status = "failed"
		s.planningService.stateManager.SaveGoal(*goal)
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32603,
				Message: fmt.Sprintf("Failed to decompose goal: %v", err),
			},
		}
	}

	// Store the plan
	if err := s.planningService.stateManager.SavePlan(*plan); err != nil {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32603,
				Message: fmt.Sprintf("Failed to store plan: %v", err),
			},
		}
	}

	// Update goal status
	goal.Status = "planned"
	s.planningService.stateManager.SaveGoal(*goal)

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": fmt.Sprintf("Goal decomposed successfully!\nPlan ID: %s\nTasks: %d\nEstimated Cost: $%.2f\nEstimated Time: %v",
						plan.ID, len(plan.Tasks), plan.EstimatedCost, plan.EstimatedTime),
				},
			},
			"isError": false,
		},
	}
}

// executeExecutePlan implements the execute_plan tool
func (s *MCPPlanningServer) executeExecutePlan(id interface{}, args map[string]interface{}) MCPResponse {
	goalID := getString(args, "goal_id")
	if goalID == "" {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32602,
				Message: "Goal ID is required",
			},
		}
	}

	dryRun := getBool(args, "dry_run")

	// Get the goal
	goal, err := s.planningService.stateManager.GetGoal(goalID)
	if err != nil {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32603,
				Message: fmt.Sprintf("Goal not found: %v", err),
			},
		}
	}

	// Get the latest plan
	plans, err := s.planningService.stateManager.ListPlans(goalID)
	if err != nil || len(plans) == 0 {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32603,
				Message: "No plan found for goal",
			},
		}
	}

	latestPlan := plans[0]
	for _, plan := range plans {
		if plan.CreatedAt.After(latestPlan.CreatedAt) {
			latestPlan = plan
		}
	}

	if dryRun {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Result: map[string]interface{}{
				"content": []map[string]interface{}{
					{
						"type": "text",
						"text": fmt.Sprintf("Dry run for goal execution:\nGoal: %s\nPlan: %s\nTasks: %d\nWould execute: %v",
							goal.Description, latestPlan.ID, len(latestPlan.Tasks), !dryRun),
					},
				},
				"isError": false,
			},
		}
	}

	// Execute plan asynchronously
	options := ExecutionOptions{
		MaxConcurrency: 3,
		Timeout:        30 * time.Minute,
	}

	// Update goal status
	goal.Status = "executing"
	s.planningService.stateManager.SaveGoal(*goal)

	// Start execution in background
	go func() {
		err := s.planningService.executionEngine.ExecutePlan(context.Background(), latestPlan, options)
		if err != nil {
			log.Printf("Plan execution failed for goal %s: %v", goalID, err)
			goal.Status = "failed"
		} else {
			goal.Status = "completed"
		}
		s.planningService.stateManager.SaveGoal(*goal)
	}()

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": fmt.Sprintf("Plan execution started!\nGoal ID: %s\nPlan ID: %s\nStatus: executing\nTasks: %d",
						goalID, latestPlan.ID, len(latestPlan.Tasks)),
				},
			},
			"isError": false,
		},
	}
}

// executeMonitorExecution implements the monitor_execution tool
func (s *MCPPlanningServer) executeMonitorExecution(id interface{}, args map[string]interface{}) MCPResponse {
	goalID := getString(args, "goal_id")
	if goalID == "" {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32602,
				Message: "Goal ID is required",
			},
		}
	}

	// Get the goal
	goal, err := s.planningService.stateManager.GetGoal(goalID)
	if err != nil {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32603,
				Message: fmt.Sprintf("Goal not found: %v", err),
			},
		}
	}

	// Get execution context if available
	execCtx, err := s.planningService.stateManager.GetExecutionContext(goalID)
	var statusText string
	if err != nil {
		statusText = fmt.Sprintf("Goal Status: %s\nNo execution context available", goal.Status)
	} else {
		statusText = fmt.Sprintf("Goal Status: %s\nExecution State: %s\nContext ID: %s\nCreated: %s\nUpdated: %s",
			goal.Status, execCtx.CurrentState, execCtx.ID, execCtx.CreatedAt.Format(time.RFC3339), execCtx.UpdatedAt.Format(time.RFC3339))
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": statusText,
				},
			},
			"isError": false,
		},
	}
}

// handlePromptsList returns the list of available prompts
func (s *MCPPlanningServer) handlePromptsList(req MCPRequest) MCPResponse {
	prompts := []MCPPrompt{
		{
			Name:        "goal_creation_wizard",
			Description: "Interactive goal creation with best practices and validation",
			Arguments: map[string]interface{}{
				"domain": map[string]interface{}{
					"type":        "string",
					"description": "Domain or area of focus (e.g., 'data analysis', 'content creation')",
				},
				"complexity": map[string]interface{}{
					"type":        "string",
					"description": "Expected complexity level (simple, medium, complex)",
				},
			},
		},
		{
			Name:        "workflow_optimization",
			Description: "Template for optimizing multi-step workflows and task sequences",
			Arguments: map[string]interface{}{
				"current_workflow": map[string]interface{}{
					"type":        "string",
					"description": "Description of current workflow to optimize",
				},
				"optimization_goals": map[string]interface{}{
					"type":        "array",
					"description": "Goals for optimization (speed, cost, quality, etc.)",
				},
			},
		},
		{
			Name:        "execution_monitoring",
			Description: "Guided execution monitoring and troubleshooting workflow",
			Arguments: map[string]interface{}{
				"goal_id": map[string]interface{}{
					"type":        "string",
					"description": "ID of the goal to monitor",
				},
				"focus_area": map[string]interface{}{
					"type":        "string",
					"description": "Specific area to focus monitoring on",
				},
			},
		},
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      req.ID,
		Result: map[string]interface{}{
			"prompts": prompts,
		},
	}
}

// handlePromptGet returns a specific prompt template
func (s *MCPPlanningServer) handlePromptGet(req MCPRequest) MCPResponse {
	params, ok := req.Params.(map[string]interface{})
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "Invalid params",
			},
		}
	}

	promptName, ok := params["name"].(string)
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "Prompt name required",
			},
		}
	}

	arguments, _ := params["arguments"].(map[string]interface{})

	switch promptName {
	case "goal_creation_wizard":
		return s.generateGoalCreationPrompt(req.ID, arguments)
	case "workflow_optimization":
		return s.generateWorkflowOptimizationPrompt(req.ID, arguments)
	case "execution_monitoring":
		return s.generateExecutionMonitoringPrompt(req.ID, arguments)
	default:
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32601,
				Message: "Prompt not found",
			},
		}
	}
}

// handleResourcesList returns the list of available resources
func (s *MCPPlanningServer) handleResourcesList(req MCPRequest) MCPResponse {
	resources := []MCPResource{
		{
			URI:         "planning://active-goals",
			Name:        "Active Goals",
			Description: "Currently executing goals and their status",
			MimeType:    "application/json",
		},
		{
			URI:         "planning://execution-history",
			Name:        "Execution History",
			Description: "Historical goal execution data and outcomes",
			MimeType:    "application/json",
		},
		{
			URI:         "planning://task-templates",
			Name:        "Task Templates",
			Description: "Reusable task patterns and workflows",
			MimeType:    "application/json",
		},
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      req.ID,
		Result: map[string]interface{}{
			"resources": resources,
		},
	}
}

// handleResourceRead reads a specific resource
func (s *MCPPlanningServer) handleResourceRead(req MCPRequest) MCPResponse {
	params, ok := req.Params.(map[string]interface{})
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "Invalid params",
			},
		}
	}

	uri, ok := params["uri"].(string)
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "URI is required",
			},
		}
	}

	switch uri {
	case "planning://active-goals":
		return s.readActiveGoals(req.ID)
	case "planning://execution-history":
		return s.readExecutionHistory(req.ID)
	case "planning://task-templates":
		return s.readTaskTemplates(req.ID)
	default:
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32601,
				Message: "Resource not found",
			},
		}
	}
}

// Prompt generation functions
func (s *MCPPlanningServer) generateGoalCreationPrompt(id interface{}, args map[string]interface{}) MCPResponse {
	domain := getString(args, "domain")
	complexity := getString(args, "complexity")

	prompt := fmt.Sprintf(`# Goal Creation Wizard

You are helping create a well-structured goal for autonomous execution.

## Domain: %s
## Complexity Level: %s

Please provide:

1. **Clear Goal Description**: What exactly do you want to achieve?
2. **Success Criteria**: How will you know when the goal is complete?
3. **Constraints**: Any limitations (time, budget, resources)?
4. **Priority**: How urgent is this goal (1-10)?

## Best Practices:
- Be specific and measurable
- Include clear success criteria
- Consider dependencies and constraints
- Set realistic timelines

Please describe your goal:`, domain, complexity)

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"description": "Interactive goal creation wizard",
			"messages": []map[string]interface{}{
				{
					"role":    "user",
					"content": map[string]interface{}{"type": "text", "text": prompt},
				},
			},
		},
	}
}

func (s *MCPPlanningServer) generateWorkflowOptimizationPrompt(id interface{}, args map[string]interface{}) MCPResponse {
	currentWorkflow := getString(args, "current_workflow")

	prompt := fmt.Sprintf(`# Workflow Optimization Analysis

## Current Workflow:
%s

## Optimization Framework:

1. **Identify Bottlenecks**: What are the slowest/most expensive steps?
2. **Parallel Opportunities**: Which tasks can run concurrently?
3. **Automation Potential**: What can be automated?
4. **Resource Optimization**: How can we use resources more efficiently?

## Analysis Questions:
- Which steps are critical path vs. can be parallelized?
- What are the dependencies between tasks?
- Where are the highest costs/time investments?
- What are potential failure points?

Please analyze this workflow and suggest optimizations:`, currentWorkflow)

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"description": "Workflow optimization template",
			"messages": []map[string]interface{}{
				{
					"role":    "user",
					"content": map[string]interface{}{"type": "text", "text": prompt},
				},
			},
		},
	}
}

func (s *MCPPlanningServer) generateExecutionMonitoringPrompt(id interface{}, args map[string]interface{}) MCPResponse {
	goalID := getString(args, "goal_id")
	focusArea := getString(args, "focus_area")

	prompt := fmt.Sprintf(`# Execution Monitoring Dashboard

## Goal ID: %s
## Focus Area: %s

## Monitoring Checklist:

1. **Progress Tracking**:
   - Current task completion status
   - Overall goal progress percentage
   - Time elapsed vs. estimated

2. **Performance Metrics**:
   - Task execution times
   - Resource utilization
   - Cost tracking

3. **Issue Detection**:
   - Failed or stuck tasks
   - Resource constraints
   - Dependency blockers

4. **Optimization Opportunities**:
   - Parallel execution possibilities
   - Resource reallocation
   - Process improvements

Use the monitor_execution tool to get current status and analyze the results:`, goalID, focusArea)

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"description": "Execution monitoring guide",
			"messages": []map[string]interface{}{
				{
					"role":    "user",
					"content": map[string]interface{}{"type": "text", "text": prompt},
				},
			},
		},
	}
}

// Resource reading functions
func (s *MCPPlanningServer) readActiveGoals(id interface{}) MCPResponse {
	// Get active goals from state manager - get all goals first
	goals, err := s.planningService.stateManager.ListGoals("", "") // Get all goals
	if err != nil {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32603,
				Message: fmt.Sprintf("Failed to retrieve goals: %v", err),
			},
		}
	}

	// Filter for active goals
	activeGoals := make([]Goal, 0)
	for _, goal := range goals {
		if goal.Status == GoalStatusExecuting || goal.Status == GoalStatusPlanning || goal.Status == "planned" {
			activeGoals = append(activeGoals, goal)
		}
	}

	goalsJSON, _ := json.MarshalIndent(activeGoals, "", "  ")
	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"contents": []map[string]interface{}{
				{
					"uri":      "planning://active-goals",
					"mimeType": "application/json",
					"text":     string(goalsJSON),
				},
			},
		},
	}
}

func (s *MCPPlanningServer) readExecutionHistory(id interface{}) MCPResponse {
	// For now, return a placeholder
	history := map[string]interface{}{
		"message":   "Execution history tracking not yet implemented",
		"timestamp": time.Now().UTC(),
		"note":      "This will contain historical execution data, performance metrics, and outcomes",
	}
	historyJSON, _ := json.MarshalIndent(history, "", "  ")
	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"contents": []map[string]interface{}{
				{
					"uri":      "planning://execution-history",
					"mimeType": "application/json",
					"text":     string(historyJSON),
				},
			},
		},
	}
}

func (s *MCPPlanningServer) readTaskTemplates(id interface{}) MCPResponse {
	// Return available task templates
	templates := map[string]interface{}{
		"data_analysis": map[string]interface{}{
			"name":        "Data Analysis Workflow",
			"description": "Standard workflow for data analysis tasks",
			"tasks": []string{
				"data_collection",
				"data_cleaning",
				"exploratory_analysis",
				"statistical_analysis",
				"visualization",
				"report_generation",
			},
		},
		"content_creation": map[string]interface{}{
			"name":        "Content Creation Pipeline",
			"description": "Workflow for creating and publishing content",
			"tasks": []string{
				"research",
				"outline_creation",
				"content_writing",
				"editing",
				"review",
				"publishing",
			},
		},
		"api_integration": map[string]interface{}{
			"name":        "API Integration Workflow",
			"description": "Standard workflow for integrating external APIs",
			"tasks": []string{
				"api_discovery",
				"authentication_setup",
				"endpoint_testing",
				"data_mapping",
				"integration_implementation",
				"testing_validation",
			},
		},
	}

	templatesJSON, _ := json.MarshalIndent(templates, "", "  ")
	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"contents": []map[string]interface{}{
				{
					"uri":      "planning://task-templates",
					"mimeType": "application/json",
					"text":     string(templatesJSON),
				},
			},
		},
	}
}

// Global MCP server instance
var mcpPlanningServer *MCPPlanningServer

// InitializeMCPPlanningServer initializes the MCP server for Planning Service
func InitializeMCPPlanningServer(ps *PlanningService) {
	mcpPlanningServer = NewMCPPlanningServer(ps)
	log.Printf("Planning Service MCP Server: Initialized successfully")
}
