# Standard Edition Frontend Ingress with proper path rewriting
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: standard-edition-frontend-ingress
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Authentication service routing for standard edition
      - path: /auth
        pathType: Prefix
        backend:
          service:
            name: auth-service
            port:
              number: 80

      # Standard Edition Frontend - rewrite /standard/X to /X
      - path: /standard(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: frontend-standard-proxy
            port:
              number: 80
