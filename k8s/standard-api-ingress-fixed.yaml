# Standard Edition API Ingress - Separate with proper rewrite rules
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: standard-api-ingress-fixed
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Standard Edition API Routes (with path rewriting /standard/api/X -> /api/X)
      - path: /standard/(api/prompts.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: policy-manager-standard-proxy-fixed
            port:
              number: 8083

      - path: /standard/(api/policies.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: policy-manager-standard-proxy-fixed
            port:
              number: 8083

      - path: /standard/(api/model-profiles.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: policy-manager-standard-proxy-fixed
            port:
              number: 8083

      - path: /standard/(api/integration.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-proxy-fixed
            port:
              number: 8081

      - path: /standard/(api/planning.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: api-fallback-standard-proxy-fixed
            port:
              number: 8080

      - path: /standard/(api/evaluation.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: api-fallback-standard-proxy-fixed
            port:
              number: 8080

      - path: /standard/(api/multi-agent.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: api-fallback-standard-proxy-fixed
            port:
              number: 8080

      # Standard Edition Dashboard API routes
      - path: /standard/(api/summary.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-proxy-fixed
            port:
              number: 8081

      - path: /standard/(api/time-series.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-proxy-fixed
            port:
              number: 8081

      - path: /standard/(api/inference-logs.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-proxy-fixed
            port:
              number: 8081

      - path: /standard/(api/backend-latencies.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-proxy-fixed
            port:
              number: 8081

      - path: /standard/(api/optimal-backend.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-proxy-fixed
            port:
              number: 8081

      # Standard Edition Dashboard (non-API)
      - path: /standard/(dashboard.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-proxy-fixed
            port:
              number: 8081

      # Standard Edition General API (proxy-gateway) - MUST be last
      - path: /standard/(api.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: proxy-gateway-standard-proxy-fixed
            port:
              number: 8080
