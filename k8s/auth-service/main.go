package main

import (
	"context"
	"crypto/rand"
	"database/sql"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
)

// User represents a user in the system
type User struct {
	ID          string                 `json:"id"`
	Email       string                 `json:"email"`
	Name        string                 `json:"name"`
	Picture     string                 `json:"picture"`
	GoogleID    string                 `json:"google_id"`
	Roles       []string               `json:"roles"`
	Status      string                 `json:"status"` // active, suspended, disabled
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	LastLoginAt *time.Time             `json:"last_login_at,omitempty"`
}

// AuthResponse represents authentication response
type AuthResponse struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	User      User      `json:"user"`
}

// GoogleUserInfo represents user info from Google OAuth
type GoogleUserInfo struct {
	ID      string `json:"id"`
	Email   string `json:"email"`
	Name    string `json:"name"`
	Picture string `json:"picture"`
}

// AuthService handles authentication operations
type AuthService struct {
	db          *sql.DB
	oauthConfig *oauth2.Config
	jwtSecret   []byte
	adminEmails map[string]bool
}

// JWT Claims
type Claims struct {
	UserID string   `json:"user_id"`
	Email  string   `json:"email"`
	Roles  []string `json:"roles"`
	jwt.RegisteredClaims
}

func main() {
	log.Println("Starting Authentication Service...")

	// Initialize database connection
	db, err := initClickHouseDB()
	if err != nil {
		log.Fatalf("Failed to connect to ClickHouse: %v", err)
	}
	defer db.Close()

	// Initialize OAuth config
	oauthConfig := &oauth2.Config{
		ClientID:     getEnv("GOOGLE_CLIENT_ID", ""),
		ClientSecret: getEnv("GOOGLE_CLIENT_SECRET", ""),
		RedirectURL:  getEnv("GOOGLE_REDIRECT_URL", "https://scale-llm.com/auth/callback"),
		Scopes:       []string{"openid", "profile", "email"},
		Endpoint:     google.Endpoint,
	}

	// Initialize JWT secret
	jwtSecret := []byte(getEnv("JWT_SECRET", generateRandomSecret()))

	// Initialize admin emails
	adminEmails := make(map[string]bool)
	adminEmailsStr := getEnv("ADMIN_EMAILS", "")
	if adminEmailsStr != "" {
		for _, email := range strings.Split(adminEmailsStr, ",") {
			adminEmails[strings.TrimSpace(email)] = true
		}
	}

	authService := &AuthService{
		db:          db,
		oauthConfig: oauthConfig,
		jwtSecret:   jwtSecret,
		adminEmails: adminEmails,
	}

	// Create tables
	if err := authService.createTables(); err != nil {
		log.Fatalf("Failed to create tables: %v", err)
	}

	// Setup routes
	router := mux.NewRouter()

	// CORS middleware
	router.Use(corsMiddleware)

	// Health check
	router.HandleFunc("/health", healthCheck).Methods("GET")

	// OAuth routes
	router.HandleFunc("/auth/google", authService.googleLogin).Methods("GET")
	router.HandleFunc("/auth/callback", authService.googleCallback).Methods("GET")
	router.HandleFunc("/auth/logout", authService.logout).Methods("POST")

	// User management routes
	router.HandleFunc("/auth/me", authService.getCurrentUser).Methods("GET")
	router.HandleFunc("/auth/profile", authService.updateProfile).Methods("PUT")
	router.HandleFunc("/auth/validate", authService.validateToken).Methods("POST")

	// Admin routes
	router.HandleFunc("/admin/users", authService.adminMiddleware(authService.listUsers)).Methods("GET")
	router.HandleFunc("/admin/users/{id}/status", authService.adminMiddleware(authService.updateUserStatus)).Methods("PUT")
	router.HandleFunc("/admin/users/{id}/roles", authService.adminMiddleware(authService.updateUserRoles)).Methods("PUT")

	port := getEnv("PORT", "8080")
	log.Printf("Authentication service listening on port %s", port)
	log.Fatal(http.ListenAndServe(":"+port, router))
}

func initClickHouseDB() (*sql.DB, error) {
	host := getEnv("CLICKHOUSE_HOST", "clickhouse")
	port := getEnv("CLICKHOUSE_PORT", "9000")
	database := getEnv("CLICKHOUSE_DATABASE", "default")
	username := getEnv("CLICKHOUSE_USERNAME", "default")
	password := getEnv("CLICKHOUSE_PASSWORD", "")

	db := clickhouse.OpenDB(&clickhouse.Options{
		Addr: []string{fmt.Sprintf("%s:%s", host, port)},
		Auth: clickhouse.Auth{
			Database: database,
			Username: username,
			Password: password,
		},
	})

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping ClickHouse: %w", err)
	}

	return db, nil
}

func (as *AuthService) createTables() error {
	query := `
	CREATE TABLE IF NOT EXISTS users (
		id String,
		email String,
		name String,
		picture String,
		google_id String,
		roles Array(String),
		status String,
		metadata String,
		created_at DateTime64(9),
		updated_at DateTime64(9),
		last_login_at Nullable(DateTime64(9))
	) ENGINE = MergeTree()
	ORDER BY (id, email)
	`

	_, err := as.db.Exec(query)
	if err != nil {
		return fmt.Errorf("failed to create users table: %w", err)
	}

	// Create sessions table for token blacklisting
	sessionQuery := `
	CREATE TABLE IF NOT EXISTS user_sessions (
		token_id String,
		user_id String,
		expires_at DateTime64(9),
		created_at DateTime64(9),
		revoked UInt8 DEFAULT 0
	) ENGINE = MergeTree()
	ORDER BY (token_id, user_id)
	`

	_, err = as.db.Exec(sessionQuery)
	if err != nil {
		return fmt.Errorf("failed to create sessions table: %w", err)
	}

	log.Println("Database tables created successfully")
	return nil
}

func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

func healthCheck(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"status": "healthy"})
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func generateRandomSecret() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// Google OAuth handlers
func (as *AuthService) googleLogin(w http.ResponseWriter, r *http.Request) {
	state := generateRandomSecret()
	url := as.oauthConfig.AuthCodeURL(state, oauth2.AccessTypeOffline)

	// Detect edition from the request path
	edition := "enterprise" // default
	if referer := r.Header.Get("Referer"); referer != "" {
		if strings.Contains(referer, "/standard") {
			edition = "standard"
		} else if strings.Contains(referer, "/enterprise") {
			edition = "enterprise"
		}
	}

	// Store state in session/cookie for validation
	http.SetCookie(w, &http.Cookie{
		Name:     "oauth_state",
		Value:    state,
		Path:     "/",
		HttpOnly: true,
		Secure:   true,
		SameSite: http.SameSiteLaxMode,
		MaxAge:   600, // 10 minutes
	})

	// Store edition for redirect after callback
	http.SetCookie(w, &http.Cookie{
		Name:     "oauth_edition",
		Value:    edition,
		Path:     "/",
		HttpOnly: true,
		Secure:   true,
		SameSite: http.SameSiteLaxMode,
		MaxAge:   600, // 10 minutes
	})

	http.Redirect(w, r, url, http.StatusTemporaryRedirect)
}

func (as *AuthService) googleCallback(w http.ResponseWriter, r *http.Request) {
	// Validate state
	stateCookie, err := r.Cookie("oauth_state")
	if err != nil || stateCookie.Value != r.URL.Query().Get("state") {
		http.Error(w, "Invalid state parameter", http.StatusBadRequest)
		return
	}

	// Clear state cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "oauth_state",
		Value:    "",
		Path:     "/",
		HttpOnly: true,
		MaxAge:   -1,
	})

	// Exchange code for token
	code := r.URL.Query().Get("code")
	token, err := as.oauthConfig.Exchange(context.Background(), code)
	if err != nil {
		log.Printf("Failed to exchange code for token: %v", err)
		http.Error(w, "Failed to exchange code for token", http.StatusInternalServerError)
		return
	}

	// Get user info from Google
	client := as.oauthConfig.Client(context.Background(), token)
	resp, err := client.Get("https://www.googleapis.com/oauth2/v2/userinfo")
	if err != nil {
		log.Printf("Failed to get user info: %v", err)
		http.Error(w, "Failed to get user info", http.StatusInternalServerError)
		return
	}
	defer resp.Body.Close()

	var googleUser GoogleUserInfo
	if err := json.NewDecoder(resp.Body).Decode(&googleUser); err != nil {
		log.Printf("Failed to decode user info: %v", err)
		http.Error(w, "Failed to decode user info", http.StatusInternalServerError)
		return
	}

	// Create or update user
	user, err := as.createOrUpdateUser(googleUser)
	if err != nil {
		log.Printf("Failed to create/update user: %v", err)
		http.Error(w, "Failed to create user", http.StatusInternalServerError)
		return
	}

	// Generate JWT token
	jwtToken, expiresAt, err := as.generateJWT(user)
	if err != nil {
		log.Printf("Failed to generate JWT: %v", err)
		http.Error(w, "Failed to generate token", http.StatusInternalServerError)
		return
	}

	// Store session
	if err := as.storeSession(jwtToken, user.ID, expiresAt); err != nil {
		log.Printf("Failed to store session: %v", err)
		// Continue anyway, session storage is not critical
	}

	// Get edition from cookie to determine redirect path
	edition := "enterprise" // default
	if editionCookie, err := r.Cookie("oauth_edition"); err == nil {
		edition = editionCookie.Value
	}

	// Clear the edition cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "oauth_edition",
		Value:    "",
		Path:     "/",
		HttpOnly: true,
		Secure:   true,
		SameSite: http.SameSiteLaxMode,
		MaxAge:   -1, // Delete cookie
	})

	// Redirect to edition-specific success page
	frontendURL := getEnv("FRONTEND_URL", "https://scale-llm.com")
	var redirectURL string
	if edition == "standard" {
		redirectURL = fmt.Sprintf("%s/standard/auth/success?token=%s", frontendURL, jwtToken)
	} else {
		redirectURL = fmt.Sprintf("%s/enterprise/auth/success?token=%s", frontendURL, jwtToken)
	}
	http.Redirect(w, r, redirectURL, http.StatusTemporaryRedirect)
}

func (as *AuthService) createOrUpdateUser(googleUser GoogleUserInfo) (*User, error) {
	// Check if user exists
	var existingUser User
	query := `SELECT id, email, name, picture, google_id, roles, status, metadata, created_at, updated_at, last_login_at
			  FROM users WHERE google_id = ? OR email = ?`

	row := as.db.QueryRow(query, googleUser.ID, googleUser.Email)
	var metadataStr string
	var lastLoginAt *time.Time

	err := row.Scan(&existingUser.ID, &existingUser.Email, &existingUser.Name, &existingUser.Picture,
		&existingUser.GoogleID, &existingUser.Roles, &existingUser.Status, &metadataStr,
		&existingUser.CreatedAt, &existingUser.UpdatedAt, &lastLoginAt)

	if err != nil && err != sql.ErrNoRows {
		return nil, fmt.Errorf("failed to query user: %w", err)
	}

	now := time.Now()

	if err == sql.ErrNoRows {
		// Create new user
		user := &User{
			ID:          uuid.New().String(),
			Email:       googleUser.Email,
			Name:        googleUser.Name,
			Picture:     googleUser.Picture,
			GoogleID:    googleUser.ID,
			Roles:       []string{"user"},
			Status:      "active",
			Metadata:    make(map[string]interface{}),
			CreatedAt:   now,
			UpdatedAt:   now,
			LastLoginAt: &now,
		}

		// Check if user should be admin
		if as.adminEmails[user.Email] {
			user.Roles = append(user.Roles, "admin")
		}

		// Insert new user
		metadataBytes, _ := json.Marshal(user.Metadata)
		insertQuery := `INSERT INTO users (id, email, name, picture, google_id, roles, status, metadata, created_at, updated_at, last_login_at)
						VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

		_, err = as.db.Exec(insertQuery, user.ID, user.Email, user.Name, user.Picture, user.GoogleID,
			user.Roles, user.Status, string(metadataBytes), user.CreatedAt, user.UpdatedAt, user.LastLoginAt)

		if err != nil {
			return nil, fmt.Errorf("failed to insert user: %w", err)
		}

		log.Printf("Created new user: %s (%s)", user.Name, user.Email)
		return user, nil
	} else {
		// Update existing user
		existingUser.Name = googleUser.Name
		existingUser.Picture = googleUser.Picture
		existingUser.UpdatedAt = now
		existingUser.LastLoginAt = &now

		// Parse metadata
		if metadataStr != "" {
			json.Unmarshal([]byte(metadataStr), &existingUser.Metadata)
		}
		if existingUser.Metadata == nil {
			existingUser.Metadata = make(map[string]interface{})
		}

		// Check if user should be admin (in case admin list changed)
		hasAdmin := false
		for _, role := range existingUser.Roles {
			if role == "admin" {
				hasAdmin = true
				break
			}
		}

		if as.adminEmails[existingUser.Email] && !hasAdmin {
			existingUser.Roles = append(existingUser.Roles, "admin")
		}

		// Update user
		metadataBytes, _ := json.Marshal(existingUser.Metadata)
		updateQuery := `ALTER TABLE users UPDATE name = ?, picture = ?, updated_at = ?, last_login_at = ?, roles = ?, metadata = ?
						WHERE id = ?`

		_, err = as.db.Exec(updateQuery, existingUser.Name, existingUser.Picture, existingUser.UpdatedAt,
			existingUser.LastLoginAt, existingUser.Roles, string(metadataBytes), existingUser.ID)

		if err != nil {
			return nil, fmt.Errorf("failed to update user: %w", err)
		}

		log.Printf("Updated existing user: %s (%s)", existingUser.Name, existingUser.Email)
		return &existingUser, nil
	}
}

func (as *AuthService) generateJWT(user *User) (string, time.Time, error) {
	expiresAt := time.Now().Add(24 * time.Hour)

	claims := &Claims{
		UserID: user.ID,
		Email:  user.Email,
		Roles:  user.Roles,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Subject:   user.ID,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(as.jwtSecret)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, expiresAt, nil
}

func (as *AuthService) storeSession(tokenID, userID string, expiresAt time.Time) error {
	query := `INSERT INTO user_sessions (token_id, user_id, expires_at, created_at, revoked) VALUES (?, ?, ?, ?, 0)`
	_, err := as.db.Exec(query, tokenID, userID, expiresAt, time.Now())
	return err
}

func (as *AuthService) validateToken(w http.ResponseWriter, r *http.Request) {
	var request struct {
		Token string `json:"token"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	user, err := as.validateJWT(request.Token)
	if err != nil {
		http.Error(w, "Invalid token", http.StatusUnauthorized)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"valid": true,
		"user":  user,
	})
}

func (as *AuthService) validateJWT(tokenString string) (*User, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return as.jwtSecret, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		// Get user from database
		user, err := as.getUserByID(claims.UserID)
		if err != nil {
			return nil, fmt.Errorf("user not found: %w", err)
		}

		if user.Status != "active" {
			return nil, fmt.Errorf("user account is not active")
		}

		return user, nil
	}

	return nil, fmt.Errorf("invalid token claims")
}

func (as *AuthService) getUserByID(userID string) (*User, error) {
	query := `SELECT id, email, name, picture, google_id, roles, status, metadata, created_at, updated_at, last_login_at
			  FROM users WHERE id = ?`

	row := as.db.QueryRow(query, userID)
	var user User
	var metadataStr string
	var lastLoginAt *time.Time

	err := row.Scan(&user.ID, &user.Email, &user.Name, &user.Picture, &user.GoogleID,
		&user.Roles, &user.Status, &metadataStr, &user.CreatedAt, &user.UpdatedAt, &lastLoginAt)

	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	user.LastLoginAt = lastLoginAt

	if metadataStr != "" {
		json.Unmarshal([]byte(metadataStr), &user.Metadata)
	}
	if user.Metadata == nil {
		user.Metadata = make(map[string]interface{})
	}

	return &user, nil
}

func (as *AuthService) getCurrentUser(w http.ResponseWriter, r *http.Request) {
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" {
		http.Error(w, "Authorization header required", http.StatusUnauthorized)
		return
	}

	parts := strings.SplitN(authHeader, " ", 2)
	if len(parts) != 2 || parts[0] != "Bearer" {
		http.Error(w, "Invalid authorization header format", http.StatusUnauthorized)
		return
	}

	user, err := as.validateJWT(parts[1])
	if err != nil {
		http.Error(w, "Invalid token", http.StatusUnauthorized)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(user)
}

func (as *AuthService) updateProfile(w http.ResponseWriter, r *http.Request) {
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" {
		http.Error(w, "Authorization header required", http.StatusUnauthorized)
		return
	}

	parts := strings.SplitN(authHeader, " ", 2)
	if len(parts) != 2 || parts[0] != "Bearer" {
		http.Error(w, "Invalid authorization header format", http.StatusUnauthorized)
		return
	}

	user, err := as.validateJWT(parts[1])
	if err != nil {
		http.Error(w, "Invalid token", http.StatusUnauthorized)
		return
	}

	var updateData struct {
		Name     string                 `json:"name"`
		Metadata map[string]interface{} `json:"metadata"`
	}

	if err := json.NewDecoder(r.Body).Decode(&updateData); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Update user
	if updateData.Name != "" {
		user.Name = updateData.Name
	}
	if updateData.Metadata != nil {
		user.Metadata = updateData.Metadata
	}
	user.UpdatedAt = time.Now()

	metadataBytes, _ := json.Marshal(user.Metadata)
	query := `ALTER TABLE users UPDATE name = ?, metadata = ?, updated_at = ? WHERE id = ?`
	_, err = as.db.Exec(query, user.Name, string(metadataBytes), user.UpdatedAt, user.ID)

	if err != nil {
		log.Printf("Failed to update user profile: %v", err)
		http.Error(w, "Failed to update profile", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(user)
}

func (as *AuthService) logout(w http.ResponseWriter, r *http.Request) {
	var request struct {
		Token string `json:"token"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Revoke session (mark as revoked in database)
	query := `ALTER TABLE user_sessions UPDATE revoked = 1 WHERE token_id = ?`
	_, err := as.db.Exec(query, request.Token)
	if err != nil {
		log.Printf("Failed to revoke session: %v", err)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"message": "Logged out successfully"})
}

func (as *AuthService) adminMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			http.Error(w, "Authorization header required", http.StatusUnauthorized)
			return
		}

		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			http.Error(w, "Invalid authorization header format", http.StatusUnauthorized)
			return
		}

		user, err := as.validateJWT(parts[1])
		if err != nil {
			http.Error(w, "Invalid token", http.StatusUnauthorized)
			return
		}

		// Check if user has admin role
		hasAdmin := false
		for _, role := range user.Roles {
			if role == "admin" {
				hasAdmin = true
				break
			}
		}

		if !hasAdmin {
			http.Error(w, "Admin access required", http.StatusForbidden)
			return
		}

		// Add user to request context
		r.Header.Set("X-User-ID", user.ID)
		r.Header.Set("X-User-Email", user.Email)

		next(w, r)
	}
}

func (as *AuthService) listUsers(w http.ResponseWriter, r *http.Request) {
	query := `SELECT id, email, name, picture, google_id, roles, status, metadata, created_at, updated_at, last_login_at
			  FROM users ORDER BY created_at DESC`

	rows, err := as.db.Query(query)
	if err != nil {
		log.Printf("Failed to query users: %v", err)
		http.Error(w, "Failed to get users", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	var users []User
	for rows.Next() {
		var user User
		var metadataStr string
		var lastLoginAt *time.Time

		err := rows.Scan(&user.ID, &user.Email, &user.Name, &user.Picture, &user.GoogleID,
			&user.Roles, &user.Status, &metadataStr, &user.CreatedAt, &user.UpdatedAt, &lastLoginAt)

		if err != nil {
			log.Printf("Failed to scan user: %v", err)
			continue
		}

		user.LastLoginAt = lastLoginAt

		if metadataStr != "" {
			json.Unmarshal([]byte(metadataStr), &user.Metadata)
		}
		if user.Metadata == nil {
			user.Metadata = make(map[string]interface{})
		}

		users = append(users, user)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(users)
}

func (as *AuthService) updateUserStatus(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	userID := vars["id"]

	var request struct {
		Status string `json:"status"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if request.Status != "active" && request.Status != "suspended" && request.Status != "disabled" {
		http.Error(w, "Invalid status. Must be: active, suspended, or disabled", http.StatusBadRequest)
		return
	}

	query := `ALTER TABLE users UPDATE status = ?, updated_at = ? WHERE id = ?`
	_, err := as.db.Exec(query, request.Status, time.Now(), userID)

	if err != nil {
		log.Printf("Failed to update user status: %v", err)
		http.Error(w, "Failed to update user status", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"message": "User status updated successfully"})
}

func (as *AuthService) updateUserRoles(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	userID := vars["id"]

	var request struct {
		Roles []string `json:"roles"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate roles
	validRoles := map[string]bool{"user": true, "admin": true, "viewer": true}
	for _, role := range request.Roles {
		if !validRoles[role] {
			http.Error(w, fmt.Sprintf("Invalid role: %s", role), http.StatusBadRequest)
			return
		}
	}

	query := `ALTER TABLE users UPDATE roles = ?, updated_at = ? WHERE id = ?`
	_, err := as.db.Exec(query, request.Roles, time.Now(), userID)

	if err != nil {
		log.Printf("Failed to update user roles: %v", err)
		http.Error(w, "Failed to update user roles", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"message": "User roles updated successfully"})
}
