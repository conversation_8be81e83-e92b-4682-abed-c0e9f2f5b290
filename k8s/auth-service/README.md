# Authentication Service

The Authentication Service provides Google OAuth-based authentication and user management for the AI Operations Hub. It supports both standard and enterprise editions with role-based access control.

## Features

- **Google OAuth Integration**: Secure login using Google accounts
- **JWT Token Management**: Stateless authentication with JWT tokens
- **User Profile Management**: Store and manage user profiles and metadata
- **Role-Based Access Control**: Support for user, admin, and custom roles
- **Admin Management**: Admin users can manage other users and roles
- **Session Management**: Track user sessions and login history
- **Multi-Edition Support**: Works with both standard and enterprise editions

## Architecture

The service is built in Go and includes:
- OAuth 2.0 flow with Google
- JWT token generation and validation
- ClickHouse database for user storage
- RESTful API for user management
- Admin endpoints for user administration

## API Endpoints

### Public Endpoints
- `GET /health` - Health check
- `GET /auth/google` - Initiate Google OAuth flow
- `GET /auth/callback` - OAuth callback handler

### Authenticated Endpoints
- `GET /auth/me` - Get current user profile
- `PUT /auth/profile` - Update user profile
- `POST /auth/validate` - Validate JWT token
- `POST /auth/logout` - Logout user

### Admin Endpoints
- `GET /admin/users` - List all users
- `PUT /admin/users/{id}/status` - Update user status
- `PUT /admin/users/{id}/roles` - Update user roles

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Service port | `8080` |
| `CLICKHOUSE_HOST` | ClickHouse hostname | `clickhouse` |
| `CLICKHOUSE_PORT` | ClickHouse port | `9000` |
| `CLICKHOUSE_DATABASE` | ClickHouse database | `default` |
| `CLICKHOUSE_USERNAME` | ClickHouse username | `default` |
| `CLICKHOUSE_PASSWORD` | ClickHouse password | - |
| `GOOGLE_CLIENT_ID` | Google OAuth client ID | - |
| `GOOGLE_CLIENT_SECRET` | Google OAuth client secret | - |
| `GOOGLE_REDIRECT_URL` | OAuth redirect URL | `https://scale-llm.com/auth/callback` |
| `JWT_SECRET` | JWT signing secret | - |
| `ADMIN_EMAILS` | Comma-separated admin emails | - |
| `FRONTEND_URL` | Frontend URL for redirects | `https://scale-llm.com` |

## Database Schema

The service creates the following tables in ClickHouse:

### users
- `id` (String) - Unique user ID
- `email` (String) - User email address
- `name` (String) - User display name
- `picture` (String) - User profile picture URL
- `google_id` (String) - Google account ID
- `roles` (Array(String)) - User roles
- `status` (String) - User status (active, suspended, disabled)
- `metadata` (String) - JSON metadata
- `created_at` (DateTime64) - Account creation time
- `updated_at` (DateTime64) - Last update time
- `last_login_at` (Nullable(DateTime64)) - Last login time

### user_sessions
- `token_id` (String) - JWT token ID
- `user_id` (String) - User ID
- `expires_at` (DateTime64) - Token expiration
- `created_at` (DateTime64) - Session creation time
- `revoked` (UInt8) - Revocation status

## Setup Instructions

### 1. Google OAuth Setup

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URI: `https://scale-llm.com/auth/callback`
6. Note the Client ID and Client Secret

### 2. Deploy Secrets

Run the setup script to create required secrets:

```bash
# Set environment variables
export GOOGLE_CLIENT_ID="your-google-client-id"
export GOOGLE_CLIENT_SECRET="your-google-client-secret"
export JWT_SECRET="your-jwt-secret"  # Optional - will be generated if not provided
export ADMIN_EMAILS="<EMAIL>,<EMAIL>"

# Run setup script
./k8s/scripts/setup-auth-secrets.sh
```

### 3. Deploy Service

For enterprise edition:
```bash
kubectl apply -f k8s/auth-service/auth-service.yaml
```

For standard edition:
```bash
kubectl apply -f k8s/standard-edition/auth-service-standard.yaml
```

## User Roles

- **user**: Basic user access to the platform
- **admin**: Full administrative access including user management
- **viewer**: Read-only access (custom role)

## Admin Access

Users listed in the `ADMIN_EMAILS` environment variable will automatically receive admin privileges when they first log in. Additional admins can be created by existing admins through the admin interface.

## Frontend Integration

The frontend includes:
- `AuthContext` for authentication state management
- `LoginButton` component for Google OAuth login
- `UserProfile` component for profile management
- `ProtectedRoute` component for route protection
- `UserManagement` component for admin user management

## Security Features

- JWT tokens with configurable expiration
- Secure HTTP-only cookies for OAuth state
- CORS protection
- Input validation and sanitization
- Audit logging for security events
- Session revocation support

## Monitoring

The service provides:
- Health check endpoint for monitoring
- Structured logging for debugging
- Metrics for authentication events
- Error tracking and reporting

## Troubleshooting

### Common Issues

1. **OAuth redirect mismatch**: Ensure the redirect URI in Google Console matches exactly
2. **JWT secret not set**: The service will generate one if not provided, but it should be consistent across restarts
3. **ClickHouse connection issues**: Check database connectivity and credentials
4. **Admin access not working**: Verify admin emails are correctly configured

### Logs

Check service logs for authentication issues:
```bash
kubectl logs deployment/auth-service -f
```

### Database Access

Connect to ClickHouse to check user data:
```bash
kubectl exec -it deployment/clickhouse -- clickhouse-client
SELECT * FROM users LIMIT 10;
```
