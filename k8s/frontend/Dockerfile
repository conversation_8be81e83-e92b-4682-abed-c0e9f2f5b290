# --------------------------
# Stage 1: Build the Vite App
# --------------------------
FROM node:20-alpine AS build-step

WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .

# Support for enterprise edition build
ARG REACT_APP_EDITION=enterprise
ARG REACT_APP_FEATURES=full
ENV REACT_APP_EDITION=${REACT_APP_EDITION}
ENV REACT_APP_FEATURES=${REACT_APP_FEATURES}
ENV VITE_API_BASE_URL=/enterprise
ENV REACT_APP_API_BASE_URL=/enterprise/api

RUN npm run build -- --mode production --base=/enterprise/

# --------------------------
# Stage 2: Serve with Nginx
# --------------------------
FROM nginx:alpine

# Copy built app to Nginx's HTML directory
COPY --from=build-step /app/dist /usr/share/nginx/html

# The default nginx:alpine image's main nginx.conf typically includes /etc/nginx/conf.d/*.conf
# We will rely on the ConfigMap to provide default.conf into /etc/nginx/nginx.conf (direct mount)

EXPOSE 80
ENTRYPOINT ["nginx", "-g", "daemon off;"]

