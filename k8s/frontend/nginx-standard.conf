events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Standard Edition specific configuration
    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Serve JavaScript files with correct MIME type
        location ~* \.js$ {
            add_header Content-Type application/javascript;
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri =404;
        }

        # Serve CSS files with correct MIME type
        location ~* \.css$ {
            add_header Content-Type text/css;
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri =404;
        }

        # Serve other assets
        location /assets/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri =404;
        }

        # Serve standard edition at root (since it's built with /standard/ base)
        location / {
            try_files $uri $uri/ /index.html;

            # Add standard edition headers
            add_header X-Edition "standard" always;
            add_header X-Features "basic" always;
        }

        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
