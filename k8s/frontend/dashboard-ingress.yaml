# enterprise-ingress.yaml - Updated to serve enterprise edition at /enterprise
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: frontend-enterprise-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      - path: /enterprise(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: frontend-dashboard
            port:
              number: 80

