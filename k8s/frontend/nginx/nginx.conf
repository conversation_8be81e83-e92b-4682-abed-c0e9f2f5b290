# Main Nginx configuration file for frontend-dashboard with proxying to internal services.

user nginx;
worker_processes auto; # Use 'auto' for optimal worker processes

error_log /var/log/nginx/error.log debug; # Increase log level to debug
pid /run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    # Custom log format from user's original config with request time
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct=$upstream_connect_time uht=$upstream_header_time urt=$upstream_response_time';

    access_log /var/log/nginx/access.log main;

    sendfile        on;
    keepalive_timeout  120; # Increased from 65
    client_body_timeout 120s; # Added
    client_header_timeout 120s; # Added
    send_timeout 120s; # Added

    # --- Kubernetes DNS Resolver Configuration ---
    # This is crucial for resolving internal Kubernetes service names at runtime.
    resolver kube-dns.kube-system.svc.cluster.local valid=5s ipv6=off;

    # --- Define Upstream Servers ---
    upstream dashboard_api_upstream {
        server dashboard-api.default.svc.cluster.local:8081 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream proxy_gateway_upstream {
        server proxy-gateway.default.svc.cluster.local:8080 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream policy_manager_upstream {
        server policy-manager.default.svc.cluster.local:8083 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream planning_service_upstream {
        server planning-service.default.svc.cluster.local:8080 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # Responsible AI Services Upstreams
    upstream bias_detection_upstream {
        server bias-detection-service.default.svc.cluster.local:8084 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream explainability_upstream {
        server explainability-service.default.svc.cluster.local:8085 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream robustness_testing_upstream {
        server robustness-testing-service.default.svc.cluster.local:8086 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream compliance_upstream {
        server compliance-service.default.svc.cluster.local:8087 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream governance_upstream {
        server governance-service.default.svc.cluster.local:8080 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream auth_service_upstream {
        server auth-service.default.svc.cluster.local:80 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    server {
        listen 80;
        server_name localhost;

        # Increase timeouts and buffer sizes
        client_max_body_size 10M;
        client_body_buffer_size 128k;
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
        proxy_buffer_size 4k;
        proxy_buffers 4 32k;
        proxy_busy_buffers_size 64k;
        proxy_temp_file_write_size 64k;

        # Enable keepalive connections to upstream
        proxy_http_version 1.1;
        proxy_set_header Connection "";

        # 1. LLM inference proxy
        location /v1/chat/ {
            proxy_pass http://proxy_gateway_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_buffering off;
            proxy_cache off;
            chunked_transfer_encoding off;
        }

        # 1.1. Planning service proxy
        location /v1/goals {
            proxy_pass http://planning_service_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-User-ID $http_x_user_id;
        }

        location /v1/templates {
            proxy_pass http://planning_service_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-User-ID $http_x_user_id;
        }

        # Planning service API routes - MUST come before general /api/ proxy
        location /api/planning/ {
            # Rewrite /api/planning/v1/* to /v1/*
            rewrite ^/api/planning/(.*) /$1 break;
            proxy_pass http://planning_service_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-User-ID $http_x_user_id;
        }

        # Authentication service routes - MUST come before general /api/ proxy
        location /auth/ {
            proxy_pass http://auth_service_upstream/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            # Don't buffer for OAuth redirects
            proxy_buffering off;
            proxy_redirect off;
        }

        # Responsible AI Services Routing
        location /v1/bias-audits {
            rewrite ^/v1/(.*) /$1 break;
            proxy_pass http://bias_detection_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /v1/bias-metrics {
            rewrite ^/v1/(.*) /$1 break;
            proxy_pass http://bias_detection_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /v1/explanations {
            rewrite ^/v1/(.*) /$1 break;
            proxy_pass http://explainability_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /v1/robustness-tests {
            rewrite ^/v1/(.*) /$1 break;
            proxy_pass http://robustness_testing_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /v1/compliance {
            rewrite ^/v1/(.*) /$1 break;
            proxy_pass http://compliance_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /v1/factsheets {
            rewrite ^/v1/(.*) /$1 break;
            proxy_pass http://governance_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 2. Policies API - both exact match and with trailing slash
        location /api/policies {
            # No rewrite - pass the full path to policy manager
            proxy_pass http://policy_manager_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 3. Model profiles API - both exact match and with trailing slash
        location /api/model_profiles {
            # No rewrite - pass the full path to policy manager
            proxy_pass http://policy_manager_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 3.1. PromptOps API - all prompt-related endpoints go to policy manager
        location /api/prompts {
            # No rewrite - pass the full path to policy manager
            proxy_pass http://policy_manager_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 4. Dashboard API specific endpoints - no rewrite needed
        location ~ ^/api/(summary|time-series|inference-logs|backend-latencies|optimal-backend|evaluation-results|curated-data|set-preference) {
            proxy_pass http://dashboard_api_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_intercept_errors on;
            error_log /var/log/nginx/dashboard_api_error.log debug;
            
            # Add debugging headers
            add_header X-Proxy-Response-Time $upstream_response_time;
            add_header X-Request-Time $request_time;
            add_header X-Proxy-Destination "dashboard_api_upstream$request_uri";
        }

        # 5. General API proxy - for dashboard-api endpoints
        location /api/ {
            # For dashboard-api, we'll keep the rewrite
            rewrite ^/api/(.*) /$1 break;
            proxy_pass http://dashboard_api_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Governance service routes
        location /governance/ {
            proxy_pass http://governance_upstream/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Governance WebSocket support
        location /ws/governance {
            proxy_pass http://governance_upstream/ws/governance;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 86400;
        }

        # 6. Static assets - serve with correct MIME types
        location ~* \.js$ {
            root /usr/share/nginx/html;
            add_header Content-Type application/javascript;
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri =404;
        }

        location ~* \.css$ {
            root /usr/share/nginx/html;
            add_header Content-Type text/css;
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri =404;
        }

        location /assets/ {
            root /usr/share/nginx/html;
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri =404;
        }

        # 7. SPA fallback for all other routes
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        # Add custom error handling
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }

        # Add health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
        }
    }
}
