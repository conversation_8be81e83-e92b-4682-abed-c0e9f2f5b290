import React, { useState } from 'react';
import {
  ThumbsUp, ThumbsDown, Meh, Users, MessageCircle, TrendingUp,
  AlertTriangle, Heart, Zap, Target, Send, Loader
} from 'lucide-react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend } from 'recharts';

const SentimentOverview = ({ data, onAnalyzeText, showNotification }) => {
  const [textToAnalyze, setTextToAnalyze] = useState('');
  const [analyzing, setAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState(null);

  const handleAnalyze = async () => {
    if (!textToAnalyze.trim()) {
      showNotification('Please enter text to analyze', 'warning');
      return;
    }

    setAnalyzing(true);
    try {
      const result = await onAnalyzeText(textToAnalyze, 'manual');
      setAnalysisResult(result);
      showNotification('Analysis completed successfully', 'success');
    } catch (error) {
      showNotification('Analysis failed', 'error');
    } finally {
      setAnalyzing(false);
    }
  };

  const sentimentColors = {
    positive: '#10B981',
    neutral: '#F59E0B',
    negative: '#EF4444'
  };

  const pieData = data?.sentiment ? [
    { name: 'Positive', value: data.sentiment.positive * 100, color: sentimentColors.positive },
    { name: 'Neutral', value: data.sentiment.neutral * 100, color: sentimentColors.neutral },
    { name: 'Negative', value: data.sentiment.negative * 100, color: sentimentColors.negative }
  ] : [];

  const emotionData = data?.emotions ? [
    { name: 'Joy', value: data.emotions.joy * 100 },
    { name: 'Trust', value: data.emotions.trust * 100 },
    { name: 'Excitement', value: data.emotions.excitement * 100 },
    { name: 'Anger', value: data.emotions.anger * 100 },
    { name: 'Fear', value: data.emotions.fear * 100 },
    { name: 'Sadness', value: data.emotions.sadness * 100 }
  ] : [];

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <ThumbsUp size={24} className="text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Positive Sentiment</p>
              <p className="text-2xl font-bold text-gray-900">
                {data?.sentiment ? Math.round(data.sentiment.positive * 100) : 65}%
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <MessageCircle size={24} className="text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Volume</p>
              <p className="text-2xl font-bold text-gray-900">
                {data?.volume?.toLocaleString() || '1,250'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <TrendingUp size={24} className="text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Satisfaction Score</p>
              <p className="text-2xl font-bold text-gray-900">
                {data?.metrics?.satisfaction_score?.toFixed(1) || '7.8'}/10
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <AlertTriangle size={24} className="text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Alerts</p>
              <p className="text-2xl font-bold text-gray-900">
                {data?.alerts?.length || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sentiment Distribution */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Sentiment Distribution</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => `${value.toFixed(1)}%`} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Emotion Analysis */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Emotion Analysis</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={emotionData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => `${value.toFixed(1)}%`} />
                <Bar dataKey="value" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Text Analysis Tool */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Analyze Text</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Enter text for sentiment analysis
            </label>
            <textarea
              value={textToAnalyze}
              onChange={(e) => setTextToAnalyze(e.target.value)}
              placeholder="Enter customer feedback, social media post, or any text to analyze..."
              className="w-full h-32 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <button
            onClick={handleAnalyze}
            disabled={analyzing || !textToAnalyze.trim()}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {analyzing ? (
              <Loader size={16} className="mr-2 animate-spin" />
            ) : (
              <Send size={16} className="mr-2" />
            )}
            {analyzing ? 'Analyzing...' : 'Analyze Text'}
          </button>
        </div>

        {/* Analysis Result */}
        {analysisResult && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-md font-semibold text-gray-900 mb-3">Analysis Result</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-600">Overall Sentiment</p>
                <p className={`text-lg font-bold ${
                  analysisResult.sentiment?.overall === 'positive' ? 'text-green-600' :
                  analysisResult.sentiment?.overall === 'negative' ? 'text-red-600' : 'text-yellow-600'
                }`}>
                  {analysisResult.sentiment?.overall?.toUpperCase() || 'NEUTRAL'}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Confidence</p>
                <p className="text-lg font-bold text-gray-900">
                  {Math.round((analysisResult.confidence || 0.7) * 100)}%
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Intent</p>
                <p className="text-lg font-bold text-gray-900">
                  {analysisResult.intent || 'General'}
                </p>
              </div>
            </div>

            {analysisResult.insights && analysisResult.insights.length > 0 && (
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-600 mb-2">Key Insights</p>
                <ul className="list-disc list-inside space-y-1">
                  {analysisResult.insights.map((insight, index) => (
                    <li key={index} className="text-sm text-gray-700">{insight}</li>
                  ))}
                </ul>
              </div>
            )}

            {analysisResult.suggestions && analysisResult.suggestions.length > 0 && (
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-600 mb-2">Suggested Actions</p>
                <div className="space-y-2">
                  {analysisResult.suggestions.map((suggestion, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        suggestion.priority === 'high' ? 'bg-red-100 text-red-800' :
                        suggestion.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {suggestion.priority}
                      </span>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{suggestion.action}</p>
                        <p className="text-xs text-gray-600">{suggestion.reason}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Trending Topics */}
      {data?.trending_topics && data.trending_topics.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Trending Topics</h3>
          <div className="flex flex-wrap gap-2">
            {data.trending_topics.map((topic, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
              >
                #{topic.replace('_', ' ')}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Active Alerts */}
      {data?.alerts && data.alerts.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Active Alerts</h3>
          <div className="space-y-3">
            {data.alerts.map((alert, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-yellow-50 rounded-lg">
                <AlertTriangle size={20} className="text-yellow-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{alert.message}</p>
                  <p className="text-xs text-gray-600">Severity: {alert.severity}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SentimentOverview;
