import React, { useState, useEffect } from 'react';
import { Star, TrendingUp, Zap, Brain, Code, MessageCircle, BarChart3, Eye, ChevronDown, ChevronUp, RefreshCw } from 'lucide-react';
import useModelScores from '../hooks/useModelScores';
import { ScoreProgressBar } from './ModelScoreDisplay';

const LiveModelScoresDashboard = ({ className = '', onNavigateToChat }) => {
  const [selectedCapability, setSelectedCapability] = useState('overall');
  const [sortBy, setSortBy] = useState('overall');
  const [showAllModels, setShowAllModels] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  
  const { modelProfiles, modelScores, getModelScore, getTopCapabilities, loading, refresh } = useModelScores();

  const capabilities = [
    { key: 'overall', label: 'Overall Performance', icon: Star, color: 'text-yellow-600' },
    { key: 'code_generation', label: 'Code Generation', icon: Code, color: 'text-blue-600' },
    { key: 'reasoning', label: 'Reasoning', icon: Brain, color: 'text-purple-600' },
    { key: 'creative_writing', label: 'Creative Writing', icon: MessageCircle, color: 'text-pink-600' },
    { key: 'factual_qa', label: 'Factual Q&A', icon: BarChart3, color: 'text-green-600' },
    { key: 'conversational', label: 'Conversational', icon: MessageCircle, color: 'text-indigo-600' },
    { key: 'rag', label: 'RAG/Search', icon: BarChart3, color: 'text-orange-600' },
    { key: 'multimodal', label: 'Multimodal', icon: Eye, color: 'text-red-600' },
    { key: 'real_time_data', label: 'Real-time Data', icon: Zap, color: 'text-cyan-600' }
  ];

  const handleRefresh = async () => {
    setRefreshing(true);
    await refresh();
    setRefreshing(false);
  };

  const getScoreColor = (score) => {
    if (score >= 0.9) return 'text-green-600 bg-green-50 border-green-200';
    if (score >= 0.8) return 'text-blue-600 bg-blue-50 border-blue-200';
    if (score >= 0.7) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getScoreBadgeColor = (score) => {
    if (score >= 0.9) return 'bg-green-100 text-green-800';
    if (score >= 0.8) return 'bg-blue-100 text-blue-800';
    if (score >= 0.7) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const sortedModels = modelProfiles
    .filter(profile => modelScores[profile.id])
    .sort((a, b) => {
      const scoreA = getModelScore(a.id, sortBy);
      const scoreB = getModelScore(b.id, sortBy);
      return scoreB - scoreA;
    })
    .slice(0, showAllModels ? undefined : 6);

  const selectedCapabilityInfo = capabilities.find(c => c.key === selectedCapability);
  const IconComponent = selectedCapabilityInfo?.icon || Star;

  const topPerformers = capabilities.map(capability => {
    const topModel = modelProfiles
      .filter(profile => modelScores[profile.id])
      .sort((a, b) => getModelScore(b.id, capability.key) - getModelScore(a.id, capability.key))[0];
    
    return {
      capability,
      model: topModel,
      score: topModel ? getModelScore(topModel.id, capability.key) : 0
    };
  }).filter(item => item.score > 0);

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg">
              <TrendingUp className="text-white" size={24} />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Live AI Optimizer Scores</h3>
              <p className="text-sm text-gray-500">Real-time model performance insights</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            {onNavigateToChat && (
              <button
                onClick={onNavigateToChat}
                className="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <MessageCircle size={16} className="mr-2" />
                Try AI Chat
              </button>
            )}
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              <RefreshCw size={16} className={`mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>
        </div>
      </div>

      {/* Top Performers Summary */}
      <div className="p-6 border-b border-gray-200 bg-gray-50">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Top Performers by Capability</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {topPerformers.slice(0, 6).map(({ capability, model, score }) => {
            const IconComp = capability.icon;
            return (
              <div key={capability.key} className="flex items-center space-x-3 p-3 bg-white rounded-lg border border-gray-200">
                <div className={`p-2 rounded-lg bg-gray-100`}>
                  <IconComp size={16} className={capability.color} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-xs font-medium text-gray-900 truncate">{capability.label}</p>
                  <p className="text-xs text-gray-500 truncate">{model?.name || 'N/A'}</p>
                </div>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getScoreBadgeColor(score)}`}>
                  {(score * 100).toFixed(0)}%
                </span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Controls */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                View by Capability
              </label>
              <select
                value={selectedCapability}
                onChange={(e) => setSelectedCapability(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                {capabilities.map(cap => (
                  <option key={cap.key} value={cap.key}>
                    {cap.label}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sort by
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                {capabilities.map(cap => (
                  <option key={cap.key} value={cap.key}>
                    {cap.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <button
            onClick={() => setShowAllModels(!showAllModels)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            {showAllModels ? (
              <>
                <ChevronUp size={16} className="mr-2" />
                Show Less
              </>
            ) : (
              <>
                <ChevronDown size={16} className="mr-2" />
                Show All Models
              </>
            )}
          </button>
        </div>
      </div>

      {/* Model Scores Grid */}
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {sortedModels.map((profile, index) => {
            const overallScore = getModelScore(profile.id, 'overall');
            const selectedScore = getModelScore(profile.id, selectedCapability);
            const topCaps = getTopCapabilities(profile.id, 3);
            
            return (
              <div
                key={profile.id}
                className={`p-4 rounded-lg border-2 transition-all duration-200 hover:shadow-md ${getScoreColor(selectedScore)}`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium text-gray-900 text-sm">{profile.name}</h4>
                      {index === 0 && (
                        <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full font-medium">
                          #1 {selectedCapabilityInfo?.label}
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-gray-500 mt-1">{profile.provider || 'Unknown Provider'}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-gray-900">
                      {(selectedScore * 100).toFixed(0)}%
                    </div>
                    <div className="text-xs text-gray-500">
                      Overall: {(overallScore * 100).toFixed(0)}%
                    </div>
                  </div>
                </div>

                {/* Score Progress Bar */}
                <ScoreProgressBar
                  score={selectedScore}
                  label={selectedCapabilityInfo?.label || 'Score'}
                  className="mb-3"
                />

                {/* Top Capabilities */}
                {topCaps.length > 0 && (
                  <div>
                    <p className="text-xs font-medium text-gray-700 mb-2">Top Capabilities:</p>
                    <div className="flex flex-wrap gap-1">
                      {topCaps.map(({ capability, score }) => (
                        <span
                          key={capability}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                        >
                          {capability} {(score * 100).toFixed(0)}%
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {!showAllModels && modelProfiles.length > 6 && (
          <div className="mt-4 text-center">
            <button
              onClick={() => setShowAllModels(true)}
              className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
            >
              View {modelProfiles.length - 6} more models →
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default LiveModelScoresDashboard;
