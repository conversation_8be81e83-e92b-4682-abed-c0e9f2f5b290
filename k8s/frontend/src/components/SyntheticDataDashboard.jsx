import React, { useState, useEffect } from 'react';
import {
  RefreshCw,
  Play,
  Pause,
  Square,
  Settings,
  Database,
  Activity,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  BarChart3,
  FileText,
  Target,
  Shield,
  Zap,
  TrendingUp,
  Users,
  Brain,
  Cpu,
  HardDrive,
  Network,
  Timer,
  Info
} from 'lucide-react';

const SyntheticDataDashboard = ({ showNotification, onFormStateChange }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(null);
  const [syntheticDataStatus, setSyntheticDataStatus] = useState({});
  const [serviceMetrics, setServiceMetrics] = useState({});
  const [activeConfigService, setActiveConfigService] = useState(null);
  const [configValues, setConfigValues] = useState({});

  // Notify parent when form state changes
  useEffect(() => {
    if (onFormStateChange) {
      onFormStateChange(false); // No forms currently open
    }
  }, [onFormStateChange]);

  // Load configuration for a service
  const loadServiceConfig = async (service) => {
    try {
      const response = await fetch(`/api/synthetic-data/${service}/config`);
      if (response.ok) {
        const config = await response.json();
        setConfigValues(prev => ({ ...prev, [service]: config }));
      }
    } catch (error) {
      console.error(`Error loading ${service} config:`, error);
    }
  };

  // Fetch synthetic data status and metrics
  const fetchSyntheticDataStatus = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/synthetic-data/status');
      if (response.ok) {
        const data = await response.json();
        setSyntheticDataStatus(data);
      }
      
      const metricsResponse = await fetch('/api/synthetic-data/metrics');
      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json();
        setServiceMetrics(metricsData);
      }
      
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Error fetching synthetic data status:', error);
      showNotification('Failed to fetch synthetic data status', 'error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSyntheticDataStatus();
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchSyntheticDataStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  // Control functions for synthetic data generation
  const controlSyntheticData = async (service, action) => {
    try {
      const response = await fetch(`/api/synthetic-data/${service}/${action}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      if (response.ok) {
        showNotification(`${service} synthetic data ${action} successful`, 'success');
        fetchSyntheticDataStatus(); // Refresh status
      } else {
        throw new Error(`Failed to ${action} ${service}`);
      }
    } catch (error) {
      console.error(`Error ${action}ing ${service}:`, error);
      showNotification(`Failed to ${action} ${service} synthetic data`, 'error');
    }
  };

  const updateConfiguration = async (service, config) => {
    try {
      const response = await fetch(`/api/synthetic-data/${service}/config`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config)
      });
      
      if (response.ok) {
        showNotification(`${service} configuration updated`, 'success');
        fetchSyntheticDataStatus();
      } else {
        throw new Error(`Failed to update ${service} configuration`);
      }
    } catch (error) {
      console.error(`Error updating ${service} configuration:`, error);
      showNotification(`Failed to update ${service} configuration`, 'error');
    }
  };

  const TabButton = ({ name, icon, label, count }) => (
    <button
      onClick={() => setActiveTab(name)}
      className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
        activeTab === name
          ? 'bg-blue-100 text-blue-700 border border-blue-200'
          : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
      }`}
    >
      {icon}
      <span>{label}</span>
      {count !== undefined && (
        <span className={`px-2 py-1 text-xs rounded-full ${
          activeTab === name ? 'bg-blue-200 text-blue-800' : 'bg-gray-200 text-gray-600'
        }`}>
          {count}
        </span>
      )}
    </button>
  );

  const StatusBadge = ({ status }) => {
    const getStatusConfig = (status) => {
      switch (status) {
        case 'running':
          return { icon: <CheckCircle size={16} />, color: 'text-green-600 bg-green-100', text: 'Running' };
        case 'stopped':
          return { icon: <XCircle size={16} />, color: 'text-red-600 bg-red-100', text: 'Stopped' };
        case 'paused':
          return { icon: <Pause size={16} />, color: 'text-yellow-600 bg-yellow-100', text: 'Paused' };
        case 'error':
          return { icon: <AlertCircle size={16} />, color: 'text-red-600 bg-red-100', text: 'Error' };
        default:
          return { icon: <AlertCircle size={16} />, color: 'text-gray-600 bg-gray-100', text: 'Unknown' };
      }
    };

    const config = getStatusConfig(status);
    return (
      <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-sm font-medium ${config.color}`}>
        {config.icon}
        <span>{config.text}</span>
      </span>
    );
  };

  const ServiceControlCard = ({ service, title, description, icon, status, metrics, onControl, onConfigure }) => (
    <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            {icon}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            <p className="text-sm text-gray-600">{description}</p>
          </div>
        </div>
        <StatusBadge status={status} />
      </div>

      {/* Metrics */}
      {metrics && (
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{metrics.generated || 0}</div>
            <div className="text-sm text-gray-500">Generated</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{metrics.rate || '0/min'}</div>
            <div className="text-sm text-gray-500">Rate</div>
          </div>
        </div>
      )}

      {/* Control Buttons */}
      <div className="flex space-x-2">
        {status === 'running' ? (
          <>
            <button
              onClick={() => onControl('pause')}
              className="flex items-center space-x-1 px-3 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
            >
              <Pause size={16} />
              <span>Pause</span>
            </button>
            <button
              onClick={() => onControl('stop')}
              className="flex items-center space-x-1 px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              <Square size={16} />
              <span>Stop</span>
            </button>
          </>
        ) : (
          <button
            onClick={() => onControl('start')}
            className="flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            <Play size={16} />
            <span>Start</span>
          </button>
        )}
        <button
          onClick={() => onControl('restart')}
          className="flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <RefreshCw size={16} />
          <span>Restart</span>
        </button>
        <button
          onClick={onConfigure}
          className="flex items-center space-x-1 px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
        >
          <Settings size={16} />
          <span>Config</span>
        </button>
      </div>
    </div>
  );

  const OverviewTab = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Total Services</h3>
              <p className="text-3xl font-bold text-blue-600">5</p>
            </div>
            <Database className="h-12 w-12 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Active Generators</h3>
              <p className="text-3xl font-bold text-green-600">
                {Object.values(syntheticDataStatus).filter(s => s?.status === 'running').length}
              </p>
            </div>
            <Activity className="h-12 w-12 text-green-500" />
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Data Generated Today</h3>
              <p className="text-3xl font-bold text-purple-600">
                {serviceMetrics.totalGenerated || 0}
              </p>
            </div>
            <TrendingUp className="h-12 w-12 text-purple-500" />
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Status Overview</h3>
        <div className="space-y-4">
          {[
            { key: 'promptops', name: 'PromptOps Evaluation Data', icon: <FileText size={20} /> },
            { key: 'planning', name: 'Planning Analytical Data', icon: <Target size={20} /> },
            { key: 'evaluation', name: 'Evaluation Test Cases', icon: <Shield size={20} /> },
            { key: 'robustness', name: 'Robustness Testing', icon: <Zap size={20} /> },
            { key: 'cache', name: 'Cache Warming Data', icon: <Database size={20} /> }
          ].map(service => (
            <div key={service.key} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gray-100 rounded-lg">
                  {service.icon}
                </div>
                <span className="font-medium text-gray-900">{service.name}</span>
              </div>
              <StatusBadge status={syntheticDataStatus[service.key]?.status || 'unknown'} />
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const PromptOpsTab = () => (
    <div className="space-y-6">
      <ServiceControlCard
        service="promptops"
        title="PromptOps Evaluation Data"
        description="Generate synthetic prompts and evaluation test cases for A/B testing and performance analysis"
        icon={<FileText className="h-6 w-6 text-blue-600" />}
        status={syntheticDataStatus.promptops?.status || 'unknown'}
        metrics={serviceMetrics.promptops}
        onControl={(action) => controlSyntheticData('promptops', action)}
        onConfigure={() => setActiveConfigService('promptops')}
      />

      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Configuration</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-3 border border-gray-200 rounded-lg">
            <div className="text-sm text-gray-600">Model</div>
            <div className="font-medium">{configValues.promptops?.model || 'gemini-2.5-flash-preview-05-20'}</div>
          </div>
          <div className="p-3 border border-gray-200 rounded-lg">
            <div className="text-sm text-gray-600">Interval</div>
            <div className="font-medium">{configValues.promptops?.interval || 60} minutes</div>
          </div>
          <div className="p-3 border border-gray-200 rounded-lg">
            <div className="text-sm text-gray-600">Batch Size</div>
            <div className="font-medium">{configValues.promptops?.batch_size || 10} test cases</div>
          </div>
        </div>
      </div>
    </div>
  );

  const PlanningTab = () => (
    <div className="space-y-6">
      <ServiceControlCard
        service="planning"
        title="Planning Analytical Data"
        description="Generate synthetic analytical data for planning service when ClickHouse queries fail"
        icon={<Target className="h-6 w-6 text-green-600" />}
        status={syntheticDataStatus.planning?.status || 'unknown'}
        metrics={serviceMetrics.planning}
        onControl={(action) => controlSyntheticData('planning', action)}
        onConfigure={() => setActiveConfigService('planning')}
      />

      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Synthetic Data Types</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="p-4 border border-gray-200 rounded-lg">
            <h4 className="font-medium text-gray-900">Model Performance</h4>
            <p className="text-sm text-gray-600 mt-1">Request counts, costs, response times, token usage</p>
            <div className="mt-2 text-sm text-blue-600">Last generated: 2 hours ago</div>
          </div>
          <div className="p-4 border border-gray-200 rounded-lg">
            <h4 className="font-medium text-gray-900">Cost Analysis</h4>
            <p className="text-sm text-gray-600 mt-1">Cost breakdowns, optimization opportunities</p>
            <div className="mt-2 text-sm text-blue-600">Last generated: 1 hour ago</div>
          </div>
          <div className="p-4 border border-gray-200 rounded-lg">
            <h4 className="font-medium text-gray-900">Usage Patterns</h4>
            <p className="text-sm text-gray-600 mt-1">Traffic patterns, peak usage times</p>
            <div className="mt-2 text-sm text-blue-600">Last generated: 30 minutes ago</div>
          </div>
        </div>
      </div>
    </div>
  );

  const EvaluationTab = () => (
    <div className="space-y-6">
      <ServiceControlCard
        service="evaluation"
        title="Evaluation Test Cases"
        description="Generate synthetic evaluation scenarios and test cases for LLM performance assessment"
        icon={<Shield className="h-6 w-6 text-purple-600" />}
        status={syntheticDataStatus.evaluation?.status || 'unknown'}
        metrics={serviceMetrics.evaluation}
        onControl={(action) => controlSyntheticData('evaluation', action)}
        onConfigure={() => setActiveConfigService('evaluation')}
      />

      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Suite Configuration</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">Goal Classification Tests</h4>
              <p className="text-sm text-gray-600">Test LLM ability to classify user goals</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" defaultChecked className="sr-only peer" />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">Constraint Extraction Tests</h4>
              <p className="text-sm text-gray-600">Test LLM ability to extract constraints from goals</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" defaultChecked className="sr-only peer" />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  );

  const RobustnessTab = () => (
    <div className="space-y-6">
      <ServiceControlCard
        service="robustness"
        title="Robustness Testing"
        description="Generate synthetic adversarial tests and robustness evaluation scenarios"
        icon={<Zap className="h-6 w-6 text-yellow-600" />}
        status={syntheticDataStatus.robustness?.status || 'unknown'}
        metrics={serviceMetrics.robustness}
        onControl={(action) => controlSyntheticData('robustness', action)}
        onConfigure={() => setActiveConfigService('robustness')}
      />

      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Types</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 border border-gray-200 rounded-lg text-center">
            <div className="text-2xl font-bold text-red-600">85%</div>
            <div className="text-sm text-gray-600">Adversarial Score</div>
          </div>
          <div className="p-4 border border-gray-200 rounded-lg text-center">
            <div className="text-2xl font-bold text-orange-600">78%</div>
            <div className="text-sm text-gray-600">Noise Resistance</div>
          </div>
          <div className="p-4 border border-gray-200 rounded-lg text-center">
            <div className="text-2xl font-bold text-blue-600">92%</div>
            <div className="text-sm text-gray-600">Drift Detection</div>
          </div>
        </div>
      </div>
    </div>
  );

  const CacheWarmingTab = () => (
    <div className="space-y-6">
      <ServiceControlCard
        service="cache"
        title="Cache Warming Data"
        description="Generate synthetic queries for cache warming and performance optimization"
        icon={<Database className="h-6 w-6 text-indigo-600" />}
        status={syntheticDataStatus.cache?.status || 'unknown'}
        metrics={serviceMetrics.cache}
        onControl={(action) => controlSyntheticData('cache', action)}
        onConfigure={() => setActiveConfigService('cache')}
      />

      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Query Categories</h3>
        <div className="space-y-3">
          {[
            { category: 'Technology', queries: 25, frequency: 'High' },
            { category: 'Education', queries: 18, frequency: 'Medium' },
            { category: 'Security', queries: 12, frequency: 'Medium' },
            { category: 'Professional', queries: 8, frequency: 'Low' }
          ].map((item, index) => (
            <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
              <div>
                <span className="font-medium text-gray-900">{item.category}</span>
                <span className="ml-2 text-sm text-gray-600">({item.queries} queries)</span>
              </div>
              <span className={`px-2 py-1 text-xs rounded-full ${
                item.frequency === 'High' ? 'bg-red-100 text-red-800' :
                item.frequency === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-green-100 text-green-800'
              }`}>
                {item.frequency}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const ConfigurationModal = ({ service, onClose }) => {
    const [localConfig, setLocalConfig] = useState(configValues[service] || {});
    const [saving, setSaving] = useState(false);

    useEffect(() => {
      if (service && !configValues[service]) {
        loadServiceConfig(service);
      }
    }, [service]);

    useEffect(() => {
      if (configValues[service]) {
        setLocalConfig(configValues[service]);
      }
    }, [configValues[service]]);

    const handleSave = async () => {
      setSaving(true);
      try {
        const response = await fetch(`/api/synthetic-data/${service}/config`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(localConfig)
        });

        if (response.ok) {
          showNotification(`${service} configuration updated successfully`, 'success');
          setConfigValues(prev => ({ ...prev, [service]: localConfig }));
          onClose();
          fetchSyntheticDataStatus(); // Refresh status
        } else {
          throw new Error('Failed to update configuration');
        }
      } catch (error) {
        console.error(`Error updating ${service} config:`, error);
        showNotification(`Failed to update ${service} configuration`, 'error');
      } finally {
        setSaving(false);
      }
    };

    const updateConfigValue = (key, value) => {
      setLocalConfig(prev => ({ ...prev, [key]: value }));
    };

    if (!service) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              Configure {service.charAt(0).toUpperCase() + service.slice(1)} Synthetic Data
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XCircle size={24} />
            </button>
          </div>

          <div className="p-6">
            {service === 'promptops' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Generation Model</label>
                  <select
                    value={localConfig.model || 'gemini-2.5-flash-preview-05-20'}
                    onChange={(e) => updateConfigValue('model', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="gemini-2.5-flash-preview-05-20">Gemini 2.5 Flash Preview</option>
                    <option value="gpt-4">GPT-4</option>
                    <option value="claude-3-haiku">Claude 3 Haiku</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Generation Interval (minutes)</label>
                  <input
                    type="number"
                    value={localConfig.interval || 60}
                    onChange={(e) => updateConfigValue('interval', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Test Cases per Batch</label>
                  <input
                    type="number"
                    value={localConfig.batch_size || 10}
                    onChange={(e) => updateConfigValue('batch_size', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            )}

            {service === 'planning' && (
              <div className="space-y-4">
                <div>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={localConfig.fallback_enabled || false}
                      onChange={(e) => updateConfigValue('fallback_enabled', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-700">Enable Fallback Data Generation</span>
                  </label>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Refresh Interval (seconds)</label>
                  <input
                    type="number"
                    value={localConfig.refresh_interval || 300}
                    onChange={(e) => updateConfigValue('refresh_interval', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            )}

            {service === 'evaluation' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Test Frequency</label>
                  <select
                    value={localConfig.test_frequency || 'daily'}
                    onChange={(e) => updateConfigValue('test_frequency', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="hourly">Hourly</option>
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                  </select>
                </div>
              </div>
            )}

            {service === 'robustness' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Batch Size</label>
                  <input
                    type="number"
                    value={localConfig.batch_size || 5}
                    onChange={(e) => updateConfigValue('batch_size', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            )}

            {service === 'cache' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Queries per Category</label>
                  <input
                    type="number"
                    value={localConfig.queries_per_category || 25}
                    onChange={(e) => updateConfigValue('queries_per_category', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Frequency</label>
                  <select
                    value={localConfig.frequency || 'medium'}
                    onChange={(e) => updateConfigValue('frequency', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                </div>
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={saving}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {saving ? 'Saving...' : 'Save Configuration'}
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Synthetic Data Management</h2>
          <p className="text-gray-600 mt-1">
            Manage and monitor synthetic data generation across all AI Operations Hub services
          </p>
        </div>
        <div className="flex items-center space-x-4">
          {lastRefresh && (
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Clock size={16} />
              <span>Last updated: {lastRefresh.toLocaleTimeString()}</span>
            </div>
          )}
          <button
            onClick={fetchSyntheticDataStatus}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <RefreshCw size={16} className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <TabButton name="overview" icon={<BarChart3 size={20} />} label="Overview" />
          <TabButton name="promptops" icon={<FileText size={20} />} label="PromptOps" />
          <TabButton name="planning" icon={<Target size={20} />} label="Planning" />
          <TabButton name="evaluation" icon={<Shield size={20} />} label="Evaluation" />
          <TabButton name="robustness" icon={<Zap size={20} />} label="Robustness" />
          <TabButton name="cache" icon={<Database size={20} />} label="Cache Warming" />
        </nav>
      </div>

      {/* Content */}
      {loading && !lastRefresh ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading synthetic data status...</span>
        </div>
      ) : (
        <div>
          {activeTab === 'overview' && <OverviewTab />}
          {activeTab === 'promptops' && <PromptOpsTab />}
          {activeTab === 'planning' && <PlanningTab />}
          {activeTab === 'evaluation' && <EvaluationTab />}
          {activeTab === 'robustness' && <RobustnessTab />}
          {activeTab === 'cache' && <CacheWarmingTab />}
        </div>
      )}

      {/* Configuration Modal */}
      {activeConfigService && (
        <ConfigurationModal
          service={activeConfigService}
          onClose={() => setActiveConfigService(null)}
        />
      )}
    </div>
  );
};

export default SyntheticDataDashboard;
