import React, { useMemo } from 'react';
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Target,
  BarChart3
} from 'lucide-react';
import { GoalStatus, TaskStatus } from '../types/planning';

const PlanningCostInsights = ({ 
  goals = [], 
  planningMetrics,
  className = '' 
}) => {
  // Calculate cost insights
  const costInsights = useMemo(() => {
    if (!goals.length) return null;

    const completedGoals = goals.filter(g => g.status === GoalStatus.COMPLETED);
    const failedGoals = goals.filter(g => g.status === GoalStatus.FAILED);
    const activeGoals = goals.filter(g => [GoalStatus.PLANNING, GoalStatus.EXECUTING].includes(g.status));

    // Extract cost data from goals (this would come from actual execution data)
    const totalEstimatedCost = goals.reduce((sum, goal) => {
      // In a real implementation, this would come from the plan's estimated_cost
      return sum + (goal.estimated_cost || 0);
    }, 0);

    const totalActualCost = completedGoals.reduce((sum, goal) => {
      // In a real implementation, this would come from actual execution costs
      return sum + (goal.actual_cost || goal.estimated_cost || 0);
    }, 0);

    const avgCostPerGoal = completedGoals.length > 0 ? totalActualCost / completedGoals.length : 0;
    const costEfficiency = totalEstimatedCost > 0 ? (totalActualCost / totalEstimatedCost) * 100 : 100;

    // Calculate cost by goal priority
    const costByPriority = goals.reduce((acc, goal) => {
      const priority = goal.priority >= 8 ? 'high' : goal.priority >= 5 ? 'medium' : 'low';
      acc[priority] = (acc[priority] || 0) + (goal.actual_cost || goal.estimated_cost || 0);
      return acc;
    }, {});

    // Calculate cost trends (mock data - in real implementation would use historical data)
    const costTrend = {
      direction: Math.random() > 0.5 ? 'up' : 'down',
      percentage: Math.floor(Math.random() * 20) + 5
    };

    return {
      totalEstimatedCost,
      totalActualCost,
      avgCostPerGoal,
      costEfficiency,
      costByPriority,
      costTrend,
      completedGoals: completedGoals.length,
      failedGoals: failedGoals.length,
      activeGoals: activeGoals.length
    };
  }, [goals]);

  // Generate optimization recommendations
  const recommendations = useMemo(() => {
    if (!costInsights) return [];

    const recs = [];

    // Cost efficiency recommendations
    if (costInsights.costEfficiency > 120) {
      recs.push({
        type: 'warning',
        title: 'High Cost Overrun',
        description: `Goals are running ${(costInsights.costEfficiency - 100).toFixed(1)}% over budget. Consider optimizing task decomposition or using more cost-effective LLM models.`,
        icon: AlertTriangle,
        priority: 'high'
      });
    } else if (costInsights.costEfficiency < 80) {
      recs.push({
        type: 'success',
        title: 'Excellent Cost Efficiency',
        description: `Goals are completing ${(100 - costInsights.costEfficiency).toFixed(1)}% under budget. Current optimization strategies are working well.`,
        icon: CheckCircle,
        priority: 'low'
      });
    }

    // Goal completion rate recommendations
    const successRate = costInsights.completedGoals / (costInsights.completedGoals + costInsights.failedGoals) * 100;
    if (successRate < 70) {
      recs.push({
        type: 'warning',
        title: 'Low Success Rate',
        description: `Only ${successRate.toFixed(1)}% of goals are completing successfully. Review goal complexity and success criteria.`,
        icon: Target,
        priority: 'high'
      });
    }

    // Priority-based cost recommendations
    if (costInsights.costByPriority.low > costInsights.costByPriority.high) {
      recs.push({
        type: 'info',
        title: 'Priority Optimization Opportunity',
        description: 'Low-priority goals are consuming more resources than high-priority ones. Consider reallocating resources.',
        icon: BarChart3,
        priority: 'medium'
      });
    }

    // Active goals recommendations
    if (costInsights.activeGoals > 5) {
      recs.push({
        type: 'warning',
        title: 'High Concurrent Load',
        description: `${costInsights.activeGoals} goals are currently active. Consider staggering execution to optimize resource usage.`,
        icon: Clock,
        priority: 'medium'
      });
    }

    // Cost trend recommendations
    if (costInsights.costTrend.direction === 'up' && costInsights.costTrend.percentage > 15) {
      recs.push({
        type: 'warning',
        title: 'Rising Cost Trend',
        description: `Costs have increased by ${costInsights.costTrend.percentage}% recently. Monitor LLM usage and optimize prompts.`,
        icon: TrendingUp,
        priority: 'medium'
      });
    }

    return recs.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }, [costInsights]);

  if (!costInsights) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Cost Optimization Insights</h3>
        <div className="text-center py-8">
          <DollarSign size={48} className="mx-auto text-gray-400 mb-4" />
          <p className="text-gray-600">No cost data available yet. Create and execute goals to see insights.</p>
        </div>
      </div>
    );
  }

  const MetricCard = ({ icon: Icon, title, value, subtitle, trend, color = 'indigo' }) => (
    <div className="bg-white p-4 rounded-lg border border-gray-200">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className={`p-2 rounded-lg bg-${color}-100 text-${color}-600 mr-3`}>
            <Icon size={20} />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-lg font-bold text-gray-900">{value}</p>
            {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
          </div>
        </div>
        {trend && (
          <div className={`flex items-center text-sm ${
            trend.direction === 'up' ? 'text-red-600' : 'text-green-600'
          }`}>
            {trend.direction === 'up' ? <TrendingUp size={16} /> : <TrendingDown size={16} />}
            <span className="ml-1">{trend.percentage}%</span>
          </div>
        )}
      </div>
    </div>
  );

  const RecommendationCard = ({ recommendation }) => {
    const { type, title, description, icon: Icon, priority } = recommendation;
    
    const typeStyles = {
      success: 'border-green-200 bg-green-50',
      warning: 'border-yellow-200 bg-yellow-50',
      info: 'border-blue-200 bg-blue-50',
      error: 'border-red-200 bg-red-50'
    };

    const iconStyles = {
      success: 'text-green-600',
      warning: 'text-yellow-600',
      info: 'text-blue-600',
      error: 'text-red-600'
    };

    const priorityBadge = {
      high: 'bg-red-100 text-red-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-green-100 text-green-800'
    };

    return (
      <div className={`border rounded-lg p-4 ${typeStyles[type]}`}>
        <div className="flex items-start">
          <Icon size={20} className={`mr-3 mt-0.5 ${iconStyles[type]}`} />
          <div className="flex-1">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-semibold text-gray-900">{title}</h4>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${priorityBadge[priority]}`}>
                {priority}
              </span>
            </div>
            <p className="text-sm text-gray-700">{description}</p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Cost Metrics Overview */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Cost Optimization Insights</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <MetricCard
            icon={DollarSign}
            title="Total Actual Cost"
            value={`$${costInsights.totalActualCost.toFixed(4)}`}
            subtitle={`Est: $${costInsights.totalEstimatedCost.toFixed(4)}`}
            color="green"
          />
          
          <MetricCard
            icon={Zap}
            title="Cost Efficiency"
            value={`${costInsights.costEfficiency.toFixed(1)}%`}
            subtitle={costInsights.costEfficiency > 100 ? 'Over budget' : 'Under budget'}
            color={costInsights.costEfficiency > 120 ? 'red' : costInsights.costEfficiency < 80 ? 'green' : 'yellow'}
          />
          
          <MetricCard
            icon={Target}
            title="Avg Cost/Goal"
            value={`$${costInsights.avgCostPerGoal.toFixed(4)}`}
            subtitle={`${costInsights.completedGoals} completed`}
            color="blue"
          />
          
          <MetricCard
            icon={TrendingUp}
            title="Cost Trend"
            value={costInsights.costTrend.direction === 'up' ? 'Rising' : 'Falling'}
            trend={costInsights.costTrend}
            color="purple"
          />
        </div>

        {/* Cost by Priority Breakdown */}
        <div className="border-t border-gray-200 pt-4">
          <h4 className="text-sm font-semibold text-gray-700 mb-3">Cost by Priority</h4>
          <div className="grid grid-cols-3 gap-4">
            {['high', 'medium', 'low'].map(priority => (
              <div key={priority} className="text-center">
                <div className={`text-lg font-bold ${
                  priority === 'high' ? 'text-red-600' : 
                  priority === 'medium' ? 'text-yellow-600' : 'text-green-600'
                }`}>
                  ${(costInsights.costByPriority[priority] || 0).toFixed(4)}
                </div>
                <div className="text-xs text-gray-500 capitalize">{priority} Priority</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Optimization Recommendations */}
      {recommendations.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Optimization Recommendations</h3>
          <div className="space-y-4">
            {recommendations.map((rec, index) => (
              <RecommendationCard key={index} recommendation={rec} />
            ))}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Optimization Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <BarChart3 size={20} className="text-blue-600 mr-3" />
            <div className="text-left">
              <div className="text-sm font-medium text-gray-900">Analyze LLM Usage</div>
              <div className="text-xs text-gray-500">Review model selection patterns</div>
            </div>
          </button>
          
          <button className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Target size={20} className="text-green-600 mr-3" />
            <div className="text-left">
              <div className="text-sm font-medium text-gray-900">Optimize Success Criteria</div>
              <div className="text-xs text-gray-500">Refine goal requirements</div>
            </div>
          </button>
          
          <button className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Clock size={20} className="text-purple-600 mr-3" />
            <div className="text-left">
              <div className="text-sm font-medium text-gray-900">Schedule Optimization</div>
              <div className="text-xs text-gray-500">Optimize execution timing</div>
            </div>
          </button>
          
          <button className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Zap size={20} className="text-yellow-600 mr-3" />
            <div className="text-left">
              <div className="text-sm font-medium text-gray-900">Performance Tuning</div>
              <div className="text-xs text-gray-500">Optimize task decomposition</div>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default PlanningCostInsights;
