import React, { useState, useEffect, useCallback } from 'react';
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Clock, 
  FileText, 
  Settings, 
  BarChart3, 
  TrendingUp, 
  Eye, 
  Plus,
  Filter,
  Search,
  RefreshCw,
  Bell,
  Users,
  Lock,
  Zap
} from 'lucide-react';
import { useNotification } from '../hooks/useNotification';
import useGovernanceData from '../hooks/useGovernanceData';
import GovernanceMetrics from './GovernanceMetrics';
import GovernancePolicyForm from './GovernancePolicyForm';

const GovernanceDashboard = ({ showNotification, onFormStateChange }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedTimeframe, setSelectedTimeframe] = useState('7d');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterSeverity, setFilterSeverity] = useState('all');
  const [showPolicyForm, setShowPolicyForm] = useState(false);
  const [editingPolicy, setEditingPolicy] = useState(null);

  const {
    governancePolicies,
    governanceAlerts,
    complianceStatus,
    auditLogs,
    governanceMetrics,
    loading,
    fetchGovernanceData,
    createPolicy,
    updatePolicy,
    deletePolicy,
    acknowledgeAlert,
    resolveAlert,
    dismissAlert,
    runComplianceCheck,
    exportAuditReport
  } = useGovernanceData({ showNotification });

  useEffect(() => {
    fetchGovernanceData(selectedTimeframe);
  }, [fetchGovernanceData, selectedTimeframe]);

  // Notify parent when form state changes
  useEffect(() => {
    if (onFormStateChange) {
      onFormStateChange(showPolicyForm);
    }
  }, [showPolicyForm, onFormStateChange]);

  const TabButton = ({ name, icon, label, count, alertCount }) => (
    <button
      onClick={() => setActiveTab(name)}
      className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors relative ${
        activeTab === name ? 'bg-indigo-600 text-white' : 'text-gray-600 hover:bg-gray-100'
      }`}
    >
      {icon}
      <span className="font-medium">{label}</span>
      {count !== undefined && (
        <span className={`px-2 py-1 text-xs rounded-full ${
          activeTab === name ? 'bg-indigo-500 text-white' : 'bg-gray-200 text-gray-600'
        }`}>
          {count}
        </span>
      )}
      {alertCount > 0 && (
        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
          {alertCount}
        </span>
      )}
    </button>
  );

  const StatusBadge = ({ status, severity }) => {
    const getStatusColor = (status) => {
      switch (status) {
        case 'active':
        case 'compliant':
        case 'resolved':
          return 'bg-green-100 text-green-800';
        case 'warning':
        case 'partial':
        case 'acknowledged':
          return 'bg-yellow-100 text-yellow-800';
        case 'violation':
        case 'non_compliant':
        case 'open':
          return 'bg-red-100 text-red-800';
        case 'inactive':
        case 'dismissed':
          return 'bg-gray-100 text-gray-800';
        default:
          return 'bg-blue-100 text-blue-800';
      }
    };

    const getSeverityColor = (severity) => {
      switch (severity) {
        case 'critical':
          return 'bg-red-500 text-white';
        case 'high':
          return 'bg-orange-500 text-white';
        case 'medium':
          return 'bg-yellow-500 text-white';
        case 'low':
          return 'bg-blue-500 text-white';
        default:
          return 'bg-gray-500 text-white';
      }
    };

    return (
      <div className="flex items-center space-x-2">
        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(status)}`}>
          {status}
        </span>
        {severity && (
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(severity)}`}>
            {severity}
          </span>
        )}
      </div>
    );
  };

  const MetricCard = ({ title, value, subtitle, icon, trend, color = 'indigo', onClick }) => (
    <div 
      className={`bg-white rounded-lg shadow p-6 ${onClick ? 'cursor-pointer hover:shadow-lg transition-shadow' : ''}`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className={`text-2xl font-semibold text-${color}-600`}>{value}</p>
          {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
        </div>
        <div className={`p-3 bg-${color}-100 rounded-lg`}>
          {icon}
        </div>
      </div>
      {trend !== undefined && (
        <div className="mt-4 flex items-center">
          <TrendingUp className={`h-4 w-4 ${trend > 0 ? 'text-green-500' : 'text-red-500'}`} />
          <span className={`text-sm ml-1 ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
            {trend > 0 ? '+' : ''}{trend}% from last period
          </span>
        </div>
      )}
    </div>
  );

  const OverviewTab = () => {
    const totalPolicies = governancePolicies?.length || 0;
    const activePolicies = governancePolicies?.filter(p => p.status === 'active').length || 0;
    const openAlerts = governanceAlerts?.filter(a => a.status === 'open').length || 0;
    const criticalAlerts = governanceAlerts?.filter(a => a.severity === 'critical' && a.status === 'open').length || 0;
    const avgComplianceScore = complianceStatus?.length > 0 
      ? complianceStatus.reduce((sum, item) => sum + item.score, 0) / complianceStatus.length 
      : 0;

    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="Active Policies"
            value={activePolicies}
            subtitle={`${totalPolicies} total policies`}
            icon={<Shield className="h-6 w-6 text-indigo-600" />}
            trend={5}
            onClick={() => setActiveTab('policies')}
          />
          <MetricCard
            title="Open Alerts"
            value={openAlerts}
            subtitle={`${criticalAlerts} critical`}
            icon={<AlertTriangle className="h-6 w-6 text-red-600" />}
            color="red"
            trend={-12}
            onClick={() => setActiveTab('alerts')}
          />
          <MetricCard
            title="Compliance Score"
            value={`${(avgComplianceScore * 100).toFixed(1)}%`}
            subtitle="Average across frameworks"
            icon={<CheckCircle className="h-6 w-6 text-green-600" />}
            color="green"
            trend={3}
            onClick={() => setActiveTab('compliance')}
          />
          <MetricCard
            title="Audit Events"
            value={auditLogs?.length || 0}
            subtitle="Last 7 days"
            icon={<FileText className="h-6 w-6 text-purple-600" />}
            color="purple"
            trend={8}
            onClick={() => setActiveTab('audit')}
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Alerts</h3>
            <div className="space-y-3">
              {(governanceAlerts || []).slice(0, 5).map((alert) => (
                <div key={alert.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{alert.title}</p>
                    <p className="text-sm text-gray-600">{alert.description}</p>
                    <p className="text-xs text-gray-500">
                      {new Date(alert.created_at).toLocaleString()}
                    </p>
                  </div>
                  <StatusBadge status={alert.status} severity={alert.severity} />
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Compliance Status</h3>
            <div className="space-y-3">
              {(complianceStatus || []).map((item) => (
                <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{item.framework}</p>
                    <p className="text-sm text-gray-600">Model: {item.model_id}</p>
                    <p className="text-xs text-gray-500">
                      Last audit: {new Date(item.last_audit).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <StatusBadge status={item.status} />
                    <p className="text-sm text-gray-600 mt-1">
                      {(item.score * 100).toFixed(1)}%
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const PoliciesTab = () => {

    const filteredPolicies = (governancePolicies || []).filter(policy => {
      const matchesSearch = policy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           policy.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = filterStatus === 'all' || policy.status === filterStatus;
      return matchesSearch && matchesStatus;
    });

    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search policies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="draft">Draft</option>
            </select>
          </div>
          <button
            onClick={() => {
              setEditingPolicy(null);
              setShowPolicyForm(true);
            }}
            className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
          >
            <Plus className="h-4 w-4" />
            <span>Create Policy</span>
          </button>
        </div>

        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Policy
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priority
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Updated
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPolicies.map((policy) => (
                <tr key={policy.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{policy.name}</div>
                      <div className="text-sm text-gray-500">{policy.description}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                      {policy.type}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <StatusBadge status={policy.status} />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {policy.priority}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(policy.updated_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => {
                        setEditingPolicy(policy);
                        setShowPolicyForm(true);
                      }}
                      className="text-indigo-600 hover:text-indigo-900 mr-3"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => deletePolicy(policy.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const AlertsTab = () => {
    const filteredAlerts = (governanceAlerts || []).filter(alert => {
      const matchesSearch = alert.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           alert.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = filterStatus === 'all' || alert.status === filterStatus;
      const matchesSeverity = filterSeverity === 'all' || alert.severity === filterSeverity;
      return matchesSearch && matchesStatus && matchesSeverity;
    });

    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search alerts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="all">All Status</option>
              <option value="open">Open</option>
              <option value="acknowledged">Acknowledged</option>
              <option value="resolved">Resolved</option>
              <option value="dismissed">Dismissed</option>
            </select>
            <select
              value={filterSeverity}
              onChange={(e) => setFilterSeverity(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="all">All Severity</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
          </div>
        </div>

        <div className="space-y-4">
          {filteredAlerts.map((alert) => (
            <div key={alert.id} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">{alert.title}</h3>
                    <StatusBadge status={alert.status} severity={alert.severity} />
                  </div>
                  <p className="text-gray-600 mb-3">{alert.description}</p>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500">
                    <div>
                      <span className="font-medium">Model:</span> {alert.model_id}
                    </div>
                    <div>
                      <span className="font-medium">User:</span> {alert.user_id}
                    </div>
                    <div>
                      <span className="font-medium">Service:</span> {alert.service_id}
                    </div>
                    <div>
                      <span className="font-medium">Created:</span> {new Date(alert.created_at).toLocaleString()}
                    </div>
                  </div>
                  {alert.context && (
                    <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium text-gray-700">Context:</span>
                      <pre className="text-sm text-gray-600 mt-1 whitespace-pre-wrap">
                        {JSON.stringify(alert.context, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
                <div className="flex flex-col space-y-2 ml-4">
                  {alert.status === 'open' && (
                    <>
                      <button
                        onClick={() => acknowledgeAlert(alert.id)}
                        className="px-3 py-1 text-sm bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200"
                      >
                        Acknowledge
                      </button>
                      <button
                        onClick={() => resolveAlert(alert.id, 'Manual resolution')}
                        className="px-3 py-1 text-sm bg-green-100 text-green-800 rounded hover:bg-green-200"
                      >
                        Resolve
                      </button>
                      <button
                        onClick={() => dismissAlert(alert.id, 'False positive')}
                        className="px-3 py-1 text-sm bg-gray-100 text-gray-800 rounded hover:bg-gray-200"
                      >
                        Dismiss
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const ComplianceTab = () => {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-900">Compliance Status</h3>
          <button
            onClick={() => runComplianceCheck('all', 'EU_AI_Act')}
            className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
          >
            <Zap className="h-4 w-4" />
            <span>Run Compliance Check</span>
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {(complianceStatus || []).map((item) => (
            <div key={item.id} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">{item.framework}</h4>
                  <p className="text-sm text-gray-600">Model: {item.model_id}</p>
                </div>
                <div className="text-right">
                  <StatusBadge status={item.status} />
                  <p className="text-2xl font-bold text-indigo-600 mt-1">
                    {(item.score * 100).toFixed(1)}%
                  </p>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Last Audit:</span>
                  <span className="text-gray-900">{new Date(item.last_audit).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Next Audit:</span>
                  <span className="text-gray-900">{new Date(item.next_audit).toLocaleDateString()}</span>
                </div>
              </div>

              {item.requirements && item.requirements.length > 0 && (
                <div className="mt-4">
                  <h5 className="font-medium text-gray-900 mb-2">Requirements Status</h5>
                  <div className="space-y-2">
                    {item.requirements.slice(0, 3).map((req) => (
                      <div key={req.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm text-gray-700">{req.name}</span>
                        <StatusBadge status={req.status} />
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {item.gaps && item.gaps.length > 0 && (
                <div className="mt-4">
                  <h5 className="font-medium text-gray-900 mb-2">Compliance Gaps</h5>
                  <div className="space-y-2">
                    {item.gaps.slice(0, 2).map((gap) => (
                      <div key={gap.id} className="p-2 bg-red-50 rounded">
                        <p className="text-sm text-red-800 font-medium">{gap.description}</p>
                        <p className="text-xs text-red-600">Due: {new Date(gap.due_date).toLocaleDateString()}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const AuditTab = () => {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search audit logs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
          </div>
          <button
            onClick={() => exportAuditReport(selectedTimeframe, 'json')}
            className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
          >
            <FileText className="h-4 w-4" />
            <span>Export Report</span>
          </button>
        </div>

        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Event Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Resource
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Result
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {(() => {
                try {
                  const logs = Array.isArray(auditLogs) ? auditLogs : [];
                  return logs.filter(log => {
                    if (!log) return false;
                    const searchLower = (searchTerm || '').toLowerCase();
                    return (
                      (log.event_type || '').toLowerCase().includes(searchLower) ||
                      (log.user_id || '').toLowerCase().includes(searchLower) ||
                      (log.resource || '').toLowerCase().includes(searchLower)
                    );
                  }).map((log, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {new Date(log.timestamp).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                      {log.event_type}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {log.user_id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {log.resource}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {log.action}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <StatusBadge status={log.result} />
                  </td>
                </tr>
                  ));
                } catch (error) {
                  console.error('Error rendering audit logs:', error);
                  return [];
                }
              })()}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold text-gray-900">Governance Dashboard</h2>
        <div className="flex items-center space-x-4">
          <button
            onClick={() => fetchGovernanceData(selectedTimeframe)}
            className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Refresh</span>
          </button>
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
        </div>
      </div>

      <div className="flex space-x-2 border-b border-gray-200">
        <TabButton 
          name="overview" 
          icon={<BarChart3 size={20} />} 
          label="Overview" 
        />
        <TabButton 
          name="policies" 
          icon={<Settings size={20} />} 
          label="Policies" 
          count={governancePolicies?.length || 0}
        />
        <TabButton 
          name="alerts" 
          icon={<Bell size={20} />} 
          label="Alerts" 
          count={governanceAlerts?.length || 0}
          alertCount={governanceAlerts?.filter(a => a.status === 'open' && a.severity === 'critical').length || 0}
        />
        <TabButton 
          name="compliance" 
          icon={<CheckCircle size={20} />} 
          label="Compliance" 
          count={complianceStatus?.length || 0}
        />
        <TabButton
          name="audit"
          icon={<FileText size={20} />}
          label="Audit Logs"
          count={auditLogs?.length || 0}
        />
        <TabButton
          name="metrics"
          icon={<BarChart3 size={20} />}
          label="Metrics"
        />
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      ) : (
        <div>
          {activeTab === 'overview' && <OverviewTab />}
          {activeTab === 'policies' && <PoliciesTab />}
          {activeTab === 'alerts' && <AlertsTab />}
          {activeTab === 'compliance' && <ComplianceTab />}
          {activeTab === 'audit' && <AuditTab />}
          {activeTab === 'metrics' && (
            <GovernanceMetrics
              metrics={governanceMetrics}
              timeframe={selectedTimeframe}
            />
          )}
        </div>
      )}

      {/* Policy Form Modal */}
      <GovernancePolicyForm
        policy={editingPolicy}
        isOpen={showPolicyForm}
        onClose={() => {
          setShowPolicyForm(false);
          setEditingPolicy(null);
        }}
        onSave={async (policyData) => {
          if (editingPolicy) {
            await updatePolicy(editingPolicy.id, policyData);
          } else {
            await createPolicy(policyData);
          }
          await fetchGovernanceData(selectedTimeframe);
        }}
        loading={loading}
      />
    </div>
  );
};

export default GovernanceDashboard;
