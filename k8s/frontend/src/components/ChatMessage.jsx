import React, { useState } from 'react';
import { <PERSON><PERSON>, User, Bo<PERSON>, AlertCircle, RotateCcw, Check, Info } from 'lucide-react';
import useModelScores from '../hooks/useModelScores';
import ModelScoreDisplay, { ModelScoreTooltip } from './ModelScoreDisplay';

const ChatMessage = ({ message, onRegenerate, isLast }) => {
  const [copied, setCopied] = useState(false);
  const [showScores, setShowScores] = useState(false);
  const { getModelScore, getModelProfile, getTopCapabilities, modelScores } = useModelScores();

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const isUser = message.role === 'user';
  const isError = message.isError;
  const isStreaming = message.isStreaming;

  return (
    <div className={`flex w-full mb-4 ${isUser ? 'justify-end' : 'justify-start'}`}>
      <div className={`flex max-w-[80%] ${isUser ? 'flex-row-reverse' : 'flex-row'} items-start space-x-3`}>
        {/* Avatar */}
        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
          isUser 
            ? 'bg-indigo-600 text-white' 
            : isError 
              ? 'bg-red-100 text-red-600' 
              : 'bg-gray-100 text-gray-600'
        }`}>
          {isUser ? (
            <User size={16} />
          ) : isError ? (
            <AlertCircle size={16} />
          ) : (
            <Bot size={16} />
          )}
        </div>

        {/* Message Content */}
        <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'}`}>
          <div className={`relative px-4 py-3 rounded-lg ${
            isUser 
              ? 'bg-indigo-600 text-white' 
              : isError 
                ? 'bg-red-50 text-red-800 border border-red-200' 
                : 'bg-gray-100 text-gray-800'
          }`}>
            {/* Message text */}
            <div className="whitespace-pre-wrap break-words">
              {message.content}
              {isStreaming && (
                <span className="inline-block w-2 h-5 bg-current opacity-75 animate-pulse ml-1">|</span>
              )}
            </div>

            {/* Message actions */}
            {!isUser && !isStreaming && (
              <div className="flex items-center space-x-2 mt-2 pt-2 border-t border-gray-200">
                <button
                  onClick={handleCopy}
                  className="flex items-center space-x-1 text-xs text-gray-500 hover:text-gray-700 transition-colors"
                  title="Copy message"
                >
                  {copied ? <Check size={12} /> : <Copy size={12} />}
                  <span>{copied ? 'Copied!' : 'Copy'}</span>
                </button>

                {onRegenerate && isLast && (
                  <button
                    onClick={() => onRegenerate(message)}
                    className="flex items-center space-x-1 text-xs text-gray-500 hover:text-gray-700 transition-colors"
                    title="Regenerate response"
                  >
                    <RotateCcw size={12} />
                    <span>Regenerate</span>
                  </button>
                )}
              </div>
            )}
          </div>

          {/* Timestamp and metadata */}
          <div className={`flex items-center space-x-2 mt-1 text-xs text-gray-500 ${
            isUser ? 'flex-row-reverse space-x-reverse' : 'flex-row'
          }`}>
            <span>{formatTimestamp(message.timestamp)}</span>
            {message.model && !isUser && (
              <>
                <span>•</span>
                <ModelScoreTooltip
                  modelId={message.model}
                  scores={modelScores[message.model]}
                  overallScore={getModelScore(message.model, 'overall')}
                >
                  <span className="font-medium cursor-help hover:text-indigo-600 transition-colors">
                    {message.model}
                  </span>
                </ModelScoreTooltip>
                {getModelScore(message.model, 'overall') > 0 && (
                  <>
                    <span>•</span>
                    <span className="text-yellow-600 font-medium">
                      {(getModelScore(message.model, 'overall') * 100).toFixed(0)}% score
                    </span>
                  </>
                )}
              </>
            )}
            {message.usage && !isUser && (
              <>
                <span>•</span>
                <span>{message.usage.total_tokens} tokens</span>
              </>
            )}
            {message.model && !isUser && getTopCapabilities(message.model, 1).length > 0 && (
              <>
                <span>•</span>
                <span className="text-blue-600">
                  Best: {getTopCapabilities(message.model, 1)[0]?.capability}
                </span>
              </>
            )}
          </div>

          {/* Model scores section (expandable) */}
          {message.model && !isUser && !isStreaming && getModelScore(message.model, 'overall') > 0 && (
            <div className="mt-2">
              <button
                onClick={() => setShowScores(!showScores)}
                className="flex items-center space-x-1 text-xs text-indigo-600 hover:text-indigo-800 transition-colors"
              >
                <Info size={12} />
                <span>{showScores ? 'Hide' : 'Show'} AI Scores</span>
              </button>

              {showScores && (
                <div className="mt-2 max-w-sm">
                  <ModelScoreDisplay
                    modelId={message.model}
                    scores={modelScores[message.model]}
                    topCapabilities={getTopCapabilities(message.model, 3)}
                    overallScore={getModelScore(message.model, 'overall')}
                    showDetails={true}
                  />
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
