import React from 'react';
import {
  Shield, CheckCircle, AlertTriangle, Clock, TrendingUp, TrendingDown,
  Users, FileText, Zap, BarChart3, Activity, Eye
} from 'lucide-react';
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';

const GovernanceOverview = ({ complianceStatus, riskAssessment, recentLogs }) => {
  const getFrameworkData = () => {
    if (!complianceStatus?.frameworks) return [];
    
    return Object.entries(complianceStatus.frameworks).map(([name, data]) => ({
      name,
      score: data.score,
      status: data.status
    }));
  };

  const getRiskData = () => {
    if (!riskAssessment?.categories) return [];
    
    return Object.entries(riskAssessment.categories).map(([name, data]) => ({
      name: name.replace(' Risk', ''),
      value: data.score,
      trend: data.trend
    }));
  };

  const getComplianceDistribution = () => {
    if (!complianceStatus?.summary) return [];
    
    const { passed, failed, total } = complianceStatus.summary;
    return [
      { name: 'Passed', value: passed, color: '#10B981' },
      { name: 'Failed', value: failed, color: '#EF4444' },
      { name: 'Pending', value: total - passed - failed, color: '#F59E0B' }
    ];
  };

  const getTrendData = () => {
    // Mock trend data - in production, this would come from historical data
    return [
      { date: '2024-01-01', compliance: 85, risk: 35 },
      { date: '2024-01-02', compliance: 87, risk: 32 },
      { date: '2024-01-03', compliance: 89, risk: 30 },
      { date: '2024-01-04', compliance: 91, risk: 28 },
      { date: '2024-01-05', compliance: 90, risk: 25 },
      { date: '2024-01-06', compliance: 92, risk: 25 },
      { date: '2024-01-07', compliance: 90, risk: 25 }
    ];
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-blue-600 bg-blue-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'compliant': return <CheckCircle size={16} className="text-green-600" />;
      case 'needs_attention': return <AlertTriangle size={16} className="text-yellow-600" />;
      case 'non_compliant': return <AlertTriangle size={16} className="text-red-600" />;
      default: return <Clock size={16} className="text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Shield size={24} className="text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Compliance Score</p>
              <p className="text-2xl font-bold text-gray-900">
                {complianceStatus?.summary ? 
                  Math.round((complianceStatus.summary.passed / complianceStatus.summary.total) * 100) : 90}%
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <BarChart3 size={24} className="text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Risk Level</p>
              <p className="text-2xl font-bold text-gray-900 capitalize">
                {riskAssessment?.overall_risk || 'Low'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Zap size={24} className="text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Auto-Workflows</p>
              <p className="text-2xl font-bold text-gray-900">12</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Activity size={24} className="text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Policies</p>
              <p className="text-2xl font-bold text-gray-900">8</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Compliance Distribution */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Compliance Distribution</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={getComplianceDistribution()}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {getComplianceDistribution().map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Compliance Trends */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Compliance & Risk Trends</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={getTrendData()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="compliance" stroke="#10B981" strokeWidth={2} name="Compliance %" />
                <Line type="monotone" dataKey="risk" stroke="#EF4444" strokeWidth={2} name="Risk Score" />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Framework Status */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Compliance Framework Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {getFrameworkData().map((framework) => (
            <div key={framework.name} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{framework.name}</h4>
                {getStatusIcon(framework.status)}
              </div>
              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold text-gray-900">{framework.score}%</span>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  framework.status === 'compliant' ? 'bg-green-100 text-green-800' :
                  framework.status === 'needs_attention' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {framework.status.replace('_', ' ')}
                </span>
              </div>
              <div className="mt-2">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      framework.score >= 90 ? 'bg-green-500' :
                      framework.score >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${framework.score}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Risk Categories */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Risk Categories</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {getRiskData().map((risk) => (
            <div key={risk.name} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{risk.name}</h4>
                {risk.trend === 'decreasing' ? (
                  <TrendingDown size={16} className="text-green-600" />
                ) : risk.trend === 'increasing' ? (
                  <TrendingUp size={16} className="text-red-600" />
                ) : (
                  <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
                )}
              </div>
              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold text-gray-900">{risk.value}</span>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  risk.value <= 25 ? 'bg-green-100 text-green-800' :
                  risk.value <= 50 ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {risk.value <= 25 ? 'Low' : risk.value <= 50 ? 'Medium' : 'High'}
                </span>
              </div>
              <div className="mt-2">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      risk.value <= 25 ? 'bg-green-500' :
                      risk.value <= 50 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${risk.value}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
          <button className="text-sm text-blue-600 hover:text-blue-800">View All</button>
        </div>
        <div className="space-y-3">
          {recentLogs.map((log) => (
            <div key={log.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
              <div className={`w-2 h-2 rounded-full mt-2 ${getSeverityColor(log.severity).split(' ')[1]}`}></div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900">{log.action}</p>
                  <span className="text-xs text-gray-500">
                    {new Date(log.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                <p className="text-xs text-gray-600 mt-1">{log.details}</p>
                <div className="flex items-center space-x-2 mt-2">
                  <span className={`text-xs px-2 py-1 rounded-full ${getSeverityColor(log.severity)}`}>
                    {log.severity}
                  </span>
                  <span className="text-xs text-gray-500">by {log.user}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recommendations */}
      {riskAssessment?.recommendations && (
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">AI Recommendations</h3>
          <div className="space-y-3">
            {riskAssessment.recommendations.map((recommendation, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-medium text-blue-600">{index + 1}</span>
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{recommendation}</p>
                  <div className="flex items-center space-x-2 mt-2">
                    <button className="text-xs text-blue-600 hover:text-blue-800">Implement</button>
                    <button className="text-xs text-gray-500 hover:text-gray-700">Dismiss</button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default GovernanceOverview;
