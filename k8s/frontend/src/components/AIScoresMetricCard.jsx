import React, { useState } from 'react';
import { Star, TrendingUp, ChevronRight, Zap } from 'lucide-react';
import useModelScores from '../hooks/useModelScores';

const AIScoresMetricCard = ({ onClick, className = '' }) => {
  const { modelProfiles, modelScores, getModelScore, loading } = useModelScores();

  if (loading) {
    return (
      <div className={`bg-white p-6 rounded-lg shadow-md border border-gray-200 ${className}`}>
        <div className="animate-pulse">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-24"></div>
          </div>
          <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-32"></div>
        </div>
      </div>
    );
  }

  // Calculate overall system performance
  const modelsWithScores = modelProfiles.filter(profile => modelScores[profile.id]);
  const averageOverallScore = modelsWithScores.length > 0 
    ? modelsWithScores.reduce((sum, profile) => sum + getModelScore(profile.id, 'overall'), 0) / modelsWithScores.length
    : 0;

  // Find top performing model
  const topModel = modelsWithScores
    .sort((a, b) => getModelScore(b.id, 'overall') - getModelScore(a.id, 'overall'))[0];

  const topModelScore = topModel ? getModelScore(topModel.id, 'overall') : 0;

  // Get performance trend indicator
  const getPerformanceColor = (score) => {
    if (score >= 0.9) return 'text-green-600';
    if (score >= 0.8) return 'text-blue-600';
    if (score >= 0.7) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPerformanceBadgeColor = (score) => {
    if (score >= 0.9) return 'bg-green-100 text-green-800';
    if (score >= 0.8) return 'bg-blue-100 text-blue-800';
    if (score >= 0.7) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  return (
    <div 
      className={`bg-white p-6 rounded-lg shadow-md border border-gray-200 cursor-pointer hover:shadow-lg transition-shadow duration-200 ${className}`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg">
            <TrendingUp className="text-white" size={24} />
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-900">AI Optimizer Scores</h3>
            <p className="text-xs text-gray-500">Live performance metrics</p>
          </div>
        </div>
        <ChevronRight size={16} className="text-gray-400" />
      </div>

      <div className="mt-4">
        <div className="flex items-baseline space-x-2">
          <span className={`text-2xl font-bold ${getPerformanceColor(averageOverallScore)}`}>
            {(averageOverallScore * 100).toFixed(0)}%
          </span>
          <span className="text-sm text-gray-500">avg system score</span>
        </div>
        
        <div className="mt-2 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Star size={14} className="text-yellow-500" />
            <span className="text-xs text-gray-600">
              Top: {topModel?.name || 'N/A'}
            </span>
          </div>
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPerformanceBadgeColor(topModelScore)}`}>
            {(topModelScore * 100).toFixed(0)}%
          </span>
        </div>

        <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
          <span>{modelsWithScores.length} models tracked</span>
          <div className="flex items-center space-x-1">
            <Zap size={12} />
            <span>Live data</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIScoresMetricCard;
