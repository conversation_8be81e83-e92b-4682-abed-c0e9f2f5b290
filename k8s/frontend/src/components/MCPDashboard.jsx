import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Network,
  Server,
  Users,
  Activity,
  Settings,
  Monitor,
  Zap,
  Shield,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  Plus,
  Eye,
  Trash2,
  Play,
  Pause,
  BarChart3,
  FileText,
  Link,
  Globe
} from 'lucide-react';
import { MetricCard } from './DashboardCards';
import { useMCPData } from '../hooks/useMCPData';

const MCPDashboard = ({ showNotification, onFormStateChange }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedServer, setSelectedServer] = useState('all');
  const [refreshing, setRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(null);

  // Modal states
  const [showAddClientModal, setShowAddClientModal] = useState(false);
  const [showConfigureServerModal, setShowConfigureServerModal] = useState(false);
  const [selectedServerForConfig, setSelectedServerForConfig] = useState(null);

  const {
    mcpStatus,
    connectedClients,
    availableServers,
    connectionHistory,
    serverMetrics,
    loading,
    fetchMCPData,
    connectToServer,
    disconnectFromServer,
    restartServer,
    updateServerConfig
  } = useMCPData({ showNotification });

  // Use ref to store fetchMCPData to avoid dependency issues
  const fetchMCPDataRef = useRef(fetchMCPData);
  fetchMCPDataRef.current = fetchMCPData;

  useEffect(() => {
    fetchMCPDataRef.current();
    // Set up auto-refresh every 5 minutes to reduce aggressive refreshing
    const interval = setInterval(() => {
      fetchMCPDataRef.current();
    }, 300000); // 5 minutes instead of 2
    return () => clearInterval(interval);
  }, []); // Remove fetchMCPData dependency to prevent recreation

  useEffect(() => {
    if (onFormStateChange) {
      const isFormOpen = showAddClientModal || showConfigureServerModal;
      onFormStateChange(isFormOpen);
    }
  }, [onFormStateChange, showAddClientModal, showConfigureServerModal]);

  const handleManualRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await fetchMCPDataRef.current();
      setLastRefresh(new Date());
      showNotification('MCP data refreshed successfully', 'success');
    } catch (error) {
      // Mark as manual refresh for error handling
      error.message = error.message + ' (manual)';
      showNotification('Failed to refresh MCP data', 'error');
    } finally {
      setRefreshing(false);
    }
  }, [showNotification]);

  // Click handlers
  const handleAddClient = useCallback(() => {
    setShowAddClientModal(true);
  }, []);

  const handleConfigureServer = useCallback((server = null) => {
    setSelectedServerForConfig(server);
    setShowConfigureServerModal(true);
  }, []);

  const handleConnectToServer = useCallback(async (serverId) => {
    try {
      await connectToServer(serverId);
      showNotification(`Successfully connected to ${serverId}`, 'success');
    } catch (error) {
      showNotification(`Failed to connect to ${serverId}`, 'error');
    }
  }, [connectToServer, showNotification]);

  const handleDisconnectFromServer = useCallback(async (serverId) => {
    try {
      await disconnectFromServer(serverId);
      showNotification(`Successfully disconnected from ${serverId}`, 'success');
    } catch (error) {
      showNotification(`Failed to disconnect from ${serverId}`, 'error');
    }
  }, [disconnectFromServer, showNotification]);

  const handleRestartServer = useCallback(async (serverId) => {
    try {
      await restartServer(serverId);
      showNotification(`Successfully restarted ${serverId}`, 'success');
    } catch (error) {
      showNotification(`Failed to restart ${serverId}`, 'error');
    }
  }, [restartServer, showNotification]);

  const TabButton = ({ name, icon, label, count }) => (
    <button
      onClick={() => setActiveTab(name)}
      className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
        activeTab === name
          ? 'bg-indigo-100 text-indigo-700 border border-indigo-200'
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
      }`}
    >
      {icon}
      <span>{label}</span>
      {count !== undefined && (
        <span className={`px-2 py-1 text-xs rounded-full ${
          activeTab === name ? 'bg-indigo-200 text-indigo-800' : 'bg-gray-200 text-gray-600'
        }`}>
          {count}
        </span>
      )}
    </button>
  );

  const OverviewTab = () => (
    <div className="space-y-6">
      {/* Summary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          icon={<Users size={24} />}
          title="Active Clients"
          value={connectedClients.length}
          description="Connected MCP clients"
        />
        <MetricCard
          icon={<Server size={24} />}
          title="Available Servers"
          value={availableServers.filter(s => s.enabled).length}
          description="Active MCP servers"
        />
        <MetricCard
          icon={<Activity size={24} />}
          title="Total Connections"
          value={connectionHistory.length}
          description="All time connections"
        />
        <MetricCard
          icon={<CheckCircle size={24} />}
          title="System Health"
          value={mcpStatus.host?.name ? "Healthy" : "Offline"}
          description="MCP host status"
        />
      </div>

      {/* MCP Host Status */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">MCP Host Status</h3>
        </div>
        
        {mcpStatus.host ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-400 mr-2" />
                <div>
                  <p className="text-sm font-medium text-green-800">Host Online</p>
                  <p className="text-sm text-green-600">{mcpStatus.host.name} v{mcpStatus.host.version}</p>
                </div>
              </div>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center">
                <Network className="h-5 w-5 text-blue-400 mr-2" />
                <div>
                  <p className="text-sm font-medium text-blue-800">Active Connections</p>
                  <p className="text-sm text-blue-600">{mcpStatus.stats?.active_connections || 0}</p>
                </div>
              </div>
            </div>
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="flex items-center">
                <Server className="h-5 w-5 text-purple-400 mr-2" />
                <div>
                  <p className="text-sm font-medium text-purple-800">Available Servers</p>
                  <p className="text-sm text-purple-600">{mcpStatus.stats?.available_servers || 0}</p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-400 mr-2" />
              <div>
                <p className="text-sm font-medium text-red-800">MCP Host Offline</p>
                <p className="text-sm text-red-600">Unable to connect to MCP host service</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={handleAddClient}
            className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Plus size={16} className="mr-2" />
            Add Client
          </button>
          <button
            onClick={() => handleConfigureServer()}
            className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Settings size={16} className="mr-2" />
            Configure Server
          </button>
          <button
            onClick={() => setActiveTab('logs')}
            className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Monitor size={16} className="mr-2" />
            View Logs
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <BarChart3 size={16} className="mr-2" />
            Analytics
          </button>
        </div>
      </div>
    </div>
  );

  const ClientsTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Connected MCP Clients</h3>
        <button
          onClick={handleAddClient}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <Plus size={16} className="mr-2" />
          Add Client
        </button>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {connectedClients.map((client) => (
            <li key={client.id} className="px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                      <Users className="h-5 w-5 text-indigo-600" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <div className="flex items-center">
                      <p className="text-sm font-medium text-gray-900">{client.id}</p>
                      <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Connected
                      </span>
                    </div>
                    <div className="mt-1 flex items-center text-sm text-gray-500">
                      <Clock className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" />
                      Connected {new Date(client.connected_at).toLocaleString()}
                    </div>
                    <div className="mt-1 text-sm text-gray-500">
                      User: {client.user_id} | Permissions: {client.permissions?.join(', ')}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => showNotification(`Viewing details for client ${client.id}`, 'info')}
                    className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                    title="View client details"
                  >
                    <Eye size={16} />
                  </button>
                  <button
                    onClick={() => showNotification(`Disconnecting client ${client.id}`, 'info')}
                    className="text-red-600 hover:text-red-900 text-sm font-medium"
                    title="Disconnect client"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
            </li>
          ))}
        </ul>
        {connectedClients.length === 0 && (
          <div className="text-center py-12">
            <Users className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No connected clients</h3>
            <p className="mt-1 text-sm text-gray-500">Get started by connecting your first MCP client.</p>
          </div>
        )}
      </div>
    </div>
  );

  const ServersTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">MCP Servers</h3>
        <div className="flex space-x-2">
          <select
            value={selectedServer}
            onChange={(e) => setSelectedServer(e.target.value)}
            className="block w-40 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
          >
            <option value="all">All Servers</option>
            {availableServers.map((server) => (
              <option key={server.id} value={server.id}>{server.name}</option>
            ))}
          </select>
          <button
            onClick={() => handleConfigureServer()}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Settings size={16} className="mr-2" />
            Configure
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {availableServers
          .filter(server => selectedServer === 'all' || server.id === selectedServer)
          .map((server) => (
            <div key={server.id} className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                      <Server className="h-5 w-5 text-blue-600" />
                    </div>
                  </div>
                  <div className="ml-3">
                    <h4 className="text-lg font-medium text-gray-900">{server.name}</h4>
                    <p className="text-sm text-gray-500">{server.id}</p>
                  </div>
                </div>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  server.enabled 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {server.enabled ? 'Enabled' : 'Disabled'}
                </span>
              </div>
              
              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-600">
                  <Globe className="h-4 w-4 mr-2" />
                  {server.url}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Zap className="h-4 w-4 mr-2" />
                  Capabilities: {server.capabilities?.join(', ')}
                </div>
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={() => server.enabled ? handleDisconnectFromServer(server.id) : handleConnectToServer(server.id)}
                  className="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  {server.enabled ? <Pause size={16} className="mr-1" /> : <Play size={16} className="mr-1" />}
                  {server.enabled ? 'Disconnect' : 'Connect'}
                </button>
                <button
                  onClick={() => handleConfigureServer(server)}
                  className="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <Settings size={16} className="mr-1" />
                  Configure
                </button>
              </div>
            </div>
          ))}
      </div>
    </div>
  );

  // Modal Components
  const AddClientModal = () => (
    showAddClientModal && (
      <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div className="mt-3">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Add MCP Client</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Client ID</label>
                <input
                  type="text"
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Enter client ID"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">User ID</label>
                <input
                  type="text"
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Enter user ID"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Permissions</label>
                <select className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                  <option>Read Only</option>
                  <option>Read/Write</option>
                  <option>Admin</option>
                </select>
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowAddClientModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  showNotification('Client connection feature coming soon!', 'info');
                  setShowAddClientModal(false);
                }}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-md"
              >
                Add Client
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  );

  const ConfigureServerModal = () => (
    showConfigureServerModal && (
      <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div className="mt-3">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {selectedServerForConfig ? `Configure ${selectedServerForConfig.name}` : 'Configure Server'}
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Server URL</label>
                <input
                  type="text"
                  defaultValue={selectedServerForConfig?.url || ''}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="wss://example.com/mcp"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Capabilities</label>
                <div className="mt-2 space-y-2">
                  <label className="flex items-center">
                    <input type="checkbox" defaultChecked className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" />
                    <span className="ml-2 text-sm text-gray-700">Tools</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" defaultChecked className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" />
                    <span className="ml-2 text-sm text-gray-700">Resources</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" />
                    <span className="ml-2 text-sm text-gray-700">Prompts</span>
                  </label>
                </div>
              </div>
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    defaultChecked={selectedServerForConfig?.enabled}
                    className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Enable Server</span>
                </label>
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowConfigureServerModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  showNotification('Server configuration saved!', 'success');
                  setShowConfigureServerModal(false);
                }}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-md"
              >
                Save Configuration
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">MCP Management</h2>
          <p className="text-gray-600">Manage Model Context Protocol connections and servers</p>
          {lastRefresh && (
            <p className="text-xs text-gray-500 mt-1">
              Last updated: {lastRefresh.toLocaleTimeString()}
            </p>
          )}
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={handleManualRefresh}
            disabled={refreshing}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            <RefreshCw size={16} className={`mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </button>
          <div className="flex items-center space-x-2">
            <div className={`h-3 w-3 rounded-full ${mcpStatus.host ? 'bg-green-400' : 'bg-red-400'}`}></div>
            <span className="text-sm text-gray-600">
              {mcpStatus.host ? 'MCP Host Online' : 'MCP Host Offline'}
            </span>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-2 border-b border-gray-200">
        <TabButton name="overview" icon={<Monitor size={20} />} label="Overview" />
        <TabButton name="clients" icon={<Users size={20} />} label="Clients" count={connectedClients.length} />
        <TabButton name="servers" icon={<Server size={20} />} label="Servers" count={availableServers.length} />
        <TabButton name="analytics" icon={<BarChart3 size={20} />} label="Analytics" />
        <TabButton name="settings" icon={<Settings size={20} />} label="Settings" />
      </div>

      {loading && !lastRefresh ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
          <span className="ml-3 text-gray-600">Loading MCP data...</span>
        </div>
      ) : (
        <div>
          {activeTab === 'overview' && <OverviewTab />}
          {activeTab === 'clients' && <ClientsTab />}
          {activeTab === 'servers' && <ServersTab />}
          {activeTab === 'analytics' && (
            <div className="text-center py-12">
              <BarChart3 className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Analytics Coming Soon</h3>
              <p className="mt-1 text-sm text-gray-500">MCP analytics and performance metrics will be available here.</p>
            </div>
          )}
          {activeTab === 'settings' && (
            <div className="text-center py-12">
              <Settings className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Settings Coming Soon</h3>
              <p className="mt-1 text-sm text-gray-500">MCP configuration settings will be available here.</p>
            </div>
          )}
        </div>
      )}

      {/* Modals */}
      <AddClientModal />
      <ConfigureServerModal />
    </div>
  );
};

export default MCPDashboard;
