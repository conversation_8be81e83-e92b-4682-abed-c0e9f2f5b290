import React, { useState } from 'react';
import {
  Users,
  Plus,
  Edit,
  Trash2,
  Activity,
  AlertCircle,
  CheckCircle,
  Clock,
  Zap,
  Settings,
  Eye,
  RefreshCw,
  ShoppingCart
} from 'lucide-react';
import AgentMarketplace from './AgentMarketplace';

const AgentRegistryView = ({ agents, onRefresh, showNotification, setIsFormOpen }) => {
  const [showRegisterForm, setShowRegisterForm] = useState(false);
  const [showMarketplace, setShowMarketplace] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  const agentTypes = [
    'data_analyst',
    'content_writer', 
    'code_generator',
    'researcher',
    'validator',
    'coordinator',
    'specialist',
    'generalist'
  ];

  const agentStatuses = ['idle', 'busy', 'offline', 'maintenance', 'error'];

  const filteredAgents = agents.filter(agent => {
    const typeMatch = filterType === 'all' || agent.type === filterType;
    const statusMatch = filterStatus === 'all' || agent.status === filterStatus;
    return typeMatch && statusMatch;
  });

  const handleRegisterAgent = async (agentData) => {
    try {
      const response = await fetch('/api/multi-agent/v1/agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(agentData),
      });

      if (response.ok) {
        showNotification('Agent registered successfully', 'success');
        setShowRegisterForm(false);
        onRefresh();
      } else {
        const error = await response.text();
        showNotification(`Failed to register agent: ${error}`, 'error');
      }
    } catch (error) {
      showNotification(`Error registering agent: ${error.message}`, 'error');
    }
  };

  const handleUpdateAgentStatus = async (agentId, newStatus) => {
    try {
      const response = await fetch(`/api/multi-agent/v1/agents/${agentId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        showNotification('Agent status updated', 'success');
        onRefresh();
      } else {
        showNotification('Failed to update agent status', 'error');
      }
    } catch (error) {
      showNotification(`Error updating agent: ${error.message}`, 'error');
    }
  };

  const handleHealthCheck = async (agentId) => {
    try {
      const response = await fetch(`/api/multi-agent/v1/agents/${agentId}/health`, {
        method: 'POST',
      });

      if (response.ok) {
        showNotification('Health check initiated', 'success');
        setTimeout(onRefresh, 2000); // Refresh after health check
      } else {
        showNotification('Failed to perform health check', 'error');
      }
    } catch (error) {
      showNotification(`Error performing health check: ${error.message}`, 'error');
    }
  };

  const handleUnregisterAgent = async (agentId) => {
    if (!confirm('Are you sure you want to unregister this agent?')) {
      return;
    }

    try {
      const response = await fetch(`/api/multi-agent/v1/agents/${agentId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        showNotification('Agent unregistered successfully', 'success');
        onRefresh();
      } else {
        showNotification('Failed to unregister agent', 'error');
      }
    } catch (error) {
      showNotification(`Error unregistering agent: ${error.message}`, 'error');
    }
  };

  const AgentStatusBadge = ({ status }) => {
    const colors = {
      idle: 'bg-green-100 text-green-800',
      busy: 'bg-yellow-100 text-yellow-800',
      offline: 'bg-red-100 text-red-800',
      maintenance: 'bg-blue-100 text-blue-800',
      error: 'bg-red-100 text-red-800'
    };

    const icons = {
      idle: <CheckCircle size={12} />,
      busy: <Activity size={12} />,
      offline: <AlertCircle size={12} />,
      maintenance: <Settings size={12} />,
      error: <AlertCircle size={12} />
    };

    return (
      <span className={`inline-flex items-center space-x-1 px-2 py-1 text-xs font-medium rounded-full ${colors[status] || 'bg-gray-100 text-gray-800'}`}>
        {icons[status]}
        <span>{status}</span>
      </span>
    );
  };

  const AgentTypeIcon = ({ type }) => {
    const icons = {
      data_analyst: <Activity size={16} />,
      content_writer: <Edit size={16} />,
      code_generator: <Zap size={16} />,
      researcher: <Eye size={16} />,
      validator: <CheckCircle size={16} />,
      coordinator: <Users size={16} />,
      specialist: <Settings size={16} />,
      generalist: <Users size={16} />
    };

    return icons[type] || <Users size={16} />;
  };

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-xl font-semibold text-gray-900">Agent Registry</h3>
          <p className="text-gray-600">Manage and monitor AI agents</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowMarketplace(true)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
          >
            <ShoppingCart size={16} className="mr-2" />
            Marketplace
          </button>
          <button
            onClick={() => {
              setShowRegisterForm(true);
              setIsFormOpen(true);
            }}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
          >
            <Plus size={16} className="mr-2" />
            Register Agent
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex space-x-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="all">All Types</option>
            {agentTypes.map(type => (
              <option key={type} value={type}>{type.replace('_', ' ')}</option>
            ))}
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="all">All Statuses</option>
            {agentStatuses.map(status => (
              <option key={status} value={status}>{status}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Agents Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAgents.map((agent) => (
          <div key={agent.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <AgentTypeIcon type={agent.type} />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">{agent.name}</h4>
                  <p className="text-sm text-gray-500">{agent.type.replace('_', ' ')}</p>
                </div>
              </div>
              <AgentStatusBadge status={agent.status} />
            </div>

            <p className="text-sm text-gray-600 mb-4">{agent.description}</p>

            {/* Capabilities */}
            <div className="mb-4">
              <p className="text-xs font-medium text-gray-700 mb-2">Capabilities</p>
              <div className="flex flex-wrap gap-1">
                {(agent.capabilities || []).slice(0, 3).map((capability, index) => (
                  <span key={index} className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                    {capability.name || capability}
                  </span>
                ))}
                {(agent.capabilities || []).length > 3 && (
                  <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                    +{(agent.capabilities || []).length - 3} more
                  </span>
                )}
              </div>
            </div>

            {/* Performance Metrics */}
            {agent.performance && (
              <div className="mb-4 grid grid-cols-2 gap-2 text-xs">
                <div>
                  <span className="text-gray-500">Success Rate</span>
                  <p className="font-medium">{(agent.performance.success_rate * 100).toFixed(1)}%</p>
                </div>
                <div>
                  <span className="text-gray-500">Tasks</span>
                  <p className="font-medium">{agent.performance.tasks_completed || 0}</p>
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex space-x-2">
              <button
                onClick={() => handleHealthCheck(agent.id)}
                className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <RefreshCw size={14} className="mr-1" />
                Health Check
              </button>
              <button
                onClick={() => setSelectedAgent(agent)}
                className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <Eye size={14} />
              </button>
              <button
                onClick={() => handleUnregisterAgent(agent.id)}
                className="px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
              >
                <Trash2 size={14} />
              </button>
            </div>
          </div>
        ))}
      </div>

      {filteredAgents.length === 0 && (
        <div className="text-center py-12">
          <Users size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No agents found</h3>
          <p className="text-gray-500 mb-4">
            {agents.length === 0 
              ? "Get started by registering your first agent"
              : "Try adjusting your filters"
            }
          </p>
          {agents.length === 0 && (
            <button
              onClick={() => setShowRegisterForm(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
            >
              <Plus size={16} className="mr-2" />
              Register First Agent
            </button>
          )}
        </div>
      )}

      {/* Register Agent Modal */}
      {showRegisterForm && (
        <AgentRegistrationForm
          onSubmit={handleRegisterAgent}
          onCancel={() => {
            setShowRegisterForm(false);
            setIsFormOpen(false);
          }}
        />
      )}

      {/* Agent Details Modal */}
      {selectedAgent && (
        <AgentDetailsModal
          agent={selectedAgent}
          onClose={() => setSelectedAgent(null)}
          onUpdateStatus={handleUpdateAgentStatus}
          onHealthCheck={handleHealthCheck}
        />
      )}

      {/* Agent Marketplace Modal */}
      {showMarketplace && (
        <AgentMarketplace
          showNotification={showNotification}
          onClose={() => setShowMarketplace(false)}
        />
      )}
    </div>
  );
};

// Agent Registration Form Component
const AgentRegistrationForm = ({ onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    type: 'generalist',
    description: '',
    endpoint: '',
    capabilities: []
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Register New Agent</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
            <select
              value={formData.type}
              onChange={(e) => setFormData({...formData, type: e.target.value})}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            >
              <option value="data_analyst">Data Analyst</option>
              <option value="content_writer">Content Writer</option>
              <option value="code_generator">Code Generator</option>
              <option value="researcher">Researcher</option>
              <option value="validator">Validator</option>
              <option value="coordinator">Coordinator</option>
              <option value="specialist">Specialist</option>
              <option value="generalist">Generalist</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
              rows="3"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Endpoint URL</label>
            <input
              type="url"
              value={formData.endpoint}
              onChange={(e) => setFormData({...formData, endpoint: e.target.value})}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
              placeholder="https://agent-endpoint.com"
              required
            />
          </div>
          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              className="flex-1 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
            >
              Register Agent
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Agent Details Modal Component
const AgentDetailsModal = ({ agent, onClose, onUpdateStatus, onHealthCheck }) => {
  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-lg font-semibold text-gray-900">{agent.name}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>
        
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Basic Information</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Type:</span>
                <p>{agent.type.replace('_', ' ')}</p>
              </div>
              <div>
                <span className="text-gray-500">Status:</span>
                <p>{agent.status}</p>
              </div>
              <div>
                <span className="text-gray-500">Endpoint:</span>
                <p className="break-all">{agent.endpoint}</p>
              </div>
              <div>
                <span className="text-gray-500">Last Seen:</span>
                <p>{new Date(agent.last_seen).toLocaleString()}</p>
              </div>
            </div>
          </div>

          {agent.capabilities && agent.capabilities.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Capabilities</h4>
              <div className="grid grid-cols-1 gap-2">
                {agent.capabilities.map((capability, index) => (
                  <div key={index} className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium">{capability.name || capability}</p>
                        {capability.description && (
                          <p className="text-sm text-gray-600">{capability.description}</p>
                        )}
                      </div>
                      {capability.quality && (
                        <span className="text-sm text-gray-500">
                          Quality: {(capability.quality * 100).toFixed(0)}%
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {agent.performance && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Performance Metrics</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Tasks Completed:</span>
                  <p>{agent.performance.tasks_completed || 0}</p>
                </div>
                <div>
                  <span className="text-gray-500">Tasks Failed:</span>
                  <p>{agent.performance.tasks_failed || 0}</p>
                </div>
                <div>
                  <span className="text-gray-500">Success Rate:</span>
                  <p>{((agent.performance.success_rate || 0) * 100).toFixed(1)}%</p>
                </div>
                <div>
                  <span className="text-gray-500">Average Latency:</span>
                  <p>{(agent.performance.average_latency || 0).toFixed(0)}ms</p>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex space-x-3 pt-6 border-t">
          <button
            onClick={() => onHealthCheck(agent.id)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Health Check
          </button>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default AgentRegistryView;
