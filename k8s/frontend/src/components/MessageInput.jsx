import React, { useState, useRef, useEffect } from 'react';
import { Send, Square, Settings } from 'lucide-react';

const MessageInput = ({ 
  onSendMessage, 
  onCancel, 
  isLoading, 
  isStreaming,
  disabled = false,
  placeholder = "Type your message...",
  showSettings = false,
  onSettingsClick,
  currentModel = 'gpt-3.5-turbo'
}) => {
  const [message, setMessage] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  const textareaRef = useRef(null);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      const maxHeight = 200; // Max height in pixels
      textarea.style.height = Math.min(scrollHeight, maxHeight) + 'px';
      setIsExpanded(scrollHeight > 60); // Expand when more than 2-3 lines
    }
  }, [message]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (message.trim() && !isLoading && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <div className="border-t border-gray-200 bg-white p-4">
      {/* Model indicator */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <span>Model:</span>
          <span className="font-medium text-indigo-600">{currentModel}</span>
        </div>
        
        {showSettings && (
          <button
            onClick={onSettingsClick}
            className="flex items-center space-x-1 text-sm text-gray-500 hover:text-gray-700 transition-colors"
            title="Chat settings"
          >
            <Settings size={16} />
            <span>Settings</span>
          </button>
        )}
      </div>

      {/* Input form */}
      <form onSubmit={handleSubmit} className="relative">
        <div className={`relative border border-gray-300 rounded-lg overflow-hidden transition-all duration-200 ${
          isExpanded ? 'rounded-lg' : 'rounded-full'
        } ${disabled ? 'opacity-50' : ''}`}>
          <textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={disabled ? 'Chat is disabled' : placeholder}
            disabled={disabled || isLoading}
            className={`w-full px-4 py-3 pr-20 resize-none border-none outline-none focus:ring-0 ${
              isExpanded ? 'rounded-lg' : 'rounded-full'
            } ${disabled ? 'cursor-not-allowed' : ''}`}
            rows={1}
            style={{ minHeight: '48px' }}
          />

          {/* Action buttons */}
          <div className="absolute right-2 bottom-2 flex items-center space-x-1">
            {isStreaming && (
              <button
                type="button"
                onClick={handleCancel}
                className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-full transition-colors"
                title="Stop generation"
              >
                <Square size={16} />
              </button>
            )}

            <button
              type="submit"
              disabled={!message.trim() || isLoading || disabled}
              className={`p-2 rounded-full transition-colors ${
                message.trim() && !isLoading && !disabled
                  ? 'text-white bg-indigo-600 hover:bg-indigo-700'
                  : 'text-gray-400 bg-gray-100 cursor-not-allowed'
              }`}
              title="Send message"
            >
              <Send size={16} />
            </button>
          </div>
        </div>

        {/* Loading indicator */}
        {isLoading && !isStreaming && (
          <div className="flex items-center justify-center mt-2">
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
              <span>Thinking...</span>
            </div>
          </div>
        )}

        {/* Streaming indicator */}
        {isStreaming && (
          <div className="flex items-center justify-center mt-2">
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-indigo-600 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-indigo-600 rounded-full animate-pulse" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-indigo-600 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span>Generating response...</span>
            </div>
          </div>
        )}

        {/* Hint text */}
        {!isLoading && !isStreaming && (
          <div className="mt-2 text-xs text-gray-400 text-center">
            Press Enter to send, Shift+Enter for new line
          </div>
        )}
      </form>
    </div>
  );
};

export default MessageInput;
