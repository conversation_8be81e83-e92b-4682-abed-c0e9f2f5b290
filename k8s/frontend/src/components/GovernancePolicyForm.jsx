import React, { useState, useEffect } from 'react';
import { X, Plus, Trash2, Save, AlertCircle } from 'lucide-react';

const GovernancePolicyForm = ({ 
  policy = null, 
  isOpen, 
  onClose, 
  onSave, 
  loading = false 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'access_control',
    status: 'active',
    priority: 1,
    rules: [
      {
        id: '',
        condition: '',
        action: 'allow',
        parameters: {},
        description: ''
      }
    ],
    scope: {
      models: [],
      users: [],
      services: [],
      operations: [],
      data_types: []
    },
    enforcement: {
      mode: 'strict',
      auto_remediate: false,
      notify_users: [],
      escalation_path: []
    },
    metadata: {}
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (policy) {
      setFormData({
        ...policy,
        rules: policy.rules || [
          {
            id: '',
            condition: '',
            action: 'allow',
            parameters: {},
            description: ''
          }
        ]
      });
    } else {
      // Reset form for new policy
      setFormData({
        name: '',
        description: '',
        type: 'access_control',
        status: 'active',
        priority: 1,
        rules: [
          {
            id: '',
            condition: '',
            action: 'allow',
            parameters: {},
            description: ''
          }
        ],
        scope: {
          models: [],
          users: [],
          services: [],
          operations: [],
          data_types: []
        },
        enforcement: {
          mode: 'strict',
          auto_remediate: false,
          notify_users: [],
          escalation_path: []
        },
        metadata: {}
      });
    }
    setErrors({});
  }, [policy, isOpen]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Policy name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Policy description is required';
    }

    if (formData.rules.length === 0) {
      newErrors.rules = 'At least one rule is required';
    }

    formData.rules.forEach((rule, index) => {
      if (!rule.condition.trim()) {
        newErrors[`rule_${index}_condition`] = 'Rule condition is required';
      }
      if (!rule.action.trim()) {
        newErrors[`rule_${index}_action`] = 'Rule action is required';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('Failed to save policy:', error);
    }
  };

  const addRule = () => {
    setFormData(prev => ({
      ...prev,
      rules: [
        ...prev.rules,
        {
          id: '',
          condition: '',
          action: 'allow',
          parameters: {},
          description: ''
        }
      ]
    }));
  };

  const removeRule = (index) => {
    setFormData(prev => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index)
    }));
  };

  const updateRule = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      rules: prev.rules.map((rule, i) => 
        i === index ? { ...rule, [field]: value } : rule
      )
    }));
  };

  const updateScope = (field, value) => {
    setFormData(prev => ({
      ...prev,
      scope: {
        ...prev.scope,
        [field]: Array.isArray(value) ? value : value.split(',').map(s => s.trim()).filter(s => s)
      }
    }));
  };

  const updateEnforcement = (field, value) => {
    setFormData(prev => ({
      ...prev,
      enforcement: {
        ...prev.enforcement,
        [field]: value
      }
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto m-4">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {policy ? 'Edit Policy' : 'Create New Policy'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Policy Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                  errors.name ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter policy name"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.name}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Policy Type
              </label>
              <select
                value={formData.type}
                onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="access_control">Access Control</option>
                <option value="data_usage">Data Usage</option>
                <option value="model_deployment">Model Deployment</option>
                <option value="compliance">Compliance</option>
                <option value="security">Security</option>
                <option value="privacy">Privacy</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="draft">Draft</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priority
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={formData.priority}
                onChange={(e) => setFormData(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                errors.description ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Describe what this policy does"
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.description}
              </p>
            )}
          </div>

          {/* Rules Section */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Policy Rules</h3>
              <button
                type="button"
                onClick={addRule}
                className="flex items-center space-x-2 px-3 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
              >
                <Plus className="h-4 w-4" />
                <span>Add Rule</span>
              </button>
            </div>

            {formData.rules.map((rule, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 mb-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-900">Rule {index + 1}</h4>
                  {formData.rules.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeRule(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Condition *
                    </label>
                    <input
                      type="text"
                      value={rule.condition}
                      onChange={(e) => updateRule(index, 'condition', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                        errors[`rule_${index}_condition`] ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="e.g., model_type == 'llm' AND data_sensitivity == 'high'"
                    />
                    {errors[`rule_${index}_condition`] && (
                      <p className="mt-1 text-sm text-red-600">{errors[`rule_${index}_condition`]}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Action *
                    </label>
                    <select
                      value={rule.action}
                      onChange={(e) => updateRule(index, 'action', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    >
                      <option value="allow">Allow</option>
                      <option value="deny">Deny</option>
                      <option value="require_approval">Require Approval</option>
                      <option value="log">Log Only</option>
                      <option value="alert">Generate Alert</option>
                    </select>
                  </div>
                </div>

                <div className="mt-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <input
                    type="text"
                    value={rule.description}
                    onChange={(e) => updateRule(index, 'description', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Describe what this rule does"
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50"
            >
              <Save className="h-4 w-4" />
              <span>{loading ? 'Saving...' : 'Save Policy'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default GovernancePolicyForm;
