import React, { useState, useEffect } from 'react';
import {
  TrendingUp, Users, Heart, MessageSquare, BarChart3, Target, Zap,
  ThumbsUp, ThumbsDown, Star, Eye, RefreshCw, Download, Filter,
  ArrowUp, ArrowDown, Minus, AlertCircle, CheckCircle, Clock
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

const ProductMarketFitDashboard = ({ showNotification, onFormStateChange }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [pmfScore, setPmfScore] = useState(null);
  const [userBehavior, setUserBehavior] = useState(null);
  const [marketIntelligence, setMarketIntelligence] = useState(null);
  const [customerFeedback, setCustomerFeedback] = useState([]);
  const [competitorAnalysis, setCompetitorAnalysis] = useState(null);
  const [featureAdoption, setFeatureAdoption] = useState([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');

  useEffect(() => {
    fetchPMFData();
  }, [timeRange]);

  const fetchPMFData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        fetchPMFScore(),
        fetchUserBehavior(),
        fetchMarketIntelligence(),
        fetchCustomerFeedback(),
        fetchCompetitorAnalysis(),
        fetchFeatureAdoption()
      ]);
    } catch (error) {
      showNotification('Failed to fetch PMF data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const fetchPMFScore = async () => {
    try {
      const response = await fetch(`/api/analytics/pmf-score?timeRange=${timeRange}`);
      const data = await response.json();
      setPmfScore(data);
    } catch (error) {
      console.error('Failed to fetch PMF score:', error);
      setPmfScore(getMockPMFScore());
    }
  };

  const fetchUserBehavior = async () => {
    try {
      const response = await fetch(`/api/analytics/user-behavior?timeRange=${timeRange}`);
      const data = await response.json();
      setUserBehavior(data);
    } catch (error) {
      console.error('Failed to fetch user behavior:', error);
      setUserBehavior(getMockUserBehavior());
    }
  };

  const fetchMarketIntelligence = async () => {
    try {
      const response = await fetch(`/api/analytics/market-intelligence?timeRange=${timeRange}`);
      const data = await response.json();
      setMarketIntelligence(data);
    } catch (error) {
      console.error('Failed to fetch market intelligence:', error);
      setMarketIntelligence(getMockMarketIntelligence());
    }
  };

  const fetchCustomerFeedback = async () => {
    try {
      const response = await fetch(`/api/analytics/customer-feedback?timeRange=${timeRange}`);
      const data = await response.json();
      setCustomerFeedback(data.feedback || []);
    } catch (error) {
      console.error('Failed to fetch customer feedback:', error);
      setCustomerFeedback(getMockCustomerFeedback());
    }
  };

  const fetchCompetitorAnalysis = async () => {
    try {
      const response = await fetch(`/api/analytics/competitor-analysis?timeRange=${timeRange}`);
      const data = await response.json();
      setCompetitorAnalysis(data);
    } catch (error) {
      console.error('Failed to fetch competitor analysis:', error);
      setCompetitorAnalysis(getMockCompetitorAnalysis());
    }
  };

  const fetchFeatureAdoption = async () => {
    try {
      const response = await fetch(`/api/analytics/feature-adoption?timeRange=${timeRange}`);
      const data = await response.json();
      setFeatureAdoption(data.features || []);
    } catch (error) {
      console.error('Failed to fetch feature adoption:', error);
      setFeatureAdoption(getMockFeatureAdoption());
    }
  };

  const getPMFScoreColor = (score) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getTrendIcon = (trend) => {
    if (trend > 0) return <ArrowUp size={16} className="text-green-600" />;
    if (trend < 0) return <ArrowDown size={16} className="text-red-600" />;
    return <Minus size={16} className="text-gray-600" />;
  };

  const getHealthStatus = (score) => {
    if (score >= 80) return { icon: <CheckCircle size={16} className="text-green-600" />, text: 'Excellent', color: 'text-green-600' };
    if (score >= 60) return { icon: <Clock size={16} className="text-yellow-600" />, text: 'Good', color: 'text-yellow-600' };
    return { icon: <AlertCircle size={16} className="text-red-600" />, text: 'Needs Attention', color: 'text-red-600' };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Product-Market Fit Analytics</h2>
          <p className="text-gray-600">Comprehensive analytics to measure and optimize product-market fit</p>
        </div>
        <div className="flex space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
            <option value="1y">Last Year</option>
          </select>
          <button
            onClick={fetchPMFData}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <RefreshCw size={16} className="mr-2" />
            Refresh
          </button>
          <button
            onClick={() => showNotification('Generating PMF report...', 'info')}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <Download size={16} className="mr-2" />
            Export Report
          </button>
        </div>
      </div>

      {/* PMF Score Card */}
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Product-Market Fit Score</h3>
            <p className="text-sm text-gray-600">Composite score based on user behavior, sentiment, and market data</p>
          </div>
          <div className="text-right">
            <div className={`text-4xl font-bold ${getPMFScoreColor(pmfScore?.current_score || 75)}`}>
              {pmfScore?.current_score || 75}
            </div>
            <div className="flex items-center justify-end space-x-1 mt-1">
              {getTrendIcon(pmfScore?.trend || 5)}
              <span className="text-sm text-gray-500">
                {Math.abs(pmfScore?.trend || 5)}% vs last period
              </span>
            </div>
          </div>
        </div>
        
        {/* PMF Components */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
          {pmfScore?.components?.map((component, index) => (
            <div key={index} className="text-center">
              <div className={`text-2xl font-bold ${getPMFScoreColor(component.score)}`}>
                {component.score}
              </div>
              <div className="text-sm text-gray-600">{component.name}</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className={`h-2 rounded-full ${
                    component.score >= 80 ? 'bg-green-500' :
                    component.score >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${component.score}%` }}
                ></div>
              </div>
            </div>
          )) || []}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users size={24} className="text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Users</p>
              <p className="text-2xl font-bold text-gray-900">
                {userBehavior?.active_users?.toLocaleString() || '2,450'}
              </p>
              <div className="flex items-center space-x-1 mt-1">
                {getTrendIcon(userBehavior?.user_growth || 12)}
                <span className="text-xs text-gray-500">
                  {Math.abs(userBehavior?.user_growth || 12)}% growth
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Heart size={24} className="text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Customer Satisfaction</p>
              <p className="text-2xl font-bold text-gray-900">
                {userBehavior?.satisfaction_score || 4.2}/5
              </p>
              <div className="flex items-center space-x-1 mt-1">
                {getTrendIcon(0.3)}
                <span className="text-xs text-gray-500">0.3 improvement</span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Target size={24} className="text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Feature Adoption</p>
              <p className="text-2xl font-bold text-gray-900">
                {userBehavior?.feature_adoption || 68}%
              </p>
              <div className="flex items-center space-x-1 mt-1">
                {getTrendIcon(8)}
                <span className="text-xs text-gray-500">8% increase</span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <TrendingUp size={24} className="text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Market Share</p>
              <p className="text-2xl font-bold text-gray-900">
                {marketIntelligence?.market_share || 15}%
              </p>
              <div className="flex items-center space-x-1 mt-1">
                {getTrendIcon(3)}
                <span className="text-xs text-gray-500">3% growth</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Overview', icon: BarChart3 },
            { id: 'behavior', name: 'User Behavior', icon: Users },
            { id: 'feedback', name: 'Customer Feedback', icon: MessageSquare },
            { id: 'market', name: 'Market Intelligence', icon: TrendingUp },
            { id: 'features', name: 'Feature Analysis', icon: Zap }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon size={16} className="mr-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <PMFOverview 
          pmfScore={pmfScore}
          userBehavior={userBehavior}
          marketIntelligence={marketIntelligence}
          getHealthStatus={getHealthStatus}
        />
      )}

      {activeTab === 'behavior' && (
        <UserBehaviorAnalysis 
          userBehavior={userBehavior}
          timeRange={timeRange}
        />
      )}

      {activeTab === 'feedback' && (
        <CustomerFeedbackAnalysis 
          feedback={customerFeedback}
          showNotification={showNotification}
        />
      )}

      {activeTab === 'market' && (
        <MarketIntelligence 
          marketData={marketIntelligence}
          competitorData={competitorAnalysis}
        />
      )}

      {activeTab === 'features' && (
        <FeatureAnalysis 
          features={featureAdoption}
          userBehavior={userBehavior}
        />
      )}
    </div>
  );
};

// Mock data functions
const getMockPMFScore = () => ({
  current_score: 75,
  trend: 5,
  components: [
    { name: 'User Retention', score: 82 },
    { name: 'Feature Adoption', score: 68 },
    { name: 'Customer Satisfaction', score: 84 },
    { name: 'Market Position', score: 65 }
  ]
});

const getMockUserBehavior = () => ({
  active_users: 2450,
  user_growth: 12,
  satisfaction_score: 4.2,
  feature_adoption: 68,
  retention_rate: 82,
  engagement_score: 7.8
});

const getMockMarketIntelligence = () => ({
  market_share: 15,
  growth_rate: 3,
  competitive_position: 'strong',
  market_size: '$2.5B',
  opportunity_score: 78
});

const getMockCustomerFeedback = () => [
  {
    id: 'feedback_1',
    text: 'AI Operations Hub has transformed our workflow efficiency',
    sentiment: 'positive',
    score: 4.5,
    source: 'product_review',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    id: 'feedback_2',
    text: 'The no-code features are game-changing for our team',
    sentiment: 'positive',
    score: 5.0,
    source: 'customer_survey',
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000)
  }
];

const getMockCompetitorAnalysis = () => ({
  competitors: [
    { name: 'Competitor A', market_share: 25, strength: 'high' },
    { name: 'Competitor B', market_share: 20, strength: 'medium' },
    { name: 'Competitor C', market_share: 18, strength: 'medium' }
  ],
  competitive_advantages: [
    'Superior AI routing capabilities',
    'Comprehensive no-code platform',
    'Advanced sentiment analysis'
  ]
});

const getMockFeatureAdoption = () => [
  { name: 'AI Chat', adoption_rate: 95, satisfaction: 4.6, usage_trend: 8 },
  { name: 'Sentiment Analysis', adoption_rate: 78, satisfaction: 4.4, usage_trend: 15 },
  { name: 'No-Code Builder', adoption_rate: 65, satisfaction: 4.2, usage_trend: 22 },
  { name: 'Multi-Agent Workflows', adoption_rate: 52, satisfaction: 4.0, usage_trend: 18 }
];

export default ProductMarketFitDashboard;
