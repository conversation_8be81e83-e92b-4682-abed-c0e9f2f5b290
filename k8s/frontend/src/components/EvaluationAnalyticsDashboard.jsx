import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>hart, Line, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, 
  ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> 
} from 'recharts';
import { 
  TrendingUp, TrendingDown, AlertTriangle, CheckCircle, 
  Brain, Target, Activity, Zap, Eye, Settings, RefreshCw,
  ThumbsUp, ThumbsDown, Clock, BarChart3
} from 'lucide-react';

const EvaluationAnalyticsDashboard = () => {
  const [analytics, setAnalytics] = useState([]);
  const [trends, setTrends] = useState([]);
  const [alerts, setAlerts] = useState([]);
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedModel, setSelectedModel] = useState('all');
  const [timeRange, setTimeRange] = useState('7d');

  useEffect(() => {
    fetchAnalyticsData();
    const interval = setInterval(fetchAnalyticsData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [selectedModel, timeRange]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      
      // Fetch analytics data
      const analyticsResponse = await fetch('/api/evaluation/analytics/models');
      const analyticsData = await analyticsResponse.json();
      setAnalytics(analyticsData);

      // Fetch trend data
      const trendsResponse = await fetch('/api/evaluation/analytics/trends');
      const trendsData = await trendsResponse.json();
      setTrends(trendsData);

      // Fetch alerts
      const alertsResponse = await fetch('/api/evaluation/analytics/alerts');
      const alertsData = await alertsResponse.json();
      setAlerts(alertsData);

      // Fetch recommendations
      const recommendationsResponse = await fetch('/api/evaluation/analytics/recommendations');
      const recommendationsData = await recommendationsResponse.json();
      setRecommendations(recommendationsData);

    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const triggerOptimization = async () => {
    try {
      await fetch('/api/evaluation/actions/trigger-optimization', { method: 'POST' });
      // Refresh data after triggering optimization
      setTimeout(fetchAnalyticsData, 2000);
    } catch (error) {
      console.error('Error triggering optimization:', error);
    }
  };

  const acknowledgeAlert = async (alertId) => {
    try {
      await fetch(`/api/evaluation/actions/acknowledge-alert/${alertId}`, { method: 'POST' });
      fetchAnalyticsData();
    } catch (error) {
      console.error('Error acknowledging alert:', error);
    }
  };

  const applyRecommendation = async (recId) => {
    try {
      await fetch(`/api/evaluation/actions/apply-recommendation/${recId}`, { method: 'POST' });
      fetchAnalyticsData();
    } catch (error) {
      console.error('Error applying recommendation:', error);
    }
  };

  // Calculate summary metrics
  const summaryMetrics = analytics.reduce((acc, model) => {
    acc.totalEvaluations += model.total_evaluations;
    acc.avgScore += model.average_score * model.total_evaluations;
    acc.avgPassRate += model.pass_rate * model.total_evaluations;
    return acc;
  }, { totalEvaluations: 0, avgScore: 0, avgPassRate: 0 });

  if (summaryMetrics.totalEvaluations > 0) {
    summaryMetrics.avgScore /= summaryMetrics.totalEvaluations;
    summaryMetrics.avgPassRate /= summaryMetrics.totalEvaluations;
  }

  const MetricCard = ({ icon, title, value, description, color = "blue", trend, onClick }) => (
    <div 
      className={`bg-white rounded-lg shadow-md p-6 border-l-4 border-${color}-500 cursor-pointer hover:shadow-lg transition-shadow`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center space-x-2">
            <div className={`text-${color}-600`}>{icon}</div>
            <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-2">{value}</p>
          <p className="text-sm text-gray-600 mt-1">{description}</p>
        </div>
        {trend && (
          <div className={`flex items-center ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
            {trend > 0 ? <TrendingUp size={20} /> : <TrendingDown size={20} />}
            <span className="ml-1 text-sm font-medium">{Math.abs(trend)}%</span>
          </div>
        )}
      </div>
    </div>
  );

  const AlertCard = ({ alert }) => (
    <div className={`bg-white rounded-lg shadow-md p-4 border-l-4 ${
      alert.severity === 'critical' ? 'border-red-500' : 
      alert.severity === 'high' ? 'border-orange-500' : 
      alert.severity === 'medium' ? 'border-yellow-500' : 'border-blue-500'
    }`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <AlertTriangle className={`mt-1 ${
            alert.severity === 'critical' ? 'text-red-600' : 
            alert.severity === 'high' ? 'text-orange-600' : 
            alert.severity === 'medium' ? 'text-yellow-600' : 'text-blue-600'
          }`} size={20} />
          <div>
            <h4 className="font-medium text-gray-900">{alert.alert_type.replace('_', ' ').toUpperCase()}</h4>
            <p className="text-sm text-gray-600 mt-1">{alert.message}</p>
            <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
              <span>Model: {alert.model_id}</span>
              <span>Task: {alert.task_type}</span>
              <span>Severity: {alert.severity}</span>
            </div>
          </div>
        </div>
        <button
          onClick={() => acknowledgeAlert(alert.id)}
          className="px-3 py-1 bg-blue-100 text-blue-700 rounded text-sm hover:bg-blue-200"
        >
          Acknowledge
        </button>
      </div>
    </div>
  );

  const RecommendationCard = ({ recommendation }) => (
    <div className="bg-white rounded-lg shadow-md p-4 border-l-4 border-green-500">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <Target className="text-green-600 mt-1" size={20} />
          <div>
            <h4 className="font-medium text-gray-900">{recommendation.type.replace('_', ' ').toUpperCase()}</h4>
            <p className="text-sm text-gray-600 mt-1">{recommendation.description}</p>
            <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
              <span>Priority: {recommendation.priority}</span>
              <span>Expected Gain: {(recommendation.expected_gain * 100).toFixed(1)}%</span>
              <span>Confidence: {(recommendation.confidence * 100).toFixed(1)}%</span>
            </div>
          </div>
        </div>
        <button
          onClick={() => applyRecommendation(recommendation.id)}
          className="px-3 py-1 bg-green-100 text-green-700 rounded text-sm hover:bg-green-200"
        >
          Apply
        </button>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Evaluation Analytics</h1>
          <p className="text-gray-600 mt-1">Monitor model performance and get actionable insights</p>
        </div>
        <div className="flex items-center space-x-4">
          <select
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="all">All Models</option>
            {analytics.map(model => (
              <option key={model.model_id} value={model.model_id}>{model.model_id}</option>
            ))}
          </select>
          <button
            onClick={triggerOptimization}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2"
          >
            <RefreshCw size={16} />
            <span>Trigger Optimization</span>
          </button>
        </div>
      </div>

      {/* Summary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          icon={<BarChart3 size={24} />}
          title="Total Evaluations"
          value={summaryMetrics.totalEvaluations.toLocaleString()}
          description="Across all models"
          color="blue"
        />
        <MetricCard
          icon={<TrendingUp size={24} />}
          title="Average Score"
          value={`${(summaryMetrics.avgScore * 100).toFixed(1)}%`}
          description="Overall performance"
          color="green"
        />
        <MetricCard
          icon={<CheckCircle size={24} />}
          title="Pass Rate"
          value={`${(summaryMetrics.avgPassRate * 100).toFixed(1)}%`}
          description="Evaluations passed"
          color="purple"
        />
        <MetricCard
          icon={<AlertTriangle size={24} />}
          title="Active Alerts"
          value={alerts.length}
          description="Requiring attention"
          color="red"
        />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Model Performance Chart */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Model Performance</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={analytics}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="model_id" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="average_score" fill="#3B82F6" name="Average Score" />
              <Bar dataKey="pass_rate" fill="#10B981" name="Pass Rate" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Trend Analysis */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Trends</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={trends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="avg_score" stroke="#3B82F6" name="Average Score" />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Alerts and Recommendations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Active Alerts */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Active Alerts</h3>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {alerts.length === 0 ? (
              <p className="text-gray-500 text-center py-8">No active alerts</p>
            ) : (
              alerts.map(alert => (
                <AlertCard key={alert.id} alert={alert} />
              ))
            )}
          </div>
        </div>

        {/* Recommendations */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Optimization Recommendations</h3>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {recommendations.length === 0 ? (
              <p className="text-gray-500 text-center py-8">No pending recommendations</p>
            ) : (
              recommendations.map(rec => (
                <RecommendationCard key={rec.id} recommendation={rec} />
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EvaluationAnalyticsDashboard;
