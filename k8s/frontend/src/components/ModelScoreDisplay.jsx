import React from 'react';
import { Star, Zap, Brain, Code, MessageCircle, BarChart3, TrendingUp } from 'lucide-react';

const ModelScoreDisplay = ({ 
  modelId, 
  scores, 
  topCapabilities, 
  overallScore, 
  compact = false,
  showDetails = false 
}) => {
  if (!scores || Object.keys(scores).length === 0) {
    return null;
  }

  const getScoreColor = (score) => {
    if (score >= 0.9) return 'text-green-600 bg-green-50';
    if (score >= 0.8) return 'text-blue-600 bg-blue-50';
    if (score >= 0.7) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const getScoreIcon = (capability) => {
    const iconMap = {
      'code_generation': Code,
      'reasoning': Brain,
      'creative_writing': MessageCircle,
      'factual_qa': BarChart3,
      'analysis': TrendingUp,
      'conversational': MessageCircle,
      'mathematical': Brain,
      'multimodal': Star,
      'rag': BarChart3,
      'real_time_data': Zap,
      'function_calling': Code,
      'translation': MessageCircle,
      'summarization': BarChart3
    };
    
    const IconComponent = iconMap[capability] || Star;
    return <IconComponent size={12} />;
  };

  const formatCapabilityName = (capability) => {
    return capability
      .replace(/_/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  };

  if (compact) {
    return (
      <div className="flex items-center space-x-2">
        <div className="flex items-center space-x-1">
          <Star size={12} className="text-yellow-500" />
          <span className="text-xs font-medium text-gray-700">
            {(overallScore * 100).toFixed(0)}%
          </span>
        </div>
        {topCapabilities && topCapabilities.length > 0 && (
          <div className="flex items-center space-x-1">
            <span className="text-xs text-gray-500">•</span>
            <span className="text-xs text-gray-600">
              Best: {topCapabilities[0].capability}
            </span>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-3 shadow-sm">
      <div className="flex items-center justify-between mb-2">
        <h4 className="text-sm font-medium text-gray-900">AI Optimizer Scores</h4>
        <div className={`px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(overallScore)}`}>
          Overall: {(overallScore * 100).toFixed(0)}%
        </div>
      </div>

      {showDetails && (
        <div className="space-y-2">
          <div className="grid grid-cols-2 gap-2">
            {Object.entries(scores)
              .filter(([key]) => key !== 'overall')
              .sort(([, a], [, b]) => b - a)
              .slice(0, 6)
              .map(([capability, score]) => (
                <div key={capability} className="flex items-center justify-between text-xs">
                  <div className="flex items-center space-x-1">
                    {getScoreIcon(capability)}
                    <span className="text-gray-600 truncate">
                      {formatCapabilityName(capability)}
                    </span>
                  </div>
                  <span className={`font-medium ${getScoreColor(score).split(' ')[0]}`}>
                    {(score * 100).toFixed(0)}%
                  </span>
                </div>
              ))}
          </div>
        </div>
      )}

      {!showDetails && topCapabilities && topCapabilities.length > 0 && (
        <div className="space-y-1">
          <p className="text-xs text-gray-500 mb-1">Top Capabilities:</p>
          <div className="flex flex-wrap gap-1">
            {topCapabilities.map(({ capability, score }) => (
              <span
                key={capability}
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(score)}`}
              >
                {capability} {(score * 100).toFixed(0)}%
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Tooltip component for hover details
export const ModelScoreTooltip = ({ modelId, scores, overallScore, children }) => {
  const [showTooltip, setShowTooltip] = React.useState(false);

  if (!scores || Object.keys(scores).length === 0) {
    return children;
  }

  return (
    <div 
      className="relative inline-block"
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      {children}
      {showTooltip && (
        <div className="absolute z-50 bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64">
          <ModelScoreDisplay
            modelId={modelId}
            scores={scores}
            overallScore={overallScore}
            showDetails={true}
          />
        </div>
      )}
    </div>
  );
};

// Progress bar component for individual scores
export const ScoreProgressBar = ({ score, label, className = '' }) => {
  const percentage = Math.round(score * 100);
  const getBarColor = (score) => {
    if (score >= 0.9) return 'bg-green-500';
    if (score >= 0.8) return 'bg-blue-500';
    if (score >= 0.7) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className={`space-y-1 ${className}`}>
      <div className="flex justify-between items-center">
        <span className="text-xs font-medium text-gray-700">{label}</span>
        <span className="text-xs text-gray-500">{percentage}%</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${getBarColor(score)}`}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
};

export default ModelScoreDisplay;
