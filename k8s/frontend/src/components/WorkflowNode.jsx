import React, { useState } from 'react';
import {
  Trash2, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Play, Pause, AlertCircle,
  Zap, MessageSquare, Database, Code, Filter, Target
} from 'lucide-react';

const WorkflowNode = ({
  node,
  zoom,
  pan,
  selected,
  onClick,
  onDelete,
  onStartConnection,
  onEndConnection,
  isConnecting
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const getNodeIcon = (type) => {
    switch (type) {
      case 'trigger': return <Zap size={16} className="text-green-600" />;
      case 'llm_call': return <MessageSquare size={16} className="text-blue-600" />;
      case 'data_transform': return <Database size={16} className="text-purple-600" />;
      case 'condition': return <Filter size={16} className="text-yellow-600" />;
      case 'code': return <Code size={16} className="text-gray-600" />;
      case 'sentiment_analysis': return <Target size={16} className="text-pink-600" />;
      default: return <Settings size={16} className="text-gray-600" />;
    }
  };

  const getNodeColor = (type) => {
    switch (type) {
      case 'trigger': return 'border-green-300 bg-green-50';
      case 'llm_call': return 'border-blue-300 bg-blue-50';
      case 'data_transform': return 'border-purple-300 bg-purple-50';
      case 'condition': return 'border-yellow-300 bg-yellow-50';
      case 'code': return 'border-gray-300 bg-gray-50';
      case 'sentiment_analysis': return 'border-pink-300 bg-pink-50';
      default: return 'border-gray-300 bg-white';
    }
  };

  const handleMouseDown = (e) => {
    if (e.target.closest('.node-port') || e.target.closest('.node-action')) {
      return;
    }
    
    setIsDragging(true);
    setDragStart({
      x: e.clientX - node.position.x * zoom,
      y: e.clientY - node.position.y * zoom
    });
    e.preventDefault();
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    
    const newX = (e.clientX - dragStart.x) / zoom;
    const newY = (e.clientY - dragStart.y) / zoom;
    
    // Update node position through parent component
    // This would need to be passed as a prop
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handlePortClick = (portType, portName) => {
    if (portType === 'output') {
      onStartConnection(node.id, portName);
    } else if (portType === 'input' && isConnecting) {
      onEndConnection(node.id, portName);
    }
  };

  const nodeStyle = {
    position: 'absolute',
    left: node.position.x * zoom + pan.x,
    top: node.position.y * zoom + pan.y,
    transform: `scale(${zoom})`,
    transformOrigin: 'top left',
    cursor: isDragging ? 'grabbing' : 'grab'
  };

  return (
    <div
      style={nodeStyle}
      className={`w-48 bg-white rounded-lg border-2 shadow-lg transition-all duration-200 ${
        selected ? 'border-blue-500 shadow-xl' : getNodeColor(node.type)
      } ${isDragging ? 'z-50' : 'z-10'}`}
      onClick={onClick}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
    >
      {/* Node Header */}
      <div className="p-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getNodeIcon(node.type)}
            <span className="font-medium text-sm text-gray-900">{node.name}</span>
          </div>
          
          <div className="flex items-center space-x-1">
            <button
              className="node-action p-1 text-gray-400 hover:text-gray-600 rounded"
              onClick={(e) => {
                e.stopPropagation();
                // Handle copy
              }}
              title="Copy Node"
            >
              <Copy size={12} />
            </button>
            
            <button
              className="node-action p-1 text-gray-400 hover:text-red-600 rounded"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(node.id);
              }}
              title="Delete Node"
            >
              <Trash2 size={12} />
            </button>
          </div>
        </div>
      </div>

      {/* Node Body */}
      <div className="p-3">
        {/* Node Configuration Preview */}
        <div className="text-xs text-gray-600 mb-2">
          {node.type === 'llm_call' && (
            <div>Model: {node.config?.model || 'Not configured'}</div>
          )}
          {node.type === 'condition' && (
            <div>Condition: {node.config?.condition || 'Not configured'}</div>
          )}
          {node.type === 'data_transform' && (
            <div>Transform: {node.config?.transformation ? 'Configured' : 'Not configured'}</div>
          )}
          {node.type === 'sentiment_analysis' && (
            <div>Source: {node.config?.source || 'user_input'}</div>
          )}
          {node.type === 'code' && (
            <div>Language: {node.config?.language || 'javascript'}</div>
          )}
        </div>

        {/* Status Indicator */}
        <div className="flex items-center space-x-2 text-xs">
          <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
          <span className="text-gray-500">Ready</span>
        </div>
      </div>

      {/* Input Ports */}
      {node.inputs && node.inputs.length > 0 && (
        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-2">
          {node.inputs.map((input, index) => (
            <div
              key={input}
              className="node-port w-4 h-4 bg-white border-2 border-gray-400 rounded-full cursor-pointer hover:border-blue-500 mb-1"
              onClick={() => handlePortClick('input', input)}
              title={`Input: ${input}`}
              style={{ marginTop: index * 20 }}
            />
          ))}
        </div>
      )}

      {/* Output Ports */}
      {node.outputs && node.outputs.length > 0 && (
        <div className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-2">
          {node.outputs.map((output, index) => (
            <div
              key={output}
              className="node-port w-4 h-4 bg-white border-2 border-gray-400 rounded-full cursor-pointer hover:border-green-500 mb-1"
              onClick={() => handlePortClick('output', output)}
              title={`Output: ${output}`}
              style={{ marginTop: index * 20 }}
            />
          ))}
        </div>
      )}

      {/* Execution Status Overlay */}
      {node.status === 'running' && (
        <div className="absolute inset-0 bg-blue-500 bg-opacity-20 rounded-lg flex items-center justify-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        </div>
      )}

      {node.status === 'error' && (
        <div className="absolute inset-0 bg-red-500 bg-opacity-20 rounded-lg flex items-center justify-center">
          <AlertCircle size={20} className="text-red-600" />
        </div>
      )}

      {node.status === 'success' && (
        <div className="absolute top-2 right-2">
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
        </div>
      )}
    </div>
  );
};

export default WorkflowNode;
