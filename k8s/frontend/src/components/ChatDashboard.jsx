import React, { useState, useCallback, useEffect } from 'react';
import { Settings, Download, Trash2, MessageSquare } from 'lucide-react';
import ConversationList from './ConversationList';
import ChatContainer from './ChatContainer';
import useChatApi from '../hooks/useChatApi';
import useModelScores from '../hooks/useModelScores';
import ModelScoreDisplay from './ModelScoreDisplay';
import { Modal } from './Modal';
import { getStorageItem, setStorageItem, STORAGE_KEYS } from '../utils/localStorage';

const ChatDashboard = ({ showNotification, onModalStateChange }) => {
  const [showSettings, setShowSettings] = useState(false);
  const [showClearConfirm, setShowClearConfirm] = useState(false);

  // Load chat settings from localStorage or use defaults
  const [chatSettings, setChatSettings] = useState(() => {
    const defaultSettings = {
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 2048,
      stream: true,
      preferredLlmId: null,
    };

    const savedSettings = getStorageItem(STORAGE_KEYS.CHAT_SETTINGS, {});
    return { ...defaultSettings, ...savedSettings };
  });

  const { modelProfiles, modelScores, getModelScore, getTopCapabilities, loading: scoresLoading } = useModelScores();

  const {
    conversations,
    activeConversation,
    activeConversationId,
    isLoading,
    isStreaming,
    createConversation,
    switchConversation,
    deleteConversation,
    updateConversationTitle,
    sendMessage,
    cancelRequest,
    clearAllConversations,
  } = useChatApi({ showNotification });

  const handleSendMessage = useCallback(async (message) => {
    await sendMessage(message, {
      model: chatSettings.model,
      temperature: chatSettings.temperature,
      max_tokens: chatSettings.maxTokens,
      stream: chatSettings.stream,
      preferredLlmId: chatSettings.preferredLlmId,
    });
  }, [sendMessage, chatSettings]);

  const handleRegenerate = useCallback(async (message) => {
    await sendMessage(message, {
      model: chatSettings.model,
      temperature: chatSettings.temperature,
      max_tokens: chatSettings.maxTokens,
      stream: chatSettings.stream,
      preferredLlmId: chatSettings.preferredLlmId,
    });
  }, [sendMessage, chatSettings]);

  const handleExportConversation = useCallback(() => {
    if (!activeConversation) return;

    const exportData = {
      title: activeConversation.title,
      createdAt: activeConversation.createdAt,
      updatedAt: activeConversation.updatedAt,
      model: activeConversation.model,
      messages: activeConversation.messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp,
      })),
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${activeConversation.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showNotification?.('Conversation exported successfully', 'success');
  }, [activeConversation, showNotification]);

  const handleClearAllConversations = useCallback(() => {
    clearAllConversations();
    setShowClearConfirm(false);
    showNotification?.('All conversations cleared', 'success');
  }, [clearAllConversations, showNotification]);

  const handleSettingsChange = useCallback((key, value) => {
    setChatSettings(prev => {
      const newSettings = { ...prev, [key]: value };
      // Save to localStorage
      setStorageItem(STORAGE_KEYS.CHAT_SETTINGS, newSettings);
      return newSettings;
    });
  }, []);

  const handleModelSelect = useCallback((modelId) => {
    setChatSettings(prev => {
      const newSettings = { ...prev, model: modelId };
      // Save to localStorage
      setStorageItem(STORAGE_KEYS.CHAT_SETTINGS, newSettings);
      return newSettings;
    });
  }, []);

  // Notify parent when any modal state changes
  useEffect(() => {
    const isAnyModalOpen = showSettings || showClearConfirm;
    if (onModalStateChange) {
      onModalStateChange(isAnyModalOpen);
    }
  }, [showSettings, showClearConfirm, onModalStateChange]);

  return (
    <div className="flex h-full bg-gray-50">
      {/* Sidebar with conversations - responsive */}
      <div className="w-80 flex-shrink-0 hidden lg:block">
        <ConversationList
          conversations={conversations}
          activeConversationId={activeConversationId}
          onSelectConversation={switchConversation}
          onCreateConversation={() => createConversation()}
          onDeleteConversation={deleteConversation}
          onUpdateTitle={updateConversationTitle}
          className="h-full"
        />
      </div>

      {/* Main chat area */}
      <div className="flex-1 flex flex-col">
        {/* Chat header with actions */}
        <div className="border-b border-gray-200 bg-white p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <MessageSquare size={20} className="text-indigo-600" />
              <h1 className="text-xl font-semibold text-gray-800">AI Chat</h1>

              {/* Mobile conversation selector */}
              <div className="lg:hidden ml-4">
                {conversations.length > 0 && (
                  <select
                    value={activeConversationId || ''}
                    onChange={(e) => e.target.value && switchConversation(e.target.value)}
                    className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    <option value="">Select conversation...</option>
                    {conversations.map(conv => (
                      <option key={conv.id} value={conv.id}>
                        {conv.title.length > 30 ? conv.title.substring(0, 27) + '...' : conv.title}
                      </option>
                    ))}
                  </select>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {/* Mobile new conversation button */}
              <button
                onClick={() => createConversation()}
                className="lg:hidden flex items-center space-x-1 px-3 py-2 text-sm text-indigo-600 hover:text-indigo-700 hover:bg-indigo-50 rounded-lg transition-colors"
                title="New conversation"
              >
                <MessageSquare size={16} />
                <span>New</span>
              </button>

              {activeConversation && (
                <button
                  onClick={handleExportConversation}
                  className="flex items-center space-x-1 px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Export conversation"
                >
                  <Download size={16} />
                  <span className="hidden sm:inline">Export</span>
                </button>
              )}

              <button
                onClick={() => setShowSettings(true)}
                className="flex items-center space-x-1 px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
                title="Chat settings"
              >
                <Settings size={16} />
                <span className="hidden sm:inline">Settings</span>
              </button>

              {conversations.length > 0 && (
                <button
                  onClick={() => setShowClearConfirm(true)}
                  className="flex items-center space-x-1 px-3 py-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
                  title="Clear all conversations"
                >
                  <Trash2 size={16} />
                  <span className="hidden sm:inline">Clear All</span>
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Chat container */}
        <div className="flex-1">
          <ChatContainer
            conversation={activeConversation}
            onSendMessage={handleSendMessage}
            onCancel={cancelRequest}
            onRegenerate={handleRegenerate}
            isLoading={isLoading}
            isStreaming={isStreaming}
            currentModel={chatSettings.model}
            showSettings={false}
            onSettingsClick={() => setShowSettings(true)}
            onModelSelect={handleModelSelect}
            className="h-full"
          />
        </div>
      </div>

      {/* Settings Modal */}
      {showSettings && (
        <Modal
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
          title="Chat Settings"
          size="md"
        >
          <div className="space-y-6">
            {/* Model Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Model Selection
              </label>
              <select
                value={chatSettings.model}
                onChange={(e) => handleSettingsChange('model', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                {scoresLoading ? (
                  <option value="">Loading models...</option>
                ) : (
                  <>
                    {/* Default models if no profiles loaded */}
                    {modelProfiles.length === 0 && (
                      <>
                        <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                        <option value="gpt-4o-mini">GPT-4o Mini</option>
                        <option value="claude-3-haiku">Claude 3 Haiku</option>
                        <option value="gemini-2.5-flash-preview-05-20">Gemini 2.5 Flash</option>
                      </>
                    )}

                    {/* Dynamic model profiles with scores */}
                    {modelProfiles
                      .sort((a, b) => (b.overallScore || 0) - (a.overallScore || 0))
                      .map((profile) => {
                        const score = getModelScore(profile.id, 'overall');
                        const topCap = getTopCapabilities(profile.id, 1)[0];
                        return (
                          <option key={profile.id} value={profile.id}>
                            {profile.name}
                            {score > 0 && ` (${(score * 100).toFixed(0)}% score)`}
                            {topCap && ` - Best: ${topCap.capability}`}
                          </option>
                        );
                      })}
                  </>
                )}
              </select>

              {/* Show selected model info */}
              {chatSettings.model && getModelScore(chatSettings.model, 'overall') > 0 && (
                <div className="mt-3">
                  <ModelScoreDisplay
                    modelId={chatSettings.model}
                    scores={modelScores[chatSettings.model]}
                    topCapabilities={getTopCapabilities(chatSettings.model, 3)}
                    overallScore={getModelScore(chatSettings.model, 'overall')}
                    compact={false}
                  />
                </div>
              )}
            </div>

            {/* Temperature */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Temperature: {chatSettings.temperature}
              </label>
              <input
                type="range"
                min="0"
                max="2"
                step="0.1"
                value={chatSettings.temperature}
                onChange={(e) => handleSettingsChange('temperature', parseFloat(e.target.value))}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Focused</span>
                <span>Balanced</span>
                <span>Creative</span>
              </div>
            </div>

            {/* Max Tokens */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Tokens
              </label>
              <input
                type="number"
                min="1"
                max="8192"
                value={chatSettings.maxTokens}
                onChange={(e) => handleSettingsChange('maxTokens', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
            </div>

            {/* Streaming */}
            <div className="flex items-center justify-between">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Streaming Responses
                </label>
                <p className="text-xs text-gray-500">Show responses as they're generated</p>
              </div>
              <input
                type="checkbox"
                checked={chatSettings.stream}
                onChange={(e) => handleSettingsChange('stream', e.target.checked)}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
            </div>

            {/* Preferred LLM ID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preferred LLM ID (Optional)
              </label>
              <input
                type="text"
                value={chatSettings.preferredLlmId || ''}
                onChange={(e) => handleSettingsChange('preferredLlmId', e.target.value || null)}
                placeholder="Leave empty for automatic routing"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                Override automatic model selection with a specific LLM backend ID
              </p>
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={() => setShowSettings(false)}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Close
            </button>
          </div>
        </Modal>
      )}

      {/* Clear All Confirmation Modal */}
      {showClearConfirm && (
        <Modal
          isOpen={showClearConfirm}
          onClose={() => setShowClearConfirm(false)}
          title="Clear All Conversations"
          size="sm"
        >
          <p className="text-gray-600 mb-6">
            Are you sure you want to delete all conversations? This action cannot be undone.
          </p>

          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setShowClearConfirm(false)}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleClearAllConversations}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Clear All
            </button>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default ChatDashboard;
