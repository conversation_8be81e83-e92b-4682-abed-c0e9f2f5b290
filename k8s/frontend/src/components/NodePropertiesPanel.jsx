import React, { useState, useEffect } from 'react';
import {
  Settings, Code, Database, MessageSquare, Filter, Target,
  Save, RotateCcw, Eye, Play, HelpCircle
} from 'lucide-react';

const NodePropertiesPanel = ({ node, onChange }) => {
  const [config, setConfig] = useState(node.config || {});
  const [errors, setErrors] = useState({});

  useEffect(() => {
    setConfig(node.config || {});
    setErrors({});
  }, [node]);

  const handleConfigChange = (key, value) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    
    // Update node immediately
    onChange({
      ...node,
      config: newConfig
    });
  };

  const validateConfig = () => {
    const newErrors = {};
    
    switch (node.type) {
      case 'llm_call':
        if (!config.prompt?.trim()) {
          newErrors.prompt = 'Prompt is required';
        }
        if (!config.model) {
          newErrors.model = 'Model is required';
        }
        break;
      case 'condition':
        if (!config.condition?.trim()) {
          newErrors.condition = 'Condition is required';
        }
        break;
      case 'data_transform':
        if (!config.transformation?.trim()) {
          newErrors.transformation = 'Transformation code is required';
        }
        break;
      case 'code':
        if (!config.code?.trim()) {
          newErrors.code = 'Code is required';
        }
        break;
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const renderLLMCallConfig = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Model *
        </label>
        <select
          value={config.model || ''}
          onChange={(e) => handleConfigChange('model', e.target.value)}
          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Select a model</option>
          <option value="gpt-4">GPT-4</option>
          <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
          <option value="claude-3-sonnet">Claude 3 Sonnet</option>
          <option value="gemini-pro">Gemini Pro</option>
        </select>
        {errors.model && <p className="text-red-500 text-xs mt-1">{errors.model}</p>}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Prompt *
        </label>
        <textarea
          value={config.prompt || ''}
          onChange={(e) => handleConfigChange('prompt', e.target.value)}
          placeholder="Enter your prompt here..."
          className="w-full h-32 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        {errors.prompt && <p className="text-red-500 text-xs mt-1">{errors.prompt}</p>}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Temperature
          </label>
          <input
            type="number"
            min="0"
            max="2"
            step="0.1"
            value={config.temperature || 0.7}
            onChange={(e) => handleConfigChange('temperature', parseFloat(e.target.value))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Max Tokens
          </label>
          <input
            type="number"
            min="1"
            max="4000"
            value={config.max_tokens || 1000}
            onChange={(e) => handleConfigChange('max_tokens', parseInt(e.target.value))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      <div>
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={config.stream || false}
            onChange={(e) => handleConfigChange('stream', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700">Enable streaming</span>
        </label>
      </div>
    </div>
  );

  const renderConditionConfig = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Condition Type
        </label>
        <select
          value={config.operator || 'javascript'}
          onChange={(e) => handleConfigChange('operator', e.target.value)}
          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="javascript">JavaScript Expression</option>
          <option value="simple">Simple Comparison</option>
          <option value="regex">Regular Expression</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Condition *
        </label>
        <textarea
          value={config.condition || ''}
          onChange={(e) => handleConfigChange('condition', e.target.value)}
          placeholder={
            config.operator === 'javascript' 
              ? 'input.length > 0' 
              : config.operator === 'regex'
              ? '/pattern/flags'
              : 'input == "expected_value"'
          }
          className="w-full h-24 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        {errors.condition && <p className="text-red-500 text-xs mt-1">{errors.condition}</p>}
      </div>

      <div className="bg-blue-50 p-3 rounded-md">
        <p className="text-xs text-blue-700">
          <strong>Available variables:</strong> input, context, variables
        </p>
      </div>
    </div>
  );

  const renderDataTransformConfig = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Language
        </label>
        <select
          value={config.language || 'javascript'}
          onChange={(e) => handleConfigChange('language', e.target.value)}
          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="javascript">JavaScript</option>
          <option value="python">Python</option>
          <option value="jq">jq (JSON processor)</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Transformation Code *
        </label>
        <textarea
          value={config.transformation || ''}
          onChange={(e) => handleConfigChange('transformation', e.target.value)}
          placeholder="return input.toUpperCase();"
          className="w-full h-32 border border-gray-300 rounded-md px-3 py-2 text-sm font-mono focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        {errors.transformation && <p className="text-red-500 text-xs mt-1">{errors.transformation}</p>}
      </div>

      <div>
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={config.safe_mode || true}
            onChange={(e) => handleConfigChange('safe_mode', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700">Safe mode (restricted operations)</span>
        </label>
      </div>
    </div>
  );

  const renderSentimentAnalysisConfig = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Input Source
        </label>
        <select
          value={config.source || 'user_input'}
          onChange={(e) => handleConfigChange('source', e.target.value)}
          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="user_input">User Input</option>
          <option value="social_media">Social Media</option>
          <option value="feedback">Customer Feedback</option>
          <option value="support">Support Tickets</option>
        </select>
      </div>

      <div>
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={config.include_emotions || true}
            onChange={(e) => handleConfigChange('include_emotions', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700">Include emotion analysis</span>
        </label>
      </div>

      <div>
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={config.include_topics || false}
            onChange={(e) => handleConfigChange('include_topics', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700">Extract topics</span>
        </label>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Confidence Threshold
        </label>
        <input
          type="number"
          min="0"
          max="1"
          step="0.1"
          value={config.confidence_threshold || 0.7}
          onChange={(e) => handleConfigChange('confidence_threshold', parseFloat(e.target.value))}
          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
    </div>
  );

  const renderCodeConfig = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Language
        </label>
        <select
          value={config.language || 'javascript'}
          onChange={(e) => handleConfigChange('language', e.target.value)}
          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="javascript">JavaScript</option>
          <option value="python">Python</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Code *
        </label>
        <textarea
          value={config.code || ''}
          onChange={(e) => handleConfigChange('code', e.target.value)}
          placeholder="// Your custom code here\nreturn input;"
          className="w-full h-40 border border-gray-300 rounded-md px-3 py-2 text-sm font-mono focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        {errors.code && <p className="text-red-500 text-xs mt-1">{errors.code}</p>}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Timeout (seconds)
        </label>
        <input
          type="number"
          min="1"
          max="300"
          value={config.timeout || 30}
          onChange={(e) => handleConfigChange('timeout', parseInt(e.target.value))}
          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
    </div>
  );

  const renderNodeConfig = () => {
    switch (node.type) {
      case 'llm_call':
        return renderLLMCallConfig();
      case 'condition':
        return renderConditionConfig();
      case 'data_transform':
        return renderDataTransformConfig();
      case 'sentiment_analysis':
        return renderSentimentAnalysisConfig();
      case 'code':
        return renderCodeConfig();
      default:
        return (
          <div className="text-center py-8 text-gray-500">
            <Settings size={32} className="mx-auto mb-2" />
            <p>No configuration available for this node type</p>
          </div>
        );
    }
  };

  return (
    <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Node Properties</h3>
        <p className="text-sm text-gray-600">{node.name}</p>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {/* Basic Properties */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Basic Settings</h4>
          
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Node Name
              </label>
              <input
                type="text"
                value={node.name}
                onChange={(e) => onChange({ ...node, name: e.target.value })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                value={node.description || ''}
                onChange={(e) => onChange({ ...node, description: e.target.value })}
                placeholder="Optional description..."
                className="w-full h-20 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Node-specific Configuration */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Configuration</h4>
          {renderNodeConfig()}
        </div>

        {/* Error Handling */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Error Handling</h4>
          
          <div className="space-y-3">
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={config.continue_on_error || false}
                  onChange={(e) => handleConfigChange('continue_on_error', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">Continue workflow on error</span>
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Retry Count
              </label>
              <input
                type="number"
                min="0"
                max="5"
                value={config.retry_count || 0}
                onChange={(e) => handleConfigChange('retry_count', parseInt(e.target.value))}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <button
            onClick={() => {
              if (validateConfig()) {
                // Configuration is valid
              }
            }}
            className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <Save size={16} className="mr-2" />
            Save
          </button>
          
          <button
            onClick={() => {
              setConfig(node.config || {});
              setErrors({});
            }}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <RotateCcw size={16} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default NodePropertiesPanel;
