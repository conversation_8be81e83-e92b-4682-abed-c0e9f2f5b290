import React, { useState, useEffect } from 'react';
import {
  Heart, MessageCircle, TrendingUp, TrendingDown, AlertTriangle,
  BarChart3, Users, Clock, Target, Zap, RefreshCw, Filter,
  ThumbsUp, ThumbsDown, Meh, Eye, Settings, Play, Pause
} from 'lucide-react';
import { Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar } from 'recharts';
import { API_BASE_URL } from '../utils/constants';
import { useAuth } from '../contexts/AuthContext';

const SentimentDashboard = ({ showNotification, onFormStateChange }) => {
  const { getAuthHeaders } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [sentimentData, setSentimentData] = useState(null);
  const [trends, setTrends] = useState([]);
  const [insights, setInsights] = useState(null);
  const [socialStatus, setSocialStatus] = useState(null);
  const [recommendations, setRecommendations] = useState(null);
  const [loading, setLoading] = useState(true);
  const [realTimeEnabled, setRealTimeEnabled] = useState(false);
  const [selectedSource, setSelectedSource] = useState('all');
  const [selectedPeriod, setSelectedPeriod] = useState('day');

  useEffect(() => {
    fetchSentimentData();
    fetchTrends();
    fetchInsights();
    fetchSocialStatus();
    fetchRecommendations();
  }, [selectedSource, selectedPeriod]);

  useEffect(() => {
    let eventSource;
    if (realTimeEnabled) {
      eventSource = new EventSource('/api/sentiment/stream');
      eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        setSentimentData(prev => ({ ...prev, ...data }));
      };
    }
    return () => {
      if (eventSource) {
        eventSource.close();
      }
    };
  }, [realTimeEnabled]);

  const fetchSentimentData = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/sentiment/insights`, {
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        }
      });
      const data = await response.json();
      setSentimentData(data);
    } catch (error) {
      console.error('Failed to fetch sentiment data:', error);
      setSentimentData(getMockSentimentData());
    }
  };

  const fetchTrends = async () => {
    try {
      const params = new URLSearchParams();
      if (selectedSource !== 'all') params.append('source', selectedSource);
      params.append('period', selectedPeriod);

      const response = await fetch(`${API_BASE_URL}/api/sentiment/trends?${params}`, {
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        }
      });
      const data = await response.json();
      setTrends(data.trends || []);
    } catch (error) {
      console.error('Failed to fetch trends:', error);
      setTrends(getMockTrends());
    }
  };

  const fetchInsights = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/sentiment/insights`);
      const data = await response.json();
      setInsights(data);
    } catch (error) {
      console.error('Failed to fetch insights:', error);
      setInsights(getMockInsights());
    } finally {
      setLoading(false);
    }
  };

  const fetchSocialStatus = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/sentiment/social-monitoring`);
      const data = await response.json();
      setSocialStatus(data);
    } catch (error) {
      console.error('Failed to fetch social status:', error);
      setSocialStatus(getMockSocialStatus());
    }
  };

  const fetchRecommendations = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/sentiment/recommendations`);
      const data = await response.json();
      setRecommendations(data);
    } catch (error) {
      console.error('Failed to fetch recommendations:', error);
      setRecommendations(getMockRecommendations());
    }
  };

  const handleAnalyzeText = async (text, source = 'manual') => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/sentiment/analyze`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text, source })
      });
      const result = await response.json();
      showNotification('Text analyzed successfully', 'success');
      return result;
    } catch (error) {
      showNotification('Failed to analyze text', 'error');
      throw error;
    }
  };

  // Mock data functions
  const getMockSentimentData = () => ({
    positive: '75%',
    neutral: '15%',
    negative: '10%',
    totalMentions: '1,234'
  });

  const getMockTrends = () => [
    { date: '2024-01-01', positive: 75, neutral: 15, negative: 10 },
    { date: '2024-01-02', positive: 78, neutral: 12, negative: 10 },
    { date: '2024-01-03', positive: 72, neutral: 18, negative: 10 },
    { date: '2024-01-04', positive: 80, neutral: 12, negative: 8 },
    { date: '2024-01-05', positive: 77, neutral: 15, negative: 8 },
  ];

  const getMockInsights = () => ({
    summary: 'Overall sentiment is positive with strong customer satisfaction.',
    keyTopics: ['product quality', 'customer service', 'pricing'],
    recommendations: ['Continue focus on quality', 'Improve response time']
  });

  const getMockSocialStatus = () => ({
    platforms: ['Twitter', 'Facebook', 'Instagram'],
    totalFollowers: '50K',
    engagement: '4.2%'
  });

  const getMockRecommendations = () => [
    { title: 'Improve Response Time', priority: 'high', impact: 'medium' },
    { title: 'Enhance Product Features', priority: 'medium', impact: 'high' }
  ];

  const toggleRealTime = () => {
    setRealTimeEnabled(!realTimeEnabled);
    showNotification(
      realTimeEnabled ? 'Real-time monitoring disabled' : 'Real-time monitoring enabled',
      'info'
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Sentiment Intelligence</h2>
          <p className="text-gray-600">AI-powered sentiment analysis and social monitoring</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={toggleRealTime}
            className={`inline-flex items-center px-4 py-2 border rounded-md text-sm font-medium ${
              realTimeEnabled
                ? 'border-red-300 text-red-700 bg-red-50 hover:bg-red-100'
                : 'border-green-300 text-green-700 bg-green-50 hover:bg-green-100'
            }`}
          >
            {realTimeEnabled ? <Pause size={16} className="mr-2" /> : <Play size={16} className="mr-2" />}
            {realTimeEnabled ? 'Pause' : 'Start'} Real-time
          </button>
          <button
            onClick={() => {
              fetchSentimentData();
              fetchTrends();
              fetchInsights();
            }}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <RefreshCw size={16} className="mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex space-x-4 bg-white p-4 rounded-lg shadow">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Source</label>
          <select
            value={selectedSource}
            onChange={(e) => setSelectedSource(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="all">All Sources</option>
            <option value="social_media">Social Media</option>
            <option value="feedback">Customer Feedback</option>
            <option value="support">Support Tickets</option>
            <option value="reviews">Product Reviews</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Period</label>
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="hour">Last 24 Hours</option>
            <option value="day">Last 7 Days</option>
            <option value="week">Last 4 Weeks</option>
            <option value="month">Last 12 Months</option>
          </select>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Overview', icon: BarChart3 },
            { id: 'trends', name: 'Trends', icon: TrendingUp },
            { id: 'social', name: 'Social Monitoring', icon: MessageCircle },
            { id: 'insights', name: 'Insights', icon: Eye },
            { id: 'recommendations', name: 'Recommendations', icon: Target }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon size={16} className="mr-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center">
                <ThumbsUp className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Positive Sentiment</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {sentimentData?.positive || '75%'}
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center">
                <Meh className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Neutral Sentiment</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {sentimentData?.neutral || '15%'}
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center">
                <ThumbsDown className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Negative Sentiment</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {sentimentData?.negative || '10%'}
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center">
                <MessageCircle className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Mentions</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {sentimentData?.totalMentions || '1,234'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Sentiment Trends Chart */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Sentiment Trends</h3>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={trends.length > 0 ? trends : getMockTrends()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="positive" stroke="#10b981" strokeWidth={2} />
                <Line type="monotone" dataKey="neutral" stroke="#f59e0b" strokeWidth={2} />
                <Line type="monotone" dataKey="negative" stroke="#ef4444" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}

      {activeTab === 'trends' && (
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Sentiment Trends</h3>
              <div className="flex space-x-2">
                <select
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm"
                >
                  <option value="hour">Last Hour</option>
                  <option value="day">Last Day</option>
                  <option value="week">Last Week</option>
                  <option value="month">Last Month</option>
                </select>
              </div>
            </div>
            <ResponsiveContainer width="100%" height={400}>
              <LineChart data={trends.length > 0 ? trends : getMockTrends()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="positive" stroke="#10b981" strokeWidth={2} name="Positive" />
                <Line type="monotone" dataKey="neutral" stroke="#f59e0b" strokeWidth={2} name="Neutral" />
                <Line type="monotone" dataKey="negative" stroke="#ef4444" strokeWidth={2} name="Negative" />
              </LineChart>
            </ResponsiveContainer>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-lg shadow">
              <h4 className="text-md font-medium text-gray-900 mb-3">Trending Positive</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Product Quality</span>
                  <span className="text-sm font-medium text-green-600">+15%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Customer Service</span>
                  <span className="text-sm font-medium text-green-600">+12%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">User Experience</span>
                  <span className="text-sm font-medium text-green-600">+8%</span>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <h4 className="text-md font-medium text-gray-900 mb-3">Trending Negative</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Pricing</span>
                  <span className="text-sm font-medium text-red-600">-5%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Delivery Time</span>
                  <span className="text-sm font-medium text-red-600">-3%</span>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <h4 className="text-md font-medium text-gray-900 mb-3">Key Insights</h4>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Overall sentiment improved by 8% this {selectedPeriod}</p>
                <p className="text-sm text-gray-600">Customer satisfaction is at an all-time high</p>
                <p className="text-sm text-gray-600">Focus on pricing strategy recommended</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'social' && (
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Social Media Monitoring</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <p className="text-2xl font-semibold text-gray-900">{socialStatus?.totalFollowers || '50K'}</p>
                <p className="text-sm text-gray-500">Total Followers</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-semibold text-gray-900">{socialStatus?.engagement || '4.2%'}</p>
                <p className="text-sm text-gray-500">Engagement Rate</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-semibold text-gray-900">{socialStatus?.platforms?.length || '3'}</p>
                <p className="text-sm text-gray-500">Connected Platforms</p>
              </div>
            </div>
            <div className="mt-6">
              <h4 className="text-md font-medium text-gray-900 mb-3">Platform Status</h4>
              <div className="space-y-2">
                {(socialStatus?.platforms || ['Twitter', 'Facebook', 'Instagram']).map((platform, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">{platform}</span>
                    <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">Active</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'insights' && (
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Sentiment Insights</h3>
            <div className="space-y-4">
              <div className="border-l-4 border-blue-400 pl-4">
                <h4 className="text-md font-medium text-gray-900">Summary</h4>
                <p className="text-sm text-gray-600 mt-1">
                  {insights?.summary || 'Overall sentiment is positive with strong customer satisfaction across all channels.'}
                </p>
              </div>
              <div className="border-l-4 border-green-400 pl-4">
                <h4 className="text-md font-medium text-gray-900">Key Topics</h4>
                <div className="flex flex-wrap gap-2 mt-2">
                  {(insights?.keyTopics || ['product quality', 'customer service', 'pricing', 'user experience']).map((topic, index) => (
                    <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                      {topic}
                    </span>
                  ))}
                </div>
              </div>
              <div className="border-l-4 border-yellow-400 pl-4">
                <h4 className="text-md font-medium text-gray-900">Recommendations</h4>
                <ul className="list-disc list-inside text-sm text-gray-600 mt-1 space-y-1">
                  {(insights?.recommendations || [
                    'Continue focus on product quality improvements',
                    'Enhance customer service response times',
                    'Address pricing concerns in marketing communications'
                  ]).map((rec, index) => (
                    <li key={index}>{rec}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'recommendations' && (
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Action Recommendations</h3>
            <div className="space-y-4">
              {(recommendations || getMockRecommendations()).map((rec, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className="text-md font-medium text-gray-900">{rec.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{rec.description || 'Detailed recommendation description would appear here.'}</p>
                      <div className="flex space-x-4 mt-2">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          rec.priority === 'high' ? 'bg-red-100 text-red-800' :
                          rec.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {rec.priority} priority
                        </span>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          rec.impact === 'high' ? 'bg-purple-100 text-purple-800' :
                          rec.impact === 'medium' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {rec.impact} impact
                        </span>
                      </div>
                    </div>
                    <button
                      onClick={() => showNotification('Recommendation implemented', 'success')}
                      className="ml-4 px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                    >
                      Implement
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Mock data functions
const getMockSentimentData = () => ({
  sentiment: { positive: 0.65, neutral: 0.25, negative: 0.10 },
  volume: 1250,
  trending_topics: ['product_feedback', 'customer_service', 'feature_requests'],
  alerts: []
});

const getMockTrends = () => [
  { period: '2024-01-15', sentiment: { positive: 0.65, neutral: 0.25, negative: 0.10, overall: 'positive' }, volume: 150 },
  { period: '2024-01-14', sentiment: { positive: 0.60, neutral: 0.30, negative: 0.10, overall: 'positive' }, volume: 120 }
];

const getMockInsights = () => ({
  summary: 'Overall sentiment is positive with strong customer satisfaction',
  alerts: [],
  metrics: { satisfaction_score: 7.8, sentiment_trend: 0.15, response_rate: 0.85 }
});

const getMockSocialStatus = () => ({
  platforms: { twitter: true, facebook: false, instagram: true, linkedin: false },
  active_monitors: 3,
  recent_mentions: 25
});

const getMockRecommendations = () => ({
  immediate_actions: [
    { priority: 'high', action: 'Address negative feedback about response times', department: 'customer_service' }
  ],
  strategic_initiatives: [
    { initiative: 'Proactive Customer Outreach Program', timeline: '1 month' }
  ]
});

export default SentimentDashboard;
