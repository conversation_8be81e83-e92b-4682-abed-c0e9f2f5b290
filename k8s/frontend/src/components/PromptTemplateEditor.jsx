import React, { useState, useEffect } from 'react';
import {
  Code, Plus, Trash2, Eye, Play, Save, AlertCircle, CheckCircle,
  Type, Hash, ToggleLeft, List, Braces, HelpCircle
} from 'lucide-react';

const PromptTemplateEditor = ({ 
  prompt = null, 
  onSave, 
  onCancel, 
  promptOpsHook,
  showNotification 
}) => {
  const [templateData, setTemplateData] = useState({
    id: '',
    name: '',
    description: '',
    content: '',
    version: '1.0.0',
    variables: [],
    tags: ['template'],
    model_targets: [],
    use_case: 'general',
    status: 'draft',
    owner: 'user'
  });
  
  const [previewMode, setPreviewMode] = useState(false);
  const [previewVariables, setPreviewVariables] = useState({});
  const [extractedVariables, setExtractedVariables] = useState([]);
  const [validationErrors, setValidationErrors] = useState([]);

  const { extractPromptVariables } = promptOpsHook;

  useEffect(() => {
    if (prompt) {
      setTemplateData({
        id: prompt.id || '',
        name: prompt.name || '',
        description: prompt.description || '',
        content: prompt.content || '',
        version: prompt.version || '1.0.0',
        variables: prompt.variables || [],
        tags: prompt.tags || ['template'],
        model_targets: prompt.model_targets || [],
        use_case: prompt.metadata?.use_case || 'general',
        status: prompt.status || 'draft',
        owner: prompt.owner || 'user'
      });
    }
  }, [prompt]);

  useEffect(() => {
    if (templateData.content) {
      extractVariablesFromContent();
    }
  }, [templateData.content]);

  const extractVariablesFromContent = async () => {
    try {
      const variables = await extractPromptVariables(templateData.content);
      setExtractedVariables(variables);
      
      // Auto-add new variables that aren't already defined
      const existingVarNames = templateData.variables.map(v => v.name);
      const newVariables = variables.filter(v => !existingVarNames.includes(v.name));
      
      if (newVariables.length > 0) {
        setTemplateData(prev => ({
          ...prev,
          variables: [...prev.variables, ...newVariables]
        }));
      }
    } catch (error) {
      console.error('Error extracting variables:', error);
    }
  };

  const handleVariableChange = (index, field, value) => {
    setTemplateData(prev => ({
      ...prev,
      variables: prev.variables.map((variable, i) => 
        i === index ? { ...variable, [field]: value } : variable
      )
    }));
  };

  const addVariable = () => {
    setTemplateData(prev => ({
      ...prev,
      variables: [...prev.variables, {
        name: '',
        type: 'string',
        description: '',
        required: true,
        default_value: '',
        validation: ''
      }]
    }));
  };

  const removeVariable = (index) => {
    setTemplateData(prev => ({
      ...prev,
      variables: prev.variables.filter((_, i) => i !== index)
    }));
  };

  const validateTemplate = () => {
    const errors = [];
    
    if (!templateData.name.trim()) {
      errors.push('Template name is required');
    }
    
    if (!templateData.content.trim()) {
      errors.push('Template content is required');
    }
    
    // Check for undefined variables in content
    const contentVariables = extractedVariables.map(v => v.name);
    const definedVariables = templateData.variables.map(v => v.name);
    const undefinedVars = contentVariables.filter(v => !definedVariables.includes(v));
    
    if (undefinedVars.length > 0) {
      errors.push(`Undefined variables in content: ${undefinedVars.join(', ')}`);
    }
    
    // Check for duplicate variable names
    const varNames = templateData.variables.map(v => v.name);
    const duplicates = varNames.filter((name, index) => varNames.indexOf(name) !== index);
    if (duplicates.length > 0) {
      errors.push(`Duplicate variable names: ${duplicates.join(', ')}`);
    }
    
    setValidationErrors(errors);
    return errors.length === 0;
  };

  const handlePreview = () => {
    if (!validateTemplate()) {
      showNotification?.('Please fix validation errors before previewing', 'error');
      return;
    }
    
    // Initialize preview variables with default values
    const initialPreviewVars = {};
    templateData.variables.forEach(variable => {
      initialPreviewVars[variable.name] = variable.default_value || 
        (variable.type === 'boolean' ? false : 
         variable.type === 'number' ? 0 : 
         variable.type === 'array' ? [] : '');
    });
    
    setPreviewVariables(initialPreviewVars);
    setPreviewMode(true);
  };

  const renderPreview = () => {
    let preview = templateData.content;
    
    // Replace variables with their preview values
    Object.entries(previewVariables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      preview = preview.replace(regex, String(value));
    });
    
    return preview;
  };

  const handleSave = () => {
    if (!validateTemplate()) {
      showNotification?.('Please fix validation errors before saving', 'error');
      return;
    }
    
    onSave(templateData);
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'string': return <Type className="h-4 w-4" />;
      case 'number': return <Hash className="h-4 w-4" />;
      case 'boolean': return <ToggleLeft className="h-4 w-4" />;
      case 'array': return <List className="h-4 w-4" />;
      case 'object': return <Braces className="h-4 w-4" />;
      default: return <Type className="h-4 w-4" />;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-6xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <Code className="h-6 w-6 text-blue-600" />
            <h2 className="text-xl font-semibold">
              {prompt ? 'Edit Template' : 'Create Template'}
            </h2>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handlePreview}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              <Eye className="h-4 w-4" />
              <span>Preview</span>
            </button>
            <button
              onClick={handleSave}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              <Save className="h-4 w-4" />
              <span>Save</span>
            </button>
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>
        </div>

        <div className="flex-1 flex overflow-hidden">
          {/* Template Editor */}
          <div className="w-1/2 border-r overflow-y-auto">
            <div className="p-6 space-y-6">
              {/* Basic Info */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Template Name *
                  </label>
                  <input
                    type="text"
                    value={templateData.name}
                    onChange={(e) => setTemplateData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter template name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={templateData.description}
                    onChange={(e) => setTemplateData(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    rows={2}
                    placeholder="Describe what this template does"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Use Case
                  </label>
                  <select
                    value={templateData.use_case}
                    onChange={(e) => setTemplateData(prev => ({ ...prev, use_case: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="general">General</option>
                    <option value="summarization">Summarization</option>
                    <option value="classification">Classification</option>
                    <option value="generation">Content Generation</option>
                    <option value="analysis">Analysis</option>
                    <option value="translation">Translation</option>
                    <option value="qa">Question Answering</option>
                  </select>
                </div>
              </div>

              {/* Template Content */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Template Content *
                  <span className="text-xs text-gray-500 ml-2">
                    Use {`{{variable_name}}`} for variables
                  </span>
                </label>
                <textarea
                  value={templateData.content}
                  onChange={(e) => setTemplateData(prev => ({ ...prev, content: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono"
                  rows={8}
                  placeholder="Enter your prompt template with {{variables}}"
                />
              </div>

              {/* Variables */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <label className="block text-sm font-medium text-gray-700">
                    Variables ({templateData.variables.length})
                  </label>
                  <button
                    onClick={addVariable}
                    className="flex items-center space-x-1 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    <Plus className="h-4 w-4" />
                    <span>Add Variable</span>
                  </button>
                </div>

                <div className="space-y-3">
                  {templateData.variables.map((variable, index) => (
                    <div key={index} className="border rounded-lg p-3 bg-gray-50">
                      <div className="grid grid-cols-2 gap-3 mb-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">
                            Name
                          </label>
                          <input
                            type="text"
                            value={variable.name}
                            onChange={(e) => handleVariableChange(index, 'name', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                            placeholder="variable_name"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">
                            Type
                          </label>
                          <select
                            value={variable.type}
                            onChange={(e) => handleVariableChange(index, 'type', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                          >
                            <option value="string">String</option>
                            <option value="number">Number</option>
                            <option value="boolean">Boolean</option>
                            <option value="array">Array</option>
                            <option value="object">Object</option>
                          </select>
                        </div>
                      </div>

                      <div className="mb-3">
                        <label className="block text-xs font-medium text-gray-600 mb-1">
                          Description
                        </label>
                        <input
                          type="text"
                          value={variable.description}
                          onChange={(e) => handleVariableChange(index, 'description', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                          placeholder="Describe this variable"
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <label className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={variable.required}
                              onChange={(e) => handleVariableChange(index, 'required', e.target.checked)}
                              className="rounded"
                            />
                            <span className="text-xs text-gray-600">Required</span>
                          </label>
                          <div className="flex items-center space-x-1 text-gray-500">
                            {getTypeIcon(variable.type)}
                            <span className="text-xs">{variable.type}</span>
                          </div>
                        </div>
                        <button
                          onClick={() => removeVariable(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Validation Errors */}
              {validationErrors.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <div className="flex items-center space-x-2 mb-2">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <span className="text-sm font-medium text-red-800">Validation Errors</span>
                  </div>
                  <ul className="text-sm text-red-700 space-y-1">
                    {validationErrors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>

          {/* Preview Panel */}
          <div className="w-1/2 overflow-y-auto">
            <div className="p-6">
              {previewMode ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">Template Preview</h3>
                    <button
                      onClick={() => setPreviewMode(false)}
                      className="text-sm text-gray-600 hover:text-gray-800"
                    >
                      Close Preview
                    </button>
                  </div>

                  {/* Variable Inputs */}
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium text-gray-700">Variable Values</h4>
                    {templateData.variables.map((variable) => (
                      <div key={variable.name}>
                        <label className="block text-xs font-medium text-gray-600 mb-1">
                          {variable.name} {variable.required && <span className="text-red-500">*</span>}
                        </label>
                        {variable.type === 'boolean' ? (
                          <input
                            type="checkbox"
                            checked={previewVariables[variable.name] || false}
                            onChange={(e) => setPreviewVariables(prev => ({
                              ...prev,
                              [variable.name]: e.target.checked
                            }))}
                            className="rounded"
                          />
                        ) : variable.type === 'number' ? (
                          <input
                            type="number"
                            value={previewVariables[variable.name] || ''}
                            onChange={(e) => setPreviewVariables(prev => ({
                              ...prev,
                              [variable.name]: parseFloat(e.target.value) || 0
                            }))}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                          />
                        ) : (
                          <input
                            type="text"
                            value={previewVariables[variable.name] || ''}
                            onChange={(e) => setPreviewVariables(prev => ({
                              ...prev,
                              [variable.name]: e.target.value
                            }))}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                            placeholder={variable.description || `Enter ${variable.name}`}
                          />
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Rendered Preview */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Rendered Output</h4>
                    <div className="bg-gray-50 p-3 rounded border font-mono text-sm whitespace-pre-wrap">
                      {renderPreview()}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <Eye className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>Click "Preview" to see the rendered template</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromptTemplateEditor;
