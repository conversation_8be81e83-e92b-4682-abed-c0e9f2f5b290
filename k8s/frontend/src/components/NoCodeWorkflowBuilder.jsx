import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  Plus, Play, Save, Download, Upload, Settings, Trash2, Copy,
  Zap, MessageSquare, Database, Code, Filter, Search, Target,
  ArrowRight, ArrowDown, RotateCcw, Eye, Edit3, Layers
} from 'lucide-react';

const NoCodeWorkflowBuilder = ({ showNotification, onSave, initialWorkflow = null }) => {
  const [workflow, setWorkflow] = useState(initialWorkflow || {
    id: '',
    name: 'New Workflow',
    description: '',
    nodes: [],
    connections: [],
    variables: {},
    settings: {
      timeout: 300,
      retries: 3,
      parallel: false
    }
  });

  const [selectedNode, setSelectedNode] = useState(null);
  const [draggedNode, setDraggedNode] = useState(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStart, setConnectionStart] = useState(null);
  const [showNodeLibrary, setShowNodeLibrary] = useState(true);
  const [showProperties, setShowProperties] = useState(true);
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });

  const canvasRef = useRef(null);
  const [canvasSize, setCanvasSize] = useState({ width: 800, height: 600 });

  // Node types available in the library
  const nodeTypes = [
    {
      type: 'trigger',
      name: 'Trigger',
      icon: <Zap size={16} />,
      color: 'bg-green-100 border-green-300',
      description: 'Start the workflow',
      inputs: [],
      outputs: ['output']
    },
    {
      type: 'llm_call',
      name: 'LLM Call',
      icon: <MessageSquare size={16} />,
      color: 'bg-blue-100 border-blue-300',
      description: 'Call an LLM with a prompt',
      inputs: ['input', 'prompt'],
      outputs: ['response', 'error']
    },
    {
      type: 'data_transform',
      name: 'Transform Data',
      icon: <Database size={16} />,
      color: 'bg-purple-100 border-purple-300',
      description: 'Transform or filter data',
      inputs: ['input'],
      outputs: ['output', 'filtered']
    },
    {
      type: 'condition',
      name: 'Condition',
      icon: <Filter size={16} />,
      color: 'bg-yellow-100 border-yellow-300',
      description: 'Branch based on conditions',
      inputs: ['input'],
      outputs: ['true', 'false']
    },
    {
      type: 'code',
      name: 'Custom Code',
      icon: <Code size={16} />,
      color: 'bg-gray-100 border-gray-300',
      description: 'Execute custom JavaScript',
      inputs: ['input'],
      outputs: ['output', 'error']
    },
    {
      type: 'sentiment_analysis',
      name: 'Sentiment Analysis',
      icon: <Target size={16} />,
      color: 'bg-pink-100 border-pink-300',
      description: 'Analyze sentiment of text',
      inputs: ['text'],
      outputs: ['sentiment', 'confidence']
    }
  ];

  // Handle drag and drop from node library
  const handleDragStart = (e, nodeType) => {
    setDraggedNode(nodeType);
    e.dataTransfer.effectAllowed = 'copy';
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  };

  const handleDrop = (e) => {
    e.preventDefault();
    if (!draggedNode) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const x = (e.clientX - rect.left - pan.x) / zoom;
    const y = (e.clientY - rect.top - pan.y) / zoom;

    const newNode = {
      id: `node_${Date.now()}`,
      type: draggedNode.type,
      name: draggedNode.name,
      position: { x, y },
      config: getDefaultConfig(draggedNode.type),
      inputs: draggedNode.inputs,
      outputs: draggedNode.outputs
    };

    setWorkflow(prev => ({
      ...prev,
      nodes: [...prev.nodes, newNode]
    }));

    setDraggedNode(null);
    showNotification(`Added ${draggedNode.name} node`, 'success');
  };

  // Get default configuration for node types
  const getDefaultConfig = (nodeType) => {
    switch (nodeType) {
      case 'llm_call':
        return {
          model: 'gpt-4',
          prompt: 'Enter your prompt here...',
          temperature: 0.7,
          max_tokens: 1000
        };
      case 'condition':
        return {
          condition: 'input.length > 0',
          operator: 'javascript'
        };
      case 'data_transform':
        return {
          transformation: 'return input.toUpperCase();',
          language: 'javascript'
        };
      case 'sentiment_analysis':
        return {
          source: 'user_input',
          include_emotions: true
        };
      case 'code':
        return {
          code: '// Your custom code here\nreturn input;',
          language: 'javascript'
        };
      default:
        return {};
    }
  };

  // Handle node selection
  const handleNodeClick = (node) => {
    setSelectedNode(node);
  };

  // Handle node deletion
  const handleDeleteNode = (nodeId) => {
    setWorkflow(prev => ({
      ...prev,
      nodes: prev.nodes.filter(n => n.id !== nodeId),
      connections: prev.connections.filter(c => c.from !== nodeId && c.to !== nodeId)
    }));
    if (selectedNode?.id === nodeId) {
      setSelectedNode(null);
    }
    showNotification('Node deleted', 'info');
  };

  // Handle connection creation
  const handleStartConnection = (nodeId, outputPort) => {
    setIsConnecting(true);
    setConnectionStart({ nodeId, port: outputPort });
  };

  const handleEndConnection = (nodeId, inputPort) => {
    if (!isConnecting || !connectionStart) return;

    const newConnection = {
      id: `conn_${Date.now()}`,
      from: connectionStart.nodeId,
      fromPort: connectionStart.port,
      to: nodeId,
      toPort: inputPort
    };

    setWorkflow(prev => ({
      ...prev,
      connections: [...prev.connections, newConnection]
    }));

    setIsConnecting(false);
    setConnectionStart(null);
    showNotification('Connection created', 'success');
  };

  // Save workflow
  const handleSave = () => {
    if (!workflow.name.trim()) {
      showNotification('Please enter a workflow name', 'error');
      return;
    }

    onSave(workflow);
    showNotification('Workflow saved successfully', 'success');
  };

  // Execute workflow
  const handleExecute = async () => {
    try {
      showNotification('Executing workflow...', 'info');
      
      // Simulate workflow execution
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      showNotification('Workflow executed successfully', 'success');
    } catch (error) {
      showNotification('Workflow execution failed', 'error');
    }
  };

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Node Library Sidebar */}
      {showNodeLibrary && (
        <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Node Library</h3>
            <p className="text-sm text-gray-600">Drag nodes to canvas</p>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-2">
              {nodeTypes.map((nodeType) => (
                <div
                  key={nodeType.type}
                  draggable
                  onDragStart={(e) => handleDragStart(e, nodeType)}
                  className={`p-3 rounded-lg border-2 border-dashed cursor-move hover:shadow-md transition-shadow ${nodeType.color}`}
                >
                  <div className="flex items-center space-x-2">
                    {nodeType.icon}
                    <span className="font-medium text-sm">{nodeType.name}</span>
                  </div>
                  <p className="text-xs text-gray-600 mt-1">{nodeType.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Main Canvas Area */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <input
                type="text"
                value={workflow.name}
                onChange={(e) => setWorkflow(prev => ({ ...prev, name: e.target.value }))}
                className="text-lg font-semibold bg-transparent border-none focus:outline-none focus:ring-2 focus:ring-blue-500 rounded px-2"
                placeholder="Workflow Name"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowNodeLibrary(!showNodeLibrary)}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
                title="Toggle Node Library"
              >
                <Layers size={20} />
              </button>
              
              <button
                onClick={() => setShowProperties(!showProperties)}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
                title="Toggle Properties"
              >
                <Settings size={20} />
              </button>
              
              <div className="w-px h-6 bg-gray-300"></div>
              
              <button
                onClick={handleSave}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <Save size={16} className="mr-2" />
                Save
              </button>
              
              <button
                onClick={handleExecute}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
              >
                <Play size={16} className="mr-2" />
                Execute
              </button>
            </div>
          </div>
        </div>

        {/* Canvas */}
        <div className="flex-1 relative overflow-hidden">
          <div
            ref={canvasRef}
            className="w-full h-full bg-gray-50"
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            style={{
              backgroundImage: `radial-gradient(circle, #e5e7eb 1px, transparent 1px)`,
              backgroundSize: `${20 * zoom}px ${20 * zoom}px`,
              backgroundPosition: `${pan.x}px ${pan.y}px`
            }}
          >
            {/* Render nodes */}
            {workflow.nodes.map((node) => (
              <WorkflowNode
                key={node.id}
                node={node}
                zoom={zoom}
                pan={pan}
                selected={selectedNode?.id === node.id}
                onClick={() => handleNodeClick(node)}
                onDelete={() => handleDeleteNode(node.id)}
                onStartConnection={handleStartConnection}
                onEndConnection={handleEndConnection}
                isConnecting={isConnecting}
              />
            ))}

            {/* Render connections */}
            <svg className="absolute inset-0 pointer-events-none">
              {workflow.connections.map((connection) => {
                const fromNode = workflow.nodes.find(n => n.id === connection.from);
                const toNode = workflow.nodes.find(n => n.id === connection.to);
                
                if (!fromNode || !toNode) return null;

                const fromX = (fromNode.position.x + 120) * zoom + pan.x;
                const fromY = (fromNode.position.y + 40) * zoom + pan.y;
                const toX = toNode.position.x * zoom + pan.x;
                const toY = (toNode.position.y + 40) * zoom + pan.y;

                return (
                  <line
                    key={connection.id}
                    x1={fromX}
                    y1={fromY}
                    x2={toX}
                    y2={toY}
                    stroke="#6366f1"
                    strokeWidth="2"
                    markerEnd="url(#arrowhead)"
                  />
                );
              })}
              
              {/* Arrow marker definition */}
              <defs>
                <marker
                  id="arrowhead"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                >
                  <polygon
                    points="0 0, 10 3.5, 0 7"
                    fill="#6366f1"
                  />
                </marker>
              </defs>
            </svg>
          </div>
        </div>
      </div>

      {/* Properties Sidebar */}
      {showProperties && selectedNode && (
        <NodePropertiesPanel
          node={selectedNode}
          onChange={(updatedNode) => {
            setWorkflow(prev => ({
              ...prev,
              nodes: prev.nodes.map(n => n.id === updatedNode.id ? updatedNode : n)
            }));
            setSelectedNode(updatedNode);
          }}
        />
      )}
    </div>
  );
};

export default NoCodeWorkflowBuilder;
