import React, { useState, useEffect } from 'react';
import {
  Shield, CheckCircle, AlertTriangle, Clock, Settings, Play, Pause,
  FileText, Users, Lock, Eye, TrendingUp, BarChart3, Zap, RefreshCw,
  Download, Upload, Filter, Search, Plus, Edit, Trash2
} from 'lucide-react';

const SimplifiedGovernanceDashboard = ({ showNotification, onFormStateChange }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [complianceStatus, setComplianceStatus] = useState(null);
  const [policies, setPolicies] = useState([]);
  const [auditLogs, setAuditLogs] = useState([]);
  const [riskAssessment, setRiskAssessment] = useState(null);
  const [automatedWorkflows, setAutomatedWorkflows] = useState([]);
  const [loading, setLoading] = useState(true);
  const [autoComplianceEnabled, setAutoComplianceEnabled] = useState(true);

  useEffect(() => {
    fetchGovernanceData();
  }, []);

  const fetchGovernanceData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        fetchComplianceStatus(),
        fetchPolicies(),
        fetchAuditLogs(),
        fetchRiskAssessment(),
        fetchAutomatedWorkflows()
      ]);
    } catch (error) {
      showNotification('Failed to fetch governance data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const fetchComplianceStatus = async () => {
    try {
      const response = await fetch('/api/governance/compliance/status');
      const data = await response.json();
      setComplianceStatus(data);
    } catch (error) {
      console.error('Failed to fetch compliance status:', error);
      setComplianceStatus(getMockComplianceStatus());
    }
  };

  const fetchPolicies = async () => {
    try {
      const response = await fetch('/api/governance/policies');
      const data = await response.json();
      setPolicies(data.policies || []);
    } catch (error) {
      console.error('Failed to fetch policies:', error);
      setPolicies(getMockPolicies());
    }
  };

  const fetchAuditLogs = async () => {
    try {
      const response = await fetch('/api/governance/audit-logs');
      const data = await response.json();
      setAuditLogs(data.logs || []);
    } catch (error) {
      console.error('Failed to fetch audit logs:', error);
      setAuditLogs(getMockAuditLogs());
    }
  };

  const fetchRiskAssessment = async () => {
    try {
      const response = await fetch('/api/governance/risk-assessment');
      const data = await response.json();
      setRiskAssessment(data);
    } catch (error) {
      console.error('Failed to fetch risk assessment:', error);
      setRiskAssessment(getMockRiskAssessment());
    }
  };

  const fetchAutomatedWorkflows = async () => {
    try {
      const response = await fetch('/api/governance/workflows');
      const data = await response.json();
      setAutomatedWorkflows(data.workflows || []);
    } catch (error) {
      console.error('Failed to fetch workflows:', error);
      setAutomatedWorkflows(getMockWorkflows());
    }
  };

  const handleToggleAutoCompliance = async () => {
    try {
      const response = await fetch('/api/governance/auto-compliance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled: !autoComplianceEnabled })
      });

      if (response.ok) {
        setAutoComplianceEnabled(!autoComplianceEnabled);
        showNotification(
          `Auto-compliance ${!autoComplianceEnabled ? 'enabled' : 'disabled'}`,
          'success'
        );
      }
    } catch (error) {
      showNotification('Failed to toggle auto-compliance', 'error');
    }
  };

  const handleRunComplianceCheck = async () => {
    try {
      showNotification('Running compliance check...', 'info');
      const response = await fetch('/api/governance/compliance/check', {
        method: 'POST'
      });

      if (response.ok) {
        const result = await response.json();
        showNotification('Compliance check completed', 'success');
        fetchComplianceStatus();
      }
    } catch (error) {
      showNotification('Compliance check failed', 'error');
    }
  };

  const getComplianceScore = () => {
    if (!complianceStatus) return 0;
    const { passed, total } = complianceStatus.summary;
    return total > 0 ? Math.round((passed / total) * 100) : 0;
  };

  const getScoreColor = (score) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Simplified Governance</h2>
          <p className="text-gray-600">Automated compliance and streamlined policy management</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleToggleAutoCompliance}
            className={`inline-flex items-center px-4 py-2 border rounded-md text-sm font-medium ${
              autoComplianceEnabled
                ? 'border-green-300 text-green-700 bg-green-50 hover:bg-green-100'
                : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
            }`}
          >
            {autoComplianceEnabled ? <Pause size={16} className="mr-2" /> : <Play size={16} className="mr-2" />}
            Auto-Compliance {autoComplianceEnabled ? 'ON' : 'OFF'}
          </button>
          <button
            onClick={handleRunComplianceCheck}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <Shield size={16} className="mr-2" />
            Run Check
          </button>
        </div>
      </div>

      {/* Compliance Score Card */}
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Compliance Score</h3>
            <p className="text-sm text-gray-600">Overall compliance status across all frameworks</p>
          </div>
          <div className="text-right">
            <div className={`text-4xl font-bold ${getScoreColor(getComplianceScore())}`}>
              {getComplianceScore()}%
            </div>
            <p className="text-sm text-gray-500">
              {complianceStatus?.summary.passed || 0} of {complianceStatus?.summary.total || 0} checks passed
            </p>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="mt-4">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${
                getComplianceScore() >= 90 ? 'bg-green-500' :
                getComplianceScore() >= 70 ? 'bg-yellow-500' : 'bg-red-500'
              }`}
              style={{ width: `${getComplianceScore()}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <QuickActionCard
          icon={<FileText size={24} className="text-blue-600" />}
          title="Policy Templates"
          description="Pre-built compliance policies"
          action="Browse Templates"
          onClick={() => setActiveTab('policies')}
        />
        <QuickActionCard
          icon={<Zap size={24} className="text-green-600" />}
          title="Auto-Remediation"
          description="Automated issue resolution"
          action="Configure"
          onClick={() => setActiveTab('workflows')}
        />
        <QuickActionCard
          icon={<BarChart3 size={24} className="text-purple-600" />}
          title="Risk Assessment"
          description="AI risk evaluation"
          action="View Report"
          onClick={() => setActiveTab('risk')}
        />
        <QuickActionCard
          icon={<Download size={24} className="text-gray-600" />}
          title="Compliance Report"
          description="Generate audit report"
          action="Download"
          onClick={() => {
            showNotification('Generating compliance report...', 'info');
            // Simulate report generation
            setTimeout(() => {
              showNotification('Report downloaded successfully', 'success');
            }, 2000);
          }}
        />
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Overview', icon: BarChart3 },
            { id: 'policies', name: 'Smart Policies', icon: FileText },
            { id: 'workflows', name: 'Auto-Workflows', icon: Zap },
            { id: 'risk', name: 'Risk Monitor', icon: AlertTriangle },
            { id: 'audit', name: 'Audit Trail', icon: Eye }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon size={16} className="mr-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <GovernanceOverview 
          complianceStatus={complianceStatus}
          riskAssessment={riskAssessment}
          recentLogs={auditLogs.slice(0, 5)}
        />
      )}

      {activeTab === 'policies' && (
        <SmartPolicies 
          policies={policies}
          onCreatePolicy={() => showNotification('Policy creation wizard opened', 'info')}
          onEditPolicy={(policy) => showNotification(`Editing policy: ${policy.name}`, 'info')}
          showNotification={showNotification}
        />
      )}

      {activeTab === 'workflows' && (
        <AutomatedWorkflows 
          workflows={automatedWorkflows}
          onCreateWorkflow={() => showNotification('Workflow builder opened', 'info')}
          onToggleWorkflow={(workflow) => {
            showNotification(`Workflow ${workflow.enabled ? 'disabled' : 'enabled'}`, 'success');
          }}
          showNotification={showNotification}
        />
      )}

      {activeTab === 'risk' && (
        <RiskMonitor 
          riskAssessment={riskAssessment}
          onRunAssessment={() => {
            showNotification('Running risk assessment...', 'info');
            setTimeout(() => {
              showNotification('Risk assessment completed', 'success');
              fetchRiskAssessment();
            }, 3000);
          }}
        />
      )}

      {activeTab === 'audit' && (
        <AuditTrail 
          auditLogs={auditLogs}
          onExportLogs={() => showNotification('Audit logs exported', 'success')}
        />
      )}
    </div>
  );
};

// Quick Action Card Component
const QuickActionCard = ({ icon, title, description, action, onClick }) => (
  <div className="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer" onClick={onClick}>
    <div className="flex items-center space-x-3">
      <div className="flex-shrink-0">{icon}</div>
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-medium text-gray-900">{title}</h4>
        <p className="text-xs text-gray-500">{description}</p>
        <button className="text-xs text-blue-600 hover:text-blue-800 mt-1">{action}</button>
      </div>
    </div>
  </div>
);

// Mock data functions
const getMockComplianceStatus = () => ({
  summary: { passed: 18, failed: 2, total: 20 },
  frameworks: {
    'EU AI Act': { score: 95, status: 'compliant' },
    'NIST AI RMF': { score: 88, status: 'compliant' },
    'ISO 27001': { score: 92, status: 'compliant' },
    'GDPR': { score: 85, status: 'needs_attention' }
  },
  last_check: new Date(Date.now() - 2 * 60 * 60 * 1000)
});

const getMockPolicies = () => [
  {
    id: 'policy_1',
    name: 'AI Model Bias Prevention',
    type: 'automated',
    status: 'active',
    compliance_frameworks: ['EU AI Act', 'NIST AI RMF'],
    last_updated: new Date(Date.now() - 24 * 60 * 60 * 1000),
    violations: 0
  },
  {
    id: 'policy_2',
    name: 'Data Privacy Protection',
    type: 'manual',
    status: 'active',
    compliance_frameworks: ['GDPR', 'ISO 27001'],
    last_updated: new Date(Date.now() - 48 * 60 * 60 * 1000),
    violations: 1
  }
];

const getMockAuditLogs = () => [
  {
    id: 'log_1',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    action: 'Policy Violation Detected',
    user: 'system',
    details: 'Bias threshold exceeded in model response',
    severity: 'medium'
  },
  {
    id: 'log_2',
    timestamp: new Date(Date.now() - 60 * 60 * 1000),
    action: 'Compliance Check Passed',
    user: 'auto-compliance',
    details: 'All EU AI Act requirements met',
    severity: 'info'
  }
];

const getMockRiskAssessment = () => ({
  overall_risk: 'low',
  risk_score: 25,
  categories: {
    'Bias Risk': { score: 20, trend: 'decreasing' },
    'Privacy Risk': { score: 15, trend: 'stable' },
    'Security Risk': { score: 30, trend: 'decreasing' },
    'Compliance Risk': { score: 35, trend: 'stable' }
  },
  recommendations: [
    'Implement additional bias testing for customer service models',
    'Review data retention policies for GDPR compliance',
    'Update security protocols for model deployment'
  ]
});

const getMockWorkflows = () => [
  {
    id: 'workflow_1',
    name: 'Auto-Bias Detection & Remediation',
    description: 'Automatically detect and remediate bias in AI responses',
    enabled: true,
    trigger: 'model_response',
    actions: ['detect_bias', 'flag_response', 'notify_admin'],
    last_run: new Date(Date.now() - 15 * 60 * 1000),
    success_rate: 98
  },
  {
    id: 'workflow_2',
    name: 'Privacy Data Anonymization',
    description: 'Automatically anonymize PII in training data',
    enabled: true,
    trigger: 'data_ingestion',
    actions: ['scan_pii', 'anonymize', 'log_action'],
    last_run: new Date(Date.now() - 45 * 60 * 1000),
    success_rate: 100
  }
];

export default SimplifiedGovernanceDashboard;
