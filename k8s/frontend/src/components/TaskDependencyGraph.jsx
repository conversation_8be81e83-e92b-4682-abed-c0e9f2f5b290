import React, { useMemo, useRef, useEffect, useState } from 'react';
import { getTaskStatusColor, TaskStatus } from '../types/planning';

const TaskDependencyGraph = ({ tasks = [], dependencies = [], className = '' }) => {
  const svgRef = useRef(null);
  const [dimensions, setDimensions] = useState({ width: 800, height: 600 });

  // Calculate layout for tasks and dependencies
  const layout = useMemo(() => {
    if (!tasks.length) return { nodes: [], edges: [] };

    // Create a map of task dependencies
    const taskDeps = new Map();
    const taskChildren = new Map();
    
    tasks.forEach(task => {
      taskDeps.set(task.id, task.dependencies || []);
      taskChildren.set(task.id, []);
    });

    // Build children map
    tasks.forEach(task => {
      (task.dependencies || []).forEach(depId => {
        if (taskChildren.has(depId)) {
          taskChildren.get(depId).push(task.id);
        }
      });
    });

    // Calculate levels (topological sort)
    const levels = new Map();
    const visited = new Set();
    const visiting = new Set();

    const calculateLevel = (taskId) => {
      if (visiting.has(taskId)) {
        // Circular dependency detected
        return 0;
      }
      if (visited.has(taskId)) {
        return levels.get(taskId) || 0;
      }

      visiting.add(taskId);
      const deps = taskDeps.get(taskId) || [];
      let maxDepLevel = -1;

      deps.forEach(depId => {
        if (tasks.find(t => t.id === depId)) {
          maxDepLevel = Math.max(maxDepLevel, calculateLevel(depId));
        }
      });

      const level = maxDepLevel + 1;
      levels.set(taskId, level);
      visiting.delete(taskId);
      visited.add(taskId);
      return level;
    };

    tasks.forEach(task => calculateLevel(task.id));

    // Group tasks by level
    const levelGroups = new Map();
    tasks.forEach(task => {
      const level = levels.get(task.id) || 0;
      if (!levelGroups.has(level)) {
        levelGroups.set(level, []);
      }
      levelGroups.get(level).push(task);
    });

    // Calculate positions
    const nodeWidth = 180;
    const nodeHeight = 60;
    const levelSpacing = 200;
    const nodeSpacing = 80;
    const maxLevel = Math.max(...levels.values());
    
    const totalWidth = (maxLevel + 1) * levelSpacing + nodeWidth;
    const nodes = [];

    levelGroups.forEach((tasksInLevel, level) => {
      const totalHeight = tasksInLevel.length * (nodeHeight + nodeSpacing) - nodeSpacing;
      const startY = (dimensions.height - totalHeight) / 2;

      tasksInLevel.forEach((task, index) => {
        nodes.push({
          id: task.id,
          task,
          x: level * levelSpacing + 50,
          y: startY + index * (nodeHeight + nodeSpacing),
          width: nodeWidth,
          height: nodeHeight
        });
      });
    });

    // Calculate edges
    const nodeMap = new Map(nodes.map(n => [n.id, n]));
    const edges = [];

    tasks.forEach(task => {
      (task.dependencies || []).forEach(depId => {
        const fromNode = nodeMap.get(depId);
        const toNode = nodeMap.get(task.id);
        
        if (fromNode && toNode) {
          edges.push({
            from: depId,
            to: task.id,
            fromX: fromNode.x + fromNode.width,
            fromY: fromNode.y + fromNode.height / 2,
            toX: toNode.x,
            toY: toNode.y + toNode.height / 2
          });
        }
      });
    });

    return { nodes, edges, totalWidth };
  }, [tasks, dependencies, dimensions]);

  // Update dimensions on resize
  useEffect(() => {
    const updateDimensions = () => {
      if (svgRef.current) {
        const container = svgRef.current.parentElement;
        if (container) {
          setDimensions({
            width: Math.max(800, container.clientWidth),
            height: Math.max(600, Math.max(400, layout.nodes.length * 100))
          });
        }
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, [layout.nodes.length]);

  // Get status color for task node
  const getNodeColor = (status) => {
    switch (status) {
      case TaskStatus.COMPLETED:
        return { bg: '#10B981', border: '#059669', text: '#FFFFFF' };
      case TaskStatus.RUNNING:
        return { bg: '#3B82F6', border: '#2563EB', text: '#FFFFFF' };
      case TaskStatus.FAILED:
        return { bg: '#EF4444', border: '#DC2626', text: '#FFFFFF' };
      case TaskStatus.READY:
        return { bg: '#F59E0B', border: '#D97706', text: '#FFFFFF' };
      case TaskStatus.SKIPPED:
        return { bg: '#6B7280', border: '#4B5563', text: '#FFFFFF' };
      default:
        return { bg: '#E5E7EB', border: '#9CA3AF', text: '#374151' };
    }
  };

  if (!tasks.length) {
    return (
      <div className={`flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 ${className}`}>
        <div className="text-center">
          <div className="text-gray-400 mb-2">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-sm font-medium text-gray-900">No tasks to display</h3>
          <p className="text-sm text-gray-500">Tasks will appear here once a plan is generated.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 overflow-auto ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Task Dependency Graph</h3>
        <p className="text-sm text-gray-600">Visual representation of task execution flow and dependencies</p>
      </div>
      
      <div className="p-4" style={{ minHeight: '400px' }}>
        <svg
          ref={svgRef}
          width={Math.max(dimensions.width, layout.totalWidth || 800)}
          height={dimensions.height}
          className="w-full"
          style={{ minWidth: '800px' }}
        >
          {/* Define arrow marker */}
          <defs>
            <marker
              id="arrowhead"
              markerWidth="10"
              markerHeight="7"
              refX="9"
              refY="3.5"
              orient="auto"
            >
              <polygon
                points="0 0, 10 3.5, 0 7"
                fill="#6B7280"
              />
            </marker>
          </defs>

          {/* Render edges (dependencies) */}
          {layout.edges.map((edge, index) => (
            <g key={`edge-${index}`}>
              <line
                x1={edge.fromX}
                y1={edge.fromY}
                x2={edge.toX - 10}
                y2={edge.toY}
                stroke="#6B7280"
                strokeWidth="2"
                markerEnd="url(#arrowhead)"
              />
            </g>
          ))}

          {/* Render nodes (tasks) */}
          {layout.nodes.map((node) => {
            const colors = getNodeColor(node.task.status);
            const isLongName = node.task.name.length > 20;
            
            return (
              <g key={node.id}>
                {/* Task node */}
                <rect
                  x={node.x}
                  y={node.y}
                  width={node.width}
                  height={node.height}
                  fill={colors.bg}
                  stroke={colors.border}
                  strokeWidth="2"
                  rx="8"
                  className="drop-shadow-sm"
                />
                
                {/* Task name */}
                <text
                  x={node.x + node.width / 2}
                  y={node.y + (isLongName ? 20 : 25)}
                  textAnchor="middle"
                  fill={colors.text}
                  fontSize="12"
                  fontWeight="600"
                  className="pointer-events-none"
                >
                  {isLongName ? 
                    `${node.task.name.substring(0, 18)}...` : 
                    node.task.name
                  }
                </text>
                
                {/* Task status */}
                <text
                  x={node.x + node.width / 2}
                  y={node.y + (isLongName ? 35 : 40)}
                  textAnchor="middle"
                  fill={colors.text}
                  fontSize="10"
                  className="pointer-events-none"
                >
                  {node.task.status}
                </text>
                
                {/* Task type */}
                <text
                  x={node.x + node.width / 2}
                  y={node.y + (isLongName ? 50 : 52)}
                  textAnchor="middle"
                  fill={colors.text}
                  fontSize="9"
                  opacity="0.8"
                  className="pointer-events-none"
                >
                  {node.task.type}
                </text>
              </g>
            );
          })}
        </svg>
      </div>

      {/* Legend */}
      <div className="px-4 py-3 bg-gray-50 border-t border-gray-200">
        <div className="flex flex-wrap items-center gap-4 text-xs">
          <span className="font-medium text-gray-700">Status:</span>
          {Object.values(TaskStatus).map(status => {
            const colors = getNodeColor(status);
            return (
              <div key={status} className="flex items-center gap-1">
                <div
                  className="w-3 h-3 rounded border"
                  style={{ 
                    backgroundColor: colors.bg, 
                    borderColor: colors.border 
                  }}
                />
                <span className="text-gray-600 capitalize">{status}</span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default TaskDependencyGraph;
