import React, { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  Star,
  Clock,
  DollarSign,
  Users,
  Eye,
  Copy,
  Download,
  Tag,
  BarChart3,
  Edit,
  FileText,
  Zap,
  CheckCircle,
  TrendingUp,
  Grid,
  List
} from 'lucide-react';

const WorkflowTemplatesLibrary = ({ onSelectTemplate, onClose, showNotification }) => {
  const [templates, setTemplates] = useState([]);
  const [categories, setCategories] = useState([]);
  const [filteredTemplates, setFilteredTemplates] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchTemplatesAndCategories();
  }, []);

  useEffect(() => {
    filterTemplates();
  }, [templates, selectedCategory, searchQuery]);

  const fetchTemplatesAndCategories = async () => {
    setLoading(true);
    try {
      // Mock data for demonstration
      const mockCategories = [
        {
          id: 'data-analytics',
          name: 'Data Analytics',
          description: 'Templates for data analysis, processing, and visualization workflows',
          icon: 'bar-chart',
          popular: true,
          template_count: 5
        },
        {
          id: 'content-creation',
          name: 'Content Creation',
          description: 'Templates for content writing, editing, and publishing workflows',
          icon: 'edit',
          popular: true,
          template_count: 8
        },
        {
          id: 'research-validation',
          name: 'Research & Validation',
          description: 'Templates for research, fact-checking, and validation workflows',
          icon: 'search',
          popular: false,
          template_count: 3
        },
        {
          id: 'automation',
          name: 'Process Automation',
          description: 'Templates for automating business processes and workflows',
          icon: 'zap',
          popular: true,
          template_count: 6
        },
        {
          id: 'quality-assurance',
          name: 'Quality Assurance',
          description: 'Templates for testing, validation, and quality control workflows',
          icon: 'check-circle',
          popular: false,
          template_count: 4
        }
      ];

      const mockTemplates = [
        {
          id: 'comprehensive-data-analysis',
          name: 'Comprehensive Data Analysis Pipeline',
          description: 'End-to-end data analysis workflow with collection, cleaning, analysis, and reporting',
          category: 'data-analytics',
          complexity: 'intermediate',
          estimated_cost: 4.30,
          estimated_time: 60,
          required_agents: 5,
          popularity_score: 4.8,
          usage_count: 234,
          tags: ['data', 'analysis', 'reporting', 'visualization'],
          created_at: new Date('2024-01-15'),
          updated_at: new Date('2024-01-20')
        },
        {
          id: 'blog-post-creation',
          name: 'Blog Post Creation Workflow',
          description: 'Complete blog post creation from research to publication',
          category: 'content-creation',
          complexity: 'simple',
          estimated_cost: 3.00,
          estimated_time: 90,
          required_agents: 4,
          popularity_score: 4.6,
          usage_count: 189,
          tags: ['content', 'writing', 'seo', 'publishing'],
          created_at: new Date('2024-01-10'),
          updated_at: new Date('2024-01-18')
        },
        {
          id: 'fact-checking-workflow',
          name: 'Comprehensive Fact-Checking Workflow',
          description: 'Multi-source fact-checking and validation process',
          category: 'research-validation',
          complexity: 'advanced',
          estimated_cost: 3.40,
          estimated_time: 45,
          required_agents: 4,
          popularity_score: 4.4,
          usage_count: 67,
          tags: ['research', 'validation', 'fact-check', 'sources'],
          created_at: new Date('2024-01-12'),
          updated_at: new Date('2024-01-16')
        },
        {
          id: 'customer-support-automation',
          name: 'Customer Support Automation',
          description: 'Automated customer support workflow with escalation handling',
          category: 'automation',
          complexity: 'intermediate',
          estimated_cost: 2.80,
          estimated_time: 30,
          required_agents: 3,
          popularity_score: 4.7,
          usage_count: 156,
          tags: ['customer-service', 'automation', 'escalation'],
          created_at: new Date('2024-01-08'),
          updated_at: new Date('2024-01-22')
        },
        {
          id: 'code-review-pipeline',
          name: 'Automated Code Review Pipeline',
          description: 'Comprehensive code review with testing and quality checks',
          category: 'quality-assurance',
          complexity: 'advanced',
          estimated_cost: 5.20,
          estimated_time: 120,
          required_agents: 6,
          popularity_score: 4.9,
          usage_count: 98,
          tags: ['code', 'review', 'testing', 'quality'],
          created_at: new Date('2024-01-05'),
          updated_at: new Date('2024-01-19')
        }
      ];

      setCategories(mockCategories);
      setTemplates(mockTemplates);
    } catch (error) {
      console.error('Failed to fetch templates:', error);
      showNotification('Failed to load templates', 'error');
    } finally {
      setLoading(false);
    }
  };

  const filterTemplates = () => {
    let filtered = templates;

    // Category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query) ||
        template.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Sort by popularity
    filtered.sort((a, b) => b.popularity_score - a.popularity_score);

    setFilteredTemplates(filtered);
  };

  const handleUseTemplate = (template) => {
    onSelectTemplate(template);
    onClose();
  };

  const getCategoryIcon = (iconName) => {
    const icons = {
      'bar-chart': <BarChart3 size={20} />,
      'edit': <Edit size={20} />,
      'search': <Eye size={20} />,
      'zap': <Zap size={20} />,
      'check-circle': <CheckCircle size={20} />
    };
    return icons[iconName] || <FileText size={20} />;
  };

  const getComplexityColor = (complexity) => {
    const colors = {
      simple: 'bg-green-100 text-green-800',
      intermediate: 'bg-yellow-100 text-yellow-800',
      advanced: 'bg-red-100 text-red-800'
    };
    return colors[complexity] || 'bg-gray-100 text-gray-800';
  };

  const TemplateCard = ({ template }) => (
    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
            {getCategoryIcon(categories.find(c => c.id === template.category)?.icon)}
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{template.name}</h3>
            <p className="text-sm text-gray-500 capitalize">{template.category.replace('-', ' ')}</p>
          </div>
        </div>
        <div className="flex items-center space-x-1">
          <Star size={16} className="text-yellow-400 fill-current" />
          <span className="text-sm font-medium">{template.popularity_score}</span>
        </div>
      </div>

      <p className="text-gray-600 text-sm mb-4 line-clamp-2">{template.description}</p>

      {/* Complexity and Tags */}
      <div className="flex items-center justify-between mb-4">
        <span className={`px-2 py-1 text-xs rounded-full ${getComplexityColor(template.complexity)}`}>
          {template.complexity}
        </span>
        <div className="flex flex-wrap gap-1">
          {template.tags.slice(0, 2).map(tag => (
            <span key={tag} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
              {tag}
            </span>
          ))}
          {template.tags.length > 2 && (
            <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
              +{template.tags.length - 2}
            </span>
          )}
        </div>
      </div>

      {/* Metrics */}
      <div className="grid grid-cols-3 gap-2 mb-4 text-xs">
        <div className="text-center">
          <div className="font-medium text-gray-900">${template.estimated_cost.toFixed(2)}</div>
          <div className="text-gray-500">Cost</div>
        </div>
        <div className="text-center">
          <div className="font-medium text-gray-900">{template.estimated_time}m</div>
          <div className="text-gray-500">Time</div>
        </div>
        <div className="text-center">
          <div className="font-medium text-gray-900">{template.required_agents}</div>
          <div className="text-gray-500">Agents</div>
        </div>
      </div>

      {/* Usage Stats */}
      <div className="flex items-center justify-between mb-4 text-sm text-gray-500">
        <div className="flex items-center space-x-1">
          <TrendingUp size={14} />
          <span>{template.usage_count} uses</span>
        </div>
        <span>Updated {template.updated_at.toLocaleDateString()}</span>
      </div>

      {/* Actions */}
      <div className="flex space-x-2">
        <button
          onClick={() => setSelectedTemplate(template)}
          className="flex-1 px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <Eye size={14} className="inline mr-1" />
          Preview
        </button>
        <button
          onClick={() => handleUseTemplate(template)}
          className="flex-1 px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
        >
          <Copy size={14} className="inline mr-1" />
          Use Template
        </button>
      </div>
    </div>
  );

  const TemplateListItem = ({ template }) => (
    <div className="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
            {getCategoryIcon(categories.find(c => c.id === template.category)?.icon)}
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900">{template.name}</h3>
            <p className="text-sm text-gray-600">{template.description}</p>
            <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
              <span>${template.estimated_cost.toFixed(2)}</span>
              <span>{template.estimated_time}m</span>
              <span>{template.required_agents} agents</span>
              <span className={`px-2 py-1 rounded-full ${getComplexityColor(template.complexity)}`}>
                {template.complexity}
              </span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1 text-sm text-gray-500">
            <Star size={14} className="text-yellow-400 fill-current" />
            <span>{template.popularity_score}</span>
          </div>
          <button
            onClick={() => setSelectedTemplate(template)}
            className="px-3 py-1 border border-gray-300 text-sm rounded-md text-gray-700 hover:bg-gray-50"
          >
            Preview
          </button>
          <button
            onClick={() => handleUseTemplate(template)}
            className="px-3 py-1 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700"
          >
            Use
          </button>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-100 rounded-lg shadow-xl w-full max-w-7xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="bg-white border-b px-6 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Workflow Templates Library</h2>
              <p className="text-gray-600">Choose from pre-built workflow templates</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>

          {/* Search and Controls */}
          <div className="mt-4 flex space-x-4">
            <div className="flex-1 relative">
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 border rounded-lg ${viewMode === 'grid' ? 'bg-indigo-50 border-indigo-300 text-indigo-700' : 'border-gray-300 text-gray-700'}`}
              >
                <Grid size={16} />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 border rounded-lg ${viewMode === 'list' ? 'bg-indigo-50 border-indigo-300 text-indigo-700' : 'border-gray-300 text-gray-700'}`}
              >
                <List size={16} />
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Categories Sidebar */}
          <div className="w-80 bg-white border-r p-6 overflow-y-auto">
            <h3 className="font-semibold text-gray-900 mb-4">Categories</h3>
            <div className="space-y-2">
              <button
                onClick={() => setSelectedCategory('all')}
                className={`w-full text-left px-3 py-2 rounded-lg ${
                  selectedCategory === 'all' ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                All Templates ({templates.length})
              </button>
              {categories.map(category => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`w-full text-left px-3 py-2 rounded-lg ${
                    selectedCategory === category.id ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    {getCategoryIcon(category.icon)}
                    <div>
                      <div className="font-medium">{category.name}</div>
                      <div className="text-xs text-gray-500">{category.template_count} templates</div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Templates */}
          <div className="flex-1 p-6 overflow-y-auto">
            <div className="mb-4 flex justify-between items-center">
              <p className="text-gray-600">
                {filteredTemplates.length} templates found
              </p>
            </div>

            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredTemplates.map(template => (
                  <TemplateCard key={template.id} template={template} />
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredTemplates.map(template => (
                  <TemplateListItem key={template.id} template={template} />
                ))}
              </div>
            )}

            {filteredTemplates.length === 0 && (
              <div className="text-center py-12">
                <FileText size={48} className="mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
                <p className="text-gray-500">Try adjusting your search or category filter</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Template Preview Modal */}
      {selectedTemplate && (
        <TemplatePreviewModal
          template={selectedTemplate}
          onClose={() => setSelectedTemplate(null)}
          onUse={handleUseTemplate}
        />
      )}
    </div>
  );
};

// Template Preview Modal Component
const TemplatePreviewModal = ({ template, onClose, onUse }) => (
  <div className="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-60">
    <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[80vh] overflow-y-auto">
      <div className="p-6">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{template.name}</h2>
            <p className="text-gray-600">{template.description}</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Info */}
          <div className="lg:col-span-2 space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Template Overview</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">${template.estimated_cost.toFixed(2)}</div>
                  <div className="text-sm text-blue-600">Estimated Cost</div>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{template.estimated_time}m</div>
                  <div className="text-sm text-green-600">Estimated Time</div>
                </div>
                <div className="p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{template.required_agents}</div>
                  <div className="text-sm text-purple-600">Required Agents</div>
                </div>
                <div className="p-4 bg-yellow-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">{template.usage_count}</div>
                  <div className="text-sm text-yellow-600">Times Used</div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {template.tags.map(tag => (
                  <span key={tag} className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-3">Details</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Category:</span>
                  <span className="capitalize">{template.category.replace('-', ' ')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Complexity:</span>
                  <span className="capitalize">{template.complexity}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Rating:</span>
                  <div className="flex items-center">
                    <Star size={14} className="text-yellow-400 fill-current mr-1" />
                    <span>{template.popularity_score}</span>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Created:</span>
                  <span>{template.created_at.toLocaleDateString()}</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <button
                onClick={() => onUse(template)}
                className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
              >
                <Copy size={16} className="inline mr-2" />
                Use This Template
              </button>
              <button
                onClick={onClose}
                className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Close Preview
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default WorkflowTemplatesLibrary;
