import React, { useState, useEffect } from 'react';
import {
  GitBranch, Clock, User, Tag, FileText, ArrowLeft, ArrowRight,
  Copy, RotateCcw, Eye, Edit3, Plus, Trash2, AlertCircle, CheckCircle
} from 'lucide-react';

const PromptVersionManager = ({ 
  promptId, 
  onClose, 
  promptOpsHook,
  showNotification 
}) => {
  const [versions, setVersions] = useState([]);
  const [selectedVersions, setSelectedVersions] = useState({ from: null, to: null });
  const [diff, setDiff] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showRollbackModal, setShowRollbackModal] = useState(false);
  const [rollbackVersion, setRollbackVersion] = useState(null);

  const { getPromptVersions, getPromptDiff, rollbackPrompt } = promptOpsHook;

  useEffect(() => {
    loadVersions();
  }, [promptId]);

  const loadVersions = async () => {
    try {
      setLoading(true);
      const versionData = await getPromptVersions(promptId);
      setVersions(versionData);
    } catch (error) {
      showNotification?.('Failed to load prompt versions', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleVersionSelect = (version, type) => {
    setSelectedVersions(prev => ({
      ...prev,
      [type]: version
    }));
  };

  const handleShowDiff = async () => {
    if (!selectedVersions.from || !selectedVersions.to) {
      showNotification?.('Please select two versions to compare', 'warning');
      return;
    }

    try {
      setLoading(true);
      const diffData = await getPromptDiff(
        promptId, 
        selectedVersions.from.version, 
        selectedVersions.to.version
      );
      setDiff(diffData);
    } catch (error) {
      showNotification?.('Failed to generate diff', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleRollback = async (changeLog) => {
    try {
      setLoading(true);
      await rollbackPrompt(promptId, rollbackVersion.version, changeLog);
      showNotification?.(`Successfully rolled back to version ${rollbackVersion.version}`, 'success');
      setShowRollbackModal(false);
      setRollbackVersion(null);
      await loadVersions();
    } catch (error) {
      showNotification?.('Failed to rollback prompt', 'error');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'deprecated': return 'bg-red-100 text-red-800';
      case 'archived': return 'bg-gray-100 text-gray-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  if (loading && versions.length === 0) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading versions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-6xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <GitBranch className="h-6 w-6 text-blue-600" />
            <h2 className="text-xl font-semibold">Version Manager</h2>
            <span className="text-sm text-gray-500">({promptId})</span>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <div className="flex-1 flex overflow-hidden">
          {/* Versions List */}
          <div className="w-1/2 border-r overflow-y-auto">
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium">Versions ({versions.length})</h3>
                <div className="flex space-x-2">
                  <button
                    onClick={handleShowDiff}
                    disabled={!selectedVersions.from || !selectedVersions.to}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded disabled:bg-gray-300"
                  >
                    Compare
                  </button>
                </div>
              </div>

              <div className="space-y-3">
                {versions.map((version) => (
                  <div
                    key={version.version}
                    className="border rounded-lg p-3 hover:bg-gray-50"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="font-mono text-sm font-medium">
                          v{version.version}
                        </span>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(version.status)}`}>
                          {version.status}
                        </span>
                        {version.tags?.includes('stable') && (
                          <Tag className="h-4 w-4 text-green-600" />
                        )}
                      </div>
                      <div className="flex space-x-1">
                        <button
                          onClick={() => handleVersionSelect(version, 'from')}
                          className={`px-2 py-1 text-xs rounded ${
                            selectedVersions.from?.version === version.version
                              ? 'bg-blue-600 text-white'
                              : 'bg-gray-200 text-gray-700'
                          }`}
                        >
                          From
                        </button>
                        <button
                          onClick={() => handleVersionSelect(version, 'to')}
                          className={`px-2 py-1 text-xs rounded ${
                            selectedVersions.to?.version === version.version
                              ? 'bg-green-600 text-white'
                              : 'bg-gray-200 text-gray-700'
                          }`}
                        >
                          To
                        </button>
                        <button
                          onClick={() => {
                            setRollbackVersion(version);
                            setShowRollbackModal(true);
                          }}
                          className="px-2 py-1 text-xs bg-orange-200 text-orange-700 rounded hover:bg-orange-300"
                        >
                          <RotateCcw className="h-3 w-3" />
                        </button>
                      </div>
                    </div>

                    <div className="text-sm text-gray-600 mb-2">
                      {version.change_log || 'No change log provided'}
                    </div>

                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center space-x-2">
                        <User className="h-3 w-3" />
                        <span>{version.owner}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-3 w-3" />
                        <span>{formatDate(version.created_at)}</span>
                      </div>
                    </div>

                    {version.variables && version.variables.length > 0 && (
                      <div className="mt-2 text-xs text-blue-600">
                        Variables: {version.variables.map(v => v.name).join(', ')}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Diff View */}
          <div className="w-1/2 overflow-y-auto">
            <div className="p-4">
              {diff ? (
                <div>
                  <h3 className="font-medium mb-4">
                    Diff: v{diff.from_version} → v{diff.to_version}
                  </h3>
                  
                  <div className="space-y-4">
                    <div className="bg-gray-50 p-3 rounded">
                      <h4 className="font-medium text-sm mb-2">Changes Summary</h4>
                      <div className="space-y-1 text-sm">
                        {Object.entries(diff.changes).map(([key, changed]) => (
                          <div key={key} className="flex items-center space-x-2">
                            {changed ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : (
                              <div className="h-4 w-4 rounded-full bg-gray-300" />
                            )}
                            <span className={changed ? 'text-green-700' : 'text-gray-500'}>
                              {key.replace('_', ' ')}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {diff.changes.content_changed && (
                      <div>
                        <h4 className="font-medium text-sm mb-2">Content Changes</h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <h5 className="text-xs font-medium text-red-600 mb-1">From (v{diff.from_version})</h5>
                            <div className="bg-red-50 p-2 rounded text-sm font-mono whitespace-pre-wrap">
                              {diff.from_prompt.content}
                            </div>
                          </div>
                          <div>
                            <h5 className="text-xs font-medium text-green-600 mb-1">To (v{diff.to_version})</h5>
                            <div className="bg-green-50 p-2 rounded text-sm font-mono whitespace-pre-wrap">
                              {diff.to_prompt.content}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <FileText className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>Select two versions to compare</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Rollback Modal */}
        {showRollbackModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-semibold mb-4">
                Rollback to v{rollbackVersion?.version}
              </h3>
              <p className="text-gray-600 mb-4">
                This will create a new version based on v{rollbackVersion?.version}. 
                Please provide a change log for this rollback.
              </p>
              <textarea
                placeholder="Describe why you're rolling back..."
                className="w-full p-3 border rounded-lg mb-4"
                rows={3}
                id="rollback-changelog"
              />
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowRollbackModal(false)}
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    const changeLog = document.getElementById('rollback-changelog').value;
                    handleRollback(changeLog);
                  }}
                  className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700"
                >
                  Rollback
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PromptVersionManager;
