import React, { useState, useEffect, useCallback } from 'react';
import { Shield, BarChart3, Sliders, FileText, Target, AlertTriangle, CheckCircle, XCircle, Clock, TrendingUp, Eye, Brain, Zap, Plus } from 'lucide-react';
import useResponsibleAiData from '../hooks/useResponsibleAiData';

const ResponsibleAiDashboard = ({ showNotification, onFormStateChange }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedModel, setSelectedModel] = useState('all');
  const {
    fairnessData,
    explainabilityData,
    robustnessData,
    complianceData,
    modelFactsheets,
    biasAudits,
    robustnessTests,
    complianceAssessments,
    loading,
    fetchResponsibleAiData,
    createBiasAudit,
    createRobustnessTest,
    createExplanation,
  } = useResponsibleAiData({ showNotification });

  useEffect(() => {
    fetchResponsibleAiData();
  }, [fetchResponsibleAiData]);

  // Notify parent when form state changes (placeholder for future forms)
  useEffect(() => {
    if (onFormStateChange) {
      onFormStateChange(false); // No forms currently open
    }
  }, [onFormStateChange]);

  const TabButton = ({ name, icon, label, count }) => (
    <button
      onClick={() => setActiveTab(name)}
      className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
        activeTab === name ? 'bg-indigo-600 text-white' : 'text-gray-600 hover:bg-gray-100'
      }`}
    >
      {icon}
      <span className="font-medium">{label}</span>
      {count !== undefined && (
        <span className={`px-2 py-1 text-xs rounded-full ${
          activeTab === name ? 'bg-indigo-500 text-white' : 'bg-gray-200 text-gray-600'
        }`}>
          {count}
        </span>
      )}
    </button>
  );

  const StatusBadge = ({ status, score }) => {
    const getStatusColor = (status) => {
      switch (status) {
        case 'pass':
        case 'compliant':
          return 'bg-green-100 text-green-800';
        case 'warning':
        case 'partial':
          return 'bg-yellow-100 text-yellow-800';
        case 'fail':
        case 'non_compliant':
          return 'bg-red-100 text-red-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    };

    return (
      <div className="flex items-center space-x-2">
        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(status)}`}>
          {status}
        </span>
        {score !== undefined && (
          <span className="text-sm text-gray-600">
            {(score * 100).toFixed(1)}%
          </span>
        )}
      </div>
    );
  };

  const MetricCard = ({ title, value, subtitle, icon, trend, color = 'indigo' }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className={`text-2xl font-semibold text-${color}-600`}>{value}</p>
          {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
        </div>
        <div className={`p-3 bg-${color}-100 rounded-lg`}>
          {icon}
        </div>
      </div>
      {trend && (
        <div className="mt-4 flex items-center">
          <TrendingUp className={`h-4 w-4 ${trend > 0 ? 'text-green-500' : 'text-red-500'}`} />
          <span className={`text-sm ml-1 ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
            {trend > 0 ? '+' : ''}{trend}% from last month
          </span>
        </div>
      )}
    </div>
  );

  const OverviewTab = () => {
    const totalModels = modelFactsheets.length;
    const avgFairnessScore = fairnessData.length > 0
      ? fairnessData.reduce((sum, item) => sum + item.equalOpportunity, 0) / fairnessData.length
      : 0;
    const avgRobustnessScore = robustnessData.length > 0
      ? robustnessData.reduce((sum, item) => sum + item.overallScore, 0) / robustnessData.length
      : 0;
    const avgComplianceScore = complianceData.length > 0
      ? complianceData.reduce((sum, item) => sum + item.avgScore, 0) / complianceData.length
      : 0;

    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="Total Models"
            value={totalModels}
            subtitle="Under governance"
            icon={<Brain className="h-6 w-6 text-indigo-600" />}
            trend={5}
          />
          <MetricCard
            title="Fairness Score"
            value={`${(avgFairnessScore * 100).toFixed(1)}%`}
            subtitle="Average across models"
            icon={<Sliders className="h-6 w-6 text-green-600" />}
            color="green"
            trend={2}
          />
          <MetricCard
            title="Robustness Score"
            value={`${(avgRobustnessScore * 100).toFixed(1)}%`}
            subtitle="Average security rating"
            icon={<Shield className="h-6 w-6 text-blue-600" />}
            color="blue"
            trend={-1}
          />
          <MetricCard
            title="Compliance Rate"
            value={`${(avgComplianceScore * 100).toFixed(1)}%`}
            subtitle="Regulatory adherence"
            icon={<CheckCircle className="h-6 w-6 text-purple-600" />}
            color="purple"
            trend={3}
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Assessments</h3>
            <div className="space-y-3">
              {fairnessData.slice(0, 3).map((item) => (
                <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{item.model}</p>
                    <p className="text-sm text-gray-600">Bias Assessment</p>
                  </div>
                  <StatusBadge status={item.status} score={item.equalOpportunity} />
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Compliance Status</h3>
            <div className="space-y-3">
              {complianceData.map((item) => (
                <div key={item.framework} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{item.framework}</p>
                    <p className="text-sm text-gray-600">{item.assessments} assessments</p>
                  </div>
                  <StatusBadge status={item.status} score={item.avgScore} />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const FairnessTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Bias Detection & Fairness Audits</h3>
        <button
          onClick={() => {
            createBiasAudit({
              model_id: selectedModel === 'all' ? 'gpt-4' : selectedModel,
              audit_type: 'comprehensive',
              protected_attributes: ['gender', 'race', 'age']
            });
          }}
          className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
        >
          <Plus className="h-4 w-4" />
          <span>New Audit</span>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {fairnessData.map((item) => (
          <div key={item.id} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-semibold text-gray-900">{item.model}</h4>
              <StatusBadge status={item.status} />
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Disparate Impact</span>
                <span className="text-sm font-medium">{item.disparateImpact?.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Equal Opportunity</span>
                <span className="text-sm font-medium">{(item.equalOpportunity * 100).toFixed(1)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Demographic Parity</span>
                <span className="text-sm font-medium">{(item.demographicParity * 100).toFixed(1)}%</span>
              </div>
              {item.remediationSuggestions > 0 && (
                <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    {item.remediationSuggestions} remediation suggestions available
                  </p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold text-gray-900">Responsible AI Dashboard</h2>
        <select
          value={selectedModel}
          onChange={(e) => setSelectedModel(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
        >
          <option value="all">All Models</option>
          {modelFactsheets.map((factsheet) => (
            <option key={factsheet.id} value={factsheet.model_name}>
              {factsheet.model_name}
            </option>
          ))}
        </select>
      </div>

      <div className="flex space-x-2 border-b border-gray-200">
        <TabButton name="overview" icon={<Shield size={20} />} label="Overview" />
        <TabButton name="fairness" icon={<Sliders size={20} />} label="Fairness Audits" count={fairnessData.length} />
        <TabButton name="explainability" icon={<FileText size={20} />} label="Explainability (XAI)" count={explainabilityData.length} />
        <TabButton name="robustness" icon={<Target size={20} />} label="Robustness" count={robustnessData.length} />
        <TabButton name="compliance" icon={<BarChart3 size={20} />} label="Compliance Status" count={complianceData.length} />
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      ) : (
        <div>
          {activeTab === 'overview' && <OverviewTab />}
          {activeTab === 'fairness' && <FairnessTab />}
          {activeTab === 'explainability' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Explainable AI (XAI) Results</h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {explainabilityData.map((item) => (
                  <div key={item.id} className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-semibold text-gray-900">{item.type}</h4>
                      <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">{item.method}</span>
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm text-gray-600">Model: {item.model}</p>
                      <p className="text-sm text-gray-600">Confidence: {(item.confidence * 100).toFixed(1)}%</p>
                      <p className="text-sm text-gray-600">Features: {item.featureCount}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          {activeTab === 'robustness' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Robustness Testing Results</h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {robustnessData.map((item) => (
                  <div key={item.id} className="bg-white rounded-lg shadow p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">{item.model}</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Overall Score</span>
                        <span className="text-sm font-medium">{(item.overallScore * 100).toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Adversarial</span>
                        <span className="text-sm font-medium">{(item.adversarialScore * 100).toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Noise Resistance</span>
                        <span className="text-sm font-medium">{(item.noiseScore * 100).toFixed(1)}%</span>
                      </div>
                      {item.vulnerabilities > 0 && (
                        <div className="mt-4 p-3 bg-red-50 rounded-lg">
                          <p className="text-sm text-red-800">
                            {item.vulnerabilities} vulnerabilities detected
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          {activeTab === 'compliance' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Regulatory Compliance Status</h3>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {complianceData.map((item) => (
                  <div key={item.framework} className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-semibold text-gray-900">{item.framework}</h4>
                      <StatusBadge status={item.status} />
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm text-gray-600">Score: {(item.avgScore * 100).toFixed(1)}%</p>
                      <p className="text-sm text-gray-600">Assessments: {item.assessments}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ResponsibleAiDashboard;