import React, { useState, useEffect } from 'react';
import {
  Play, Clock, DollarSign, <PERSON>h, Eye, AlertT<PERSON>gle, CheckCircle,
  BarChart3, Zap, Brain, <PERSON>ting<PERSON>, Copy, Download, RefreshCw
} from 'lucide-react';

const PromptPlayground = ({ 
  prompt, 
  onClose, 
  promptOpsHook,
  showNotification 
}) => {
  const [selectedModel, setSelectedModel] = useState('gpt-3.5-turbo');
  const [variables, setVariables] = useState({});
  const [executionResults, setExecutionResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [renderedPrompt, setRenderedPrompt] = useState('');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [executionSettings, setExecutionSettings] = useState({
    temperature: 0.7,
    max_tokens: 1000,
    top_p: 1.0,
    frequency_penalty: 0,
    presence_penalty: 0
  });

  const { executePromptWithModel } = promptOpsHook;

  const availableModels = [
    { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', provider: 'OpenAI', cost: 0.002 },
    { id: 'gpt-4', name: 'GPT-4', provider: 'OpenAI', cost: 0.03 },
    { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', provider: 'Anthropic', cost: 0.015 },
    { id: 'claude-3-haiku', name: 'Claude 3 Haiku', provider: 'Anthropic', cost: 0.0025 },
    { id: 'gemini-pro', name: 'Gemini Pro', provider: 'Google', cost: 0.001 }
  ];

  useEffect(() => {
    if (prompt?.variables) {
      const initialVariables = {};
      prompt.variables.forEach(variable => {
        initialVariables[variable.name] = variable.default_value || '';
      });
      setVariables(initialVariables);
    }
  }, [prompt]);

  useEffect(() => {
    updateRenderedPrompt();
  }, [prompt?.content, variables]);

  const updateRenderedPrompt = () => {
    if (!prompt?.content) return;
    
    let rendered = prompt.content;
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      rendered = rendered.replace(regex, String(value));
    });
    
    setRenderedPrompt(rendered);
  };

  const handleVariableChange = (name, value) => {
    setVariables(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateExecution = () => {
    const errors = [];
    
    if (!renderedPrompt.trim()) {
      errors.push('Prompt content is empty');
    }
    
    // Check for unresolved variables
    const unresolvedVars = renderedPrompt.match(/\{\{[^}]+\}\}/g);
    if (unresolvedVars) {
      errors.push(`Unresolved variables: ${unresolvedVars.join(', ')}`);
    }
    
    // Check required variables
    if (prompt?.variables) {
      const missingRequired = prompt.variables
        .filter(v => v.required && (!variables[v.name] || variables[v.name].toString().trim() === ''))
        .map(v => v.name);
      
      if (missingRequired.length > 0) {
        errors.push(`Missing required variables: ${missingRequired.join(', ')}`);
      }
    }
    
    return errors;
  };

  const executePrompt = async () => {
    const validationErrors = validateExecution();
    if (validationErrors.length > 0) {
      showNotification?.(validationErrors.join('; '), 'error');
      return;
    }

    try {
      setLoading(true);
      const startTime = Date.now();
      
      const result = await executePromptWithModel(prompt.id, selectedModel, {
        ...variables,
        ...executionSettings
      });
      
      const endTime = Date.now();
      const latency = endTime - startTime;
      
      const executionResult = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        model: selectedModel,
        prompt: renderedPrompt,
        response: result.response || 'No response received',
        latency: latency,
        tokens: result.tokens || { input: 0, output: 0 },
        cost: result.cost || 0,
        success: true,
        settings: { ...executionSettings }
      };
      
      setExecutionResults(prev => [executionResult, ...prev]);
      showNotification?.('Prompt executed successfully', 'success');
      
    } catch (error) {
      const errorResult = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        model: selectedModel,
        prompt: renderedPrompt,
        response: error.message || 'Execution failed',
        latency: 0,
        tokens: { input: 0, output: 0 },
        cost: 0,
        success: false,
        error: error.message
      };
      
      setExecutionResults(prev => [errorResult, ...prev]);
      showNotification?.('Prompt execution failed', 'error');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    showNotification?.('Copied to clipboard', 'success');
  };

  const exportResults = () => {
    const dataStr = JSON.stringify(executionResults, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `prompt-execution-results-${Date.now()}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const getModelInfo = (modelId) => {
    return availableModels.find(m => m.id === modelId) || availableModels[0];
  };

  const formatCost = (cost) => {
    return `$${cost.toFixed(6)}`;
  };

  const formatTokens = (tokens) => {
    if (typeof tokens === 'object') {
      return `${tokens.input + tokens.output} (${tokens.input}+${tokens.output})`;
    }
    return tokens.toString();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-7xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <Brain className="h-6 w-6 text-purple-600" />
            <h2 className="text-xl font-semibold">Prompt Playground</h2>
            <span className="text-sm text-gray-500">({prompt?.name})</span>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={exportResults}
              disabled={executionResults.length === 0}
              className="flex items-center space-x-2 px-3 py-2 text-sm bg-gray-600 text-white rounded hover:bg-gray-700 disabled:bg-gray-300"
            >
              <Download className="h-4 w-4" />
              <span>Export</span>
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>
        </div>

        <div className="flex-1 flex overflow-hidden">
          {/* Configuration Panel */}
          <div className="w-1/3 border-r overflow-y-auto">
            <div className="p-6 space-y-6">
              {/* Model Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Model
                </label>
                <select
                  value={selectedModel}
                  onChange={(e) => setSelectedModel(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                >
                  {availableModels.map(model => (
                    <option key={model.id} value={model.id}>
                      {model.name} ({model.provider}) - {formatCost(model.cost)}/1K tokens
                    </option>
                  ))}
                </select>
              </div>

              {/* Variables */}
              {prompt?.variables && prompt.variables.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Variables ({prompt.variables.length})
                  </label>
                  <div className="space-y-3">
                    {prompt.variables.map((variable) => (
                      <div key={variable.name}>
                        <label className="block text-xs font-medium text-gray-600 mb-1">
                          {variable.name}
                          {variable.required && <span className="text-red-500 ml-1">*</span>}
                        </label>
                        <div className="text-xs text-gray-500 mb-1">
                          {variable.description}
                        </div>
                        {variable.type === 'boolean' ? (
                          <input
                            type="checkbox"
                            checked={variables[variable.name] || false}
                            onChange={(e) => handleVariableChange(variable.name, e.target.checked)}
                            className="rounded"
                          />
                        ) : variable.type === 'number' ? (
                          <input
                            type="number"
                            value={variables[variable.name] || ''}
                            onChange={(e) => handleVariableChange(variable.name, parseFloat(e.target.value) || 0)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                          />
                        ) : (
                          <textarea
                            value={variables[variable.name] || ''}
                            onChange={(e) => handleVariableChange(variable.name, e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                            rows={variable.type === 'array' || variable.type === 'object' ? 3 : 2}
                            placeholder={variable.default_value || `Enter ${variable.name}`}
                          />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Advanced Settings */}
              <div>
                <button
                  onClick={() => setShowAdvanced(!showAdvanced)}
                  className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-800"
                >
                  <Settings className="h-4 w-4" />
                  <span>Advanced Settings</span>
                </button>
                
                {showAdvanced && (
                  <div className="mt-3 space-y-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-600 mb-1">
                        Temperature: {executionSettings.temperature}
                      </label>
                      <input
                        type="range"
                        min="0"
                        max="2"
                        step="0.1"
                        value={executionSettings.temperature}
                        onChange={(e) => setExecutionSettings(prev => ({
                          ...prev,
                          temperature: parseFloat(e.target.value)
                        }))}
                        className="w-full"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-xs font-medium text-gray-600 mb-1">
                        Max Tokens
                      </label>
                      <input
                        type="number"
                        value={executionSettings.max_tokens}
                        onChange={(e) => setExecutionSettings(prev => ({
                          ...prev,
                          max_tokens: parseInt(e.target.value) || 1000
                        }))}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Execute Button */}
              <button
                onClick={executePrompt}
                disabled={loading}
                className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-400"
              >
                {loading ? (
                  <RefreshCw className="h-5 w-5 animate-spin" />
                ) : (
                  <Play className="h-5 w-5" />
                )}
                <span>{loading ? 'Executing...' : 'Execute Prompt'}</span>
              </button>
            </div>
          </div>

          {/* Prompt Preview & Results */}
          <div className="w-2/3 flex flex-col">
            {/* Prompt Preview */}
            <div className="border-b p-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium">Rendered Prompt</h3>
                <button
                  onClick={() => copyToClipboard(renderedPrompt)}
                  className="flex items-center space-x-1 px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
                >
                  <Copy className="h-3 w-3" />
                  <span>Copy</span>
                </button>
              </div>
              <div className="bg-gray-50 p-3 rounded border font-mono text-sm whitespace-pre-wrap max-h-32 overflow-y-auto">
                {renderedPrompt || 'No prompt content'}
              </div>
            </div>

            {/* Execution Results */}
            <div className="flex-1 overflow-y-auto p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium">Execution Results ({executionResults.length})</h3>
                {executionResults.length > 0 && (
                  <button
                    onClick={() => setExecutionResults([])}
                    className="text-sm text-gray-600 hover:text-gray-800"
                  >
                    Clear All
                  </button>
                )}
              </div>

              {executionResults.length === 0 ? (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <Zap className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>Execute a prompt to see results</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {executionResults.map((result) => (
                    <div
                      key={result.id}
                      className={`border rounded-lg p-4 ${
                        result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          {result.success ? (
                            <CheckCircle className="h-5 w-5 text-green-600" />
                          ) : (
                            <AlertTriangle className="h-5 w-5 text-red-600" />
                          )}
                          <span className="font-medium">{getModelInfo(result.model).name}</span>
                          <span className="text-sm text-gray-500">
                            {new Date(result.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <div className="flex items-center space-x-1">
                            <Clock className="h-4 w-4" />
                            <span>{result.latency}ms</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Hash className="h-4 w-4" />
                            <span>{formatTokens(result.tokens)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <DollarSign className="h-4 w-4" />
                            <span>{formatCost(result.cost)}</span>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white p-3 rounded border">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Response</h4>
                        <div className="text-sm whitespace-pre-wrap">
                          {result.response}
                        </div>
                      </div>

                      {result.error && (
                        <div className="mt-2 bg-red-100 p-2 rounded text-sm text-red-700">
                          <strong>Error:</strong> {result.error}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromptPlayground;
