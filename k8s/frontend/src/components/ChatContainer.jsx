import React, { useEffect, useRef } from 'react';
import { MessageSquare } from 'lucide-react';
import ChatMessage from './ChatMessage';
import MessageInput from './MessageInput';
import ModelComparison from './ModelComparison';

const ChatContainer = ({
  conversation,
  onSendMessage,
  onCancel,
  onRegenerate,
  isLoading,
  isStreaming,
  currentModel,
  showSettings = false,
  onSettingsClick,
  onModelSelect,
  className = ''
}) => {
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [conversation?.messages]);

  // Auto-scroll during streaming
  useEffect(() => {
    if (isStreaming && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [isStreaming]);

  const handleRegenerate = (message) => {
    if (onRegenerate && conversation) {
      // Find the user message that prompted this response
      const messageIndex = conversation.messages.findIndex(m => m.id === message.id);
      if (messageIndex > 0) {
        const userMessage = conversation.messages[messageIndex - 1];
        if (userMessage.role === 'user') {
          onRegenerate(userMessage.content);
        }
      }
    }
  };

  if (!conversation) {
    return (
      <div className={`flex flex-col h-full bg-white ${className}`}>
        {/* Empty state */}
        <div className="flex-1 flex flex-col items-center justify-center text-gray-500 p-8">
          <MessageSquare size={64} className="mb-4 opacity-50" />
          <h3 className="text-xl font-semibold mb-2">Welcome to AI Operations Hub Chat</h3>
          <p className="text-center max-w-md">
            Start a conversation with our intelligent AI system. Your messages will be routed to the optimal model based on your request.
          </p>
          <div className="mt-6 p-4 bg-indigo-50 rounded-lg max-w-md">
            <h4 className="font-medium text-indigo-800 mb-2">Features:</h4>
            <ul className="text-sm text-indigo-700 space-y-1">
              <li>• Intelligent model routing</li>
              <li>• Real-time streaming responses</li>
              <li>• Conversation history</li>
              <li>• Cost optimization</li>
            </ul>
          </div>
        </div>

        {/* Input for starting new conversation */}
        <MessageInput
          onSendMessage={onSendMessage}
          onCancel={onCancel}
          isLoading={isLoading}
          isStreaming={isStreaming}
          placeholder="Start a new conversation..."
          showSettings={showSettings}
          onSettingsClick={onSettingsClick}
          currentModel={currentModel}
        />
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Chat header */}
      <div className="border-b border-gray-200 p-4 bg-gray-50">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="font-semibold text-gray-800 truncate">{conversation.title}</h2>
            <p className="text-sm text-gray-500">
              {conversation.messages.length} messages • Created {new Date(conversation.createdAt).toLocaleDateString()}
            </p>
          </div>
          <div className="text-sm text-gray-500">
            Model: <span className="font-medium text-indigo-600">{conversation.model}</span>
          </div>
        </div>
      </div>

      {/* Model comparison panel */}
      <div className="border-b border-gray-200 bg-gray-50">
        <ModelComparison
          currentModel={currentModel}
          onModelSelect={onModelSelect}
          className="mx-4 my-2"
        />
      </div>

      {/* Messages area */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
        style={{ scrollBehavior: 'smooth' }}
      >
        {conversation.messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-500">
            <MessageSquare size={48} className="mb-4 opacity-50" />
            <p>No messages in this conversation yet</p>
            <p className="text-sm mt-1">Send a message to get started</p>
          </div>
        ) : (
          <>
            {conversation.messages.map((message, index) => (
              <ChatMessage
                key={message.id}
                message={message}
                onRegenerate={handleRegenerate}
                isLast={index === conversation.messages.length - 1}
              />
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Message input */}
      <MessageInput
        onSendMessage={onSendMessage}
        onCancel={onCancel}
        isLoading={isLoading}
        isStreaming={isStreaming}
        placeholder="Type your message..."
        showSettings={showSettings}
        onSettingsClick={onSettingsClick}
        currentModel={currentModel}
      />
    </div>
  );
};

export default ChatContainer;
