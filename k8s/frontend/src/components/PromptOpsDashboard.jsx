import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON><PERSON>, Bar, LineChart, Line, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, Legend,
  ResponsiveC<PERSON>r, <PERSON><PERSON><PERSON>, <PERSON>, Cell
} from 'recharts';
import {
  FileText, Edit3, Copy, Play, Pause, TrendingUp, TrendingDown,
  GitBranch, Target, Zap, Eye, Settings, Plus, Search, Filter,
  CheckCircle, AlertCircle, Clock, BarChart3, Users, Layers, X, Code
} from 'lucide-react';
import usePromptOps from '../hooks/usePromptOps';
import PromptVersionManager from './PromptVersionManager';
import PromptTemplateEditor from './PromptTemplateEditor';
import PromptPlayground from './PromptPlayground';

// Enhanced PromptCard component
const PromptCard = ({
  prompt,
  modelProfiles = {},
  onVersions,
  onEdit,
  onClone,
  onPlayground,
  onDelete
}) => {
  const modelName = modelProfiles[prompt.model_id]?.name || prompt.model_id;

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'deprecated': return 'bg-red-100 text-red-800';
      case 'archived': return 'bg-gray-100 text-gray-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <h3 className="text-lg font-semibold text-gray-900">{prompt.name}</h3>
            {prompt.tags?.includes('template') && (
              <Code className="h-4 w-4 text-blue-600" title="Template" />
            )}
            {prompt.tags?.includes('stable') && (
              <CheckCircle className="h-4 w-4 text-green-600" title="Stable" />
            )}
          </div>

          <p className="text-sm text-gray-600 mb-3">{prompt.description}</p>

          <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
            <span>v{prompt.version}</span>
            <span>{prompt.owner}</span>
            <span>{new Date(prompt.created_at).toLocaleDateString()}</span>
          </div>

          {prompt.variables && prompt.variables.length > 0 && (
            <div className="flex items-center space-x-2 mb-3">
              <span className="text-xs text-blue-600">
                Variables: {prompt.variables.map(v => v.name).join(', ')}
              </span>
            </div>
          )}

          {prompt.model_targets && prompt.model_targets.length > 0 && (
            <div className="flex items-center space-x-2 mb-3">
              <span className="text-xs text-purple-600">
                Models: {prompt.model_targets.join(', ')}
              </span>
            </div>
          )}
        </div>

        <div className="flex flex-col items-end space-y-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(prompt.status)}`}>
            {prompt.status}
          </span>

          <div className="flex items-center space-x-1">
            <button
              onClick={() => onVersions?.(prompt)}
              className="p-1 text-gray-400 hover:text-blue-600"
              title="Version History"
            >
              <GitBranch size={14} />
            </button>
            <button
              onClick={() => onPlayground?.(prompt)}
              className="p-1 text-gray-400 hover:text-purple-600"
              title="Playground"
            >
              <Zap size={14} />
            </button>
            <button
              onClick={() => onEdit?.(prompt)}
              className="p-1 text-gray-400 hover:text-gray-600"
              title="Edit"
            >
              <Edit3 size={14} />
            </button>
            <button
              onClick={() => onClone?.(prompt)}
              className="p-1 text-gray-400 hover:text-gray-600"
              title="Clone"
            >
              <Copy size={14} />
            </button>
          </div>
        </div>
      </div>

      {prompt.performance_data && (
        <div className="mt-4 grid grid-cols-4 gap-4 pt-4 border-t border-gray-200">
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">
              {prompt.performance_data.total_executions || 0}
            </div>
            <div className="text-xs text-gray-500">Executions</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">
              {prompt.performance_data.avg_latency?.toFixed(0) || 0}ms
            </div>
            <div className="text-xs text-gray-500">Avg Latency</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">
              ${prompt.performance_data.avg_cost?.toFixed(4) || 0}
            </div>
            <div className="text-xs text-gray-500">Avg Cost</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">
              {((prompt.performance_data.success_rate || 0) * 100).toFixed(1)}%
            </div>
            <div className="text-xs text-gray-500">Success Rate</div>
          </div>
        </div>
      )}
    </div>
  );
};

// AbTestCard component
const AbTestCard = ({ test, modelProfiles = {} }) => {
  const getModelName = (modelId) => modelProfiles[modelId]?.name || modelId;

  const getWinnerVariant = () => {
    // Handle both old and new data structures
    if (test.variants) {
      // Old structure with variants.a and variants.b
      if (!test.variants.a?.metrics?.requests || !test.variants.b?.metrics?.requests) {
        return null;
      }
      const aScore = test.variants.a.metrics.quality_score;
      const bScore = test.variants.b.metrics.quality_score;
      if (Math.abs(aScore - bScore) < 0.05) return 'tie';
      return aScore > bScore ? 'a' : 'b';
    } else {
      // New structure with flat fields
      if (!test.variant_a_usage || !test.variant_b_usage) {
        return null;
      }
      const aScore = test.variant_a_score || 0;
      const bScore = test.variant_b_score || 0;
      if (Math.abs(aScore - bScore) < 0.05) return 'tie';
      return aScore > bScore ? 'a' : 'b';
    }
  };

  const winner = getWinnerVariant();

  return (
    <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{test.name}</h3>
          <div className="flex items-center space-x-2 mt-1">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              test.status === 'running' ? 'bg-blue-100 text-blue-800' :
              test.status === 'completed' ? 'bg-green-100 text-green-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {test.status}
            </span>
            {winner && (
              <span className="text-xs text-gray-500">
                Winner: {winner === 'tie' ? 'Tie' : `Variant ${winner.toUpperCase()}`}
              </span>
            )}
          </div>
        </div>
        <div className="flex space-x-2">
          <button className="p-2 text-gray-400 hover:text-gray-600">
            <Eye size={16} />
          </button>
          <button className="p-2 text-gray-400 hover:text-gray-600">
            <Settings size={16} />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="border border-gray-200 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Variant A</span>
            <span className="text-xs text-gray-500">
              {test.variants ? `${test.variants.a?.traffic_percentage || 50}%` : `${test.traffic_split || 50}%`}
            </span>
          </div>
          <div className="text-xs text-gray-600 mb-2">
            Model: {test.variants ? getModelName(test.variants.a?.model_id) : (test.prompt_a_id || 'N/A')}
          </div>
          <div className="space-y-1 text-xs">
            <div className="flex justify-between">
              <span>Requests:</span>
              <span>{test.variants ? (test.variants.a?.metrics?.requests || 0) : (test.variant_a_usage || 0)}</span>
            </div>
            <div className="flex justify-between">
              <span>Quality:</span>
              <span>{test.variants ? ((test.variants.a?.metrics?.quality_score || 0) * 100).toFixed(1) : ((test.variant_a_score || 0) * 100).toFixed(1)}%</span>
            </div>
            <div className="flex justify-between">
              <span>Latency:</span>
              <span>{test.variants ? (test.variants.a?.metrics?.avg_latency || 0) : 0}ms</span>
            </div>
          </div>
        </div>

        <div className="border border-gray-200 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Variant B</span>
            <span className="text-xs text-gray-500">
              {test.variants ? `${test.variants.b?.traffic_percentage || 50}%` : `${100 - (test.traffic_split || 50)}%`}
            </span>
          </div>
          <div className="text-xs text-gray-600 mb-2">
            Model: {test.variants ? getModelName(test.variants.b?.model_id) : (test.prompt_b_id || 'N/A')}
          </div>
          <div className="space-y-1 text-xs">
            <div className="flex justify-between">
              <span>Requests:</span>
              <span>{test.variants ? (test.variants.b?.metrics?.requests || 0) : (test.variant_b_usage || 0)}</span>
            </div>
            <div className="flex justify-between">
              <span>Quality:</span>
              <span>{test.variants ? ((test.variants.b?.metrics?.quality_score || 0) * 100).toFixed(1) : ((test.variant_b_score || 0) * 100).toFixed(1)}%</span>
            </div>
            <div className="flex justify-between">
              <span>Latency:</span>
              <span>{test.variants ? (test.variants.b?.metrics?.avg_latency || 0) : 0}ms</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const PromptOpsDashboard = ({ showNotification, onModalStateChange, modelProfiles = {} }) => {
  const promptOpsHook = usePromptOps();
  const {
    prompts,
    abTests,
    templates,
    loading,
    createPrompt,
    createTemplate,
    startAbTest,
    stopAbTest,
    deployWinner,
    executePrompt,
    fetchPrompts,
    fetchAbTests,
    updatePrompt,
    getPromptVersions,
    getPromptDiff,
    rollbackPrompt,
    clonePrompt,
    extractPromptVariables,
    testPrompt,
    executePromptWithModel
  } = promptOpsHook;

  const [promptAnalytics, setPromptAnalytics] = useState([]);
  const [selectedPrompt, setSelectedPrompt] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [showAbTestModal, setShowAbTestModal] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Enhanced PromptOps state
  const [showVersionManager, setShowVersionManager] = useState(false);
  const [showTemplateEditor, setShowTemplateEditor] = useState(false);
  const [showPlayground, setShowPlayground] = useState(false);
  const [versionManagerPrompt, setVersionManagerPrompt] = useState(null);
  const [templateEditorPrompt, setTemplateEditorPrompt] = useState(null);
  const [playgroundPrompt, setPlaygroundPrompt] = useState(null);

  // GitOps state
  const [showGitOpsModal, setShowGitOpsModal] = useState(false);
  const [gitOpsStatus, setGitOpsStatus] = useState(null);
  const [gitOpsLoading, setGitOpsLoading] = useState(false);

  // Define fetchPromptData before using it in useEffect
  const fetchPromptData = useCallback(async () => {
    try {
      // Use the hook's fetch functions
      await Promise.all([
        fetchPrompts(),
        fetchAbTests()
      ]);
    } catch (error) {
      console.error('Error fetching prompt data:', error);
      if (showNotification) {
        showNotification('Failed to fetch prompt data', 'error');
      }
    }
  }, [fetchPrompts, fetchAbTests, showNotification]);

  useEffect(() => {
    const isAnyModalOpen = showCreateModal || showTemplateModal || showAbTestModal ||
                          showVersionManager || showTemplateEditor || showPlayground;

    if (!isAnyModalOpen) {
      fetchPromptData();
      const interval = setInterval(fetchPromptData, 30000);
      return () => clearInterval(interval);
    }
  }, [fetchPromptData, showCreateModal, showTemplateModal, showAbTestModal,
      showVersionManager, showTemplateEditor, showPlayground]);

  // Notify parent when any modal state changes
  useEffect(() => {
    const isAnyModalOpen = showCreateModal || showTemplateModal || showAbTestModal ||
                          showVersionManager || showTemplateEditor || showPlayground || showGitOpsModal;
    if (onModalStateChange) {
      onModalStateChange(isAnyModalOpen);
    }
  }, [showCreateModal, showTemplateModal, showAbTestModal, showVersionManager,
      showTemplateEditor, showPlayground, showGitOpsModal, onModalStateChange]);

  // Generate analytics data when prompts change
  useEffect(() => {
    if (prompts.length > 0) {
      const analyticsData = prompts.map(prompt => ({
        prompt_id: prompt.id,
        performance_score: Math.random() * 0.3 + 0.7, // Mock performance score
        usage_count: Math.floor(Math.random() * 2000) + 500,
        avg_latency: Math.random() * 2 + 0.5
      }));
      setPromptAnalytics(analyticsData);
    }
  }, [prompts]);

  // Enhanced PromptOps handlers
  const handleVersions = (prompt) => {
    setVersionManagerPrompt(prompt);
    setShowVersionManager(true);
  };

  const handleEditPrompt = (prompt) => {
    setTemplateEditorPrompt(prompt);
    setShowTemplateEditor(true);
  };

  const handleClonePrompt = async (prompt) => {
    try {
      const newId = `${prompt.id}_copy_${Date.now()}`;
      const newName = `${prompt.name} (Copy)`;
      await clonePrompt(prompt.id, newId, newName, `Copy of ${prompt.description}`);
      showNotification?.(`Successfully cloned prompt to ${newId}`, 'success');
      await fetchPrompts();
    } catch (error) {
      showNotification?.('Failed to clone prompt', 'error');
    }
  };

  const handlePlayground = (prompt) => {
    setPlaygroundPrompt(prompt);
    setShowPlayground(true);
  };

  const handleCreateTemplate = () => {
    setTemplateEditorPrompt(null);
    setShowTemplateEditor(true);
  };

  // Check if we're in standard edition
  const isStandardEdition = import.meta.env.VITE_APP_EDITION === 'standard' ||
                            import.meta.env.REACT_APP_EDITION === 'standard' ||
                            window.location.pathname.startsWith('/standard');

  // GitOps handlers
  const handleGitOpsExport = async () => {
    if (isStandardEdition) {
      showNotification?.('GitOps integration is available in Enterprise Edition', 'info');
      return;
    }

    setGitOpsLoading(true);
    try {
      const response = await fetch('/api/integration/promptops/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ source: 'promptops' })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.status === 'unavailable' && result.edition === 'standard') {
          showNotification?.('GitOps integration is available in Enterprise Edition', 'info');
        } else {
          showNotification?.('Prompts exported to Git successfully', 'success');
          await fetchGitOpsStatus();
        }
      } else {
        showNotification?.('Failed to export prompts to Git', 'error');
      }
    } catch (error) {
      showNotification?.('Error exporting prompts to Git', 'error');
    } finally {
      setGitOpsLoading(false);
    }
  };

  const handleGitOpsImport = async () => {
    if (isStandardEdition) {
      showNotification?.('GitOps integration is available in Enterprise Edition', 'info');
      return;
    }

    setGitOpsLoading(true);
    try {
      const response = await fetch('/api/integration/promptops/import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.status === 'unavailable' && result.edition === 'standard') {
          showNotification?.('GitOps integration is available in Enterprise Edition', 'info');
        } else {
          showNotification?.('Prompts imported from Git successfully', 'success');
          await fetchPromptData();
          await fetchGitOpsStatus();
        }
      } else {
        showNotification?.('Failed to import prompts from Git', 'error');
      }
    } catch (error) {
      showNotification?.('Error importing prompts from Git', 'error');
    } finally {
      setGitOpsLoading(false);
    }
  };

  const handleGitOpsSync = async () => {
    if (isStandardEdition) {
      showNotification?.('GitOps integration is available in Enterprise Edition', 'info');
      return;
    }

    setGitOpsLoading(true);
    try {
      const response = await fetch('/api/integration/gitops/sync', {
        method: 'POST'
      });

      if (response.ok) {
        const result = await response.json();
        if (result.status === 'unavailable' && result.edition === 'standard') {
          showNotification?.('GitOps integration is available in Enterprise Edition', 'info');
        } else {
          showNotification?.('Git repository synced successfully', 'success');
          await fetchGitOpsStatus();
        }
      } else {
        showNotification?.('Failed to sync Git repository', 'error');
      }
    } catch (error) {
      showNotification?.('Error syncing Git repository', 'error');
    } finally {
      setGitOpsLoading(false);
    }
  };

  const fetchGitOpsStatus = async () => {
    try {
      const response = await fetch('/api/integration/gitops/status');
      if (response.ok) {
        const status = await response.json();
        // Check if this is a standard edition fallback response
        if (status.status === 'unavailable' && status.edition === 'standard') {
          console.log('GitOps integration not available in Standard Edition');
          setGitOpsStatus({
            available: false,
            message: 'GitOps integration is available in Enterprise Edition',
            edition: 'standard'
          });
        } else {
          setGitOpsStatus(status);
        }
      }
    } catch (error) {
      console.error('Error fetching GitOps status:', error);
      setGitOpsStatus({
        available: false,
        message: 'Unable to connect to GitOps service',
        error: true
      });
    }
  };

  // Fetch GitOps status on component mount
  useEffect(() => {
    fetchGitOpsStatus();
  }, []);

  const handleSaveTemplate = async (templateData) => {
    try {
      if (templateEditorPrompt) {
        // Update existing template
        await updatePrompt(templateEditorPrompt.id, templateData);
        showNotification?.('Template updated successfully', 'success');
      } else {
        // Create new template
        await createTemplate(templateData);
        showNotification?.('Template created successfully', 'success');
      }
      setShowTemplateEditor(false);
      setTemplateEditorPrompt(null);
      await fetchPrompts();
    } catch (error) {
      showNotification?.('Failed to save template', 'error');
    }
  };

  const MetricCard = ({ icon, title, value, description, color = "blue", trend, onClick }) => (
    <div 
      className={`bg-white rounded-lg shadow-md p-6 border-l-4 border-${color}-500 cursor-pointer hover:shadow-lg transition-shadow`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center space-x-2">
            <div className={`text-${color}-600`}>{icon}</div>
            <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-2">{value}</p>
          <p className="text-sm text-gray-600 mt-1">{description}</p>
        </div>
        {trend && (
          <div className={`flex items-center ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
            {trend > 0 ? <TrendingUp size={20} /> : <TrendingDown size={20} />}
            <span className="ml-1 text-sm font-medium">{Math.abs(trend)}%</span>
          </div>
        )}
      </div>
    </div>
  );

  // Duplicate PromptCard removed - using enhanced version above

  const AbTestCard = ({ test }) => (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-start justify-between">
        <div>
          <div className="flex items-center space-x-2">
            <Target className="text-purple-600" size={20} />
            <h3 className="font-semibold text-gray-900">{test.name}</h3>
            <span className={`px-2 py-1 rounded text-xs ${
              test.status === 'running' ? 'bg-green-100 text-green-700' : 
              test.status === 'completed' ? 'bg-blue-100 text-blue-700' : 
              'bg-gray-100 text-gray-700'
            }`}>
              {test.status}
            </span>
          </div>
          <div className="mt-4 grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">Variant A</p>
              <p className="text-lg font-semibold text-gray-900">{((test.variant_a_score || 0) * 100).toFixed(1)}%</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Variant B</p>
              <p className="text-lg font-semibold text-gray-900">{((test.variant_b_score || 0) * 100).toFixed(1)}%</p>
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            Traffic Split: {test.traffic_split || 50}% | Started: {test.start_date || test.created_at || 'N/A'}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {test.status === 'running' ? (
            <button
              onClick={async () => {
                try {
                  await stopAbTest(test.id);
                  if (showNotification) {
                    showNotification('A/B test stopped successfully!', 'success');
                  }
                } catch (error) {
                  if (showNotification) {
                    showNotification('Failed to stop A/B test: ' + error.message, 'error');
                  }
                }
              }}
              className="px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200"
            >
              <Pause size={14} className="inline mr-1" />
              Stop
            </button>
          ) : test.status === 'completed' ? (
            <button
              onClick={async () => {
                try {
                  const result = await deployWinner(test.id);
                  if (showNotification) {
                    showNotification(
                      `🚀 Winner Deployed to Production!\n` +
                      `• Variant ${result.winner} (${result.promptId}) is now live\n` +
                      `• Production prompt created: ${result.promptId}-production\n` +
                      `• Routing policy updated for optimal performance\n` +
                      `• GitOps export completed`,
                      'success'
                    );
                  }
                } catch (error) {
                  if (showNotification) {
                    showNotification('Failed to deploy winner: ' + error.message, 'error');
                  }
                }
              }}
              className="px-3 py-1 bg-green-100 text-green-700 rounded text-sm hover:bg-green-200"
            >
              <Play size={14} className="inline mr-1" />
              Deploy Winner
            </button>
          ) : test.status === 'deployed' ? (
            <span className="px-3 py-1 bg-blue-100 text-blue-700 rounded text-sm">
              ✓ Winner Deployed
            </span>
          ) : null}
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Prompt Operations</h1>
          <p className="text-gray-600 mt-1">Manage, optimize, and monitor your prompt lifecycle</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex space-x-3">
            <button
              onClick={() => setShowCreateModal(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2"
            >
              <Plus size={16} />
              <span>Create Prompt</span>
            </button>
            <button
              onClick={handleCreateTemplate}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center space-x-2"
            >
              <Code size={16} />
              <span>Create Template</span>
            </button>

            {/* GitOps Controls */}
            <div className="border-l border-gray-300 pl-3 ml-3">
              <div className="flex space-x-2">
                <button
                  onClick={() => setShowGitOpsModal(true)}
                  className="px-3 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center space-x-2 text-sm"
                  title="GitOps Management"
                >
                  <GitBranch size={14} />
                  <span>GitOps</span>
                </button>

                {gitOpsStatus && (
                  <div className="flex items-center space-x-2 text-xs text-gray-600">
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>{gitOpsStatus.current_branch}</span>
                    </div>
                    <span className="text-gray-400">•</span>
                    <span title={gitOpsStatus.current_commit}>
                      {gitOpsStatus.current_commit?.substring(0, 7)}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {['overview', 'prompts', 'ab-tests', 'templates', 'analytics'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1).replace('-', ' ')}
            </button>
          ))}
        </nav>
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Summary Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              icon={<FileText size={24} />}
              title="Total Prompts"
              value={prompts.length}
              description="Active prompts"
              color="blue"
            />
            <MetricCard
              icon={<GitBranch size={24} />}
              title="A/B Tests"
              value={abTests.filter(t => t.status === 'running').length}
              description="Currently running"
              color="purple"
            />
            <MetricCard
              icon={<TrendingUp size={24} />}
              title="Avg Performance"
              value={`${(promptAnalytics.reduce((acc, p) => acc + p.performance_score, 0) / promptAnalytics.length * 100).toFixed(1)}%`}
              description="Across all prompts"
              color="green"
            />
            <MetricCard
              icon={<Users size={24} />}
              title="Total Usage"
              value={promptAnalytics.reduce((acc, p) => acc + p.usage_count, 0).toLocaleString()}
              description="This month"
              color="orange"
            />
          </div>

          {/* Performance Chart */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Prompt Performance</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={promptAnalytics}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="prompt_id" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="performance_score" fill="#3B82F6" name="Performance Score" />
                <Bar dataKey="usage_count" fill="#10B981" name="Usage Count" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}

      {/* Prompts Tab */}
      {activeTab === 'prompts' && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <input
                  type="text"
                  placeholder="Search prompts..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md"
                />
              </div>
              <button className="px-3 py-2 border border-gray-300 rounded-md flex items-center space-x-2">
                <Filter size={16} />
                <span>Filter</span>
              </button>
            </div>
          </div>
          
          <div className="grid grid-cols-1 gap-4">
            {prompts.length > 0 ? (
              prompts.map(prompt => (
                <PromptCard
                  key={prompt.id}
                  prompt={prompt}
                  modelProfiles={modelProfiles}
                  onVersions={handleVersions}
                  onEdit={handleEditPrompt}
                  onClone={handleClonePrompt}
                  onPlayground={handlePlayground}
                />
              ))
            ) : (
              <div className="text-center py-12">
                <FileText className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No prompts yet</h3>
                <p className="mt-1 text-sm text-gray-500">Get started by creating your first prompt.</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* A/B Tests Tab */}
      {activeTab === 'ab-tests' && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">A/B Tests</h2>
            <button
              onClick={() => setShowAbTestModal(true)}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
            >
              Start New Test
            </button>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {abTests.length > 0 ? (
              abTests.map(test => (
                <AbTestCard key={test.id} test={test} modelProfiles={modelProfiles} />
              ))
            ) : (
              <div className="text-center py-12">
                <GitBranch className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No A/B tests yet</h3>
                <p className="mt-1 text-sm text-gray-500">Start testing different prompts and models.</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Templates Tab */}
      {activeTab === 'templates' && (
        <div className="space-y-6">
          <div className="text-center py-12">
            <Layers className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No templates yet</h3>
            <p className="mt-1 text-sm text-gray-500">Get started by creating a prompt template.</p>
            <div className="mt-6">
              <button
                onClick={() => setShowTemplateModal(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Create Template
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Usage Trends</h3>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={promptAnalytics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="prompt_id" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="usage_count" stroke="#3B82F6" name="Usage Count" />
                </LineChart>
              </ResponsiveContainer>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Distribution</h3>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={promptAnalytics}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ prompt_id, performance_score }) => `${prompt_id}: ${(performance_score * 100).toFixed(1)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="performance_score"
                  >
                    {promptAnalytics.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={['#3B82F6', '#10B981', '#F59E0B', '#EF4444'][index % 4]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      )}

      {/* Create Prompt Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Create New Prompt</h3>
              <button
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>
            </div>
            <form
              className="space-y-4"
              onSubmit={async (e) => {
                e.preventDefault();
                try {
                  const formData = new FormData(e.target);
                  const promptData = {
                    name: formData.get('prompt-name'),
                    description: formData.get('prompt-description'),
                    content: formData.get('prompt-content'),
                    model_id: formData.get('prompt-model')
                  };

                  await createPrompt(promptData);
                  setShowCreateModal(false);
                  if (showNotification) {
                    showNotification('Prompt created successfully!', 'success');
                  }
                } catch (error) {
                  if (showNotification) {
                    showNotification('Failed to create prompt: ' + error.message, 'error');
                  }
                }
              }}
            >
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Prompt Name
                </label>
                <input
                  id="prompt-name"
                  name="prompt-name"
                  type="text"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter prompt name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  id="prompt-description"
                  name="prompt-description"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Describe the prompt purpose"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Target Model
                </label>
                <select
                  id="prompt-model"
                  name="prompt-model"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select a model</option>
                  {Object.values(modelProfiles).map(profile => (
                    <option key={profile.id} value={profile.id}>
                      {profile.name} ({profile.backend_type})
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Prompt Content
                </label>
                <textarea
                  id="prompt-content"
                  name="prompt-content"
                  rows={5}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your prompt template"
                />
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Create Prompt
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Create Template Modal */}
      {showTemplateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Create Template</h3>
              <button
                onClick={() => setShowTemplateModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>
            </div>
            <form
              className="space-y-4"
              onSubmit={async (e) => {
                e.preventDefault();
                try {
                  const formData = new FormData(e.target);
                  const templateData = {
                    name: formData.get('template-name'),
                    category: formData.get('template-category'),
                    content: formData.get('template-content')
                  };

                  await createTemplate(templateData);
                  setShowTemplateModal(false);
                  if (showNotification) {
                    showNotification('Template created successfully!', 'success');
                  }
                } catch (error) {
                  if (showNotification) {
                    showNotification('Failed to create template: ' + error.message, 'error');
                  }
                }
              }}
            >
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Template Name
                </label>
                <input
                  id="template-name"
                  name="template-name"
                  type="text"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter template name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  id="template-category"
                  name="template-category"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select category</option>
                  <option value="chat">Chat</option>
                  <option value="completion">Completion</option>
                  <option value="classification">Classification</option>
                  <option value="summarization">Summarization</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Template Content
                </label>
                <textarea
                  id="template-content"
                  name="template-content"
                  rows={5}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter template with variables like {{variable_name}}"
                />
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowTemplateModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Create Template
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Start A/B Test Modal */}
      {showAbTestModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Start A/B Test</h3>
              <button
                onClick={() => setShowAbTestModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>
            </div>
            <form
              className="space-y-4"
              onSubmit={async (e) => {
                e.preventDefault();
                try {
                  const formData = new FormData(e.target);
                  const testName = formData.get('test-name');
                  const testData = {
                    id: `ab-test-${Date.now()}`, // Generate unique ID
                    name: testName,
                    prompt_a_id: formData.get('variant-a-prompt-id') || 'default-prompt-a',
                    prompt_b_id: formData.get('variant-b-prompt-id') || 'default-prompt-b',
                    traffic_split: parseInt(formData.get('traffic-split')) || 50,
                    // Additional fields for frontend compatibility
                    variant_a_model: formData.get('variant-a-model'),
                    variant_a_prompt: formData.get('variant-a-prompt'),
                    variant_b_model: formData.get('variant-b-model'),
                    variant_b_prompt: formData.get('variant-b-prompt'),
                    duration_hours: parseInt(formData.get('test-duration')) || 24,
                    sample_size: parseInt(formData.get('sample-size')) || 1000,
                    success_metrics: ['quality', 'latency', 'cost']
                  };

                  await startAbTest(testData);
                  setShowAbTestModal(false);
                  if (showNotification) {
                    showNotification('A/B test started successfully!', 'success');
                  }
                } catch (error) {
                  if (showNotification) {
                    showNotification('Failed to start A/B test: ' + error.message, 'error');
                  }
                }
              }}
            >
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Test Name
                </label>
                <input
                  id="test-name"
                  name="test-name"
                  type="text"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  placeholder="Enter test name"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Variant A - Model
                  </label>
                  <select
                    id="variant-a-model"
                    name="variant-a-model"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="">Select model for A</option>
                    {Object.values(modelProfiles).map(profile => (
                      <option key={profile.id} value={profile.id}>
                        {profile.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Variant B - Model
                  </label>
                  <select
                    id="variant-b-model"
                    name="variant-b-model"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="">Select model for B</option>
                    {Object.values(modelProfiles).map(profile => (
                      <option key={profile.id} value={profile.id}>
                        {profile.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Variant A - Prompt (Control)
                </label>
                <textarea
                  id="variant-a-prompt"
                  name="variant-a-prompt"
                  rows={3}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  placeholder="Enter control prompt"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Variant B - Prompt (Test)
                </label>
                <textarea
                  id="variant-b-prompt"
                  name="variant-b-prompt"
                  rows={3}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  placeholder="Enter test prompt"
                />
              </div>

              {/* Hidden fields for prompt IDs - these will be auto-generated */}
              <input type="hidden" name="variant-a-prompt-id" value={`prompt-a-${Date.now()}`} />
              <input type="hidden" name="variant-b-prompt-id" value={`prompt-b-${Date.now()}`} />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Traffic Split (%)
                  </label>
                  <input
                    id="traffic-split"
                    name="traffic-split"
                    type="number"
                    min="10"
                    max="90"
                    defaultValue="50"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Test Duration (hours)
                  </label>
                  <input
                    id="test-duration"
                    name="test-duration"
                    type="number"
                    min="1"
                    max="168"
                    defaultValue="24"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sample Size
                  </label>
                  <input
                    id="sample-size"
                    name="sample-size"
                    type="number"
                    min="10"
                    max="10000"
                    defaultValue="100"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Success Metrics
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input type="checkbox" className="mr-2" defaultChecked />
                    <span className="text-sm">Response Quality Score</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="mr-2" defaultChecked />
                    <span className="text-sm">Response Latency</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="mr-2" defaultChecked />
                    <span className="text-sm">Cost per Request</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="mr-2" />
                    <span className="text-sm">User Satisfaction</span>
                  </label>
                </div>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowAbTestModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                >
                  Start Test
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Enhanced PromptOps Modals */}
      {showVersionManager && versionManagerPrompt && (
        <PromptVersionManager
          promptId={versionManagerPrompt.id}
          onClose={() => {
            setShowVersionManager(false);
            setVersionManagerPrompt(null);
          }}
          promptOpsHook={promptOpsHook}
          showNotification={showNotification}
        />
      )}

      {showTemplateEditor && (
        <PromptTemplateEditor
          prompt={templateEditorPrompt}
          onSave={handleSaveTemplate}
          onCancel={() => {
            setShowTemplateEditor(false);
            setTemplateEditorPrompt(null);
          }}
          promptOpsHook={promptOpsHook}
          showNotification={showNotification}
        />
      )}

      {showPlayground && playgroundPrompt && (
        <PromptPlayground
          prompt={playgroundPrompt}
          onClose={() => {
            setShowPlayground(false);
            setPlaygroundPrompt(null);
          }}
          promptOpsHook={promptOpsHook}
          showNotification={showNotification}
        />
      )}

      {/* GitOps Management Modal */}
      {showGitOpsModal && (
        <GitOpsModal
          isOpen={showGitOpsModal}
          onClose={() => setShowGitOpsModal(false)}
          gitOpsStatus={gitOpsStatus}
          onExport={handleGitOpsExport}
          onImport={handleGitOpsImport}
          onSync={handleGitOpsSync}
          loading={gitOpsLoading}
          showNotification={showNotification}
        />
      )}
    </div>
  );
};

// GitOps Management Modal Component
const GitOpsModal = ({
  isOpen,
  onClose,
  gitOpsStatus,
  onExport,
  onImport,
  onSync,
  loading,
  showNotification
}) => {
  const [diffData, setDiffData] = useState(null);
  const [loadingDiff, setLoadingDiff] = useState(false);

  const fetchDiff = async () => {
    setLoadingDiff(true);
    try {
      const response = await fetch('/api/integration/promptops/diff', {
        method: 'POST'
      });
      if (response.ok) {
        const diff = await response.json();
        setDiffData(diff);
      } else {
        showNotification?.('Failed to fetch diff', 'error');
      }
    } catch (error) {
      showNotification?.('Error fetching diff', 'error');
    } finally {
      setLoadingDiff(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchDiff();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto m-4">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
            <GitBranch className="h-5 w-5" />
            <span>GitOps Management</span>
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Status Section */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Repository Status</h3>
            {gitOpsStatus ? (
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Current Branch:</span>
                  <span className="ml-2 text-gray-900">{gitOpsStatus.current_branch}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Latest Commit:</span>
                  <span className="ml-2 text-gray-900 font-mono">{gitOpsStatus.current_commit?.substring(0, 12)}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Target Branch:</span>
                  <span className="ml-2 text-gray-900">{gitOpsStatus.target_branch}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Last Updated:</span>
                  <span className="ml-2 text-gray-900">{new Date(gitOpsStatus.timestamp).toLocaleString()}</span>
                </div>
              </div>
            ) : (
              <p className="text-gray-500">Loading status...</p>
            )}
          </div>

          {/* Diff Section */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Changes Summary</h3>
            {loadingDiff ? (
              <p className="text-gray-500">Loading diff...</p>
            ) : diffData ? (
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{diffData.summary?.added || 0}</div>
                  <div className="text-gray-600">Added</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">{diffData.summary?.modified || 0}</div>
                  <div className="text-gray-600">Modified</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{diffData.summary?.deleted || 0}</div>
                  <div className="text-gray-600">Deleted</div>
                </div>
              </div>
            ) : (
              <p className="text-gray-500">No diff data available</p>
            )}
          </div>

          {/* Actions Section */}
          <div className="flex flex-wrap gap-3">
            <button
              onClick={onSync}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
            >
              <GitBranch size={16} />
              <span>Sync Repository</span>
            </button>

            <button
              onClick={onExport}
              disabled={loading}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center space-x-2"
            >
              <Plus size={16} />
              <span>Export to Git</span>
            </button>

            <button
              onClick={onImport}
              disabled={loading}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 flex items-center space-x-2"
            >
              <Plus size={16} />
              <span>Import from Git</span>
            </button>
          </div>

          {/* Help Section */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">GitOps Workflow</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• <strong>Sync:</strong> Pull latest changes from Git repository</li>
              <li>• <strong>Export:</strong> Push current prompts to Git repository</li>
              <li>• <strong>Import:</strong> Load prompts from Git repository to PromptOps</li>
              <li>• Changes are automatically deployed via CI/CD pipeline</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromptOpsDashboard;
