import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Star, Zap, DollarSign, Clock } from 'lucide-react';
import useModelScores from '../hooks/useModelScores';
import { ScoreProgressBar } from './ModelScoreDisplay';

const ModelComparison = ({ onModelSelect, currentModel, className = '' }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedCapability, setSelectedCapability] = useState('overall');
  const { modelProfiles, modelScores, getModelScore, getTopCapabilities } = useModelScores();

  const capabilities = [
    { key: 'overall', label: 'Overall Score', icon: Star },
    { key: 'code_generation', label: 'Code Generation', icon: Zap },
    { key: 'reasoning', label: 'Reasoning', icon: Star },
    { key: 'creative_writing', label: 'Creative Writing', icon: Star },
    { key: 'factual_qa', label: 'Factual Q&A', icon: Star },
    { key: 'analysis', label: 'Analysis', icon: Star },
    { key: 'conversational', label: 'Conversational', icon: Star },
    { key: 'mathematical', label: 'Mathematical', icon: Star },
    { key: 'rag', label: 'RAG/Search', icon: Star },
    { key: 'multimodal', label: 'Multimodal', icon: Star },
    { key: 'real_time_data', label: 'Real-time Data', icon: Zap },
    { key: 'function_calling', label: 'Function Calling', icon: Zap }
  ];

  const getModelCost = (modelId) => {
    const profile = modelProfiles.find(p => p.id === modelId);
    return profile?.expected_cost || 0;
  };

  const getModelLatency = (modelId) => {
    const profile = modelProfiles.find(p => p.id === modelId);
    return profile?.expected_latency_ms || 0;
  };

  const sortedModels = modelProfiles
    .filter(profile => modelScores[profile.id])
    .sort((a, b) => {
      const scoreA = getModelScore(a.id, selectedCapability);
      const scoreB = getModelScore(b.id, selectedCapability);
      return scoreB - scoreA;
    })
    .slice(0, 8); // Show top 8 models

  const selectedCapabilityInfo = capabilities.find(c => c.key === selectedCapability);

  if (modelProfiles.length === 0) {
    return null;
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 transition-colors"
      >
        <div className="flex items-center space-x-2">
          <Star className="text-yellow-500" size={16} />
          <span className="font-medium text-gray-900">AI Model Comparison</span>
          <span className="text-sm text-gray-500">
            ({sortedModels.length} models available)
          </span>
        </div>
        {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
      </button>

      {isExpanded && (
        <div className="border-t border-gray-200 p-4">
          {/* Capability selector */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Compare by capability:
            </label>
            <select
              value={selectedCapability}
              onChange={(e) => setSelectedCapability(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              {capabilities.map(cap => (
                <option key={cap.key} value={cap.key}>
                  {cap.label}
                </option>
              ))}
            </select>
          </div>

          {/* Model comparison grid */}
          <div className="space-y-3">
            {sortedModels.map((profile, index) => {
              const score = getModelScore(profile.id, selectedCapability);
              const overallScore = getModelScore(profile.id, 'overall');
              const cost = getModelCost(profile.id);
              const latency = getModelLatency(profile.id);
              const isCurrentModel = profile.id === currentModel;
              const topCaps = getTopCapabilities(profile.id, 2);

              return (
                <div
                  key={profile.id}
                  className={`p-3 border rounded-lg transition-all cursor-pointer hover:shadow-md ${
                    isCurrentModel 
                      ? 'border-indigo-500 bg-indigo-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => onModelSelect && onModelSelect(profile.id)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium text-gray-900 text-sm">
                          {profile.name}
                        </h4>
                        {index === 0 && (
                          <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                            Best for {selectedCapabilityInfo?.label}
                          </span>
                        )}
                        {isCurrentModel && (
                          <span className="px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">
                            Current
                          </span>
                        )}
                      </div>
                      
                      {/* Top capabilities */}
                      {topCaps.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-1">
                          {topCaps.map(({ capability, score: capScore }) => (
                            <span
                              key={capability}
                              className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded"
                            >
                              {capability} {(capScore * 100).toFixed(0)}%
                            </span>
                          ))}
                        </div>
                      )}
                    </div>

                    <div className="text-right">
                      <div className="text-lg font-bold text-gray-900">
                        {(score * 100).toFixed(0)}%
                      </div>
                      <div className="text-xs text-gray-500">
                        Overall: {(overallScore * 100).toFixed(0)}%
                      </div>
                    </div>
                  </div>

                  {/* Score bar */}
                  <ScoreProgressBar
                    score={score}
                    label={selectedCapabilityInfo?.label || 'Score'}
                    className="mb-2"
                  />

                  {/* Model metrics */}
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1">
                        <DollarSign size={12} />
                        <span>${(cost * 1000).toFixed(3)}/1K tokens</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock size={12} />
                        <span>{latency}ms</span>
                      </div>
                    </div>
                    <div className="text-gray-400">
                      {profile.provider || 'Unknown'}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Quick actions */}
          <div className="mt-4 pt-3 border-t border-gray-200">
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>Click any model to select it for your next message</span>
              <button
                onClick={() => setIsExpanded(false)}
                className="text-indigo-600 hover:text-indigo-800"
              >
                Close comparison
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModelComparison;
