import React, { useState, useEffect } from 'react';
import {
  Network,
  Plus,
  Play,
  Pause,
  Square,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  AlertTriangle,
  Clock,
  Users,
  Settings,
  Copy,
  Download,
  Activity
} from 'lucide-react';
import WorkflowVisualization from './WorkflowVisualization';
import WorkflowTemplatesLibrary from './WorkflowTemplatesLibrary';
import { MULTI_AGENT_API_PREFIX } from '../utils/constants';

const WorkflowDesignerView = ({ workflows, agents, onRefresh, showNotification, setIsFormOpen }) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedWorkflow, setSelectedWorkflow] = useState(null);
  const [showVisualization, setShowVisualization] = useState(false);
  const [visualizationWorkflowId, setVisualizationWorkflowId] = useState(null);
  const [showTemplatesLibrary, setShowTemplatesLibrary] = useState(false);
  const [templates, setTemplates] = useState([]);
  const [filterStatus, setFilterStatus] = useState('all');

  const workflowStatuses = ['draft', 'active', 'executing', 'completed', 'failed', 'paused', 'cancelled'];

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      const response = await fetch(`${MULTI_AGENT_API_PREFIX}/templates`);
      if (response.ok) {
        const data = await response.json();
        setTemplates(data);
      }
    } catch (error) {
      console.error('Failed to fetch templates:', error);
    }
  };

  const filteredWorkflows = workflows.filter(workflow => {
    return filterStatus === 'all' || workflow.status === filterStatus;
  });

  const handleCreateWorkflow = async (workflowData) => {
    try {
      const response = await fetch(`${MULTI_AGENT_API_PREFIX}/workflows`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(workflowData),
      });

      if (response.ok) {
        showNotification('Workflow created successfully', 'success');
        setShowCreateForm(false);
        setIsFormOpen(false);
        onRefresh();
      } else {
        const error = await response.text();
        showNotification(`Failed to create workflow: ${error}`, 'error');
      }
    } catch (error) {
      showNotification(`Error creating workflow: ${error.message}`, 'error');
    }
  };

  const handleSelectTemplate = (template) => {
    // Pre-fill the create form with template data
    setShowCreateForm(true);
    // In a real implementation, this would pass the template data to the form
    showNotification(`Selected template: ${template.name}`, 'success');
  };

  const handleExecuteWorkflow = async (workflowId) => {
    try {
      const response = await fetch(`${MULTI_AGENT_API_PREFIX}/workflows/${workflowId}/execute`, {
        method: 'POST',
      });

      if (response.ok) {
        showNotification('Workflow execution started', 'success');
        onRefresh();
      } else {
        const error = await response.text();
        showNotification(`Failed to execute workflow: ${error}`, 'error');
      }
    } catch (error) {
      showNotification(`Error executing workflow: ${error.message}`, 'error');
    }
  };

  const handlePauseWorkflow = async (workflowId) => {
    try {
      const response = await fetch(`${MULTI_AGENT_API_PREFIX}/workflows/${workflowId}/pause`, {
        method: 'POST',
      });

      if (response.ok) {
        showNotification('Workflow paused', 'success');
        onRefresh();
      } else {
        showNotification('Failed to pause workflow', 'error');
      }
    } catch (error) {
      showNotification(`Error pausing workflow: ${error.message}`, 'error');
    }
  };

  const handleResumeWorkflow = async (workflowId) => {
    try {
      const response = await fetch(`${MULTI_AGENT_API_PREFIX}/workflows/${workflowId}/resume`, {
        method: 'POST',
      });

      if (response.ok) {
        showNotification('Workflow resumed', 'success');
        onRefresh();
      } else {
        showNotification('Failed to resume workflow', 'error');
      }
    } catch (error) {
      showNotification(`Error resuming workflow: ${error.message}`, 'error');
    }
  };

  const handleCancelWorkflow = async (workflowId) => {
    if (!confirm('Are you sure you want to cancel this workflow?')) {
      return;
    }

    try {
      const response = await fetch(`${MULTI_AGENT_API_PREFIX}/workflows/${workflowId}/cancel`, {
        method: 'POST',
      });

      if (response.ok) {
        showNotification('Workflow cancelled', 'success');
        onRefresh();
      } else {
        showNotification('Failed to cancel workflow', 'error');
      }
    } catch (error) {
      showNotification(`Error cancelling workflow: ${error.message}`, 'error');
    }
  };

  const handleDeleteWorkflow = async (workflowId) => {
    if (!confirm('Are you sure you want to delete this workflow?')) {
      return;
    }

    try {
      const response = await fetch(`${MULTI_AGENT_API_PREFIX}/workflows/${workflowId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        showNotification('Workflow deleted successfully', 'success');
        onRefresh();
      } else {
        showNotification('Failed to delete workflow', 'error');
      }
    } catch (error) {
      showNotification(`Error deleting workflow: ${error.message}`, 'error');
    }
  };

  const WorkflowStatusBadge = ({ status }) => {
    const colors = {
      draft: 'bg-gray-100 text-gray-800',
      active: 'bg-blue-100 text-blue-800',
      executing: 'bg-yellow-100 text-yellow-800',
      completed: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      paused: 'bg-orange-100 text-orange-800',
      cancelled: 'bg-gray-100 text-gray-800'
    };

    const icons = {
      draft: <Edit size={12} />,
      active: <CheckCircle size={12} />,
      executing: <Play size={12} />,
      completed: <CheckCircle size={12} />,
      failed: <AlertTriangle size={12} />,
      paused: <Pause size={12} />,
      cancelled: <Square size={12} />
    };

    return (
      <span className={`inline-flex items-center space-x-1 px-2 py-1 text-xs font-medium rounded-full ${colors[status] || 'bg-gray-100 text-gray-800'}`}>
        {icons[status]}
        <span>{status}</span>
      </span>
    );
  };

  const getWorkflowActions = (workflow) => {
    const actions = [];

    if (workflow.status === 'draft') {
      actions.push(
        <button
          key="execute"
          onClick={() => handleExecuteWorkflow(workflow.id)}
          className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700"
        >
          <Play size={12} className="mr-1" />
          Execute
        </button>
      );
    }

    if (workflow.status === 'executing') {
      actions.push(
        <button
          key="pause"
          onClick={() => handlePauseWorkflow(workflow.id)}
          className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-yellow-600 hover:bg-yellow-700"
        >
          <Pause size={12} className="mr-1" />
          Pause
        </button>
      );
    }

    if (workflow.status === 'paused') {
      actions.push(
        <button
          key="resume"
          onClick={() => handleResumeWorkflow(workflow.id)}
          className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700"
        >
          <Play size={12} className="mr-1" />
          Resume
        </button>
      );
    }

    if (['executing', 'paused'].includes(workflow.status)) {
      actions.push(
        <button
          key="cancel"
          onClick={() => handleCancelWorkflow(workflow.id)}
          className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700"
        >
          <Square size={12} className="mr-1" />
          Cancel
        </button>
      );
    }

    if (['draft', 'completed', 'failed', 'cancelled'].includes(workflow.status)) {
      actions.push(
        <button
          key="delete"
          onClick={() => handleDeleteWorkflow(workflow.id)}
          className="inline-flex items-center px-3 py-1 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50"
        >
          <Trash2 size={12} className="mr-1" />
          Delete
        </button>
      );
    }

    return actions;
  };

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-xl font-semibold text-gray-900">Workflow Designer</h3>
          <p className="text-gray-600">Create and manage multi-agent workflows</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowTemplatesLibrary(true)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
          >
            <Copy size={16} className="mr-2" />
            Templates
          </button>
          <button
            onClick={() => {
              setShowCreateForm(true);
              setIsFormOpen(true);
            }}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
          >
            <Plus size={16} className="mr-2" />
            Create Workflow
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex space-x-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="all">All Statuses</option>
            {workflowStatuses.map(status => (
              <option key={status} value={status}>{status}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Workflows Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredWorkflows.map((workflow) => (
          <div key={workflow.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Network size={20} className="text-purple-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">{workflow.name}</h4>
                  <p className="text-sm text-gray-500">{workflow.agents?.length || 0} agents</p>
                </div>
              </div>
              <WorkflowStatusBadge status={workflow.status} />
            </div>

            <p className="text-sm text-gray-600 mb-4">{workflow.description}</p>

            {/* Workflow Stats */}
            <div className="grid grid-cols-3 gap-4 mb-4 text-xs">
              <div>
                <span className="text-gray-500">Tasks</span>
                <p className="font-medium">{workflow.tasks?.length || 0}</p>
              </div>
              <div>
                <span className="text-gray-500">Est. Cost</span>
                <p className="font-medium">${(workflow.estimated_cost || 0).toFixed(2)}</p>
              </div>
              <div>
                <span className="text-gray-500">Est. Time</span>
                <p className="font-medium">{Math.round((workflow.estimated_time || 0) / 60)}m</p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setSelectedWorkflow(workflow)}
                className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
              >
                <Eye size={12} className="mr-1" />
                View
              </button>
              {['executing', 'paused', 'completed'].includes(workflow.status) && (
                <button
                  onClick={() => {
                    setVisualizationWorkflowId(workflow.id);
                    setShowVisualization(true);
                  }}
                  className="inline-flex items-center px-3 py-1 border border-blue-300 text-xs font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100"
                >
                  <Activity size={12} className="mr-1" />
                  Visualize
                </button>
              )}
              {getWorkflowActions(workflow)}
            </div>
          </div>
        ))}
      </div>

      {filteredWorkflows.length === 0 && (
        <div className="text-center py-12">
          <Network size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No workflows found</h3>
          <p className="text-gray-500 mb-4">
            {workflows.length === 0 
              ? "Get started by creating your first workflow"
              : "Try adjusting your filters"
            }
          </p>
          {workflows.length === 0 && (
            <button
              onClick={() => setShowCreateForm(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
            >
              <Plus size={16} className="mr-2" />
              Create First Workflow
            </button>
          )}
        </div>
      )}

      {/* Create Workflow Modal */}
      {showCreateForm && (
        <WorkflowCreationForm
          templates={templates}
          agents={agents}
          onSubmit={handleCreateWorkflow}
          onCancel={() => {
            setShowCreateForm(false);
            setIsFormOpen(false);
          }}
        />
      )}

      {/* Workflow Details Modal */}
      {selectedWorkflow && (
        <WorkflowDetailsModal
          workflow={selectedWorkflow}
          agents={agents}
          onClose={() => setSelectedWorkflow(null)}
          onExecute={handleExecuteWorkflow}
          onPause={handlePauseWorkflow}
          onResume={handleResumeWorkflow}
          onCancel={handleCancelWorkflow}
        />
      )}

      {/* Workflow Visualization Modal */}
      {showVisualization && visualizationWorkflowId && (
        <WorkflowVisualization
          workflowId={visualizationWorkflowId}
          onClose={() => {
            setShowVisualization(false);
            setVisualizationWorkflowId(null);
          }}
        />
      )}

      {/* Workflow Templates Library Modal */}
      {showTemplatesLibrary && (
        <WorkflowTemplatesLibrary
          onSelectTemplate={handleSelectTemplate}
          onClose={() => setShowTemplatesLibrary(false)}
          showNotification={showNotification}
        />
      )}
    </div>
  );
};

// Workflow Creation Form Component
const WorkflowCreationForm = ({ templates, agents, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    template_id: '',
    agent_assignments: {},
    custom_tasks: []
  });

  const [selectedTemplate, setSelectedTemplate] = useState(null);

  useEffect(() => {
    if (formData.template_id) {
      const template = templates.find(t => t.id === formData.template_id);
      setSelectedTemplate(template);
    } else {
      setSelectedTemplate(null);
    }
  }, [formData.template_id, templates]);

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const availableAgents = agents.filter(agent => agent.status === 'idle');

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Create New Workflow</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
              rows="3"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Template (Optional)</label>
            <select
              value={formData.template_id}
              onChange={(e) => setFormData({...formData, template_id: e.target.value})}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            >
              <option value="">Custom Workflow</option>
              {templates.map(template => (
                <option key={template.id} value={template.id}>{template.name}</option>
              ))}
            </select>
          </div>

          {selectedTemplate && (
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">{selectedTemplate.name}</h4>
              <p className="text-sm text-blue-700 mb-2">{selectedTemplate.description}</p>
              <div className="text-xs text-blue-600">
                <span>Required Agents: {selectedTemplate.required_agents?.length || 0}</span>
                <span className="ml-4">Est. Cost: ${(selectedTemplate.estimated_cost || 0).toFixed(2)}</span>
                <span className="ml-4">Est. Time: {Math.round((selectedTemplate.estimated_time || 0) / 60)}m</span>
              </div>
            </div>
          )}

          {availableAgents.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Available Agents</label>
              <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                {availableAgents.map(agent => (
                  <div key={agent.id} className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                    <input
                      type="checkbox"
                      id={`agent-${agent.id}`}
                      className="rounded"
                    />
                    <label htmlFor={`agent-${agent.id}`} className="text-sm">
                      {agent.name} ({agent.type.replace('_', ' ')})
                    </label>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Agent Assignments */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Agent Assignments</label>
            <div className="space-y-2">
              <select
                multiple
                value={Object.values(formData.agent_assignments)}
                onChange={(e) => {
                  const selectedAgents = Array.from(e.target.selectedOptions).map(option => option.value);
                  const newAssignments = {};
                  selectedAgents.forEach((agentId, index) => {
                    newAssignments[`task_${index}`] = agentId;
                  });
                  setFormData({ ...formData, agent_assignments: newAssignments });
                }}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                {availableAgents.map(agent => (
                  <option key={agent.id} value={agent.id}>
                    {agent.name} ({agent.type.replace('_', ' ')})
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              className="flex-1 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
            >
              Create Workflow
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Workflow Details Modal Component
const WorkflowDetailsModal = ({ workflow, agents, onClose, onExecute, onPause, onResume, onCancel }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [executionStatus, setExecutionStatus] = useState(null);

  useEffect(() => {
    if (['executing', 'paused'].includes(workflow.status)) {
      fetchExecutionStatus();
      const interval = setInterval(fetchExecutionStatus, 5000);
      return () => clearInterval(interval);
    }
  }, [workflow.id, workflow.status]);

  const fetchExecutionStatus = async () => {
    try {
      const response = await fetch(`${MULTI_AGENT_API_PREFIX}/workflows/${workflow.id}/status`);
      if (response.ok) {
        const data = await response.json();
        setExecutionStatus(data);
      }
    } catch (error) {
      console.error('Failed to fetch execution status:', error);
    }
  };

  const getAgentName = (agentId) => {
    const agent = agents.find(a => a.id === agentId);
    return agent ? agent.name : agentId;
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-4xl max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{workflow.name}</h3>
            <p className="text-gray-600">{workflow.description}</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>

        {/* Tabs */}
        <div className="flex space-x-4 border-b border-gray-200 mb-4">
          <button
            onClick={() => setActiveTab('overview')}
            className={`pb-2 px-1 ${activeTab === 'overview' ? 'border-b-2 border-indigo-500 text-indigo-600' : 'text-gray-500'}`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab('tasks')}
            className={`pb-2 px-1 ${activeTab === 'tasks' ? 'border-b-2 border-indigo-500 text-indigo-600' : 'text-gray-500'}`}
          >
            Tasks
          </button>
          <button
            onClick={() => setActiveTab('agents')}
            className={`pb-2 px-1 ${activeTab === 'agents' ? 'border-b-2 border-indigo-500 text-indigo-600' : 'text-gray-500'}`}
          >
            Agents
          </button>
          {executionStatus && (
            <button
              onClick={() => setActiveTab('execution')}
              className={`pb-2 px-1 ${activeTab === 'execution' ? 'border-b-2 border-indigo-500 text-indigo-600' : 'text-gray-500'}`}
            >
              Execution
            </button>
          )}
        </div>

        {/* Tab Content */}
        <div className="space-y-4">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Basic Information</h4>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="text-gray-500">Status:</span>
                    <span className="ml-2">{workflow.status}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Created:</span>
                    <span className="ml-2">{new Date(workflow.created_at).toLocaleString()}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Updated:</span>
                    <span className="ml-2">{new Date(workflow.updated_at).toLocaleString()}</span>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Estimates</h4>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="text-gray-500">Cost:</span>
                    <span className="ml-2">${(workflow.estimated_cost || 0).toFixed(2)}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Time:</span>
                    <span className="ml-2">{Math.round((workflow.estimated_time || 0) / 60)} minutes</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Tasks:</span>
                    <span className="ml-2">{workflow.tasks?.length || 0}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'tasks' && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Workflow Tasks</h4>
              <div className="space-y-2">
                {(workflow.tasks || []).map((task, index) => (
                  <div key={task.id || index} className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium">{task.name}</p>
                        <p className="text-sm text-gray-600">{task.description}</p>
                        <p className="text-xs text-gray-500">
                          Assigned to: {getAgentName(task.assigned_agent_id)}
                        </p>
                      </div>
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {task.type}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'agents' && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Assigned Agents</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {(workflow.agents || []).map(agentId => {
                  const agent = agents.find(a => a.id === agentId);
                  return agent ? (
                    <div key={agentId} className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                          <Users size={16} className="text-indigo-600" />
                        </div>
                        <div>
                          <p className="font-medium">{agent.name}</p>
                          <p className="text-sm text-gray-500">{agent.type.replace('_', ' ')}</p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div key={agentId} className="p-3 bg-red-50 rounded-lg">
                      <p className="text-red-600">Agent not found: {agentId}</p>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {activeTab === 'execution' && executionStatus && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Execution Status</h4>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <p className="text-2xl font-bold text-blue-600">{executionStatus.metrics?.completed_tasks || 0}</p>
                  <p className="text-sm text-blue-600">Completed</p>
                </div>
                <div className="text-center p-3 bg-yellow-50 rounded-lg">
                  <p className="text-2xl font-bold text-yellow-600">{executionStatus.current_tasks?.length || 0}</p>
                  <p className="text-sm text-yellow-600">In Progress</p>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <p className="text-2xl font-bold text-red-600">{executionStatus.metrics?.failed_tasks || 0}</p>
                  <p className="text-sm text-red-600">Failed</p>
                </div>
              </div>

              {executionStatus.agent_states && (
                <div>
                  <h5 className="font-medium text-gray-900 mb-2">Agent States</h5>
                  <div className="space-y-2">
                    {Object.entries(executionStatus.agent_states).map(([agentId, state]) => (
                      <div key={agentId} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <span>{getAgentName(agentId)}</span>
                        <span className={`px-2 py-1 text-xs rounded ${
                          state.status === 'busy' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                        }`}>
                          {state.status}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex space-x-3 pt-6 border-t">
          {workflow.status === 'draft' && (
            <button
              onClick={() => onExecute(workflow.id)}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              Execute Workflow
            </button>
          )}
          {workflow.status === 'executing' && (
            <button
              onClick={() => onPause(workflow.id)}
              className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700"
            >
              Pause Workflow
            </button>
          )}
          {workflow.status === 'paused' && (
            <button
              onClick={() => onResume(workflow.id)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Resume Workflow
            </button>
          )}
          {['executing', 'paused'].includes(workflow.status) && (
            <button
              onClick={() => onCancel(workflow.id)}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Cancel Workflow
            </button>
          )}
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default WorkflowDesignerView;
