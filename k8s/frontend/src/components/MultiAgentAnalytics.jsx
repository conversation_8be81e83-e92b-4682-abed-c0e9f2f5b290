import React, { useState, useEffect } from 'react';
import {
  Bar<PERSON>hart3,
  TrendingUp,
  Users,
  Network,
  MessageSquare,
  Clock,
  DollarSign,
  Zap,
  Activity,
  Target
} from 'lucide-react';
import { MetricCard } from './DashboardCards';

const MultiAgentAnalytics = ({ analytics, agents, workflows, showNotification }) => {
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('efficiency');

  // Calculate analytics metrics
  const calculateMetrics = () => {
    const totalAgents = agents.length;
    const activeAgents = agents.filter(a => ['idle', 'busy'].includes(a.status)).length;
    const totalWorkflows = workflows.length;
    const completedWorkflows = workflows.filter(w => w.status === 'completed').length;
    const executingWorkflows = workflows.filter(w => w.status === 'executing').length;
    
    // Calculate average success rate
    const agentSuccessRates = agents
      .filter(a => a.performance && a.performance.success_rate !== undefined)
      .map(a => a.performance.success_rate);
    const avgSuccessRate = agentSuccessRates.length > 0 
      ? agentSuccessRates.reduce((sum, rate) => sum + rate, 0) / agentSuccessRates.length 
      : 0;

    // Calculate total tasks completed
    const totalTasksCompleted = agents
      .filter(a => a.performance && a.performance.tasks_completed)
      .reduce((sum, a) => sum + a.performance.tasks_completed, 0);

    // Calculate average collaboration score (simulated)
    const collaborationScore = analytics?.collaboration?.average_score || 0.85;

    // Calculate cost efficiency
    const totalCost = workflows.reduce((sum, w) => sum + (w.actual_cost || w.estimated_cost || 0), 0);
    const totalEstimatedCost = workflows.reduce((sum, w) => sum + (w.estimated_cost || 0), 0);
    const costEfficiency = totalEstimatedCost > 0 ? (totalEstimatedCost - totalCost) / totalEstimatedCost : 0;

    return {
      totalAgents,
      activeAgents,
      totalWorkflows,
      completedWorkflows,
      executingWorkflows,
      avgSuccessRate,
      totalTasksCompleted,
      collaborationScore,
      costEfficiency,
      totalCost
    };
  };

  const metrics = calculateMetrics();

  // Agent performance data for charts
  const agentPerformanceData = agents
    .filter(a => a.performance)
    .map(a => ({
      name: a.name,
      successRate: (a.performance.success_rate || 0) * 100,
      tasksCompleted: a.performance.tasks_completed || 0,
      avgLatency: a.performance.average_latency || 0,
      costEfficiency: (a.performance.cost_efficiency || 0) * 100
    }))
    .sort((a, b) => b.successRate - a.successRate);

  // Workflow performance data
  const workflowPerformanceData = workflows
    .filter(w => w.status === 'completed')
    .map(w => ({
      name: w.name,
      actualCost: w.actual_cost || 0,
      estimatedCost: w.estimated_cost || 0,
      actualTime: w.actual_time || 0,
      estimatedTime: w.estimated_time || 0,
      efficiency: w.estimated_cost > 0 ? ((w.estimated_cost - (w.actual_cost || 0)) / w.estimated_cost) * 100 : 0
    }));

  // Collaboration metrics
  const collaborationData = {
    totalMessages: analytics?.collaboration?.total_messages || 0,
    avgResponseTime: analytics?.collaboration?.avg_response_time || 0,
    successfulCollaborations: analytics?.collaboration?.successful_collaborations || 0,
    collaborationEfficiency: analytics?.collaboration?.efficiency || 0
  };

  const MetricChart = ({ data, metric, title }) => {
    if (!data || data.length === 0) {
      return (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
          <div className="text-center py-8 text-gray-500">
            No data available
          </div>
        </div>
      );
    }

    const maxValue = Math.max(...data.map(d => d[metric]));

    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
        <div className="space-y-3">
          {data.slice(0, 10).map((item, index) => (
            <div key={index} className="flex items-center space-x-3">
              <div className="w-24 text-sm text-gray-600 truncate">
                {item.name}
              </div>
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-indigo-600 h-2 rounded-full"
                  style={{ width: `${(item[metric] / maxValue) * 100}%` }}
                ></div>
              </div>
              <div className="w-16 text-sm font-medium text-gray-900 text-right">
                {typeof item[metric] === 'number' ? item[metric].toFixed(1) : item[metric]}
                {metric.includes('Rate') || metric.includes('Efficiency') ? '%' : ''}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const CollaborationInsights = () => (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Collaboration Insights</h3>
      <div className="grid grid-cols-2 gap-4">
        <div className="text-center p-4 bg-blue-50 rounded-lg">
          <MessageSquare size={24} className="mx-auto text-blue-600 mb-2" />
          <p className="text-2xl font-bold text-blue-600">{collaborationData.totalMessages}</p>
          <p className="text-sm text-blue-600">Total Messages</p>
        </div>
        <div className="text-center p-4 bg-green-50 rounded-lg">
          <Clock size={24} className="mx-auto text-green-600 mb-2" />
          <p className="text-2xl font-bold text-green-600">{collaborationData.avgResponseTime}ms</p>
          <p className="text-sm text-green-600">Avg Response Time</p>
        </div>
        <div className="text-center p-4 bg-purple-50 rounded-lg">
          <Target size={24} className="mx-auto text-purple-600 mb-2" />
          <p className="text-2xl font-bold text-purple-600">{collaborationData.successfulCollaborations}</p>
          <p className="text-sm text-purple-600">Successful Collaborations</p>
        </div>
        <div className="text-center p-4 bg-yellow-50 rounded-lg">
          <Zap size={24} className="mx-auto text-yellow-600 mb-2" />
          <p className="text-2xl font-bold text-yellow-600">{(collaborationData.collaborationEfficiency * 100).toFixed(1)}%</p>
          <p className="text-sm text-yellow-600">Collaboration Efficiency</p>
        </div>
      </div>
    </div>
  );

  const PerformanceTrends = () => (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Trends</h3>
      <div className="space-y-4">
        <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <TrendingUp size={20} className="text-green-600" />
            <span className="font-medium">Agent Efficiency</span>
          </div>
          <span className="text-green-600 font-bold">↑ 12%</span>
        </div>
        <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <Network size={20} className="text-blue-600" />
            <span className="font-medium">Workflow Success Rate</span>
          </div>
          <span className="text-blue-600 font-bold">↑ 8%</span>
        </div>
        <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <DollarSign size={20} className="text-purple-600" />
            <span className="font-medium">Cost Optimization</span>
          </div>
          <span className="text-purple-600 font-bold">↑ 15%</span>
        </div>
        <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <MessageSquare size={20} className="text-orange-600" />
            <span className="font-medium">Collaboration Quality</span>
          </div>
          <span className="text-orange-600 font-bold">↑ 5%</span>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-xl font-semibold text-gray-900">Multi-Agent Analytics</h3>
          <p className="text-gray-600">Performance insights and collaboration metrics</p>
        </div>
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="1d">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          icon={<Users size={24} />}
          title="Agent Utilization"
          value={`${((metrics.activeAgents / metrics.totalAgents) * 100).toFixed(1)}%`}
          description={`${metrics.activeAgents}/${metrics.totalAgents} agents active`}
          color="blue"
        />
        <MetricCard
          icon={<Network size={24} />}
          title="Workflow Success"
          value={`${((metrics.completedWorkflows / Math.max(metrics.totalWorkflows, 1)) * 100).toFixed(1)}%`}
          description={`${metrics.completedWorkflows} completed workflows`}
          color="green"
        />
        <MetricCard
          icon={<Zap size={24} />}
          title="Collaboration Score"
          value={`${(metrics.collaborationScore * 100).toFixed(1)}%`}
          description="Agent cooperation efficiency"
          color="purple"
        />
        <MetricCard
          icon={<DollarSign size={24} />}
          title="Cost Efficiency"
          value={`${(metrics.costEfficiency * 100).toFixed(1)}%`}
          description={`$${metrics.totalCost.toFixed(2)} total cost`}
          color="yellow"
        />
      </div>

      {/* Charts and Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <MetricChart
          data={agentPerformanceData}
          metric="successRate"
          title="Agent Success Rates"
        />
        <MetricChart
          data={agentPerformanceData}
          metric="tasksCompleted"
          title="Tasks Completed by Agent"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <CollaborationInsights />
        <PerformanceTrends />
      </div>

      {/* Workflow Performance */}
      {workflowPerformanceData.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <MetricChart
            data={workflowPerformanceData}
            metric="efficiency"
            title="Workflow Cost Efficiency"
          />
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Workflow Insights</h3>
            <div className="space-y-3">
              {workflowPerformanceData.slice(0, 5).map((workflow, index) => (
                <div key={index} className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium text-gray-900">{workflow.name}</p>
                      <p className="text-sm text-gray-600">
                        Cost: ${workflow.actualCost.toFixed(2)} / ${workflow.estimatedCost.toFixed(2)}
                      </p>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      workflow.efficiency > 0 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {workflow.efficiency > 0 ? '+' : ''}{workflow.efficiency.toFixed(1)}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Recommendations */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Optimization Recommendations</h3>
        <div className="space-y-3">
          <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
            <TrendingUp size={20} className="text-blue-600 mt-0.5" />
            <div>
              <p className="font-medium text-blue-900">Improve Agent Load Balancing</p>
              <p className="text-sm text-blue-700">
                Some agents are underutilized. Consider redistributing tasks for better efficiency.
              </p>
            </div>
          </div>
          <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
            <MessageSquare size={20} className="text-green-600 mt-0.5" />
            <div>
              <p className="font-medium text-green-900">Enhance Communication Patterns</p>
              <p className="text-sm text-green-700">
                Optimize message routing to reduce response times and improve collaboration.
              </p>
            </div>
          </div>
          <div className="flex items-start space-x-3 p-3 bg-purple-50 rounded-lg">
            <Zap size={20} className="text-purple-600 mt-0.5" />
            <div>
              <p className="font-medium text-purple-900">Workflow Template Optimization</p>
              <p className="text-sm text-purple-700">
                Create templates for high-performing workflows to standardize best practices.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MultiAgentAnalytics;
