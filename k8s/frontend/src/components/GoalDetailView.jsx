import React, { useState, useEffect, useCallback } from 'react';
import {
  ArrowLeft,
  Target,
  Play,
  Pause,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  BarChart3,
  Activity,
  RefreshCw,
  Eye,
  Calendar,
  TrendingUp,
  DollarSign
} from 'lucide-react';
import { getGoalStatusColor, getTaskStatusColor, GoalStatus, TaskStatus } from '../types/planning';
import TaskDependencyGraph from './TaskDependencyGraph';

const GoalDetailView = ({
  goal,
  plan,
  tasks = [],
  executionStatus,
  onBack,
  onExecuteGoal,
  onGeneratePlan,
  onRefreshStatus,
  planningHook
}) => {
  const [refreshing, setRefreshing] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(false);

  // Auto-refresh execution status for active goals
  useEffect(() => {
    if (!autoRefresh || !goal || ![GoalStatus.EXECUTING].includes(goal.status)) {
      return;
    }

    const interval = setInterval(() => {
      if (onRefreshStatus) {
        onRefreshStatus(goal.id);
      }
    }, 5000); // Refresh every 5 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, goal, onRefreshStatus]);

  // Handle manual refresh
  const handleRefresh = useCallback(async () => {
    if (!goal || !onRefreshStatus) return;
    
    setRefreshing(true);
    try {
      await onRefreshStatus(goal.id);
    } catch (error) {
      console.error('Error refreshing status:', error);
    } finally {
      setRefreshing(false);
    }
  }, [goal, onRefreshStatus]);

  // Handle goal execution
  const handleExecute = useCallback(async () => {
    if (!goal || !onExecuteGoal) return;
    
    try {
      await onExecuteGoal(goal.id);
    } catch (error) {
      console.error('Error executing goal:', error);
    }
  }, [goal, onExecuteGoal]);

  // Handle plan generation
  const handleGeneratePlan = useCallback(async () => {
    if (!goal || !onGeneratePlan) return;
    
    try {
      await onGeneratePlan(goal.id);
    } catch (error) {
      console.error('Error generating plan:', error);
    }
  }, [goal, onGeneratePlan]);

  if (!goal) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-600">No goal selected</p>
      </div>
    );
  }

  // Calculate progress
  const totalTasks = tasks.length;
  const completedTasks = tasks.filter(t => t.status === TaskStatus.COMPLETED).length;
  const failedTasks = tasks.filter(t => t.status === TaskStatus.FAILED).length;
  const runningTasks = tasks.filter(t => t.status === TaskStatus.RUNNING).length;
  const progress = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

  // Status badge component
  const StatusBadge = ({ status, type = 'goal' }) => {
    const colorClass = type === 'goal' ? getGoalStatusColor(status) : getTaskStatusColor(status);
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>
        {status}
      </span>
    );
  };

  // Progress bar component
  const ProgressBar = ({ progress, className = '' }) => (
    <div className={`w-full bg-gray-200 rounded-full h-2 ${className}`}>
      <div
        className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
        style={{ width: `${Math.min(progress, 100)}%` }}
      />
    </div>
  );

  // Task item component
  const TaskItem = ({ task, index }) => (
    <div className="border border-gray-200 rounded-lg p-4 space-y-3">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <h4 className="text-sm font-medium text-gray-900">{task.name}</h4>
            <StatusBadge status={task.status} type="task" />
          </div>
          <p className="text-sm text-gray-600 mb-2">{task.description}</p>
          
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            <span>Type: {task.type}</span>
            {task.estimated_cost > 0 && (
              <span className="flex items-center">
                <DollarSign size={12} className="mr-1" />
                ${task.estimated_cost.toFixed(4)}
              </span>
            )}
            {task.estimated_time > 0 && (
              <span className="flex items-center">
                <Clock size={12} className="mr-1" />
                {Math.round(task.estimated_time / 60)}m
              </span>
            )}
          </div>
        </div>
      </div>

      {task.dependencies && task.dependencies.length > 0 && (
        <div className="text-xs text-gray-500">
          <span className="font-medium">Dependencies:</span> {task.dependencies.join(', ')}
        </div>
      )}

      {task.error && (
        <div className="bg-red-50 border border-red-200 rounded p-2">
          <p className="text-xs text-red-600">{task.error}</p>
        </div>
      )}

      {task.result && (
        <div className="bg-green-50 border border-green-200 rounded p-2">
          <p className="text-xs text-green-600">Task completed successfully</p>
        </div>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft size={20} />
          </button>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Goal Details</h2>
            <p className="text-sm text-gray-600">ID: {goal.id}</p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {/* Auto-refresh toggle */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="auto-refresh"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label htmlFor="auto-refresh" className="text-sm text-gray-700">
              Auto-refresh
            </label>
          </div>

          {/* Manual refresh */}
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
          >
            <RefreshCw size={16} className={refreshing ? 'animate-spin' : ''} />
          </button>

          {/* Action buttons */}
          {goal.status === GoalStatus.PENDING && (
            <button
              onClick={handleGeneratePlan}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
            >
              <BarChart3 size={16} className="mr-2" />
              Generate Plan
            </button>
          )}

          {goal.status === GoalStatus.READY && (
            <button
              onClick={handleExecute}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
            >
              <Play size={16} className="mr-2" />
              Execute Goal
            </button>
          )}
        </div>
      </div>

      {/* Goal Overview */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-3">
              <h3 className="text-lg font-semibold text-gray-900">Goal Description</h3>
              <StatusBadge status={goal.status} />
            </div>
            <p className="text-gray-700 mb-4">{goal.description}</p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-600">Priority:</span>
                <span className="ml-2 text-gray-900">{goal.priority}/10</span>
              </div>
              <div>
                <span className="font-medium text-gray-600">Created:</span>
                <span className="ml-2 text-gray-900">{new Date(goal.created_at).toLocaleDateString()}</span>
              </div>
              {goal.deadline && (
                <div>
                  <span className="font-medium text-gray-600">Deadline:</span>
                  <span className="ml-2 text-gray-900">{new Date(goal.deadline).toLocaleDateString()}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Progress Section */}
        {totalTasks > 0 && (
          <div className="border-t border-gray-200 pt-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-700">Execution Progress</h4>
              <span className="text-sm text-gray-600">{completedTasks}/{totalTasks} tasks completed</span>
            </div>
            <ProgressBar progress={progress} className="mb-2" />
            <div className="flex items-center space-x-4 text-xs text-gray-600">
              <span className="flex items-center">
                <CheckCircle size={12} className="mr-1 text-green-600" />
                {completedTasks} completed
              </span>
              {runningTasks > 0 && (
                <span className="flex items-center">
                  <Activity size={12} className="mr-1 text-blue-600" />
                  {runningTasks} running
                </span>
              )}
              {failedTasks > 0 && (
                <span className="flex items-center">
                  <XCircle size={12} className="mr-1 text-red-600" />
                  {failedTasks} failed
                </span>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Success Criteria */}
      {goal.success_criteria && goal.success_criteria.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Success Criteria</h3>
          <div className="space-y-3">
            {goal.success_criteria.map((criterion, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{criterion.description}</p>
                    <div className="flex items-center space-x-4 text-xs text-gray-600 mt-1">
                      <span>Metric: {criterion.metric}</span>
                      <span>Target: {criterion.operator} {criterion.target}</span>
                      <span>Weight: {criterion.weight}</span>
                      {criterion.required && (
                        <span className="text-red-600 font-medium">Required</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Constraints */}
      {goal.constraints && goal.constraints.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Constraints</h3>
          <div className="space-y-3">
            {goal.constraints.map((constraint, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{constraint.description}</p>
                    <div className="flex items-center space-x-4 text-xs text-gray-600 mt-1">
                      <span>Type: {constraint.type}</span>
                      <span>Limit: {constraint.operator} {constraint.limit}</span>
                      <span className={`font-medium ${constraint.severity === 'hard' ? 'text-red-600' : 'text-yellow-600'}`}>
                        {constraint.severity}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Plan Information */}
      {plan && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Execution Plan</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-4">
            <div>
              <span className="font-medium text-gray-600">Plan Name:</span>
              <span className="ml-2 text-gray-900">{plan.name}</span>
            </div>
            <div>
              <span className="font-medium text-gray-600">Estimated Cost:</span>
              <span className="ml-2 text-gray-900">${plan.estimated_cost?.toFixed(4) || '0.0000'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-600">Estimated Time:</span>
              <span className="ml-2 text-gray-900">{Math.round((plan.estimated_time || 0) / 60)} minutes</span>
            </div>
          </div>
          {plan.description && (
            <p className="text-gray-700 text-sm">{plan.description}</p>
          )}
        </div>
      )}

      {/* Task Dependency Graph */}
      {tasks.length > 0 && (
        <TaskDependencyGraph
          tasks={tasks}
          dependencies={plan?.dependencies || []}
          className="mb-6"
        />
      )}

      {/* Tasks */}
      {tasks.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Tasks ({tasks.length})</h3>
          <div className="space-y-4">
            {tasks.map((task, index) => (
              <TaskItem key={task.id || index} task={task} index={index} />
            ))}
          </div>
        </div>
      )}

      {/* Execution Status */}
      {executionStatus && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Execution Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-600">Status:</span>
              <span className="ml-2 text-gray-900">{executionStatus.status}</span>
            </div>
            <div>
              <span className="font-medium text-gray-600">Progress:</span>
              <span className="ml-2 text-gray-900">{executionStatus.progress?.toFixed(1) || 0}%</span>
            </div>
            {executionStatus.current_task && (
              <div className="md:col-span-2">
                <span className="font-medium text-gray-600">Current Task:</span>
                <span className="ml-2 text-gray-900">{executionStatus.current_task}</span>
              </div>
            )}
            {executionStatus.estimated_completion && (
              <div className="md:col-span-2">
                <span className="font-medium text-gray-600">Estimated Completion:</span>
                <span className="ml-2 text-gray-900">
                  {new Date(executionStatus.estimated_completion).toLocaleString()}
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default GoalDetailView;
