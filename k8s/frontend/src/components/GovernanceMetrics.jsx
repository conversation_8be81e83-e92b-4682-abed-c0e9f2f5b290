import React from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CartesianGrid, 
  <PERSON><PERSON><PERSON>, 
  Legend, 
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  Alert<PERSON><PERSON>gle, 
  CheckCircle, 
  Shield, 
  Clock,
  Users,
  FileText
} from 'lucide-react';

const GovernanceMetrics = ({ metrics = {}, timeframe = '7d' }) => {
  // Sample data - in a real implementation, this would come from the metrics prop
  const policyComplianceData = [
    { name: 'Access Control', compliant: 85, violations: 15 },
    { name: 'Data Usage', compliant: 92, violations: 8 },
    { name: 'Model Deployment', compliant: 78, violations: 22 },
    { name: 'Security', compliant: 95, violations: 5 },
    { name: 'Privacy', compliant: 88, violations: 12 }
  ];

  const alertTrendData = [
    { date: '2024-01-01', critical: 2, high: 5, medium: 8, low: 12 },
    { date: '2024-01-02', critical: 1, high: 3, medium: 6, low: 10 },
    { date: '2024-01-03', critical: 3, high: 7, medium: 9, low: 15 },
    { date: '2024-01-04', critical: 0, high: 4, medium: 7, low: 11 },
    { date: '2024-01-05', critical: 1, high: 2, medium: 5, low: 8 },
    { date: '2024-01-06', critical: 2, high: 6, medium: 8, low: 13 },
    { date: '2024-01-07', critical: 1, high: 3, medium: 4, low: 9 }
  ];

  const complianceScoreData = [
    { framework: 'EU AI Act', score: 92 },
    { framework: 'NIST AI RMF', score: 88 },
    { framework: 'ISO 23053', score: 85 },
    { framework: 'GDPR', score: 95 },
    { framework: 'CCPA', score: 90 }
  ];

  const riskDistributionData = [
    { name: 'Low Risk', value: 65, color: '#10B981' },
    { name: 'Medium Risk', value: 25, color: '#F59E0B' },
    { name: 'High Risk', value: 8, color: '#EF4444' },
    { name: 'Critical Risk', value: 2, color: '#7C2D12' }
  ];

  const COLORS = ['#10B981', '#F59E0B', '#EF4444', '#7C2D12'];

  const MetricCard = ({ title, value, subtitle, icon, trend, color = 'blue' }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className={`text-2xl font-semibold text-${color}-600`}>{value}</p>
          {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
        </div>
        <div className={`p-3 bg-${color}-100 rounded-lg`}>
          {icon}
        </div>
      </div>
      {trend !== undefined && (
        <div className="mt-4 flex items-center">
          {trend > 0 ? (
            <TrendingUp className="h-4 w-4 text-green-500" />
          ) : (
            <TrendingDown className="h-4 w-4 text-red-500" />
          )}
          <span className={`text-sm ml-1 ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
            {trend > 0 ? '+' : ''}{trend}% from last period
          </span>
        </div>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Overall Compliance"
          value="89.2%"
          subtitle="Across all frameworks"
          icon={<CheckCircle className="h-6 w-6 text-green-600" />}
          trend={2.3}
          color="green"
        />
        <MetricCard
          title="Active Policies"
          value="24"
          subtitle="3 updated this week"
          icon={<Shield className="h-6 w-6 text-blue-600" />}
          trend={12.5}
          color="blue"
        />
        <MetricCard
          title="Open Alerts"
          value="7"
          subtitle="2 critical, 5 high"
          icon={<AlertTriangle className="h-6 w-6 text-red-600" />}
          trend={-15.2}
          color="red"
        />
        <MetricCard
          title="Audit Events"
          value="1,247"
          subtitle="Last 7 days"
          icon={<FileText className="h-6 w-6 text-purple-600" />}
          trend={8.7}
          color="purple"
        />
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Policy Compliance Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Policy Compliance by Type</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={policyComplianceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="compliant" stackId="a" fill="#10B981" name="Compliant" />
              <Bar dataKey="violations" stackId="a" fill="#EF4444" name="Violations" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Risk Distribution Pie Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Risk Distribution</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={riskDistributionData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {riskDistributionData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Alert Trends */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Alert Trends (Last 7 Days)</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={alertTrendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Area type="monotone" dataKey="critical" stackId="1" stroke="#7C2D12" fill="#7C2D12" />
              <Area type="monotone" dataKey="high" stackId="1" stroke="#EF4444" fill="#EF4444" />
              <Area type="monotone" dataKey="medium" stackId="1" stroke="#F59E0B" fill="#F59E0B" />
              <Area type="monotone" dataKey="low" stackId="1" stroke="#10B981" fill="#10B981" />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Compliance Scores */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Compliance Scores by Framework</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={complianceScoreData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" domain={[0, 100]} />
              <YAxis dataKey="framework" type="category" width={80} />
              <Tooltip />
              <Bar dataKey="score" fill="#3B82F6" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Detailed Metrics Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Detailed Governance Metrics</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Metric
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Current Value
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Target
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trend
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  Policy Compliance Rate
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">89.2%</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">≥95%</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                    Below Target
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">+2.3%</td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  Mean Time to Resolution
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">4.2 hours</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">≤6 hours</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                    On Target
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">-12%</td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  Critical Alert Rate
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">0.8/day</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">≤1/day</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                    On Target
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">-25%</td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  Audit Coverage
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">94.7%</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">≥90%</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                    Exceeds Target
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">+1.2%</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default GovernanceMetrics;
