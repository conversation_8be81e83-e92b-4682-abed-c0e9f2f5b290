import React, { useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { CheckCircle, Loader } from 'lucide-react';

const AuthSuccess = () => {
  const { login, user, loading } = useAuth();

  useEffect(() => {
    // Get token from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');

    if (token) {
      // Store token (this will trigger validation)
      login(token);
    } else {
      // No token found, redirect to login
      setTimeout(() => {
        window.location.href = '/';
      }, 3000);
    }
  }, [login]);

  // Redirect once user is authenticated
  useEffect(() => {
    if (user && !loading) {
      // User is authenticated, redirect to appropriate dashboard
      setTimeout(() => {
        // Determine which edition we're in based on the current path
        const currentPath = window.location.pathname;
        let redirectPath = '/enterprise'; // default to enterprise

        if (currentPath.startsWith('/standard')) {
          redirectPath = '/standard';
        } else if (currentPath.startsWith('/enterprise')) {
          redirectPath = '/enterprise';
        }

        window.location.href = redirectPath;
      }, 1000); // Shorter delay since validation is complete
    }
  }, [user, loading]);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <h2 className="text-lg font-medium text-gray-900 mb-2">
              Authentication Successful!
            </h2>
            <p className="text-sm text-gray-600 mb-4">
              {loading ? 'Validating your credentials...' : user ? 'Redirecting to dashboard...' : 'Processing authentication...'}
            </p>
            <div className="flex items-center justify-center">
              <Loader className="h-5 w-5 text-blue-600 animate-spin" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthSuccess;
