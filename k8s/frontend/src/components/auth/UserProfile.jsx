import React, { useState } from 'react';
import { User, Settings, LogOut, Shield, Edit2, Save, X } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

const UserProfile = ({ isDropdown = false, onClose = null }) => {
  const { user, logout, updateProfile, isAdmin } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    name: user?.name || '',
    metadata: user?.metadata || {},
  });
  const [loading, setLoading] = useState(false);

  const handleSave = async () => {
    setLoading(true);
    try {
      await updateProfile(editData);
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
      alert('Failed to update profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setEditData({
      name: user?.name || '',
      metadata: user?.metadata || {},
    });
    setIsEditing(false);
  };

  const handleLogout = async () => {
    try {
      await logout();
      if (onClose) onClose();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  if (!user) return null;

  const containerClass = isDropdown
    ? 'bg-white rounded-lg shadow-lg border border-gray-200 p-4 min-w-80'
    : 'bg-white rounded-lg shadow-sm border border-gray-200 p-6 max-w-md mx-auto';

  return (
    <div className={containerClass}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Profile</h3>
        {isDropdown && onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        )}
      </div>

      {/* User Avatar and Basic Info */}
      <div className="flex items-center space-x-3 mb-4">
        {user.picture ? (
          <img
            src={user.picture}
            alt={user.name}
            className="h-12 w-12 rounded-full"
          />
        ) : (
          <div className="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center">
            <User className="h-6 w-6 text-gray-600" />
          </div>
        )}
        <div className="flex-1">
          {isEditing ? (
            <input
              type="text"
              value={editData.name}
              onChange={(e) => setEditData({ ...editData, name: e.target.value })}
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm font-medium"
              placeholder="Your name"
            />
          ) : (
            <h4 className="text-sm font-medium text-gray-900">{user.name}</h4>
          )}
          <p className="text-sm text-gray-500">{user.email}</p>
        </div>
      </div>

      {/* Roles */}
      <div className="mb-4">
        <div className="flex flex-wrap gap-1">
          {user.roles?.map((role) => (
            <span
              key={role}
              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                role === 'admin'
                  ? 'bg-red-100 text-red-800'
                  : 'bg-blue-100 text-blue-800'
              }`}
            >
              {role === 'admin' && <Shield className="h-3 w-3 mr-1" />}
              {role}
            </span>
          ))}
        </div>
      </div>

      {/* Status */}
      <div className="mb-4">
        <span
          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            user.status === 'active'
              ? 'bg-green-100 text-green-800'
              : 'bg-yellow-100 text-yellow-800'
          }`}
        >
          {user.status}
        </span>
      </div>

      {/* Last Login */}
      {user.last_login_at && (
        <div className="mb-4 text-xs text-gray-500">
          Last login: {new Date(user.last_login_at).toLocaleString()}
        </div>
      )}

      {/* Actions */}
      <div className="flex justify-between items-center pt-4 border-t border-gray-200">
        {isEditing ? (
          <div className="flex space-x-2">
            <button
              onClick={handleSave}
              disabled={loading}
              className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <Save className="h-3 w-3 mr-1" />
              {loading ? 'Saving...' : 'Save'}
            </button>
            <button
              onClick={handleCancel}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <X className="h-3 w-3 mr-1" />
              Cancel
            </button>
          </div>
        ) : (
          <button
            onClick={() => setIsEditing(true)}
            className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Edit2 className="h-3 w-3 mr-1" />
            Edit
          </button>
        )}

        <button
          onClick={handleLogout}
          className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          <LogOut className="h-3 w-3 mr-1" />
          Logout
        </button>
      </div>
    </div>
  );
};

export default UserProfile;
