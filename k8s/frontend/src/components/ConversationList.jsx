import React, { useState } from 'react';
import { MessageSquare, Plus, Trash2, Edit3, Check, X, MoreVertical } from 'lucide-react';

const ConversationList = ({ 
  conversations, 
  activeConversationId, 
  onSelectConversation, 
  onCreateConversation, 
  onDeleteConversation,
  onUpdateTitle,
  className = ''
}) => {
  const [editingId, setEditingId] = useState(null);
  const [editTitle, setEditTitle] = useState('');
  const [showDropdown, setShowDropdown] = useState(null);

  const handleStartEdit = (conversation) => {
    setEditingId(conversation.id);
    setEditTitle(conversation.title);
    setShowDropdown(null);
  };

  const handleSaveEdit = () => {
    if (editTitle.trim() && editingId) {
      onUpdateTitle(editingId, editTitle.trim());
    }
    setEditingId(null);
    setEditTitle('');
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditTitle('');
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return 'Today';
    } else if (diffDays === 2) {
      return 'Yesterday';
    } else if (diffDays <= 7) {
      return `${diffDays - 1} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getLastMessage = (conversation) => {
    if (conversation.messages.length === 0) return 'No messages yet';
    const lastMessage = conversation.messages[conversation.messages.length - 1];
    const content = lastMessage.content.length > 50 
      ? lastMessage.content.substring(0, 47) + '...' 
      : lastMessage.content;
    return content || 'Empty message';
  };

  return (
    <div className={`flex flex-col h-full bg-gray-50 border-r border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-800">Conversations</h2>
          <button
            onClick={onCreateConversation}
            className="p-2 text-indigo-600 hover:text-indigo-700 hover:bg-indigo-50 rounded-lg transition-colors"
            title="New conversation"
          >
            <Plus size={20} />
          </button>
        </div>
      </div>

      {/* Conversation list */}
      <div className="flex-1 overflow-y-auto">
        {conversations.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-500 p-4">
            <MessageSquare size={48} className="mb-4 opacity-50" />
            <p className="text-center">No conversations yet</p>
            <p className="text-sm text-center mt-1">Start a new chat to begin</p>
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {conversations.map((conversation) => (
              <div
                key={conversation.id}
                className={`relative group rounded-lg transition-colors cursor-pointer ${
                  activeConversationId === conversation.id
                    ? 'bg-indigo-100 border border-indigo-200'
                    : 'hover:bg-gray-100'
                }`}
                onClick={() => onSelectConversation(conversation.id)}
              >
                <div className="p-3">
                  {/* Title */}
                  {editingId === conversation.id ? (
                    <div className="flex items-center space-x-2" onClick={(e) => e.stopPropagation()}>
                      <input
                        type="text"
                        value={editTitle}
                        onChange={(e) => setEditTitle(e.target.value)}
                        onKeyDown={handleKeyDown}
                        className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        autoFocus
                      />
                      <button
                        onClick={handleSaveEdit}
                        className="p-1 text-green-600 hover:text-green-700"
                        title="Save"
                      >
                        <Check size={14} />
                      </button>
                      <button
                        onClick={handleCancelEdit}
                        className="p-1 text-red-600 hover:text-red-700"
                        title="Cancel"
                      >
                        <X size={14} />
                      </button>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between">
                      <h3 className={`font-medium text-sm truncate ${
                        activeConversationId === conversation.id ? 'text-indigo-800' : 'text-gray-800'
                      }`}>
                        {conversation.title}
                      </h3>
                      
                      {/* Dropdown menu */}
                      <div className="relative">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowDropdown(showDropdown === conversation.id ? null : conversation.id);
                          }}
                          className="opacity-0 group-hover:opacity-100 p-1 text-gray-500 hover:text-gray-700 rounded transition-opacity"
                          title="More options"
                        >
                          <MoreVertical size={14} />
                        </button>

                        {showDropdown === conversation.id && (
                          <div className="absolute right-0 top-full mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleStartEdit(conversation);
                              }}
                              className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-t-lg"
                            >
                              <Edit3 size={14} />
                              <span>Rename</span>
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onDeleteConversation(conversation.id);
                                setShowDropdown(null);
                              }}
                              className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-b-lg"
                            >
                              <Trash2 size={14} />
                              <span>Delete</span>
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Last message preview */}
                  <p className={`text-xs mt-1 truncate ${
                    activeConversationId === conversation.id ? 'text-indigo-600' : 'text-gray-500'
                  }`}>
                    {getLastMessage(conversation)}
                  </p>

                  {/* Date */}
                  <p className={`text-xs mt-1 ${
                    activeConversationId === conversation.id ? 'text-indigo-500' : 'text-gray-400'
                  }`}>
                    {formatDate(conversation.updatedAt)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Click outside to close dropdown */}
      {showDropdown && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowDropdown(null)}
        />
      )}
    </div>
  );
};

export default ConversationList;
