import React, { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  Star,
  Download,
  DollarSign,
  Clock,
  Shield,
  Zap,
  Users,
  Eye,
  Plus,
  CheckCircle,
  AlertTriangle,
  ExternalLink,
  Tag,
  TrendingUp
} from 'lucide-react';

const AgentMarketplace = ({ showNotification, onClose }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [agents, setAgents] = useState([]);
  const [filteredAgents, setFilteredAgents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    categories: [],
    types: [],
    priceRange: { min: 0, max: 1000 },
    minRating: 0,
    sortBy: 'rating'
  });

  const categories = [
    'analytics', 'data-science', 'content', 'automation', 
    'research', 'validation', 'communication', 'optimization'
  ];

  const agentTypes = [
    'data_analyst', 'content_writer', 'code_generator', 
    'researcher', 'validator', 'coordinator', 'specialist', 'generalist'
  ];

  useEffect(() => {
    fetchMarketplaceAgents();
  }, []);

  useEffect(() => {
    filterAndSortAgents();
  }, [agents, searchQuery, filters]);

  const fetchMarketplaceAgents = async () => {
    setLoading(true);
    try {
      // Mock data for demonstration
      const mockAgents = [
        {
          id: 'huggingface-data-analyst',
          provider_id: 'huggingface',
          name: 'Advanced Data Analyst Pro',
          description: 'State-of-the-art data analysis agent with machine learning capabilities',
          type: 'data_analyst',
          categories: ['analytics', 'data-science'],
          capabilities: [
            { id: 'statistical_analysis', name: 'Statistical Analysis', quality: 0.95 },
            { id: 'data_visualization', name: 'Data Visualization', quality: 0.90 },
            { id: 'ml_modeling', name: 'ML Modeling', quality: 0.88 }
          ],
          pricing: {
            type: 'per_request',
            base_price: 0.15,
            currency: 'USD',
            unit: 'request'
          },
          performance: {
            average_latency: 250,
            success_rate: 0.98,
            quality_score: 0.95,
            reliability: 0.99
          },
          rating: 4.8,
          review_count: 156,
          downloads: 2340,
          tags: ['data', 'analytics', 'ml', 'python'],
          license: 'MIT',
          support_level: 'community',
          last_updated: new Date('2024-01-15'),
          documentation: 'https://example.com/docs'
        },
        {
          id: 'openai-content-writer',
          provider_id: 'openai',
          name: 'GPT-4 Content Creator',
          description: 'Professional content writing agent powered by GPT-4',
          type: 'content_writer',
          categories: ['content', 'automation'],
          capabilities: [
            { id: 'content_writing', name: 'Content Writing', quality: 0.96 },
            { id: 'copywriting', name: 'Copywriting', quality: 0.94 },
            { id: 'editing', name: 'Editing', quality: 0.92 }
          ],
          pricing: {
            type: 'per_request',
            base_price: 0.25,
            currency: 'USD',
            unit: 'request'
          },
          performance: {
            average_latency: 180,
            success_rate: 0.97,
            quality_score: 0.96,
            reliability: 0.98
          },
          rating: 4.9,
          review_count: 324,
          downloads: 5670,
          tags: ['content', 'writing', 'gpt-4', 'creative'],
          license: 'Commercial',
          support_level: 'enterprise',
          last_updated: new Date('2024-01-20'),
          documentation: 'https://openai.com/docs'
        },
        {
          id: 'anthropic-researcher',
          provider_id: 'anthropic',
          name: 'Claude Research Assistant',
          description: 'Comprehensive research agent with fact-checking capabilities',
          type: 'researcher',
          categories: ['research', 'validation'],
          capabilities: [
            { id: 'web_research', name: 'Web Research', quality: 0.93 },
            { id: 'fact_checking', name: 'Fact Checking', quality: 0.97 },
            { id: 'source_analysis', name: 'Source Analysis', quality: 0.91 }
          ],
          pricing: {
            type: 'subscription',
            base_price: 20,
            currency: 'USD',
            unit: 'month'
          },
          performance: {
            average_latency: 320,
            success_rate: 0.96,
            quality_score: 0.94,
            reliability: 0.97
          },
          rating: 4.7,
          review_count: 89,
          downloads: 1230,
          tags: ['research', 'fact-check', 'claude', 'analysis'],
          license: 'Commercial',
          support_level: 'professional',
          last_updated: new Date('2024-01-18'),
          documentation: 'https://anthropic.com/docs'
        }
      ];

      setAgents(mockAgents);
    } catch (error) {
      console.error('Failed to fetch marketplace agents:', error);
      showNotification('Failed to load marketplace agents', 'error');
    } finally {
      setLoading(false);
    }
  };

  const filterAndSortAgents = () => {
    let filtered = agents.filter(agent => {
      // Search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesName = agent.name.toLowerCase().includes(query);
        const matchesDescription = agent.description.toLowerCase().includes(query);
        const matchesTags = agent.tags.some(tag => tag.toLowerCase().includes(query));
        
        if (!matchesName && !matchesDescription && !matchesTags) {
          return false;
        }
      }

      // Category filter
      if (filters.categories.length > 0) {
        const hasCategory = filters.categories.some(cat => 
          agent.categories.includes(cat)
        );
        if (!hasCategory) return false;
      }

      // Type filter
      if (filters.types.length > 0) {
        if (!filters.types.includes(agent.type)) return false;
      }

      // Rating filter
      if (filters.minRating > 0 && agent.rating < filters.minRating) {
        return false;
      }

      // Price filter
      if (agent.pricing.base_price < filters.priceRange.min || 
          agent.pricing.base_price > filters.priceRange.max) {
        return false;
      }

      return true;
    });

    // Sort agents
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'downloads':
          return b.downloads - a.downloads;
        case 'price':
          return a.pricing.base_price - b.pricing.base_price;
        case 'performance':
          return b.performance.quality_score - a.performance.quality_score;
        case 'name':
          return a.name.localeCompare(b.name);
        default:
          return b.rating - a.rating;
      }
    });

    setFilteredAgents(filtered);
  };

  const handleIntegrateAgent = async (agent) => {
    try {
      // Mock integration - in real implementation, this would call the API
      const config = {
        endpoint: `https://api.${agent.provider_id}.com/agents/${agent.id}`,
        api_key: 'your-api-key-here'
      };

      showNotification(`Successfully integrated ${agent.name}`, 'success');
      
      // Close marketplace after integration
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (error) {
      showNotification(`Failed to integrate ${agent.name}: ${error.message}`, 'error');
    }
  };

  const AgentCard = ({ agent }) => (
    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Users size={24} className="text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{agent.name}</h3>
            <p className="text-sm text-gray-500">{agent.type.replace('_', ' ')}</p>
          </div>
        </div>
        <div className="flex items-center space-x-1">
          <Star size={16} className="text-yellow-400 fill-current" />
          <span className="text-sm font-medium">{agent.rating}</span>
          <span className="text-xs text-gray-500">({agent.review_count})</span>
        </div>
      </div>

      <p className="text-gray-600 text-sm mb-4 line-clamp-2">{agent.description}</p>

      {/* Categories */}
      <div className="flex flex-wrap gap-1 mb-4">
        {agent.categories.slice(0, 3).map(category => (
          <span key={category} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
            {category}
          </span>
        ))}
        {agent.categories.length > 3 && (
          <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
            +{agent.categories.length - 3}
          </span>
        )}
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-3 gap-2 mb-4 text-xs">
        <div className="text-center">
          <div className="font-medium text-gray-900">{(agent.performance.success_rate * 100).toFixed(0)}%</div>
          <div className="text-gray-500">Success</div>
        </div>
        <div className="text-center">
          <div className="font-medium text-gray-900">{agent.performance.average_latency}ms</div>
          <div className="text-gray-500">Latency</div>
        </div>
        <div className="text-center">
          <div className="font-medium text-gray-900">{(agent.performance.quality_score * 100).toFixed(0)}%</div>
          <div className="text-gray-500">Quality</div>
        </div>
      </div>

      {/* Pricing */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-1">
          <DollarSign size={16} className="text-green-600" />
          <span className="font-medium text-gray-900">
            ${agent.pricing.base_price}
          </span>
          <span className="text-sm text-gray-500">/{agent.pricing.unit}</span>
        </div>
        <div className="flex items-center space-x-1 text-sm text-gray-500">
          <Download size={14} />
          <span>{agent.downloads.toLocaleString()}</span>
        </div>
      </div>

      {/* Actions */}
      <div className="flex space-x-2">
        <button
          onClick={() => setSelectedAgent(agent)}
          className="flex-1 px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <Eye size={14} className="inline mr-1" />
          Details
        </button>
        <button
          onClick={() => handleIntegrateAgent(agent)}
          className="flex-1 px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
        >
          <Plus size={14} className="inline mr-1" />
          Integrate
        </button>
      </div>
    </div>
  );

  const FilterPanel = () => (
    <div className="bg-white rounded-lg shadow p-6 space-y-6">
      <h3 className="font-semibold text-gray-900">Filters</h3>
      
      {/* Categories */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Categories</label>
        <div className="space-y-2">
          {categories.map(category => (
            <label key={category} className="flex items-center">
              <input
                type="checkbox"
                checked={filters.categories.includes(category)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setFilters(prev => ({
                      ...prev,
                      categories: [...prev.categories, category]
                    }));
                  } else {
                    setFilters(prev => ({
                      ...prev,
                      categories: prev.categories.filter(c => c !== category)
                    }));
                  }
                }}
                className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
              />
              <span className="ml-2 text-sm text-gray-700 capitalize">{category}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Agent Types */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Agent Types</label>
        <div className="space-y-2">
          {agentTypes.map(type => (
            <label key={type} className="flex items-center">
              <input
                type="checkbox"
                checked={filters.types.includes(type)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setFilters(prev => ({
                      ...prev,
                      types: [...prev.types, type]
                    }));
                  } else {
                    setFilters(prev => ({
                      ...prev,
                      types: prev.types.filter(t => t !== type)
                    }));
                  }
                }}
                className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
              />
              <span className="ml-2 text-sm text-gray-700">{type.replace('_', ' ')}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Rating */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Minimum Rating: {filters.minRating}
        </label>
        <input
          type="range"
          min="0"
          max="5"
          step="0.1"
          value={filters.minRating}
          onChange={(e) => setFilters(prev => ({ ...prev, minRating: parseFloat(e.target.value) }))}
          className="w-full"
        />
      </div>

      {/* Sort By */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
        <select
          value={filters.sortBy}
          onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value }))}
          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
        >
          <option value="rating">Rating</option>
          <option value="downloads">Downloads</option>
          <option value="price">Price</option>
          <option value="performance">Performance</option>
          <option value="name">Name</option>
        </select>
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-100 rounded-lg shadow-xl w-full max-w-7xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="bg-white border-b px-6 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Agent Marketplace</h2>
              <p className="text-gray-600">Discover and integrate AI agents from the community</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>

          {/* Search and Controls */}
          <div className="mt-4 flex space-x-4">
            <div className="flex-1 relative">
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search agents..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-2 border rounded-lg flex items-center space-x-2 ${
                showFilters ? 'bg-indigo-50 border-indigo-300 text-indigo-700' : 'border-gray-300 text-gray-700'
              }`}
            >
              <Filter size={16} />
              <span>Filters</span>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Filters Sidebar */}
          {showFilters && (
            <div className="w-80 p-6 overflow-y-auto">
              <FilterPanel />
            </div>
          )}

          {/* Main Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
              </div>
            ) : (
              <>
                <div className="mb-4 flex justify-between items-center">
                  <p className="text-gray-600">
                    {filteredAgents.length} agents found
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredAgents.map(agent => (
                    <AgentCard key={agent.id} agent={agent} />
                  ))}
                </div>

                {filteredAgents.length === 0 && !loading && (
                  <div className="text-center py-12">
                    <Users size={48} className="mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No agents found</h3>
                    <p className="text-gray-500">Try adjusting your search or filters</p>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Agent Details Modal */}
      {selectedAgent && (
        <AgentDetailsModal
          agent={selectedAgent}
          onClose={() => setSelectedAgent(null)}
          onIntegrate={handleIntegrateAgent}
        />
      )}
    </div>
  );
};

// Agent Details Modal Component
const AgentDetailsModal = ({ agent, onClose, onIntegrate }) => (
  <div className="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-60">
    <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[80vh] overflow-y-auto">
      <div className="p-6">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{agent.name}</h2>
            <p className="text-gray-600">{agent.description}</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Info */}
          <div className="lg:col-span-2 space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Capabilities</h3>
              <div className="space-y-2">
                {agent.capabilities.map((capability, index) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium">{capability.name}</span>
                    <span className="text-sm text-gray-600">Quality: {(capability.quality * 100).toFixed(0)}%</span>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Performance Metrics</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{(agent.performance.success_rate * 100).toFixed(1)}%</div>
                  <div className="text-sm text-blue-600">Success Rate</div>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{agent.performance.average_latency}ms</div>
                  <div className="text-sm text-green-600">Avg Latency</div>
                </div>
                <div className="p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{(agent.performance.quality_score * 100).toFixed(1)}%</div>
                  <div className="text-sm text-purple-600">Quality Score</div>
                </div>
                <div className="p-4 bg-yellow-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">{(agent.performance.reliability * 100).toFixed(1)}%</div>
                  <div className="text-sm text-yellow-600">Reliability</div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-3">Pricing</h4>
              <div className="text-2xl font-bold text-gray-900">
                ${agent.pricing.base_price}
                <span className="text-sm font-normal text-gray-600">/{agent.pricing.unit}</span>
              </div>
              <div className="text-sm text-gray-600 capitalize">{agent.pricing.type}</div>
            </div>

            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-3">Details</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Rating:</span>
                  <div className="flex items-center">
                    <Star size={14} className="text-yellow-400 fill-current mr-1" />
                    <span>{agent.rating} ({agent.review_count})</span>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Downloads:</span>
                  <span>{agent.downloads.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">License:</span>
                  <span>{agent.license}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Support:</span>
                  <span className="capitalize">{agent.support_level}</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <button
                onClick={() => onIntegrate(agent)}
                className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
              >
                <Plus size={16} className="inline mr-2" />
                Integrate Agent
              </button>
              <a
                href={agent.documentation}
                target="_blank"
                rel="noopener noreferrer"
                className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 flex items-center justify-center"
              >
                <ExternalLink size={16} className="mr-2" />
                Documentation
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default AgentMarketplace;
