import React, { useState } from 'react';
import {
  Zap,
  DollarSign,
  Clock,
  Shield,
  CheckCircle,
  ArrowRight,
  Star,
  Users,
  TrendingUp,
  MessageSquare,
  FileText,
  BarChart3
} from 'lucide-react';
import LoginButton from './auth/LoginButton';

const StartupLanding = ({ onGetStarted }) => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSignup = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate signup process
    setTimeout(() => {
      setIsSubmitting(false);
      if (onGetStarted) {
        onGetStarted();
      }
    }, 1000);
  };

  const features = [
    {
      icon: <Zap className="h-6 w-6 text-indigo-600" />,
      title: "Intelligent LLM Routing",
      description: "Automatically route requests to the best LLM based on cost, performance, and quality."
    },
    {
      icon: <DollarSign className="h-6 w-6 text-green-600" />,
      title: "20-30% Cost Savings",
      description: "Reduce your AI costs with smart routing and optimization algorithms."
    },
    {
      icon: <Clock className="h-6 w-6 text-blue-600" />,
      title: "Deploy in 7 Days",
      description: "Get your AI chatbot up and running in less than a week with our no-code interface."
    },
    {
      icon: <FileText className="h-6 w-6 text-purple-600" />,
      title: "PromptOps Management",
      description: "Version control, A/B testing, and optimization for your prompts."
    },
    {
      icon: <BarChart3 className="h-6 w-6 text-orange-600" />,
      title: "Real-time Analytics",
      description: "Track usage, costs, and performance with comprehensive dashboards."
    },
    {
      icon: <Shield className="h-6 w-6 text-red-600" />,
      title: "Enterprise Security",
      description: "Built-in governance and compliance features for peace of mind."
    }
  ];

  const pricingTiers = [
    {
      name: "Free",
      price: "$0",
      period: "/month",
      description: "Perfect for getting started",
      features: [
        "1,000 API calls/month",
        "Basic chat interface",
        "Simple analytics",
        "Community support"
      ],
      cta: "Start Free",
      popular: false
    },
    {
      name: "Starter",
      price: "$29",
      period: "/month",
      description: "For growing startups",
      features: [
        "10,000 API calls/month",
        "Full PromptOps features",
        "3 integrations",
        "Email support",
        "A/B testing"
      ],
      cta: "Start Trial",
      popular: true
    },
    {
      name: "Growth",
      price: "$99",
      period: "/month",
      description: "For scaling businesses",
      features: [
        "50,000 API calls/month",
        "Unlimited integrations",
        "Advanced analytics",
        "Priority support",
        "Custom workflows"
      ],
      cta: "Contact Sales",
      popular: false
    }
  ];

  const testimonials = [
    {
      name: "Sarah Chen",
      role: "CTO, TechStart",
      content: "AI Operations Hub helped us reduce our LLM costs by 35% while improving response quality. The no-code interface made deployment incredibly fast.",
      avatar: "SC"
    },
    {
      name: "Mike Rodriguez",
      role: "Founder, ChatBot Pro",
      content: "We went from idea to production in just 5 days. The intelligent routing saved us thousands in the first month alone.",
      avatar: "MR"
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
            <main className="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
              <div className="sm:text-center lg:text-left">
                <h1 className="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
                  <span className="block xl:inline">Affordable</span>{' '}
                  <span className="block text-indigo-600 xl:inline">no-code AI</span>{' '}
                  <span className="block xl:inline">for startups</span>
                </h1>
                <p className="mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0">
                  Intelligent LLM routing, cost optimization, and no-code prompt management. 
                  Deploy AI chatbots in under 7 days and save 20-30% on your AI costs.
                </p>
                <div className="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
                  <div className="rounded-md shadow">
                    <button
                      onClick={() => onGetStarted && onGetStarted()}
                      className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10"
                    >
                      Start Free Trial
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </button>
                  </div>
                  <div className="mt-3 sm:mt-0 sm:ml-3">
                    <button className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 md:py-4 md:text-lg md:px-10">
                      Watch Demo
                    </button>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
        <div className="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
          <div className="h-56 w-full bg-gradient-to-br from-indigo-500 to-purple-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center">
            <div className="text-white text-center">
              <MessageSquare className="h-24 w-24 mx-auto mb-4 opacity-80" />
              <p className="text-xl font-semibold">AI Operations Hub</p>
              <p className="text-indigo-200">Startup Edition</p>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base text-indigo-600 font-semibold tracking-wide uppercase">Features</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              Everything you need to scale your AI operations
            </p>
          </div>

          <div className="mt-10">
            <div className="space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10">
              {features.map((feature, index) => (
                <div key={index} className="relative">
                  <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white">
                    {feature.icon}
                  </div>
                  <p className="ml-16 text-lg leading-6 font-medium text-gray-900">{feature.title}</p>
                  <p className="mt-2 ml-16 text-base text-gray-500">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Section */}
      <div className="bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="sm:text-center">
            <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              Simple, transparent pricing
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Choose the plan that's right for your startup
            </p>
          </div>
          <div className="mt-12 space-y-4 sm:mt-16 sm:space-y-0 sm:grid sm:grid-cols-3 sm:gap-6 lg:max-w-4xl lg:mx-auto xl:max-w-none xl:mx-0">
            {pricingTiers.map((tier, index) => (
              <div key={index} className={`border border-gray-200 rounded-lg shadow-sm divide-y divide-gray-200 ${tier.popular ? 'border-indigo-500 shadow-lg' : ''}`}>
                {tier.popular && (
                  <div className="bg-indigo-500 text-white text-center py-2 rounded-t-lg">
                    <span className="text-sm font-medium">Most Popular</span>
                  </div>
                )}
                <div className="p-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">{tier.name}</h3>
                  <p className="mt-4 text-sm text-gray-500">{tier.description}</p>
                  <p className="mt-8">
                    <span className="text-4xl font-extrabold text-gray-900">{tier.price}</span>
                    <span className="text-base font-medium text-gray-500">{tier.period}</span>
                  </p>
                  <button className={`mt-8 block w-full border border-transparent rounded-md py-2 text-sm font-semibold text-center ${tier.popular ? 'bg-indigo-600 text-white hover:bg-indigo-700' : 'bg-indigo-50 text-indigo-700 hover:bg-indigo-100'}`}>
                    {tier.cta}
                  </button>
                </div>
                <div className="pt-6 pb-8 px-6">
                  <h4 className="text-xs font-medium text-gray-900 tracking-wide uppercase">What's included</h4>
                  <ul className="mt-6 space-y-4">
                    {tier.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex space-x-3">
                        <CheckCircle className="flex-shrink-0 h-5 w-5 text-green-500" />
                        <span className="text-sm text-gray-500">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Testimonials */}
      <div className="bg-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900">Trusted by innovative startups</h2>
          </div>
          <div className="mt-12 grid grid-cols-1 gap-8 lg:grid-cols-2">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 rounded-full bg-indigo-500 flex items-center justify-center text-white font-medium">
                      {testimonial.avatar}
                    </div>
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-gray-900">{testimonial.name}</div>
                    <div className="text-sm text-gray-500">{testimonial.role}</div>
                  </div>
                </div>
                <p className="mt-4 text-gray-600">"{testimonial.content}"</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-indigo-700">
        <div className="max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-extrabold text-white sm:text-4xl">
            <span className="block">Ready to get started?</span>
            <span className="block">Start your free trial today.</span>
          </h2>
          <p className="mt-4 text-lg leading-6 text-indigo-200">
            Join hundreds of startups already saving money and time with AI Operations Hub.
          </p>
          <div className="mt-8 flex justify-center">
            <LoginButton size="large" className="px-8 py-4 text-lg" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default StartupLanding;
