import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Play,
  Pause,
  Square,
  CheckCircle,
  AlertTriangle,
  Clock,
  Users,
  MessageSquare,
  Zap,
  Network,
  Activity,
  ArrowRight,
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Maximize2
} from 'lucide-react';
import { MULTI_AGENT_API_PREFIX } from '../utils/constants';

const WorkflowVisualization = ({ workflowId, onClose }) => {
  const [workflow, setWorkflow] = useState(null);
  const [execution, setExecution] = useState(null);
  const [messages, setMessages] = useState([]);
  const [agents, setAgents] = useState([]);
  const [viewMode, setViewMode] = useState('graph'); // 'graph', 'timeline', 'network'
  const [selectedNode, setSelectedNode] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const svgRef = useRef(null);
  const intervalRef = useRef(null);

  // Fetch workflow data
  useEffect(() => {
    fetchWorkflowData();
    
    // Set up real-time updates
    if (isPlaying) {
      intervalRef.current = setInterval(fetchWorkflowData, 2000);
    }
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [workflowId, isPlaying]);

  const fetchWorkflowData = async () => {
    try {
      // Fetch workflow details
      const workflowResponse = await fetch(`${MULTI_AGENT_API_PREFIX}/workflows/${workflowId}`);
      if (workflowResponse.ok) {
        const workflowData = await workflowResponse.json();
        setWorkflow(workflowData);
      }

      // Fetch execution status
      const executionResponse = await fetch(`${MULTI_AGENT_API_PREFIX}/workflows/${workflowId}/status`);
      if (executionResponse.ok) {
        const executionData = await executionResponse.json();
        setExecution(executionData);
      }

      // Fetch agents
      const agentsResponse = await fetch(`${MULTI_AGENT_API_PREFIX}/agents`);
      if (agentsResponse.ok) {
        const agentsData = await agentsResponse.json();
        setAgents(agentsData);
      }

      // Fetch recent messages (simulated for now)
      // In a real implementation, this would come from the communication hub
      setMessages(generateMockMessages());
    } catch (error) {
      console.error('Failed to fetch workflow data:', error);
    }
  };

  const generateMockMessages = () => {
    // Generate mock inter-agent messages for visualization
    return [
      {
        id: '1',
        from: 'agent-1',
        to: 'agent-2',
        type: 'task_request',
        timestamp: new Date(Date.now() - 30000),
        content: 'Data analysis complete, sending results'
      },
      {
        id: '2',
        from: 'agent-2',
        to: 'agent-3',
        type: 'data_share',
        timestamp: new Date(Date.now() - 15000),
        content: 'Processed data ready for report generation'
      }
    ];
  };

  const getAgentById = (agentId) => {
    return agents.find(a => a.id === agentId) || { id: agentId, name: `Agent ${agentId}`, status: 'unknown' };
  };

  const getTaskStatus = (taskId) => {
    if (!execution) return 'pending';
    
    if (execution.completed_tasks?.includes(taskId)) return 'completed';
    if (execution.failed_tasks?.includes(taskId)) return 'failed';
    if (execution.current_tasks?.includes(taskId)) return 'running';
    return 'pending';
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: '#94a3b8',
      running: '#f59e0b',
      completed: '#10b981',
      failed: '#ef4444',
      unknown: '#6b7280'
    };
    return colors[status] || colors.unknown;
  };

  const getStatusIcon = (status) => {
    const icons = {
      pending: <Clock size={16} />,
      running: <Activity size={16} className="animate-pulse" />,
      completed: <CheckCircle size={16} />,
      failed: <AlertTriangle size={16} />,
      unknown: <Clock size={16} />
    };
    return icons[status] || icons.unknown;
  };

  // Graph layout calculation
  const calculateLayout = useCallback(() => {
    if (!workflow || !workflow.tasks) return { nodes: [], edges: [] };

    const tasks = workflow.tasks;
    const dependencies = workflow.dependencies || [];
    
    // Simple force-directed layout
    const nodes = tasks.map((task, index) => {
      const agent = getAgentById(task.assigned_agent_id);
      const status = getTaskStatus(task.id);
      
      return {
        id: task.id,
        label: task.name,
        x: 100 + (index % 3) * 200,
        y: 100 + Math.floor(index / 3) * 150,
        status,
        agent,
        task
      };
    });

    const edges = dependencies.map(dep => ({
      id: `${dep.from_task_id}-${dep.to_task_id}`,
      from: dep.from_task_id,
      to: dep.to_task_id,
      type: dep.type || 'dependency'
    }));

    return { nodes, edges };
  }, [workflow, execution, agents]);

  const { nodes, edges } = calculateLayout();

  const handleNodeClick = (node) => {
    setSelectedNode(node);
  };

  const handleZoom = (delta) => {
    setZoom(prev => Math.max(0.1, Math.min(3, prev + delta)));
  };

  const handleReset = () => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
    setSelectedNode(null);
  };

  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
  };

  // Graph View Component
  const GraphView = () => (
    <div className="relative w-full h-full bg-gray-50 overflow-hidden">
      <svg
        ref={svgRef}
        className="w-full h-full"
        viewBox={`${-pan.x} ${-pan.y} ${800 / zoom} ${600 / zoom}`}
      >
        {/* Edges */}
        {edges.map(edge => {
          const fromNode = nodes.find(n => n.id === edge.from);
          const toNode = nodes.find(n => n.id === edge.to);
          if (!fromNode || !toNode) return null;

          return (
            <g key={edge.id}>
              <line
                x1={fromNode.x}
                y1={fromNode.y}
                x2={toNode.x}
                y2={toNode.y}
                stroke="#94a3b8"
                strokeWidth="2"
                markerEnd="url(#arrowhead)"
              />
            </g>
          );
        })}

        {/* Nodes */}
        {nodes.map(node => (
          <g key={node.id} onClick={() => handleNodeClick(node)} className="cursor-pointer">
            <circle
              cx={node.x}
              cy={node.y}
              r="30"
              fill={getStatusColor(node.status)}
              stroke={selectedNode?.id === node.id ? '#3b82f6' : '#ffffff'}
              strokeWidth={selectedNode?.id === node.id ? '3' : '2'}
              className="transition-all duration-200 hover:stroke-blue-500"
            />
            <text
              x={node.x}
              y={node.y + 5}
              textAnchor="middle"
              className="text-xs font-medium fill-white pointer-events-none"
            >
              {node.label.length > 10 ? node.label.substring(0, 10) + '...' : node.label}
            </text>
          </g>
        ))}

        {/* Arrow marker definition */}
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon
              points="0 0, 10 3.5, 0 7"
              fill="#94a3b8"
            />
          </marker>
        </defs>
      </svg>

      {/* Controls */}
      <div className="absolute top-4 right-4 flex flex-col space-y-2">
        <button
          onClick={() => handleZoom(0.1)}
          className="p-2 bg-white rounded-lg shadow-md hover:bg-gray-50"
        >
          <ZoomIn size={16} />
        </button>
        <button
          onClick={() => handleZoom(-0.1)}
          className="p-2 bg-white rounded-lg shadow-md hover:bg-gray-50"
        >
          <ZoomOut size={16} />
        </button>
        <button
          onClick={handleReset}
          className="p-2 bg-white rounded-lg shadow-md hover:bg-gray-50"
        >
          <RotateCcw size={16} />
        </button>
      </div>
    </div>
  );

  // Timeline View Component
  const TimelineView = () => (
    <div className="w-full h-full bg-white p-6 overflow-y-auto">
      <h3 className="text-lg font-semibold mb-4">Execution Timeline</h3>
      <div className="space-y-4">
        {workflow?.tasks?.map((task, index) => {
          const status = getTaskStatus(task.id);
          const agent = getAgentById(task.assigned_agent_id);
          
          return (
            <div key={task.id} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex-shrink-0">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white`}
                     style={{ backgroundColor: getStatusColor(status) }}>
                  {getStatusIcon(status)}
                </div>
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{task.name}</h4>
                <p className="text-sm text-gray-600">{task.description}</p>
                <p className="text-xs text-gray-500">Assigned to: {agent.name}</p>
              </div>
              <div className="text-right">
                <span className={`px-2 py-1 text-xs rounded-full`}
                      style={{ 
                        backgroundColor: getStatusColor(status) + '20',
                        color: getStatusColor(status)
                      }}>
                  {status}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );

  // Network View Component
  const NetworkView = () => (
    <div className="w-full h-full bg-white p-6 overflow-y-auto">
      <h3 className="text-lg font-semibold mb-4">Agent Communication Network</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Agents */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Active Agents</h4>
          <div className="space-y-2">
            {workflow?.agents?.map(agentId => {
              const agent = getAgentById(agentId);
              const agentState = execution?.agent_states?.[agentId];
              
              return (
                <div key={agentId} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-3 h-3 rounded-full"
                       style={{ backgroundColor: getStatusColor(agentState?.status || 'unknown') }}>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{agent.name}</p>
                    <p className="text-sm text-gray-600">{agent.type?.replace('_', ' ')}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Messages */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Recent Messages</h4>
          <div className="space-y-2">
            {messages.map(message => (
              <div key={message.id} className="p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center space-x-2 mb-1">
                  <MessageSquare size={14} className="text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">
                    {getAgentById(message.from).name} → {getAgentById(message.to).name}
                  </span>
                </div>
                <p className="text-sm text-gray-700">{message.content}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {message.timestamp.toLocaleTimeString()}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  if (!workflow) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{workflow.name}</h2>
            <p className="text-gray-600">Real-time Workflow Visualization</p>
          </div>
          <div className="flex items-center space-x-4">
            {/* View Mode Selector */}
            <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('graph')}
                className={`px-3 py-1 text-sm rounded-md ${
                  viewMode === 'graph' ? 'bg-white shadow-sm' : 'text-gray-600'
                }`}
              >
                Graph
              </button>
              <button
                onClick={() => setViewMode('timeline')}
                className={`px-3 py-1 text-sm rounded-md ${
                  viewMode === 'timeline' ? 'bg-white shadow-sm' : 'text-gray-600'
                }`}
              >
                Timeline
              </button>
              <button
                onClick={() => setViewMode('network')}
                className={`px-3 py-1 text-sm rounded-md ${
                  viewMode === 'network' ? 'bg-white shadow-sm' : 'text-gray-600'
                }`}
              >
                Network
              </button>
            </div>

            {/* Playback Controls */}
            <button
              onClick={togglePlayback}
              className={`p-2 rounded-lg ${
                isPlaying ? 'bg-red-100 text-red-600' : 'bg-green-100 text-green-600'
              }`}
            >
              {isPlaying ? <Pause size={16} /> : <Play size={16} />}
            </button>

            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex">
          {/* Main Visualization */}
          <div className="flex-1">
            {viewMode === 'graph' && <GraphView />}
            {viewMode === 'timeline' && <TimelineView />}
            {viewMode === 'network' && <NetworkView />}
          </div>

          {/* Side Panel */}
          {selectedNode && (
            <div className="w-80 border-l bg-gray-50 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Task Details</h3>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Name</label>
                  <p className="text-gray-900">{selectedNode.task.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Status</label>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(selectedNode.status)}
                    <span className="capitalize">{selectedNode.status}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Assigned Agent</label>
                  <p className="text-gray-900">{selectedNode.agent.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Description</label>
                  <p className="text-gray-600">{selectedNode.task.description}</p>
                </div>
                {selectedNode.task.estimated_cost && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Estimated Cost</label>
                    <p className="text-gray-900">${selectedNode.task.estimated_cost.toFixed(2)}</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Status Bar */}
        <div className="border-t bg-gray-50 px-6 py-3">
          <div className="flex justify-between items-center text-sm text-gray-600">
            <div className="flex items-center space-x-4">
              <span>Status: {workflow.status}</span>
              {execution && (
                <>
                  <span>Tasks: {execution.metrics?.completed_tasks || 0}/{execution.metrics?.total_tasks || 0}</span>
                  <span>Active Agents: {execution.metrics?.active_agents || 0}</span>
                </>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Activity size={14} className={isPlaying ? 'text-green-600 animate-pulse' : 'text-gray-400'} />
              <span>{isPlaying ? 'Live' : 'Paused'}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkflowVisualization;
