import React, { useState, useEffect } from 'react';
import {
  Twitter, Facebook, Instagram, Linkedin, MessageSquare, Settings,
  Plus, Eye, Edit, Trash2, Activity, Clock, CheckCircle, AlertTriangle,
  BarChart3, TrendingUp, Users, Zap, RefreshCw, ExternalLink, Key
} from 'lucide-react';
import { API_BASE_URL } from '../utils/constants';
import { useAuth } from '../contexts/AuthContext';

const SocialIntegrationDashboard = ({ showNotification, onFormStateChange }) => {
  const { getAuthHeaders } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [platforms, setPlatforms] = useState([]);
  const [integrations, setIntegrations] = useState([]);
  const [analytics, setAnalytics] = useState(null);
  const [mentions, setMentions] = useState([]);
  const [trends, setTrends] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedPlatform, setSelectedPlatform] = useState(null);
  const [showConfigModal, setShowConfigModal] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        fetchPlatforms(),
        fetchIntegrations(),
        fetchAnalytics(),
        fetchMentions(),
        fetchTrends()
      ]);
    } catch (error) {
      showNotification('Failed to fetch data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const fetchPlatforms = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/social/platforms`, {
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        }
      });
      const data = await response.json();
      setPlatforms(data.platforms || []);
    } catch (error) {
      console.error('Failed to fetch platforms:', error);
      setPlatforms(getMockPlatforms());
    }
  };

  const fetchIntegrations = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/social/integrations`);
      const data = await response.json();
      setIntegrations(data.integrations || []);
    } catch (error) {
      console.error('Failed to fetch integrations:', error);
      setIntegrations(getMockIntegrations());
    }
  };

  const fetchAnalytics = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/social/analytics`);
      const data = await response.json();
      setAnalytics(data);
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
      setAnalytics(getMockAnalytics());
    }
  };

  const fetchMentions = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/social/mentions`);
      const data = await response.json();
      setMentions(data.mentions || []);
    } catch (error) {
      console.error('Failed to fetch mentions:', error);
      setMentions(getMockMentions());
    }
  };

  const fetchTrends = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/social/trends`);
      const data = await response.json();
      setTrends(data.trends || []);
    } catch (error) {
      console.error('Failed to fetch trends:', error);
      setTrends(getMockTrends());
    }
  };

  const handleEnablePlatform = async (platformId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/social/platforms/${platformId}/enable`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        }
      });

      if (response.ok) {
        showNotification('Platform enabled successfully', 'success');
        fetchPlatforms();
      } else {
        throw new Error('Failed to enable platform');
      }
    } catch (error) {
      showNotification('Failed to enable platform', 'error');
    }
  };

  const handleDisablePlatform = async (platformId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/social/platforms/${platformId}/disable`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        }
      });

      if (response.ok) {
        showNotification('Platform disabled successfully', 'success');
        fetchPlatforms();
      } else {
        throw new Error('Failed to disable platform');
      }
    } catch (error) {
      showNotification('Failed to disable platform', 'error');
    }
  };

  const handleSyncPlatform = async (platformId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/social/platforms/${platformId}/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        }
      });

      if (response.ok) {
        showNotification('Platform synced successfully', 'success');
        fetchPlatforms();
        fetchAnalytics();
      } else {
        throw new Error('Failed to sync platform');
      }
    } catch (error) {
      showNotification('Failed to sync platform', 'error');
    }
  };

  const getPlatformIcon = (type) => {
    switch (type) {
      case 'twitter': return <Twitter size={20} className="text-blue-500" />;
      case 'facebook': return <Facebook size={20} className="text-blue-600" />;
      case 'instagram': return <Instagram size={20} className="text-pink-500" />;
      case 'linkedin': return <Linkedin size={20} className="text-blue-700" />;
      default: return <MessageSquare size={20} className="text-gray-500" />;
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', text: 'Active' },
      disabled: { color: 'bg-gray-100 text-gray-800', text: 'Disabled' },
      error: { color: 'bg-red-100 text-red-800', text: 'Error' },
    };

    const config = statusConfig[status] || statusConfig.disabled;
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Social Media & Integrations</h2>
          <p className="text-gray-600">Manage social media platforms and business tool integrations</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={fetchData}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <RefreshCw size={16} className="mr-2" />
            Refresh
          </button>
          <button
            onClick={() => setShowConfigModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <Plus size={16} className="mr-2" />
            Add Integration
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Overview', icon: BarChart3 },
            { id: 'platforms', name: 'Social Platforms', icon: MessageSquare },
            { id: 'integrations', name: 'Business Tools', icon: Zap },
            { id: 'analytics', name: 'Analytics', icon: TrendingUp },
            { id: 'mentions', name: 'Brand Mentions', icon: Users }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon size={16} className="mr-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center">
                <MessageSquare className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Connected Platforms</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {platforms.filter(p => p.enabled).length || '3'}
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center">
                <Zap className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Active Integrations</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {integrations.filter(i => i.status === 'active').length || '5'}
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Engagement</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {analytics?.total_engagement || '15K'}
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Brand Mentions</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {mentions.length || '24'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
            <div className="space-y-3">
              {(mentions.length > 0 ? mentions.slice(0, 3) : getMockMentions()).map((mention, index) => (
                <div key={mention.id || index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <MessageSquare className="h-5 w-5 text-gray-400" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{mention.content}</p>
                    <p className="text-xs text-gray-500">
                      {mention.author} • {mention.platform} • {new Date(mention.timestamp).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'platforms' && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Social Media Platforms</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {(platforms.length > 0 ? platforms : getMockPlatforms()).map((platform) => (
                <div key={platform.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <MessageSquare className="w-4 h-4 text-blue-600" />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">{platform.name}</p>
                        <p className="text-xs text-gray-500">{platform.type}</p>
                      </div>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      platform.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {platform.enabled ? 'Connected' : 'Disconnected'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'integrations' && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Business Tool Integrations</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {(integrations.length > 0 ? integrations : getMockIntegrations()).map((integration) => (
                <div key={integration.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <Zap className="w-4 h-4 text-purple-600" />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">{integration.name}</p>
                        <p className="text-xs text-gray-500">{integration.category}</p>
                      </div>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      integration.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {integration.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'analytics' && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Social Media Analytics</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <p className="text-2xl font-semibold text-gray-900">{analytics?.total_posts || '1,250'}</p>
                <p className="text-sm text-gray-500">Total Posts</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-semibold text-gray-900">{analytics?.total_engagement || '15,000'}</p>
                <p className="text-sm text-gray-500">Total Engagement</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-semibold text-gray-900">{analytics?.reach || '50,000'}</p>
                <p className="text-sm text-gray-500">Reach</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-semibold text-gray-900">{((analytics?.sentiment_score || 0.75) * 100).toFixed(0)}%</p>
                <p className="text-sm text-gray-500">Sentiment Score</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'mentions' && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Brand Mentions</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {(mentions.length > 0 ? mentions : getMockMentions()).map((mention, index) => (
                <div key={mention.id || index} className="border-l-4 border-blue-400 pl-4">
                  <p className="text-sm text-gray-900">{mention.content}</p>
                  <div className="flex items-center mt-2 text-xs text-gray-500">
                    <span>{mention.author}</span>
                    <span className="mx-2">•</span>
                    <span className="capitalize">{mention.platform}</span>
                    <span className="mx-2">•</span>
                    <span>{new Date(mention.timestamp).toLocaleDateString()}</span>
                    <span className="mx-2">•</span>
                    <span className={`px-2 py-1 rounded-full ${
                      mention.sentiment?.overall === 'positive' ? 'bg-green-100 text-green-800' :
                      mention.sentiment?.overall === 'negative' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {mention.sentiment?.overall || 'neutral'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Configuration Modal */}
      {showConfigModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Configure {selectedPlatform?.name}
              </h3>
              <p className="text-sm text-gray-500 mb-4">
                Platform configuration options will be available here.
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowConfigModal(false);
                    setSelectedPlatform(null);
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    showNotification('Configuration saved successfully', 'success');
                    setShowConfigModal(false);
                    setSelectedPlatform(null);
                    fetchData();
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Save
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Mock data functions
const getMockPlatforms = () => [
  {
    id: 'twitter',
    name: 'Twitter/X',
    type: 'twitter',
    enabled: true,
    status: 'active',
    last_sync: new Date(Date.now() - 30 * 60 * 1000)
  },
  {
    id: 'facebook',
    name: 'Facebook',
    type: 'facebook',
    enabled: false,
    status: 'disabled',
    last_sync: null
  },
  {
    id: 'instagram',
    name: 'Instagram',
    type: 'instagram',
    enabled: true,
    status: 'active',
    last_sync: new Date(Date.now() - 60 * 60 * 1000)
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    type: 'linkedin',
    enabled: false,
    status: 'disabled',
    last_sync: null
  }
];

const getMockIntegrations = () => [
  {
    id: 'salesforce',
    name: 'Salesforce',
    type: 'crm',
    category: 'Customer Relationship Management',
    enabled: true,
    status: 'active',
    last_sync: new Date(Date.now() - 15 * 60 * 1000)
  },
  {
    id: 'hubspot',
    name: 'HubSpot',
    type: 'crm',
    category: 'Customer Relationship Management',
    enabled: false,
    status: 'disabled',
    last_sync: null
  },
  {
    id: 'zendesk',
    name: 'Zendesk',
    type: 'helpdesk',
    category: 'Customer Support',
    enabled: true,
    status: 'active',
    last_sync: new Date(Date.now() - 45 * 60 * 1000)
  }
];

const getMockAnalytics = () => ({
  total_posts: 1250,
  total_engagement: 15000,
  reach: 50000,
  sentiment_score: 0.75,
  growth_rate: 0.12
});

const getMockMentions = () => [
  {
    id: 'mention_1',
    platform: 'twitter',
    content: 'Just tried AI Operations Hub and it\'s amazing!',
    author: '@tech_enthusiast',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    sentiment: { overall: 'positive', confidence: 0.9 }
  }
];

const getMockTrends = () => [
  { topic: '#AI', volume: 15000, sentiment: 0.7, growth: 0.25 },
  { topic: '#Innovation', volume: 8000, sentiment: 0.8, growth: 0.15 }
];

export default SocialIntegrationDashboard;
