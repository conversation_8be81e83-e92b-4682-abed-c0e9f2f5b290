import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>gle, 
  X, 
  Bell, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Eye,
  EyeOff,
  Volume2,
  VolumeX
} from 'lucide-react';

const RealTimeGovernanceAlerts = ({ 
  alerts = [], 
  onAcknowledge, 
  onResolve, 
  onDismiss,
  className = '' 
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [expandedAlerts, setExpandedAlerts] = useState(new Set());

  // Play notification sound for critical alerts
  useEffect(() => {
    if (soundEnabled && alerts.length > 0) {
      const criticalAlerts = alerts.filter(alert => 
        alert.severity === 'critical' && alert.status === 'open'
      );
      
      if (criticalAlerts.length > 0) {
        // Create a simple beep sound
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.value = 800;
        oscillator.type = 'sine';
        
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.5);
      }
    }
  }, [alerts, soundEnabled]);

  const toggleExpanded = (alertId) => {
    setExpandedAlerts(prev => {
      const newSet = new Set(prev);
      if (newSet.has(alertId)) {
        newSet.delete(alertId);
      } else {
        newSet.add(alertId);
      }
      return newSet;
    });
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-500 text-white border-red-600';
      case 'high':
        return 'bg-orange-500 text-white border-orange-600';
      case 'medium':
        return 'bg-yellow-500 text-white border-yellow-600';
      case 'low':
        return 'bg-blue-500 text-white border-blue-600';
      default:
        return 'bg-gray-500 text-white border-gray-600';
    }
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="h-5 w-5" />;
      case 'high':
        return <AlertTriangle className="h-5 w-5" />;
      case 'medium':
        return <Clock className="h-5 w-5" />;
      case 'low':
        return <CheckCircle className="h-5 w-5" />;
      default:
        return <Bell className="h-5 w-5" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'open':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'acknowledged':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'resolved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'dismissed':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  if (!isVisible || alerts.length === 0) {
    return (
      <div className={`fixed top-4 right-4 z-50 ${className}`}>
        <button
          onClick={() => setIsVisible(true)}
          className="bg-indigo-600 text-white p-3 rounded-full shadow-lg hover:bg-indigo-700 transition-colors"
          title="Show governance alerts"
        >
          <Bell className="h-6 w-6" />
          {alerts.length > 0 && (
            <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center">
              {alerts.length}
            </span>
          )}
        </button>
      </div>
    );
  }

  return (
    <div className={`fixed top-4 right-4 z-50 max-w-md w-full ${className}`}>
      <div className="bg-white rounded-lg shadow-xl border border-gray-200 max-h-96 overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-2">
            <Bell className="h-5 w-5 text-indigo-600" />
            <h3 className="font-semibold text-gray-900">
              Governance Alerts ({alerts.length})
            </h3>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setSoundEnabled(!soundEnabled)}
              className="text-gray-500 hover:text-gray-700"
              title={soundEnabled ? 'Disable sound' : 'Enable sound'}
            >
              {soundEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
            </button>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-500 hover:text-gray-700"
              title="Hide alerts"
            >
              <EyeOff className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Alerts List */}
        <div className="max-h-80 overflow-y-auto">
          {alerts.map((alert) => (
            <div
              key={alert.id}
              className={`border-b border-gray-100 last:border-b-0 ${
                alert.severity === 'critical' ? 'bg-red-50' : ''
              }`}
            >
              <div className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getSeverityColor(alert.severity)}`}>
                        {getSeverityIcon(alert.severity)}
                        <span className="ml-1">{alert.severity}</span>
                      </span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(alert.status)}`}>
                        {alert.status}
                      </span>
                    </div>
                    
                    <h4 className="font-medium text-gray-900 mb-1">
                      {alert.title}
                    </h4>
                    
                    <p className="text-sm text-gray-600 mb-2">
                      {alert.description}
                    </p>
                    
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>
                        {new Date(alert.created_at).toLocaleString()}
                      </span>
                      <button
                        onClick={() => toggleExpanded(alert.id)}
                        className="text-indigo-600 hover:text-indigo-800 flex items-center space-x-1"
                      >
                        {expandedAlerts.has(alert.id) ? (
                          <>
                            <EyeOff className="h-3 w-3" />
                            <span>Less</span>
                          </>
                        ) : (
                          <>
                            <Eye className="h-3 w-3" />
                            <span>More</span>
                          </>
                        )}
                      </button>
                    </div>

                    {/* Expanded Details */}
                    {expandedAlerts.has(alert.id) && (
                      <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <span className="font-medium">Model:</span> {alert.model_id}
                          </div>
                          <div>
                            <span className="font-medium">User:</span> {alert.user_id}
                          </div>
                          <div>
                            <span className="font-medium">Service:</span> {alert.service_id}
                          </div>
                          <div>
                            <span className="font-medium">Policy:</span> {alert.policy_id}
                          </div>
                        </div>
                        
                        {alert.context && (
                          <div className="mt-2">
                            <span className="font-medium text-xs">Context:</span>
                            <pre className="text-xs text-gray-600 mt-1 whitespace-pre-wrap bg-white p-2 rounded border">
                              {JSON.stringify(alert.context, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                {alert.status === 'open' && (
                  <div className="flex space-x-2 mt-3">
                    <button
                      onClick={() => onAcknowledge?.(alert.id)}
                      className="flex-1 px-3 py-1 text-xs bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 transition-colors"
                    >
                      Acknowledge
                    </button>
                    <button
                      onClick={() => onResolve?.(alert.id, 'Resolved from alert panel')}
                      className="flex-1 px-3 py-1 text-xs bg-green-100 text-green-800 rounded hover:bg-green-200 transition-colors"
                    >
                      Resolve
                    </button>
                    <button
                      onClick={() => onDismiss?.(alert.id, 'Dismissed from alert panel')}
                      className="flex-1 px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded hover:bg-gray-200 transition-colors"
                    >
                      Dismiss
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        {alerts.length > 5 && (
          <div className="p-3 bg-gray-50 border-t border-gray-200 text-center">
            <button className="text-sm text-indigo-600 hover:text-indigo-800">
              View all alerts in dashboard
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default RealTimeGovernanceAlerts;
