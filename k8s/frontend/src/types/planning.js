/**
 * Planning-related type definitions and interfaces
 * These mirror the Go types from the planning service
 */

// Goal Status enum
export const GoalStatus = {
  PENDING: 'pending',
  PLANNING: 'planning',
  READY: 'ready',
  EXECUTING: 'executing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
};

// Task Status enum
export const TaskStatus = {
  PENDING: 'pending',
  READY: 'ready',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  SKIPPED: 'skipped'
};

// Plan Status enum
export const PlanStatus = {
  DRAFT: 'draft',
  ACTIVE: 'active',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
};

// Task Type enum
export const TaskType = {
  LLM_CALL: 'llm_call',
  DATA_PROCESSING: 'data_processing',
  API_CALL: 'api_call',
  ANALYSIS: 'analysis',
  VALIDATION: 'validation',
  NOTIFICATION: 'notification'
};

/**
 * Success Criterion structure
 * @typedef {Object} SuccessCriterion
 * @property {string} description - Human-readable description
 * @property {string} metric - Metric name to evaluate
 * @property {number|string} target - Target value
 * @property {string} operator - Comparison operator (>=, <=, ==, etc.)
 * @property {number} weight - Weight for this criterion (0.0-1.0)
 * @property {boolean} required - Whether this criterion is required
 */

/**
 * Constraint structure
 * @typedef {Object} Constraint
 * @property {string} type - Constraint type (time, cost, quality, resource)
 * @property {string} description - Human-readable description
 * @property {string} limit - Constraint limit value
 * @property {string} operator - Comparison operator
 * @property {string} severity - Constraint severity (soft, hard)
 */

/**
 * Goal structure
 * @typedef {Object} Goal
 * @property {string} id - Unique goal identifier
 * @property {string} user_id - User who created the goal
 * @property {string} description - Goal description
 * @property {SuccessCriterion[]} success_criteria - Success criteria
 * @property {Constraint[]} constraints - Goal constraints
 * @property {Object} context - Additional context data
 * @property {number} priority - Goal priority (1-10)
 * @property {string|null} deadline - Goal deadline (ISO string)
 * @property {string} created_at - Creation timestamp
 * @property {string} updated_at - Last update timestamp
 * @property {string} status - Current goal status
 * @property {Object} metadata - Additional metadata
 */

/**
 * Task structure
 * @typedef {Object} Task
 * @property {string} id - Unique task identifier
 * @property {string} plan_id - Parent plan ID
 * @property {string} name - Task name
 * @property {string} description - Task description
 * @property {string} type - Task type
 * @property {Object} parameters - Task parameters
 * @property {string[]} dependencies - Task dependency IDs
 * @property {number} estimated_cost - Estimated cost
 * @property {number} estimated_time - Estimated time in seconds
 * @property {string} status - Current task status
 * @property {Object|null} result - Task execution result
 * @property {string|null} error - Error message if failed
 * @property {string} created_at - Creation timestamp
 * @property {string} updated_at - Last update timestamp
 * @property {Object} metadata - Additional metadata
 */

/**
 * Dependency structure
 * @typedef {Object} Dependency
 * @property {string} from_task_id - Source task ID
 * @property {string} to_task_id - Target task ID
 * @property {string} type - Dependency type
 * @property {Object} metadata - Additional metadata
 */

/**
 * Plan structure
 * @typedef {Object} Plan
 * @property {string} id - Unique plan identifier
 * @property {string} goal_id - Parent goal ID
 * @property {string} name - Plan name
 * @property {string} description - Plan description
 * @property {Task[]} tasks - Plan tasks
 * @property {Dependency[]} dependencies - Task dependencies
 * @property {number} estimated_cost - Total estimated cost
 * @property {number} estimated_time - Total estimated time in seconds
 * @property {string} created_at - Creation timestamp
 * @property {string} updated_at - Last update timestamp
 * @property {string} status - Current plan status
 * @property {number} version - Plan version
 * @property {Object} metadata - Additional metadata
 */

/**
 * Goal Request structure for creating new goals
 * @typedef {Object} GoalRequest
 * @property {string} description - Goal description
 * @property {SuccessCriterion[]} success_criteria - Success criteria (optional)
 * @property {Constraint[]} constraints - Goal constraints (optional)
 * @property {Object} context - Additional context data (optional)
 * @property {number} priority - Goal priority (optional, default: 5)
 * @property {string|null} deadline - Goal deadline (optional)
 * @property {Object} metadata - Additional metadata (optional)
 */

/**
 * Execution Status structure
 * @typedef {Object} ExecutionStatus
 * @property {string} goal_id - Goal ID
 * @property {string} status - Current execution status
 * @property {number} progress - Progress percentage (0-100)
 * @property {number} completed_tasks - Number of completed tasks
 * @property {number} total_tasks - Total number of tasks
 * @property {number} estimated_completion - Estimated completion time
 * @property {string} current_task - Currently executing task
 * @property {Object} metrics - Execution metrics
 * @property {string} updated_at - Last update timestamp
 */

/**
 * Planning Metrics structure
 * @typedef {Object} PlanningMetrics
 * @property {number} total_goals - Total number of goals
 * @property {number} active_goals - Number of active goals
 * @property {number} completed_goals - Number of completed goals
 * @property {number} failed_goals - Number of failed goals
 * @property {number} average_completion_time - Average completion time
 * @property {number} success_rate - Success rate percentage
 * @property {number} total_cost - Total cost across all goals
 * @property {Object} status_breakdown - Breakdown by status
 */

// Default values for forms
export const DEFAULT_GOAL_REQUEST = {
  description: '',
  success_criteria: [],
  constraints: [],
  context: {},
  priority: 5,
  deadline: null,
  metadata: {}
};

export const DEFAULT_SUCCESS_CRITERION = {
  id: '',
  description: '',
  metric: '',
  target: '',
  operator: '>=',
  weight: 1.0,
  required: true
};

export const DEFAULT_CONSTRAINT = {
  id: '',
  type: 'time',
  description: '',
  limit: '',
  operator: '<=',
  severity: 'soft'
};

// Utility functions for status checking
export const isGoalActive = (status) => [
  GoalStatus.PLANNING,
  GoalStatus.READY,
  GoalStatus.EXECUTING
].includes(status);

export const isGoalCompleted = (status) => [
  GoalStatus.COMPLETED,
  GoalStatus.FAILED,
  GoalStatus.CANCELLED
].includes(status);

export const isTaskActive = (status) => [
  TaskStatus.READY,
  TaskStatus.RUNNING
].includes(status);

export const isTaskCompleted = (status) => [
  TaskStatus.COMPLETED,
  TaskStatus.FAILED,
  TaskStatus.SKIPPED
].includes(status);

// Status color mappings for UI
export const getGoalStatusColor = (status) => {
  switch (status) {
    case GoalStatus.PENDING:
      return 'bg-gray-100 text-gray-800';
    case GoalStatus.PLANNING:
      return 'bg-blue-100 text-blue-800';
    case GoalStatus.READY:
      return 'bg-yellow-100 text-yellow-800';
    case GoalStatus.EXECUTING:
      return 'bg-purple-100 text-purple-800';
    case GoalStatus.COMPLETED:
      return 'bg-green-100 text-green-800';
    case GoalStatus.FAILED:
      return 'bg-red-100 text-red-800';
    case GoalStatus.CANCELLED:
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const getTaskStatusColor = (status) => {
  switch (status) {
    case TaskStatus.PENDING:
      return 'bg-gray-100 text-gray-800';
    case TaskStatus.READY:
      return 'bg-yellow-100 text-yellow-800';
    case TaskStatus.RUNNING:
      return 'bg-blue-100 text-blue-800';
    case TaskStatus.COMPLETED:
      return 'bg-green-100 text-green-800';
    case TaskStatus.FAILED:
      return 'bg-red-100 text-red-800';
    case TaskStatus.SKIPPED:
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};
