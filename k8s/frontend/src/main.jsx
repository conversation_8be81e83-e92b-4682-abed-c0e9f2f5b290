import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'
import StandardApp from './StandardApp.jsx'
import AuthSuccess from './components/auth/AuthSuccess.jsx'
import { AuthProvider } from './contexts/AuthContext.jsx'

// Check if this is the auth success page
const isAuthSuccess = window.location.pathname === '/auth/success' ||
                      window.location.pathname === '/enterprise/auth/success' ||
                      window.location.pathname === '/standard/auth/success';

// Determine which app to load based on environment
const isStandardEdition = import.meta.env.VITE_APP_EDITION === 'standard' ||
                          import.meta.env.REACT_APP_EDITION === 'standard' ||
                          window.location.pathname.startsWith('/standard');

let AppComponent;
if (isAuthSuccess) {
  AppComponent = () => (
    <AuthProvider>
      <AuthSuccess />
    </AuthProvider>
  );
} else {
  AppComponent = isStandardEdition ? StandardApp : App;
}

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <AppComponent />
  </StrictMode>,
)
