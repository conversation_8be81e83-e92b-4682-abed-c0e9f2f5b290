import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

// Authentication imports
import { AuthProvider, useAuth } from './contexts/AuthContext';
import ProtectedRoute from './components/auth/ProtectedRoute';
import UserProfile from './components/auth/UserProfile';
import UserManagement from './components/admin/UserManagement';

// Icon imports
import {
  LineChart as LineChartIcon,
  Activity,
  DollarSign,
  Zap,
  Cpu,
  PlusCircle,
  Edit,
  Trash2,
  Info as InfoIcon,
  ChevronDown,
  ChevronUp,
  Search,
  Filter,
  ChevronRight,
  ChevronLeft,
  ExternalLink,
  Settings,
  Database,
  Eye,
  RefreshCw,
  AlertTriangle,
  ClipboardCopy,
  Gauge, // Gauge icon for optimal LLM
  Target, // Target icon for planning
  Network,
  Shield,
  Users,
  FileText, // FileText icon for prompts
  MessageSquare, // MessageSquare icon for chat
  User, // User icon for profile and admin
  Heart, // Heart icon for sentiment intelligence
 } from 'lucide-react';
 
 const Info = InfoIcon;

// Import recharts components
import {
  ResponsiveContainer,
  LineChart,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  Line
} from 'recharts';

// Utility imports
import { API_BASE_URL, POLICY_MANAGER_API_PREFIX, DASHBOARD_API_PREFIX } from './utils/constants';
import { getDateRange } from './utils/dateUtils'; // Assuming this utility is still needed for date range calculations (though useDashboardData handles fetching)
import { Notification, ConfirmModal, Modal } from './components/Modal';
import { TabButton, SidebarButton, MetricCard } from './components/DashboardCards';
import { Th } from './components/DataTableComponents';
import PolicyForm from './forms/PolicyForm'; // CORRECTED IMPORT PATH
import ModelProfileForm from './forms/ModelProfileForm'; // CORRECTED IMPORT PATH
import { formatValue, formatRules } from './utils/stringUtils'; // Assuming these are still used
import PlanningDashboard from './components/PlanningDashboard';
import GoalDetailView from './components/GoalDetailView';
import MultiAgentDashboard from './components/MultiAgentDashboard';
import ResponsibleAiDashboard from './components/ResponsibleAiDashboard';
import GovernanceDashboard from './components/GovernanceDashboard';
import PromptOpsDashboard from './components/PromptOpsDashboard';
import ChatDashboard from './components/ChatDashboard';
import MCPDashboard from './components/MCPDashboard';
import SyntheticDataDashboard from './components/SyntheticDataDashboard';
import RealTimeGovernanceAlerts from './components/RealTimeGovernanceAlerts';
import LiveModelScoresDashboard from './components/LiveModelScoresDashboard';
import AIScoresMetricCard from './components/AIScoresMetricCard';
import SentimentDashboard from './components/SentimentDashboard';
import SocialIntegrationDashboard from './components/SocialIntegrationDashboard';
import SimplifiedGovernanceDashboard from './components/SimplifiedGovernanceDashboard';
import ProductMarketFitDashboard from './components/ProductMarketFitDashboard';

// Custom Hook Imports (keeping these as per your provided App.jsx)
import { useNotification } from './hooks/useNotification';
import { usePolicyManagement } from './hooks/usePolicyManagement';
import { useModelProfileManagement } from './hooks/useModelProfileManagement';
import { useDashboardData } from './hooks/useDashboardData';
import usePlanningManagement from './hooks/usePlanningManagement';
import useRealTimeMonitoring from './hooks/useRealTimeMonitoring';
import useGovernanceData from './hooks/useGovernanceData';

dayjs.extend(relativeTime);
dayjs.extend(utc);
dayjs.extend(timezone);

const App = () => {
  const { getAuthHeaders } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmModalMessage, setConfirmModalMessage] = useState('');
  const [onConfirmAction, setOnConfirmAction] = useState(() => () => {});

  const [showPolicyForm, setShowPolicyForm] = useState(false);
  const [editingPolicy, setEditingPolicy] = useState(null);
  const [policyFormErrors, setPolicyFormErrors] = useState({});

  const [showModelProfileForm, setShowModelProfileForm] = useState(false);
  const [editingModelProfile, setEditingModelProfile] = useState(null);
  const [modelProfileFormErrors, setModelProfileFormErrors] = useState({});

  // Planning state
  const [selectedGoalId, setSelectedGoalId] = useState(null);
  const [showGoalDetail, setShowGoalDetail] = useState(false);

  // PromptOps modal state
  const [promptOpsModalOpen, setPromptOpsModalOpen] = useState(false);

  // MCP form states
  const [mcpFormOpen, setMcpFormOpen] = useState(false);

  // Multi-Agent form states
  const [multiAgentFormOpen, setMultiAgentFormOpen] = useState(false);

  // Planning form states
  const [planningFormOpen, setPlanningFormOpen] = useState(false);

  // Chat modal states
  const [chatModalOpen, setChatModalOpen] = useState(false);

  // Governance form states
  const [governanceFormOpen, setGovernanceFormOpen] = useState(false);

  // Responsible AI form states
  const [responsibleAiFormOpen, setResponsibleAiFormOpen] = useState(false);

  // Synthetic Data form states
  const [syntheticDataFormOpen, setSyntheticDataFormOpen] = useState(false);

  // Sentiment form states
  const [sentimentFormOpen, setSentimentFormOpen] = useState(false);

  // Social integration form states
  const [socialFormOpen, setSocialFormOpen] = useState(false);

  // PMF analytics form states
  const [pmfFormOpen, setPmfFormOpen] = useState(false);

  const { notification, showNotification, closeNotification } = useNotification();

  // Ensuring showNotification is consistently passed
  const safeShowNotification = useCallback((message, type = 'info', duration = 3000) => {
    if (message) {
      showNotification({
        message: String(message),
        type: type || 'info',
        duration // Pass duration if you want to control it
      });
    }
  }, [showNotification]);

  const {
    policies,
    fetchPolicies,
    policiesLoading,
    modelProfiles,
    fetchModelProfiles,
    modelProfilesLoading,
    inferenceSummary,
    timeSeriesData,
    recentInferenceLogs,
    backendLatencies,
    optimalBackend, // This will be object {optimalBackendId, averageMetric, preference, timestamp, message}
    preferredBackend,
    setOptimizationPreference,
    selectedTimeframe,
    setSelectedTimeframe,
    fetchDashboardData,
    evaluationResults,
    curatedData,
    evaluationResultsLoading,
    curatedDataLoading,
  } = useDashboardData({ showNotification: safeShowNotification });

  const {
    handleCreatePolicy,
    handleUpdatePolicy,
    handleDeletePolicy: policyDeleteHandler,
    validatePolicyForm,
  } = usePolicyManagement(fetchPolicies, safeShowNotification, setPolicyFormErrors);

  const {
    handleCreateModelProfile,
    handleUpdateModelProfile,
    handleDeleteModelProfile: modelProfileDeleteHandler,
    validateModelProfileForm,
  } = useModelProfileManagement(fetchModelProfiles, safeShowNotification, setModelProfileFormErrors);

  // Planning management hook
  const planningHook = usePlanningManagement(safeShowNotification);
  const {
    goals,
    goalsLoading,
    activeGoal,
    activePlan,
    activeTasks,
    executionStatus,
    planningMetrics,
    createGoal,
    getGoal,
    updateGoal,
    deleteGoal,
    generatePlan,
    executeGoal,
    getExecutionStatus,
    getTasks,
    getPlan
  } = planningHook;

  // Real-time monitoring hook
  const {
    isMonitoring,
    monitoringGoals,
    startRealTimeMonitoring,
    stopRealTimeMonitoring,
    isGoalBeingMonitored,
    getLastUpdateTime
  } = useRealTimeMonitoring(planningHook, safeShowNotification);

  // Governance data hook
  const {
    governanceAlerts,
    acknowledgeAlert,
    resolveAlert,
    dismissAlert,
    connectWebSocket,
    disconnectWebSocket
  } = useGovernanceData({ showNotification: safeShowNotification });

  // Connect to governance WebSocket on mount
  useEffect(() => {
    connectWebSocket();
    return () => disconnectWebSocket();
  }, [connectWebSocket, disconnectWebSocket]);

  const [apiStatus, setApiStatus] = useState({
    dashboard: 'ok',
    policies: 'ok',
    modelProfiles: 'ok',
    evaluations: 'ok',
    curated: 'ok',
  });

  const modelProfileMap = useMemo(() => {
    return (Array.isArray(modelProfiles) ? modelProfiles : []).reduce((map, profile) => {
      map[profile.id] = profile.name;
      return map;
    }, {});
  }, [modelProfiles]);


  const handleDeletePolicy = (policyId) => {
    setConfirmModalMessage(`Are you sure you want to delete policy ID: ${policyId}?`);
    setOnConfirmAction(() => () => policyDeleteHandler(policyId));
    setShowConfirmModal(true);
  };

  const handleDeleteModelProfile = (profileId) => {
    setConfirmModalMessage(`Are you sure you want to delete LLM profile ID: ${profileId}?`);
    setOnConfirmAction(() => () => modelProfileDeleteHandler(profileId));
    setShowConfirmModal(true);
  };

  // Planning handlers
  const handleViewGoal = useCallback(async (goalId) => {
    try {
      setSelectedGoalId(goalId);
      await getGoal(goalId);
      await getPlan(goalId);
      await getTasks(goalId);
      await getExecutionStatus(goalId);
      setShowGoalDetail(true);
    } catch (error) {
      console.error('Error viewing goal:', error);
    }
  }, [getGoal, getPlan, getTasks, getExecutionStatus]);

  const handleBackToPlanning = useCallback(() => {
    setShowGoalDetail(false);
    setSelectedGoalId(null);
  }, []);

  const handleRefreshGoalStatus = useCallback(async (goalId) => {
    try {
      await getGoal(goalId);
      await getTasks(goalId);
      await getExecutionStatus(goalId);
    } catch (error) {
      console.error('Error refreshing goal status:', error);
    }
  }, [getGoal, getTasks, getExecutionStatus]);

  const [currentPage, setCurrentPage] = useState(1);
  const [logsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBackend, setFilterBackend] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [sortColumn, setSortColumn] = useState('timestamp');
  const [sortDirection, setSortDirection] = useState('desc');

  const [evalCurrentPage, setEvalCurrentPage] = useState(1);
  const [evalSearchTerm, setEvalSearchTerm] = useState('');
  const [evalFilterModel, setEvalFilterModel] = useState('');
  const [evalFilterType, setEvalFilterType] = useState('');
  const [evalSortColumn, setEvalSortColumn] = useState('evaluated_at');
  const [evalSortDirection, setEvalSortDirection] = useState('desc');
  const [evalsPerPage] = useState(10);

  const [curatedCurrentPage, setCuratedCurrentPage] = useState(1);
  const [curatedSearchTerm, setCuratedSearchTerm] = useState('');
  const [curatedFilterModel, setCuratedFilterModel] = useState('');
  const [curatedFilterType, setCuratedFilterType] = useState('');
  const [curatedSortColumn, setCuratedSortColumn] = useState('generated_at');
  const [curatedSortDirection, setCuratedSortDirection] = useState('desc');
  const [curatedPerPage] = useState(10);


  const [refreshing, setRefreshing] = useState(false);
  const [refreshAttempts, setRefreshAttempts] = useState(0);
  const maxRefreshAttempts = 3;

  const filteredAndSortedLogs = useMemo(() => {
    let logs = [...(Array.isArray(recentInferenceLogs) ? recentInferenceLogs : [])];

    if (searchTerm) {
      const lowerCaseSearchTerm = searchTerm.toLowerCase();
      logs = logs.filter(log =>
        Object.values(log).some(value =>
          String(value).toLowerCase().includes(lowerCaseSearchTerm)
        )
      );
    }

    if (filterBackend) {
      logs = logs.filter(log => log.selected_backend_id === filterBackend);
    }

    if (filterStatus) {
      logs = logs.filter(log => String(log.status_code) === filterStatus);
    }

    logs.sort((a, b) => {
      let aValue = a[sortColumn];
      let bValue = b[sortColumn];

      // Special handling for LLM column
      if (sortColumn === 'selected_backend_id') {
        aValue = modelProfileMap[a.selected_backend_id] || a.selected_backend_id;
        bValue = modelProfileMap[b.selected_backend_id] || b.selected_backend_id;
      }
      // Special handling for status_code (numeric sort)
      if (sortColumn === 'status_code') {
        aValue = Number(a.status_code);
        bValue = Number(b.status_code);
      }
      // Special handling for latency_ms (numeric sort)
      if (sortColumn === 'latency_ms') {
        aValue = Number(a.latency_ms);
        bValue = Number(b.latency_ms);
      }
      // Special handling for total_cost (numeric sort)
      if (sortColumn === 'total_cost') {
        aValue = Number(a.total_cost);
        bValue = Number(b.total_cost);
      }
      // Special handling for model_used (string sort, using mapped name)
      if (sortColumn === 'model_used') {
        aValue = modelProfileMap[a.model_used] || a.model_used;
        bValue = modelProfileMap[b.model_used] || b.model_used;
      }


      const safeAValue = (typeof aValue === 'number' && !isNaN(aValue)) ? aValue : (typeof aValue === 'string' ? aValue : '');
      const safeBValue = (typeof bValue === 'number' && !isNaN(bValue)) ? bValue : (typeof bValue === 'string' ? bValue : '');


      if (typeof safeAValue === 'string' && typeof safeBValue === 'string') {
        return sortDirection === 'asc' ? safeAValue.localeCompare(safeBValue) : bValue.localeCompare(aValue);
      } else {
        return sortDirection === 'asc' ? (safeAValue - safeBValue) : (safeBValue - safeAValue);
      }
    });

    return logs;
  }, [recentInferenceLogs, searchTerm, filterBackend, filterStatus, sortColumn, sortDirection, modelProfileMap]);


  const indexOfLastLog = currentPage * logsPerPage;
  const indexOfFirstLog = indexOfLastLog - logsPerPage;
  const currentLogs = filteredAndSortedLogs.slice(indexOfFirstLog, indexOfLastLog);
  const totalPages = Math.ceil(filteredAndSortedLogs.length / logsPerPage);

  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  const handleSort = (column) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };


  const filteredAndSortedEvaluationResults = useMemo(() => {
    let evals = [...(Array.isArray(evaluationResults) ? evaluationResults : [])];

    if (evalSearchTerm) {
      const lowerCaseSearchTerm = evalSearchTerm.toLowerCase();
      evals = evals.filter(e =>
        Object.values(e).some(value =>
          String(value).toLowerCase().includes(lowerCaseSearchTerm)
        )
      );
    }

    if (evalFilterModel) {
      evals = evals.filter(e => e.model_id === evalFilterModel);
    }

    if (evalFilterType) {
      evals = evals.filter(e => e.evaluation_type === evalFilterType);
    }

    evals.sort((a, b) => {
      let aValue = a[evalSortColumn];
      let bValue = b[evalSortColumn];

      // Special handling for model_id (numeric sort)
      if (evalSortColumn === 'model_id') {
        aValue = modelProfileMap[a.model_id] || a.model_id;
        bValue = modelProfileMap[b.model_id] || b.model_id;
      }

      const safeAValue = (typeof aValue === 'number' && !isNaN(aValue)) ? aValue : (typeof aValue === 'string' ? aValue : '');
      const safeBValue = (typeof bValue === 'number' && !isNaN(bValue)) ? bValue : (typeof bValue === 'string' ? bValue : '');

      if (typeof safeAValue === 'string' && typeof safeBValue === 'string') {
        return evalSortDirection === 'asc' ? safeAValue.localeCompare(safeBValue) : safeBValue.localeCompare(safeAValue);
      } else {
        return evalSortDirection === 'asc' ? (safeAValue - safeBValue) : (safeBValue - safeAValue);
      }
    });
    return evals;
  }, [evaluationResults, evalSearchTerm, evalFilterModel, evalFilterType, evalSortColumn, evalSortDirection, modelProfileMap]);

  const indexOfLastEval = evalCurrentPage * evalsPerPage;
  const indexOfFirstEval = indexOfLastEval - evalsPerPage;
  const currentEvaluations = filteredAndSortedEvaluationResults.slice(indexOfFirstEval, indexOfLastEval);
  const totalEvalPages = Math.ceil(filteredAndSortedEvaluationResults.length / evalsPerPage);
  const paginateEvaluations = (pageNumber) => setEvalCurrentPage(pageNumber);
  const handleEvalSort = (column) => {
    if (evalSortColumn === column) {
      setEvalSortDirection(evalSortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setEvalSortColumn(column);
      setEvalSortDirection('asc');
    }
  };

  const filteredAndSortedCuratedData = useMemo(() => {
    let curated = [...(Array.isArray(curatedData) ? curatedData : [])];

    if (curatedSearchTerm) {
      const lowerCaseSearchTerm = curatedSearchTerm.toLowerCase();
      curated = curated.filter(d =>
        Object.values(d).some(value =>
          String(value).toLowerCase().includes(lowerCaseSearchTerm)
        )
      );
    }

    if (curatedFilterModel) {
      curated = curated.filter(d => d.model_id === curatedFilterModel);
    }

    if (curatedFilterType) {
      curated = curated.filter(d => d.task_type === curatedFilterType);
    }

    curated.sort((a, b) => {
      let aValue = a[curatedSortColumn];
      let bValue = b[curatedSortColumn];

      // Special handling for model_id (numeric sort)
      if (curatedSortColumn === 'model_id') {
        aValue = modelProfileMap[a.model_id] || a.model_id;
        bValue = modelProfileMap[b.model_id] || b.model_id;
      }

      const safeAValue = (typeof aValue === 'number' && !isNaN(aValue)) ? aValue : (typeof aValue === 'string' ? aValue : '');
      const safeBValue = (typeof bValue === 'number' && !isNaN(bValue)) ? bValue : (typeof bValue === 'string' ? bValue : '');

      if (typeof safeAValue === 'string' && typeof safeBValue === 'string') {
        return curatedSortDirection === 'asc' ? safeAValue.localeCompare(safeBValue) : safeBValue.localeCompare(safeAValue);
      } else {
        return curatedSortDirection === 'asc' ? (safeAValue - safeBValue) : (safeBValue - safeAValue);
      }
    });
    return curated;
  }, [curatedData, curatedSearchTerm, curatedFilterModel, curatedFilterType, curatedSortColumn, curatedSortDirection, modelProfileMap]);

  const indexOfLastCurated = curatedCurrentPage * curatedPerPage;
  const indexOfFirstCurated = indexOfLastCurated - curatedPerPage;
  const currentCuratedData = filteredAndSortedCuratedData.slice(indexOfFirstCurated, indexOfLastCurated);
  const totalCuratedPages = Math.ceil(filteredAndSortedCuratedData.length / curatedPerPage);
  const paginateCurated = (pageNumber) => setCuratedCurrentPage(pageNumber);
  const handleCuratedSort = (column) => {
    if (curatedSortColumn === column) {
      setCuratedSortDirection(curatedSortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setCuratedSortColumn(column);
      setCuratedSortDirection('asc');
    }
  };


  const copyToClipboard = (text, message) => {
    const textarea = document.createElement('textarea');
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();
    try {
      document.execCommand('copy');
      safeShowNotification(message, 'success');
    } catch (err) {
      console.error('Failed to copy text: ', err);
      safeShowNotification('Failed to copy to clipboard.', 'error');
    }
    document.body.removeChild(textarea);
  };


  const safeFetchDashboardData = useCallback(async () => {
    setApiStatus(prev => ({ ...prev, dashboard: 'loading', evaluations: 'loading', curated: 'loading' }));
    try {
      const result = await fetchDashboardData(selectedTimeframe);
      setApiStatus(prev => ({ ...prev, dashboard: 'ok', evaluations: 'ok', curated: 'ok' }));
      return result;
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      setApiStatus(prev => ({ ...prev, dashboard: 'error', evaluations: 'error', curated: 'error' }));
      return null;
    }
  }, [fetchDashboardData, selectedTimeframe]);

  const safeFetchPolicies = useCallback(async (silent = false) => {
    setApiStatus(prev => ({ ...prev, policies: 'loading' }));
    try {
      const result = await fetchPolicies(silent);
      setApiStatus(prev => ({ ...prev, policies: 'ok' }));
      return result;
    } catch (error) {
      console.error("Error fetching policies:", error);
      setApiStatus(prev => ({ ...prev, policies: 'error' }));
      return null;
    }
  }, [fetchPolicies]);

  const safeFetchModelProfiles = useCallback(async (silent = false) => {
    setApiStatus(prev => ({ ...prev, modelProfiles: 'loading' }));
    try {
      const result = await fetchModelProfiles(silent);
      setApiStatus(prev => ({ ...prev, modelProfiles: 'ok' }));
      return result;
    } catch (error) {
      console.error("Failed to fetch model profiles:", error);
      setApiStatus(prev => ({ ...prev, modelProfiles: 'error' }));
      return null;
    }
  }, [fetchModelProfiles]);

  const checkEndpointsAvailability = useCallback(async () => {
    try {
      const endpoints = [
        `${DASHBOARD_API_PREFIX}/backend-latencies`,
        `${DASHBOARD_API_PREFIX}/optimal-backend`,
        `${POLICY_MANAGER_API_PREFIX}/policies`,
        `${POLICY_MANAGER_API_PREFIX}/model-profiles`,
        `${DASHBOARD_API_PREFIX}/evaluation-results`,
        `${DASHBOARD_API_PREFIX}/curated-data`,
        `${DASHBOARD_API_PREFIX}/time-series`,
        `${DASHBOARD_API_PREFIX}/summary`,
      ];

      // Get auth headers for endpoint availability checks
      const authHeaders = getAuthHeaders();
      console.log('[App] Using auth headers for endpoint checks:', authHeaders);

      const results = await Promise.allSettled(
        endpoints.map(endpoint =>
          fetch(endpoint, {
            headers: {
              'Content-Type': 'application/json',
              ...authHeaders
            }
          })
            .then(response => ({ endpoint, available: response.ok }))
            .catch(() => ({ endpoint, available: false }))
        )
      );
      
      console.log('API Endpoints Availability:', results.map(r => r.status === 'fulfilled' ? r.value : r.reason));
      
      const availabilityMap = results.reduce((acc, result) => {
        if (result.status === 'fulfilled') {
            const endpointPath = result.value.endpoint.split('/api/').pop();
            acc[endpointPath] = result.value.available;
        } else {
            const endpointPath = result.reason?.endpoint?.split('/api/').pop() || 'unknown';
            acc[endpointPath] = false;
        }
        return acc;
      }, {});

      setApiStatus(prev => ({
        dashboard: (availabilityMap['backend-latencies'] && availabilityMap['optimal-backend'] && availabilityMap['time-series'] && availabilityMap['summary']) ? 'ok' : 'error',
        policies: availabilityMap['policies'] ? 'ok' : 'error',
        modelProfiles: availabilityMap['model-profiles'] ? 'ok' : 'error',
        evaluations: availabilityMap['evaluation-results'] ? 'ok' : 'error',
        curated: availabilityMap['curated-data'] ? 'ok' : 'error',
      }));
      
      return availabilityMap;
    } catch (error) {
      console.error('Error checking endpoints availability:', error);
      return {};
    }
  }, []);

  const handleManualRefresh = useCallback(async () => {
    setRefreshing(true);
    setRefreshAttempts(0);

    setApiStatus({
      dashboard: 'loading',
      policies: 'loading',
      modelProfiles: 'loading',
      evaluations: 'loading',
      curated: 'loading',
    });
    
    try {
      await checkEndpointsAvailability();
      await fetchDashboardData(selectedTimeframe);
      await fetchPolicies();
      await fetchModelProfiles();
      
      safeShowNotification('Dashboard data refreshed successfully', 'success');
    } catch (error) {
      console.error("Error during manual refresh:", error);
      safeShowNotification('Failed to refresh dashboard data', 'error');
    } finally {
      setRefreshing(false);
    }
  }, [checkEndpointsAvailability, fetchDashboardData, fetchPolicies, fetchModelProfiles, safeShowNotification, selectedTimeframe]);

  useEffect(() => {
    const isFormOpen = showPolicyForm || showModelProfileForm || promptOpsModalOpen ||
                      multiAgentFormOpen || planningFormOpen || chatModalOpen ||
                      governanceFormOpen || responsibleAiFormOpen || sentimentFormOpen ||
                      socialFormOpen || pmfFormOpen;

    if (isFormOpen || refreshAttempts >= maxRefreshAttempts) {
      return;
    }
    
    let isMounted = true;
    
    const loadInitialData = async () => {
      try {
        setRefreshAttempts(prev => prev + 1);
        console.log(`Dashboard refresh attempt #${refreshAttempts + 1}`);
        
        const availability = await checkEndpointsAvailability();
        
        if (isMounted && availability.dashboard === true) {
          await fetchDashboardData(selectedTimeframe);
        }
        
        if (isMounted && availability.policies === true) {
          try {
            await fetchPolicies(true);
          } catch (error) {
            console.warn("Could not load policies, will continue with other data:", error);
          }
        }
        
        if (isMounted && availability.model_profiles === true) {
          try {
            await fetchModelProfiles(true);
          } catch (error) {
            console.warn("Could not load model profiles, will continue with other data:", error);
          }
        }
      } catch (error) {
        console.error("Error loading initial data:", error);
      }
    };
    
    loadInitialData();

    const intervalId = setInterval(loadInitialData, 30000);

    return () => {
      isMounted = false;
      clearInterval(intervalId);
    };
  }, [refreshAttempts, showPolicyForm, showModelProfileForm, promptOpsModalOpen,
      multiAgentFormOpen, planningFormOpen, chatModalOpen, governanceFormOpen, responsibleAiFormOpen,
      sentimentFormOpen, socialFormOpen, pmfFormOpen, checkEndpointsAvailability, fetchDashboardData, fetchPolicies, fetchModelProfiles, selectedTimeframe]);

  // --- DEBUGGING LOGS ADDED HERE ---
  console.log("--- Rendering App Component ---");
  console.log("Current inferenceSummary:", inferenceSummary);
  console.log("Current optimalBackend:", optimalBackend);
  console.log("Current backendLatencies:", backendLatencies);
  console.log("Current timeSeriesData:", timeSeriesData);
  console.log("Current modelProfiles:", modelProfiles);
  console.log("Current modelProfileMap:", modelProfileMap);
  // --- END DEBUGGING LOGS ---

  return (
    <div className="min-h-screen bg-gray-50 text-gray-800 font-sans antialiased flex">
      <script src="https://cdn.tailwindcss.com"></script>
      <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet" />
      <style>
        {`
        body { font-family: 'Inter', sans-serif; }
        .card {
          @apply bg-white rounded-lg shadow-md p-6;
        }
        .table-header {
          @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
        }
        .table-data {
          @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
        }
        .score-passed {
            @apply bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs font-medium;
        }
        .score-failed {
            @apply bg-red-100 text-red-800 px-2 py-0.5 rounded-full text-xs font-medium;
        }
        `}
      </style>

      {/* Notification Component */}
      {notification && notification.message && (
        <Notification
          notification={notification}
          onClose={closeNotification}
        />
      )}

      {/* Confirmation Modal */}
      <ConfirmModal
        isOpen={showConfirmModal}
        message={confirmModalMessage}
        onConfirm={() => { onConfirmAction(); setShowConfirmModal(false); }}
        onCancel={() => setShowConfirmModal(false)}
      />

      {/* Sidebar Navigation */}
      <aside className="w-64 bg-gradient-to-b from-indigo-700 to-purple-800 text-white shadow-lg flex-shrink-0">
        <div className="p-6">
          <h1 className="text-xl font-bold">AI Operations Hub</h1>
        </div>
        <nav className="mt-6">
          <div className="space-y-1 px-3">
            <SidebarButton name="dashboard" icon={<LineChartIcon size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="Dashboard" />
            <SidebarButton name="chat" icon={<MessageSquare size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="AI Chat" />
            <SidebarButton name="sentiment" icon={<Heart size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="Sentiment Intelligence" />
            <SidebarButton name="social" icon={<Users size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="Social & Integrations" />
            <SidebarButton name="planning" icon={<Target size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="Planning" />
            <SidebarButton name="prompts" icon={<FileText size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="PromptOps" />
            <SidebarButton name="synthetic-data" icon={<RefreshCw size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="Synthetic Data" />
            <SidebarButton name="multi-agent" icon={<Network size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="Multi-Agent" />
            <SidebarButton name="policies" icon={<Settings size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="Policies" />
            <SidebarButton name="models" icon={<Database size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="LLM Profiles" />
            <SidebarButton name="responsible-ai" icon={<Shield size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="Responsible AI" />
            <SidebarButton name="governance" icon={<Shield size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="Governance" />
            <SidebarButton name="logs" icon={<Activity size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="Inference Logs" />
            <SidebarButton name="mcp" icon={<Network size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="MCP Management" />
            <SidebarButton name="evaluations" icon={<ClipboardCopy size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="Evaluations" />
            <SidebarButton name="curated-data" icon={<Database size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="Curated Data" />
            <SidebarButton name="profile" icon={<User size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="Profile" />
            <SidebarButton name="admin" icon={<Shield size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="Admin" />
            <SidebarButton name="about" icon={<InfoIcon size={20} />} activeTab={activeTab} setActiveTab={setActiveTab} label="About" />
          </div>
        </nav>

        {/* API Status in Sidebar */}
        <div className="mt-8 px-3">
          <div className="text-xs text-gray-300 mb-2">API Status</div>
          <div className="space-y-1">
            <div className={`flex items-center text-xs ${
              apiStatus.dashboard === 'ok' ? 'text-green-300' :
              apiStatus.dashboard === 'loading' ? 'text-blue-300' :
              'text-red-300'
            }`}>
              <span className="w-2 h-2 rounded-full mr-2 ${
                apiStatus.dashboard === 'ok' ? 'bg-green-300' :
                apiStatus.dashboard === 'loading' ? 'bg-blue-300' :
                'bg-red-300'
              }"></span>
              Dashboard
            </div>
            <div className={`flex items-center text-xs ${
              apiStatus.policies === 'ok' ? 'text-green-300' :
              apiStatus.policies === 'loading' ? 'text-blue-300' :
              'text-red-300'
            }`}>
              <span className="w-2 h-2 rounded-full mr-2 ${
                apiStatus.policies === 'ok' ? 'bg-green-300' :
                apiStatus.policies === 'loading' ? 'bg-blue-300' :
                'bg-red-300'
              }"></span>
              Policies
            </div>
            <div className={`flex items-center text-xs ${
              apiStatus.modelProfiles === 'ok' ? 'text-green-300' :
              apiStatus.modelProfiles === 'loading' ? 'text-blue-300' :
              'text-red-300'
            }`}>
              <span className="w-2 h-2 rounded-full mr-2 ${
                apiStatus.modelProfiles === 'ok' ? 'bg-green-300' :
                apiStatus.modelProfiles === 'loading' ? 'bg-blue-300' :
                'bg-red-300'
              }"></span>
              Models
            </div>
          </div>
        </div>
      </aside>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        <main className="flex-1 p-6 overflow-auto">
        {activeTab === 'dashboard' && (
          <div className="space-y-8">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-3xl font-bold text-gray-900">Overview Dashboard</h2>
              <div className="flex items-center space-x-4">
                {/* Refresh Button */}
                <button
                  onClick={handleManualRefresh}
                  disabled={refreshing}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                >
                  {refreshing ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Refreshing...
                    </>
                  ) : (
                    <>
                      <RefreshCw size={16} className="mr-2" />
                      Refresh Data
                    </>
                  )}
                </button>

                {/* Add auto-refresh toggle */}
                <div className="ml-4 flex items-center">
                  <input
                    id="auto-refresh-toggle"
                    type="checkbox"
                    checked={false} // Removed auto-refresh feature, set to false
                    onChange={(e) => { /* setAutoRefreshEnabled(e.target.checked); */ safeShowNotification("Auto-refresh is currently disabled.", "info"); }}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label htmlFor="auto-refresh-toggle" className="ml-2 text-sm text-gray-700">
                    Auto-refresh (Disabled)
                  </label>
                </div>

                {/* Timeframe Selector */}
                <div className="relative inline-block text-gray-700">
                  <select
                    id="timeframe-selector"
                    name="timeframe-selector"
                    value={selectedTimeframe}
                    onChange={(e) => setSelectedTimeframe(e.target.value)}
                    className="block appearance-none w-full bg-white border border-gray-300 hover:border-gray-400 px-4 py-2 pr-8 rounded-md shadow leading-tight focus:outline-none focus:shadow-outline focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option value="1h">Last Hour</option>
                    <option value="24h">Last 24 Hours</option>
                    <option value="7d">Last 7 Days</option>
                    <option value="30d">Last 30 Days</option>
                    <option value="90d">Last 90 Days</option>
                    <option value="all">All Time</option>
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <ChevronDown size={16} />
                  </div>
                </div>
              </div>
            </div>

            {apiStatus.policies === 'error' && (
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="h-5 w-5 text-yellow-400" aria-hidden="true" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">
                      The policies API endpoint is not available. Some features may be limited.
                      <button
                        onClick={() => safeFetchPolicies()}
                        className="ml-2 font-medium text-yellow-700 underline hover:text-yellow-600"
                      >
                        Try again
                      </button>
                    </p>
                  </div>
                </div>
              </div>
            )}

            {apiStatus.modelProfiles === 'error' && (
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="h-5 w-5 text-yellow-400" aria-hidden="true" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">
                      The model profiles API endpoint is not available. Some features may be limited.
                      <button
                        onClick={() => safeFetchModelProfiles()}
                        className="ml-2 font-medium text-yellow-700 underline hover:text-yellow-600"
                      >
                        Try again
                      </button>
                    </p>
                  </div>
                </div>
              </div>
            )}

            {apiStatus.evaluations === 'error' && (
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="h-5 w-5 text-yellow-400" aria-hidden="true" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">
                      The evaluation results API endpoint is not available. Evaluation data may be incomplete.
                      <button
                        onClick={handleManualRefresh}
                        className="ml-2 font-medium text-yellow-700 underline hover:text-yellow-600"
                      >
                        Try again
                      </button>
                    </p>
                  </div>
                </div>
              </div>
            )}

            {apiStatus.curated === 'error' && (
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="h-5 w-5 text-yellow-400" aria-hidden="true" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">
                      The curated data API endpoint is not available. Curated data may be incomplete.
                      <button
                        onClick={handleManualRefresh}
                        className="ml-2 font-medium text-yellow-700 underline hover:text-yellow-600"
                      >
                        Try again
                      </button>
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Overall Metrics - Combined into a single row, horizontally scrollable */}
            <div className="flex flex-row flex-nowrap justify-start overflow-x-auto gap-6 pb-2">
              <MetricCard
                icon={<Activity size={24} className="text-indigo-600" />}
                title="Total Requests"
                value={inferenceSummary ? (inferenceSummary.total_requests ?? 0).toLocaleString() : '0'}
                description="Total inference requests for the selected timeframe"
              />
              <MetricCard
                icon={<DollarSign size={24} className="text-green-600" />}
                title="Total Cost"
                value={inferenceSummary ? `$${(inferenceSummary.total_cost ?? 0).toFixed(6)}` : '$0.000000'}
                description="Cumulative cost for the selected timeframe"
              />
              <MetricCard
                icon={<Zap size={24} className="text-yellow-600" />}
                title="Avg Latency"
                value={inferenceSummary ? `${(inferenceSummary.average_latency_ms ?? 0).toFixed(2)} ms` : '0.00 ms'}
                description="Average response time for the selected timeframe"
              />
              <MetricCard
                icon={<Cpu size={24} className="text-purple-600" />}
                title="Total Tokens"
                value={inferenceSummary ? ((inferenceSummary.total_input_tokens ?? 0) + (inferenceSummary.total_output_tokens ?? 0)).toLocaleString() : '0'}
                description="Total input & output tokens processed for the selected timeframe"
              />
              <MetricCard
                icon={<Gauge size={24} className="text-blue-600" />}
                title="Optimal LLM"
                value={
                  optimalBackend
                    ? (modelProfileMap[optimalBackend.optimalBackendId] || optimalBackend.optimalBackendId || 'N/A')
                    : 'N/A'
                }
                // Corrected field name and added preference
                description={optimalBackend ? `Metric: ${(optimalBackend.averageMetric ?? 0).toFixed(2)} (${optimalBackend.preference})` : 'No optimal LLM data'}
              />
              <AIScoresMetricCard
                onClick={() => {
                  const aiScoresElement = document.getElementById('ai-scores-dashboard');
                  if (aiScoresElement) {
                    aiScoresElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                  }
                }}
              />
            </div>

            {/* Live AI Optimizer Scores Dashboard */}
            <div id="ai-scores-dashboard">
              <LiveModelScoresDashboard
                className="mb-8"
                onNavigateToChat={() => setActiveTab('chat')}
              />
            </div>

            {/* Time Series Charts - Adjusted for existing data structure */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Request Count Over Time Chart */}
                <div className="bg-white p-6 rounded-lg shadow-md">
                    <h3 className="text-xl font-semibold text-gray-800 mb-4">Request Count Over Time</h3>
                    {timeSeriesData.length > 1 ? (
                        <ResponsiveContainer width="100%" height={300}>
                            <LineChart data={Array.isArray(timeSeriesData) ? timeSeriesData : []} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                                <XAxis dataKey="timeInterval" tickFormatter={(tick) => dayjs(tick).format('MMM DD, HH:mm')} angle={-45} textAnchor="end" height={60} />
                                <YAxis label={{ value: 'Request Count', angle: -90, position: 'insideLeft' }} />
                                <Tooltip labelFormatter={(label) => dayjs(label).format('MMM DD, HH:mm:ss')} />
                                <Legend />
                                <Line type="monotone" dataKey="requestCount" stroke="#8884d8" name="Requests" dot={false} />
                            </LineChart>
                        </ResponsiveContainer>
                    ) : (
                        <p className="text-gray-600 text-center py-10">
                            Not enough data points (need at least 2) to display the chart for "Request Count Over Time".
                            <br/>Generate more inference logs to see the chart.
                        </p>
                    )}
                </div>

                {/* Avg Latency Over Time Chart */}
                <div className="bg-white p-6 rounded-lg shadow-md">
                    <h3 className="text-xl font-semibold text-gray-800 mb-4">Average Latency Over Time</h3>
                    {timeSeriesData.length > 1 ? (
                        <ResponsiveContainer width="100%" height={300}>
                            <LineChart data={Array.isArray(timeSeriesData) ? timeSeriesData : []} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                                <XAxis dataKey="timeInterval" tickFormatter={(tick) => dayjs(tick).format('MMM DD, HH:mm')} angle={-45} textAnchor="end" height={60} />
                                <YAxis label={{ value: 'Latency (ms)', angle: -90, position: 'insideLeft' }} />
                                <Tooltip labelFormatter={(label) => dayjs(label).format('MMM DD, HH:mm:ss')} />
                                <Legend />
                                <Line type="monotone" dataKey="averageLatencyMs" stroke="#82ca9d" name="Latency" dot={false} />
                            </LineChart>
                        </ResponsiveContainer>
                    ) : (
                        <p className="text-gray-600 text-center py-10">
                            Not enough data points (need at least 2) to display the chart for "Average Latency Over Time".
                            <br/>Generate more inference logs to see the chart.
                        </p>
                    )}
                </div>

                {/* Total Cost Over Time Chart */}
                <div className="bg-white p-6 rounded-lg shadow-md">
                    <h3 className="text-xl font-semibold text-gray-800 mb-4">Total Cost Over Time</h3>
                    {timeSeriesData.length > 1 ? (
                        <ResponsiveContainer width="100%" height={300}>
                            <LineChart data={Array.isArray(timeSeriesData) ? timeSeriesData : []} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                                <XAxis dataKey="timeInterval" tickFormatter={(tick) => dayjs(tick).format('MMM DD, HH:mm')} angle={-45} textAnchor="end" height={60} />
                                <YAxis label={{ value: 'Cost ($)', angle: -90, position: 'insideLeft' }} />
                                <Tooltip labelFormatter={(label) => dayjs(label).format('MMM DD, HH:mm:ss')} />
                                <Legend />
                                <Line type="monotone" dataKey="totalCost" stroke="#ffc658" name="Cost" dot={false} />
                            </LineChart>
                        </ResponsiveContainer>
                    ) : (
                        <p className="text-gray-600 text-center py-10">
                            Not enough data points (need at least 2) to display the chart for "Total Cost Over Time".
                            <br/>Generate more inference logs to see the chart.
                        </p>
                    )}
                </div>

                {/* Total Tokens Over Time Chart */}
                <div className="bg-white p-6 rounded-lg shadow-md">
                    <h3 className="text-xl font-semibold text-gray-800 mb-4">Total Tokens Over Time</h3>
                    {timeSeriesData.length > 1 ? (
                        <ResponsiveContainer width="100%" height={300}>
                            <LineChart data={Array.isArray(timeSeriesData) ? timeSeriesData : []} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                                <XAxis dataKey="timeInterval" tickFormatter={(tick) => dayjs(tick).format('MMM DD, HH:mm')} angle={-45} textAnchor="end" height={60} />
                                <YAxis label={{ value: 'Tokens', angle: -90, position: 'insideLeft' }} />
                                <Tooltip labelFormatter={(label) => dayjs(label).format('MMM DD, HH:mm:ss')} />
                                <Legend />
                                <Line
                                    type="monotone"
                                    dataKey={(data) => (data.totalInputTokens || 0) + (data.totalOutputTokens || 0)}
                                    stroke="#52528c"
                                    name="Total Tokens"
                                    dot={false}
                                />
                            </LineChart>
                        </ResponsiveContainer>
                    ) : (
                        <p className="text-gray-600 text-center py-10">
                            Not enough data points (need at least 2) to display the chart for "Total Tokens Over Time".
                            <br/>Generate more inference logs to see the chart.
                        </p>
                    )}
                </div>
            </div>


            {/* LLM Specific Metrics */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-gray-800 mb-4">LLM Performance Summary</h3>
              {(inferenceSummary && inferenceSummary.total_requests !== undefined) || (Array.isArray(backendLatencies) && backendLatencies.length > 0) ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">LLM</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requests</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Cost</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Latency (ms)</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Input Tokens</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Output Tokens</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {/* Iterate through model profiles to ensure all relevant LLMs are shown, even if they have no logs */}
                      {Object.values(modelProfiles).map((profile, index) => {
                        const breakdown = inferenceSummary?.backend_breakdown?.find(b => b.backend_id === profile.id);
                        const liveLatency = Array.isArray(backendLatencies) ? backendLatencies.find(l => l.backend_id === profile.id) : null;
                        
                        if (!breakdown && !liveLatency && !(profile.id in modelProfileMap)) return null;

                        return (
                          <tr key={profile.id || index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{profile.name || profile.id || 'N/A'}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{(breakdown?.request_count ?? 0).toLocaleString()}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{`$${(breakdown?.total_cost ?? 0).toFixed(6)}`}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{(liveLatency?.averageLatencyMs ?? 0).toFixed(2)}</td> {/* Corrected from latency_ms */}
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{(breakdown?.total_input_tokens ?? 0).toLocaleString()}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{(breakdown?.total_output_tokens ?? 0).toLocaleString()}</td>
                          </tr>
                        );
                      })}
                      {/* Also add any backend latencies that don't have a model profile (e.g., temporary ones) */}
                      {(Array.isArray(backendLatencies) ? backendLatencies : [])
                          .filter(l => !modelProfileMap[l.backend_id])
                          .map((llmLatency, index) => {
                          const breakdown = inferenceSummary?.backend_breakdown?.find(b => b.backend_id === llmLatency.backend_id);
                          return (
                              <tr key={llmLatency.backend_id || `unmapped-${index}`}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{llmLatency.backend_id} (Unmapped)</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{(breakdown?.request_count ?? 0).toLocaleString()}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{`$${(breakdown?.total_cost ?? 0).toFixed(6)}`}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{(llmLatency.averageLatencyMs ?? 0).toFixed(2)}</td> {/* Corrected from latency_ms */}
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{(breakdown?.total_input_tokens ?? 0).toLocaleString()}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{(breakdown?.total_output_tokens ?? 0).toLocaleString()}</td>
                              </tr>
                          );
                      })}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-gray-600">No LLM performance summary data available. Make some requests via the proxy gateway!</p>
              )}
            </div>

            {/* Optimal LLM Preference & Latency Table */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-xl font-semibold text-gray-800 mb-4">Optimal LLM Preference</h3>
                <div className="space-y-4">
                  {optimalBackend ? (
                    <div>
                      <p className="text-lg text-gray-700 mb-2">
                        Currently optimizing for: <span className="font-semibold text-indigo-700">{optimalBackend.preference}</span>
                      </p>
                      <p className="text-sm text-gray-500">
                        Last updated: {dayjs(optimalBackend.timestamp).format('MMM DD, HH:mm:ss')} ({dayjs(optimalBackend.timestamp).fromNow()})
                      </p>
                      {optimalBackend.optimalBackendId && (
                        <p className="text-sm text-gray-500 mt-1">
                          Optimal LLM ID: <span className="font-medium">{modelProfileMap[optimalBackend.optimalBackendId] || optimalBackend.optimalBackendId}</span> (Metric: {(optimalBackend.averageMetric ?? 0).toFixed(2)})
                        </p>
                      )}
                      {optimalBackend.message && (
                        <p className="text-sm text-gray-500 mt-1">
                          Message: <span className="font-medium">{optimalBackend.message}</span>
                        </p>
                      )}
                    </div>
                  ) : (
                    <p className="text-gray-600">No optimal LLM preference data available. Ensure the AI Optimizer is running.</p>
                  )}

                  <div className="flex flex-wrap gap-3">
                    <button
                      onClick={() => setOptimizationPreference('COST')}
                      className={`px-4 py-2 rounded-md font-medium transition-all duration-200
                                  ${preferredBackend === 'COST' ? 'bg-indigo-600 text-white shadow-md' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'}`}
                    >
                      Optimize for Cost
                    </button>
                    <button
                      onClick={() => setOptimizationPreference('LATENCY')}
                      className={`px-4 py-2 rounded-md font-medium transition-all duration-200
                                  ${preferredBackend === 'LATENCY' ? 'bg-indigo-600 text-white shadow-md' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'}`}
                    >
                      Optimize for Latency
                    </button>
                    <button
                      onClick={() => setOptimizationPreference('BALANCED')}
                      className={`px-4 py-2 rounded-md font-medium transition-all duration-200
                                  ${preferredBackend === 'BALANCED' ? 'bg-indigo-600 text-white shadow-md' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'}`}
                    >
                      Balanced Optimization
                    </button>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-xl font-semibold text-gray-800 mb-4">Real-time LLM Latencies</h3>
                {(Array.isArray(backendLatencies) && backendLatencies.length > 0) ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">LLM</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Latency (ms)</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {backendLatencies.map((latency, index) => (
                          <tr key={latency.backendId || index}> {/* Corrected from backend_id */}
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{modelProfileMap[latency.backendId] || latency.backendId}</td> {/* Corrected from backend_id */}
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{(latency.averageLatencyMs ?? 0).toFixed(2)}</td> {/* Corrected from latency_ms */}
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{dayjs(latency.lastUpdated).fromNow()}</td> {/* Corrected from last_updated */}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <p className="text-gray-600">No real-time LLM latency data available. Ensure the AI Optimizer is running.</p>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'planning' && (
          <div className="space-y-8">
            {showGoalDetail && activeGoal ? (
              <GoalDetailView
                goal={activeGoal}
                plan={activePlan}
                tasks={activeTasks}
                executionStatus={executionStatus}
                onBack={handleBackToPlanning}
                onExecuteGoal={executeGoal}
                onGeneratePlan={generatePlan}
                onRefreshStatus={handleRefreshGoalStatus}
                planningHook={planningHook}
              />
            ) : (
              <PlanningDashboard
                goals={goals}
                goalsLoading={goalsLoading}
                planningMetrics={planningMetrics}
                onCreateGoal={createGoal}
                onUpdateGoal={updateGoal}
                onDeleteGoal={deleteGoal}
                onViewGoal={handleViewGoal}
                onExecuteGoal={executeGoal}
                onGeneratePlan={generatePlan}
                showNotification={safeShowNotification}
                isMonitoring={isMonitoring}
                monitoringGoals={monitoringGoals}
                onStartMonitoring={startRealTimeMonitoring}
                onStopMonitoring={stopRealTimeMonitoring}
                onFormStateChange={setPlanningFormOpen}
              />
            )}
          </div>
        )}

        {activeTab === 'prompts' && (
          <PromptOpsDashboard
            showNotification={safeShowNotification}
            onModalStateChange={setPromptOpsModalOpen}
            modelProfiles={modelProfiles || {}}
          />
        )}

        {activeTab === 'synthetic-data' && (
          <SyntheticDataDashboard
            showNotification={safeShowNotification}
            onFormStateChange={setSyntheticDataFormOpen}
          />
        )}

        {activeTab === 'mcp' && (
          <MCPDashboard
            showNotification={safeShowNotification}
            onFormStateChange={setMcpFormOpen}
          />
        )}

        {activeTab === 'chat' && (
          <ChatDashboard
            showNotification={safeShowNotification}
            onModalStateChange={setChatModalOpen}
          />
        )}

        {activeTab === 'sentiment' && (
          <SentimentDashboard
            showNotification={safeShowNotification}
            onFormStateChange={setSentimentFormOpen}
          />
        )}

        {activeTab === 'social' && (
          <SocialIntegrationDashboard
            showNotification={safeShowNotification}
            onFormStateChange={setSocialFormOpen}
          />
        )}

        {activeTab === 'governance-simplified' && (
          <SimplifiedGovernanceDashboard
            showNotification={safeShowNotification}
            onFormStateChange={setGovernanceFormOpen}
          />
        )}

        {activeTab === 'pmf-analytics' && (
          <ProductMarketFitDashboard
            showNotification={safeShowNotification}
            onFormStateChange={setPmfFormOpen}
          />
        )}

        {activeTab === 'multi-agent' && (
          <MultiAgentDashboard
            showNotification={safeShowNotification}
            onFormStateChange={setMultiAgentFormOpen}
          />
         )}
     
         {activeTab === 'responsible-ai' && (
          <ResponsibleAiDashboard
          	showNotification={safeShowNotification}
            onFormStateChange={setResponsibleAiFormOpen}
          />
         )}

         {activeTab === 'governance' && (
          <GovernanceDashboard
            showNotification={safeShowNotification}
            onFormStateChange={setGovernanceFormOpen}
          />
         )}
     
         {activeTab === 'policies' && (
          <div className="space-y-8">
          	<h2 className="text-3xl font-bold text-gray-900 mb-6 flex items-center justify-between">
              API Routing Policies
              <button
                onClick={() => { setShowPolicyForm(true); setEditingPolicy(null); setPolicyFormErrors({}); }}
                className="px-5 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-all duration-200 flex items-center space-x-2 shadow-md"
              >
                <PlusCircle size={20} /> <span>Create New Policy</span>
              </button>
            </h2>

            {(Array.isArray(policies) && policies.length > 0) ? (
              <div className="bg-white p-6 rounded-lg shadow-md overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">LLM ID</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Criteria</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rules</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {policies.map(policy => (
                      <tr key={policy.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{policy.name}</td>
                        <td className="px-6 py-4 text-sm text-gray-700">{policy.description}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{policy.action}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{modelProfileMap[policy.backend_id] || policy.backend_id || 'N/A'}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{policy.priority}</td>
                        <td className="px-6 py-4 text-sm text-gray-700 font-mono text-xs max-w-xs overflow-auto">
                          {formatValue(policy.criteria)}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-700 font-mono text-xs max-w-xs overflow-auto whitespace-pre-line">
                          {formatRules(policy.rules)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{dayjs(policy.updated_at).fromNow()}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => { setShowPolicyForm(true); setEditingPolicy(policy); }}
                              className="text-indigo-600 hover:text-indigo-900"
                              title="Edit Policy"
                            >
                              <Edit size={18} />
                            </button>
                            <button
                              onClick={() => handleDeletePolicy(policy.id)}
                              className="text-red-600 hover:text-red-900"
                              title="Delete Policy"
                            >
                              <Trash2 size={18} />
                            </button>
                            <a href={`${POLICY_MANAGER_API_PREFIX}/policies/${policy.id}`} target="_blank" rel="noopener noreferrer"
                                className="text-gray-500 hover:text-gray-700" title="View Raw JSON">
                              <ExternalLink size={18} />
                            </a>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-gray-600">No policies defined yet. Create your first policy to route API requests!</p>
            )}
          </div>
        )}

        {activeTab === 'models' && (
          <div className="space-y-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-6 flex items-center justify-between">
              LLM Profiles
              <button
                onClick={() => { setShowModelProfileForm(true); setEditingModelProfile(null); setModelProfileFormErrors({}); }}
                className="px-5 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-all duration-200 flex items-center space-x-2 shadow-md"
              >
                <PlusCircle size={20} /> <span>Create New LLM Profile</span>
              </button>
            </h2>

            {(Array.isArray(modelProfiles) && modelProfiles.length > 0) ? (
              <div className="bg-white p-6 rounded-lg shadow-md overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aliases</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Capabilities</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pricing Tier</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Latency (ms)</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">LLM URL</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">LLM Type</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {modelProfiles.map(profile => (
                      <tr key={profile.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{profile.name}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{formatValue(profile.aliases)}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{formatValue(profile.capabilities)}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{profile.pricing_tier}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{(profile.expected_latency_ms ?? 0).toFixed(2)}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{(profile.expected_cost ?? 0).toFixed(6)}</td>
                        <td className="px-6 py-4 text-sm text-gray-700 font-mono text-xs max-w-xs overflow-auto">{profile.url}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{profile.backend_type}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{dayjs(profile.updated_at).fromNow()}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => { setShowModelProfileForm(true); setEditingModelProfile(profile); }}
                              className="text-indigo-600 hover:text-indigo-900"
                              title="Edit LLM Profile"
                            >
                              <Edit size={18} />
                            </button>
                            <button
                              onClick={() => handleDeleteModelProfile(profile.id)}
                              className="text-red-600 hover:text-red-900"
                              title="Delete LLM Profile"
                            >
                              <Trash2 size={18} />
                            </button>
                            <a href={`${POLICY_MANAGER_API_PREFIX}/model_profiles/${profile.id}`} target="_blank" rel="noopener noreferrer"
                                className="text-gray-500 hover:text-gray-700" title="View Raw JSON">
                              <ExternalLink size={18} />
                            </a>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-gray-600">No LLM profiles defined yet. Create your first LLM profile!</p>
            )}
          </div>
        )}

        {activeTab === 'logs' && (
          <div className="space-y-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Recent Inference Logs</h2>

            {/* Filter and Search */}
            <div className="bg-white p-6 rounded-lg shadow-md flex flex-col sm:flex-row gap-4">
              <div className="flex-1 relative">
                <input
                  id="search-logs"
                  name="search-logs"
                  type="text"
                  placeholder="Search logs..."
                  value={searchTerm}
                  onChange={(e) => { setCurrentPage(1); setSearchTerm(e.target.value); }}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                />
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
              </div>

              <div className="flex-none w-full sm:w-auto">
                <select
                  id="filter-backend"
                  name="filter-backend"
                  value={filterBackend}
                  onChange={(e) => { setCurrentPage(1); setFilterBackend(e.target.value); }}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">All LLMs</option>
                  {(Array.isArray(modelProfiles) ? modelProfiles : []).map(profile => (
                    <option key={profile.id} value={profile.id}>{profile.name}</option>
                  ))}
                  {(() => {
                    try {
                      const logs = Array.isArray(recentInferenceLogs) ? recentInferenceLogs : [];
                      const profiles = Array.isArray(modelProfiles) ? modelProfiles : [];
                      const backendIds = logs.map(log => log?.selected_backend_id).filter(Boolean);
                      const uniqueIds = Array.from(new Set(backendIds));
                      return uniqueIds
                        .filter(id => id && !profiles.some(p => p?.id === id))
                        .map(id => id && <option key={id} value={id}>{id}</option>);
                    } catch (error) {
                      console.error('Error rendering backend options:', error);
                      return [];
                    }
                  })()}
                </select>
              </div>

              <div className="flex-none w-full sm:w-auto">
                <select
                  id="filter-status"
                  name="filter-status"
                  value={filterStatus}
                  onChange={(e) => { setCurrentPage(1); setFilterStatus(e.target.value); }}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">All Statuses</option>
                  <option value="200">200 (OK)</option>
                  <option value="400">400 (Bad Request)</option>
                  <option value="401">401 (Unauthorized)</option>
                  <option value="403">403 (Forbidden)</option>
                  <option value="404">404 (Not Found)</option>
                  <option value="500">500 (Internal Server Error)</option>
                  {(() => {
                    try {
                      const logs = Array.isArray(recentInferenceLogs) ? recentInferenceLogs : [];
                      const statusCodes = logs.map(log => String(log?.status_code || '')).filter(Boolean);
                      const uniqueStatuses = Array.from(new Set(statusCodes));
                      const commonStatuses = ['200', '400', '401', '403', '404', '500'];
                      return uniqueStatuses
                        .filter(status => status && !commonStatuses.includes(status))
                        .map(status => status && <option key={status} value={status}>{status}</option>);
                    } catch (error) {
                      console.error('Error rendering status options:', error);
                      return [];
                    }
                  })()}
                </select>
              </div>
            </div>


            {filteredAndSortedLogs.length > 0 ? (
              <div className="bg-white p-6 rounded-lg shadow-md overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <Th sortColumn={sortColumn} sortDirection={sortDirection} handleSort={handleSort} column="timestamp" label="Timestamp" />
                      <Th sortColumn={sortColumn} sortDirection={sortDirection} handleSort={handleSort} column="method" label="Method" />
                      <Th sortColumn={sortColumn} sortDirection={sortDirection} handleSort={handleSort} column="path" label="Path" />
                      <Th sortColumn={sortColumn} sortDirection={sortDirection} handleSort={handleSort} column="selected_backend_id" label="LLM" />
                      <Th sortColumn={sortColumn} sortDirection={sortDirection} handleSort={handleSort} column="status_code" label="Status" />
                      <Th sortColumn={sortColumn} sortDirection={sortDirection} handleSort={handleSort} column="latency_ms" label="Latency (ms)" />
                      <Th sortColumn={sortColumn} sortDirection={sortDirection} handleSort={handleSort} column="total_cost" label="Cost" />
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Error</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Policy ID</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model Used</th> {/* This column will now be populated */}
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {currentLogs.map((log) => (
                      <tr key={log.request_id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{dayjs(log.timestamp).format('YYYY-MM-DD HH:mm:ss')}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{log.method}</td>
                        <td className="px-6 py-4 text-sm text-gray-700 font-mono text-xs max-w-xs overflow-auto">{log.path}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{modelProfileMap[log.selected_backend_id] || log.selected_backend_id}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{log.status_code}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{(log.latency_ms ?? 0).toFixed(2)}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{(log.total_cost ?? 0).toFixed(6)}</td>
                        <td className="px-6 py-4 text-sm text-red-700 max-w-xs overflow-auto">{log.error}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{log.policy_id_applied}</td>
                        {/* THIS IS THE KEY LINE FOR model_used */}
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{modelProfileMap[log.model_used] || log.model_used}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => copyToClipboard(
                              `Request ID: ${log.request_id}\n\nRequest Headers: ${JSON.stringify(log.request_headers, null, 2)}\n\nRequest Body: ${log.request_body_snippet}\n\nResponse Headers: ${JSON.stringify(log.response_headers, null, 2)}\n\nResponse Body: ${log.response_body_snippet}`,
                              'Inference log details copied to clipboard!'
                            )}
                            className="text-indigo-600 hover:text-indigo-900"
                            title="Copy Details to Clipboard"
                          >
                            <ClipboardCopy size={18} />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {/* Pagination Controls */}
                <div className="flex justify-between items-center mt-4">
                  <button
                    onClick={() => paginate(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="px-4 py-2 bg-gray-200 rounded-md disabled:opacity-50 flex items-center space-x-1"
                  >
                    <ChevronLeft size={16} /> <span>Previous</span>
                  </button>
                  <span className="text-sm text-gray-700">Page {currentPage} of {totalPages}</span>
                  <button
                    onClick={() => paginate(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="px-4 py-2 bg-gray-200 rounded-md disabled:opacity-50 flex items-center space-x-1"
                  >
                    <span>Next</span> <ChevronRight size={16} />
                  </button>
                </div>
              </div>
            ) : (
              <p className="text-gray-600">No recent inference logs available. Make some requests via the proxy gateway to see them appear here!</p>
            )}
          </div>
        )}

        {/* Evaluations Tab */}
        {activeTab === 'evaluations' && (
          <div className="space-y-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">LLM Evaluation Results</h2>

            {/* Filter and Search for Evaluations */}
            <div className="bg-white p-6 rounded-lg shadow-md flex flex-col sm:flex-row gap-4">
              <div className="flex-1 relative">
                <input
                  id="search-evaluations"
                  name="search-evaluations"
                  type="text"
                  placeholder="Search evaluations..."
                  value={evalSearchTerm}
                  onChange={(e) => { setEvalCurrentPage(1); setEvalSearchTerm(e.target.value); }}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                />
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
              </div>

              <div className="flex-none w-full sm:w-auto">
                <select
                  id="eval-filter-model"
                  name="eval-filter-model"
                  value={evalFilterModel}
                  onChange={(e) => { setEvalCurrentPage(1); setEvalFilterModel(e.target.value); }}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">All Models</option>
                  {(Array.isArray(modelProfiles) ? modelProfiles : []).map(profile => (
                    <option key={profile.id} value={profile.id}>{profile.name}</option>
                  ))}
                  {(() => {
                    try {
                      const evals = Array.isArray(evaluationResults) ? evaluationResults : [];
                      const profiles = Array.isArray(modelProfiles) ? modelProfiles : [];
                      const modelIds = evals.map(e => e?.model_id).filter(Boolean);
                      const uniqueIds = Array.from(new Set(modelIds));
                      return uniqueIds
                        .filter(id => id && !profiles.some(p => p?.id === id))
                        .map(id => id && <option key={id} value={id}>{id}</option>);
                    } catch (error) {
                      console.error('Error rendering evaluation model options:', error);
                      return [];
                    }
                  })()}
                </select>
              </div>

              <div className="flex-none w-full sm:w-auto">
                <select
                  id="eval-filter-type"
                  name="eval-filter-type"
                  value={evalFilterType}
                  onChange={(e) => { setEvalCurrentPage(1); setEvalFilterType(e.target.value); }}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">All Evaluation Types</option>
                  {(() => {
                    try {
                      const evals = Array.isArray(evaluationResults) ? evaluationResults : [];
                      const types = evals.map(e => e?.evaluation_type).filter(Boolean);
                      const uniqueTypes = Array.from(new Set(types));
                      return uniqueTypes.map(type => type && <option key={type} value={type}>{type}</option>);
                    } catch (error) {
                      console.error('Error rendering evaluation type options:', error);
                      return [];
                    }
                  })()}
                </select>
              </div>
            </div>

            {evaluationResultsLoading ? (
              <p className="text-gray-600">Loading evaluation results...</p>
            ) : filteredAndSortedEvaluationResults.length > 0 ? (
              <div className="bg-white p-6 rounded-lg shadow-md overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <Th sortColumn={evalSortColumn} sortDirection={evalSortDirection} handleSort={handleEvalSort} column="evaluated_at" label="Evaluated At" />
                      <Th sortColumn={evalSortColumn} sortDirection={evalSortDirection} handleSort={handleEvalSort} column="model_id" label="Model" />
                      <Th sortColumn={evalSortColumn} sortDirection={evalSortDirection} handleSort={handleEvalSort} column="task_type" label="Task Type" />
                      <Th sortColumn={evalSortColumn} sortDirection={evalSortDirection} handleSort={handleEvalSort} column="evaluation_type" label="Eval Type" />
                      <Th sortColumn={evalSortColumn} sortDirection={evalSortDirection} handleSort={handleEvalSort} column="score" label="Score" />
                      <Th sortColumn={evalSortColumn} sortDirection={evalSortDirection} handleSort={handleEvalSort} column="passed" label="Passed" />
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prompt</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">LLM Response</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expected Response</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Feedback</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Raw Metrics</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Metadata</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {currentEvaluations.map((evalItem) => (
                      <tr key={evalItem.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{dayjs(evalItem.evaluated_at).format('YYYY-MM-DD HH:mm:ss')}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{modelProfileMap[evalItem.model_id] || evalItem.model_id}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{evalItem.task_type}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{evalItem.evaluation_type}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{(evalItem.score ?? 0).toFixed(4)}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={evalItem.passed ? 'score-passed' : 'score-failed'}>
                            {String(evalItem.passed)}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-700 max-w-xs overflow-auto">{evalItem.prompt}</td>
                        <td className="px-6 py-4 text-sm text-gray-700 max-w-xs overflow-auto">{evalItem.llm_response}</td>
                        <td className="px-6 py-4 text-sm text-gray-700 max-w-xs overflow-auto">{evalItem.expected_response}</td>
                        <td className="px-6 py-4 text-sm text-gray-700 max-w-xs overflow-auto">{evalItem.feedback}</td>
                        <td className="px-6 py-4 text-sm text-gray-700 max-w-xs overflow-auto font-mono text-xs">{evalItem.raw_metrics}</td>
                        <td className="px-6 py-4 text-sm text-gray-700 max-w-xs overflow-auto font-mono text-xs">{evalItem.metadata}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => copyToClipboard(
                              JSON.stringify(evalItem, null, 2),
                              'Evaluation result copied to clipboard!'
                            )}
                            className="text-indigo-600 hover:text-indigo-900"
                            title="Copy Raw JSON"
                          >
                            <ClipboardCopy size={18} />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {/* Pagination Controls */}
                <div className="flex justify-between items-center mt-4">
                  <button
                    onClick={() => paginateEvaluations(evalCurrentPage - 1)}
                    disabled={evalCurrentPage === 1}
                    className="px-4 py-2 bg-gray-200 rounded-md disabled:opacity-50 flex items-center space-x-1"
                  >
                    <ChevronLeft size={16} /> <span>Previous</span>
                  </button>
                  <span className="text-sm text-gray-700">Page {evalCurrentPage} of {totalEvalPages}</span>
                  <button
                    onClick={() => paginateEvaluations(evalCurrentPage + 1)}
                    disabled={evalCurrentPage === totalEvalPages}
                    className="px-4 py-2 bg-gray-200 rounded-md disabled:opacity-50 flex items-center space-x-1"
                  >
                    <span>Next</span> <ChevronRight size={16} />
                  </button>
                </div>
              </div>
            ) : (
              <p className="text-gray-600">No evaluation results available. Ensure the Data Processor and Evaluation Service are running and generating synthetic data.</p>
            )}
          </div>
        )}

        {/* Curated Data Tab */}

        {/* Curated Data Tab */}
        {activeTab === 'curated-data' && (
          <div className="space-y-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Curated Data</h2>

            {/* Filter and Search for Curated Data */}
            <div className="bg-white p-6 rounded-lg shadow-md flex flex-col sm:flex-row gap-4">
              <div className="flex-1 relative">
                <input
                  id="search-curated-data"
                  name="search-curated-data"
                  type="text"
                  placeholder="Search curated data..."
                  value={curatedSearchTerm}
                  onChange={(e) => { setCuratedCurrentPage(1); setCuratedSearchTerm(e.target.value); }}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                />
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
              </div>

              <div className="flex-none w-full sm:w-auto">
                <select
                  id="curated-filter-model"
                  name="curated-filter-model"
                  value={curatedFilterModel}
                  onChange={(e) => { setCuratedCurrentPage(1); setCuratedFilterModel(e.target.value); }}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">All Models</option>
                  {(Array.isArray(modelProfiles) ? modelProfiles : []).map(profile => (
                    <option key={profile.id} value={profile.id}>{profile.name}</option>
                  ))}
                  {(() => {
                    try {
                      const curated = Array.isArray(curatedData) ? curatedData : [];
                      const profiles = Array.isArray(modelProfiles) ? modelProfiles : [];
                      const modelIds = curated.map(d => d?.model_id).filter(Boolean);
                      const uniqueIds = Array.from(new Set(modelIds));
                      return uniqueIds
                        .filter(id => id && !profiles.some(p => p?.id === id))
                        .map(id => id && <option key={id} value={id}>{id}</option>);
                    } catch (error) {
                      console.error('Error rendering curated model options:', error);
                      return [];
                    }
                  })()}
                </select>
              </div>

              <div className="flex-none w-full sm:w-auto">
                <select
                  id="curated-filter-type"
                  name="curated-filter-type"
                  value={curatedFilterType}
                  onChange={(e) => { setCuratedCurrentPage(1); setCuratedFilterType(e.target.value); }}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">All Task Types</option>
                  {(() => {
                    try {
                      const curated = Array.isArray(curatedData) ? curatedData : [];
                      const types = curated.map(d => d?.task_type).filter(Boolean);
                      const uniqueTypes = Array.from(new Set(types));
                      return uniqueTypes.map(type => type && <option key={type} value={type}>{type}</option>);
                    } catch (error) {
                      console.error('Error rendering curated task type options:', error);
                      return [];
                    }
                  })()}
                </select>
              </div>
            </div>

            {curatedDataLoading ? (
              <p className="text-gray-600">Loading curated data...</p>
            ) : filteredAndSortedCuratedData.length > 0 ? (
              <div className="bg-white p-6 rounded-lg shadow-md overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <Th sortColumn={curatedSortColumn} sortDirection={curatedSortDirection} handleSort={handleCuratedSort} column="generated_at" label="Generated At" />
                      <Th sortColumn={curatedSortColumn} sortDirection={curatedSortDirection} handleSort={handleCuratedSort} column="model_id" label="Model" />
                      <Th sortColumn={curatedSortColumn} sortDirection={curatedSortDirection} handleSort={handleCuratedSort} column="task_type" label="Task Type" />
                      <Th sortColumn={curatedSortColumn} sortDirection={curatedSortDirection} handleSort={handleCuratedSort} column="evaluation_type" label="Eval Type" />
                      <Th sortColumn={curatedSortColumn} sortDirection={curatedSortDirection} handleSort={handleCuratedSort} column="evaluation_score" label="Eval Score" />
                      <Th sortColumn={curatedSortColumn} sortDirection={curatedSortDirection} handleSort={handleCuratedSort} column="evaluation_passed" label="Eval Passed" />
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prompt</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">LLM Response</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Feedback</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Raw Metrics</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Metadata</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {currentCuratedData.map((curatedItem) => (
                      <tr key={curatedItem.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{dayjs(curatedItem.generated_at).format('YYYY-MM-DD HH:mm:ss')}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{modelProfileMap[curatedItem.model_id] || curatedItem.model_id}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{curatedItem.task_type}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{curatedItem.evaluation_type}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{(curatedItem.evaluation_score ?? 0).toFixed(4)}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={curatedItem.evaluation_passed ? 'score-passed' : 'score-failed'}>
                            {String(curatedItem.evaluation_passed)}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-700 max-w-xs overflow-auto">{curatedItem.prompt}</td>
                        <td className="px-6 py-4 text-sm text-gray-700 max-w-xs overflow-auto">{curatedItem.llm_response}</td>
                        <td className="px-6 py-4 text-sm text-gray-700 max-w-xs overflow-auto">{curatedItem.feedback}</td>
                        <td className="px-6 py-4 text-sm text-gray-700 max-w-xs overflow-auto font-mono text-xs">{curatedItem.raw_metrics}</td>
                        <td className="px-6 py-4 text-sm text-gray-700 max-w-xs overflow-auto font-mono text-xs">{curatedItem.metadata}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => copyToClipboard(
                              JSON.stringify(curatedItem, null, 2),
                              'Curated data entry copied to clipboard!'
                            )}
                            className="text-indigo-600 hover:text-indigo-900"
                            title="Copy Raw JSON"
                          >
                            <ClipboardCopy size={18} />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {/* Pagination Controls */}
                <div className="flex justify-between items-center mt-4">
                  <button
                    onClick={() => paginateCurated(curatedCurrentPage - 1)}
                    disabled={curatedCurrentPage === 1}
                    className="px-4 py-2 bg-gray-200 rounded-md disabled:opacity-50 flex items-center space-x-1"
                  >
                    <ChevronLeft size={16} /> <span>Previous</span>
                  </button>
                  <span className="text-sm text-gray-700">Page {curatedCurrentPage} of {totalCuratedPages}</span>
                  <button
                    onClick={() => paginateCurated(curatedCurrentPage + 1)}
                    disabled={curatedCurrentPage === totalCuratedPages}
                    className="px-4 py-2 bg-gray-200 rounded-md disabled:opacity-50 flex items-center space-x-1"
                  >
                    <span>Next</span> <ChevronRight size={16} />
                  </button>
                </div>
              </div>
            ) : (
              <p className="text-gray-600">No curated data available. Ensure the Data Processor and Evaluation Service are running and generating synthetic data.</p>
            )}
          </div>
        )}

        {activeTab === 'about' && (
          <div className="space-y-8 p-6 bg-white rounded-lg shadow-md">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">About AI Operations Hub</h2>

            <section className="space-y-4">
              <h3 className="text-2xl font-semibold text-gray-800">Product Overview</h3>
              <p className="text-gray-700">
                AI Operations Hub is a comprehensive platform that provides intelligent LLM routing, autonomous task execution, and multi-agent orchestration capabilities.
                The system operates in multiple modes to optimize AI workloads while ensuring responsible AI practices through advanced governance and monitoring.
              </p>
              <ul className="list-disc list-inside text-gray-700 space-y-1">
                <li><span className="font-semibold">Intelligent LLM Routing:</span> Automatically route requests to optimal models based on cost, performance, and quality.</li>
                <li><span className="font-semibold">Autonomous Planning:</span> Submit high-level goals for autonomous multi-step task execution and workflow orchestration.</li>
                <li><span className="font-semibold">Multi-Agent Orchestration:</span> Coordinate multiple specialized AI agents working together on complex collaborative workflows.</li>
                <li><span className="font-semibold">Responsible AI Governance:</span> Comprehensive bias detection, explainability, robustness testing, and compliance monitoring.</li>
                <li><span className="font-semibold">Advanced Analytics:</span> Real-time performance monitoring, cost optimization insights, and predictive analytics.</li>
                <li><span className="font-semibold">PromptOps Management:</span> A/B testing, prompt optimization, template management, and performance evaluation.</li>
              </ul>
            </section>

            <section className="space-y-4">
              <h3 className="text-2xl font-semibold text-gray-800">Integration Approaches</h3>
              <p className="text-gray-700">
                AI Operations Hub provides three main integration approaches for different use cases:
              </p>
              <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                <div>
                  <h4 className="font-semibold text-gray-800">1. Direct LLM Integration</h4>
                  <p className="text-sm text-gray-600">Send individual LLM requests through our intelligent Proxy Gateway for cost optimization and routing.</p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">2. Autonomous Planning Integration</h4>
                  <p className="text-sm text-gray-600">Submit high-level goals to our Planning Engine for autonomous multi-step task execution.</p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">3. Multi-Agent Orchestration</h4>
                  <p className="text-sm text-gray-600">Leverage our comprehensive multi-agent platform for complex collaborative AI workflows.</p>
                </div>
              </div>

              <h4 className="text-xl font-semibold text-gray-800 mt-6">Direct LLM API Usage</h4>
              <p className="text-gray-700">
                For direct LLM integration, send all chat completion requests to:
              </p>
              <pre className="bg-gray-100 p-3 rounded-md text-sm overflow-x-auto font-mono text-gray-800">
                <code>{`${API_BASE_URL.replace('/api', '/v1')}/chat/completions`}</code>
              </pre>
              <p className="text-gray-700">
                The request body should follow the standard LLM Chat Completion API format (e.g., OpenAI's endpoint).
              </p>

              <h4 className="text-xl font-semibold text-gray-800 mt-4">Sample Curl Requests:</h4>

              <h5 className="text-lg font-medium text-gray-700 mt-3">Basic Request (Intelligent Routing Applied):</h5>
              <pre className="bg-gray-100 p-3 rounded-md text-sm overflow-x-auto font-mono text-gray-800">
                <code>{`curl -X POST \\
  ${API_BASE_URL.replace('/api', '/v1')}/chat/completions \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Tell me a short, imaginative story about a space squirrel."}
    ]
  }'`}</code>
              </pre>

              <h5 className="text-lg font-medium text-gray-700 mt-3">Conversational Turn 1 (New Conversation):</h5>
              <p className="text-gray-700">
                For the first turn, omit <code>conversation_id</code>. The proxy will generate one.
              </p>
              <pre className="bg-gray-100 p-3 rounded-md text-sm overflow-x-auto font-mono text-gray-800">
                <code>{`curl -X POST \\
  ${API_BASE_URL.replace('/api', '/v1')}/chat/completions \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "claude-3-opus-********",
    "messages": [
      {"role": "user", "content": "Hi, who are you and what do you do?"}
    ]
  }'`}</code>
              </pre>

              <h5 className="text-lg font-medium text-gray-700 mt-3">Conversational Turn 2 (Continuing the Conversation):</h5>
              <p className="text-gray-700">
                Use the <code>conversation_id</code> obtained from the first turn's logs.
              </p>
              <pre className="bg-gray-100 p-3 rounded-md text-sm overflow-x-auto font-mono text-gray-800">
                <code>{`curl -X POST \\
  ${API_BASE_URL.replace('/api', '/v1')}/chat/completions \\
  -H "Content-Type: application/json" \\
  -d '{
    "conversation_id": "<YOUR_CONVERSATION_ID>",
    "model": "claude-3-opus-********",
    "messages": [
      {"role": "user", "content": "Can you elaborate on the second part of your previous answer?"}
    ]
  }'`}</code>
              </pre>

              <h5 className="text-lg font-medium text-gray-700 mt-3">Request with User Override (using `X-Preferred-LLM-ID` header):</h5>
              <p className="text-gray-700">
                Override automatic routing by specifying a preferred LLM ID.
              </p>
              <pre className="bg-gray-100 p-3 rounded-md text-sm overflow-x-auto font-mono text-gray-800">
                <code>{`curl -X POST \\
  ${API_BASE_URL.replace('/api', '/v1')}/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "X-LLM-API-Key: YOUR_VIRTUAL_LLM_API_KEY" \\
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Explain quantum entanglement in simple terms."}
    ]
  }'`}</code>
              </pre>
            </section>
          </div>
        )}

        {activeTab === 'profile' && (
          <div className="space-y-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">User Profile</h2>
            <UserProfile />
          </div>
        )}

        {activeTab === 'admin' && (
          <ProtectedRoute requireAdmin={true}>
            <div className="space-y-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Administration</h2>
              <UserManagement />
            </div>
          </ProtectedRoute>
        )}
        </main>
      </div>

      {/* Policy Form Modal */}
      {showPolicyForm && (
        <PolicyForm
          policy={editingPolicy ? editingPolicy : { id: '', name: '', description: '', criteria: [], action: 'ROUTE', backend_id: '', priority: 0, rules: '{}' }}
          onSave={editingPolicy ? handleUpdatePolicy : handleCreatePolicy}
          onCancel={() => { setShowPolicyForm(false); setEditingPolicy(null); setPolicyFormErrors({}); }}
          errors={policyFormErrors}
        />
      )}

      {/* Model Profile Form Modal */}
      {showModelProfileForm && (
        <ModelProfileForm
          profile={editingModelProfile ? editingModelProfile : { id: '', name: '', aliases: [], capabilities: [], pricing_tier: '', data_sensitivity: '', expected_latency_ms: 0, expected_cost: 0, url: '', backend_type: '', cost_per_input_token: 0, cost_per_output_token: 0, cpu_cost_per_hour: 0, memory_cost_per_hour: 0, api_key: '' }}
          onSave={editingModelProfile ? handleUpdateModelProfile : handleCreateModelProfile}
          onCancel={() => { setShowModelProfileForm(false); setEditingModelProfile(null); setModelProfileFormErrors({}); }}
          errors={modelProfileFormErrors}
        />
      )}

      {/* Real-time Governance Alerts */}
      <RealTimeGovernanceAlerts
        alerts={governanceAlerts || []}
        onAcknowledge={acknowledgeAlert}
        onResolve={resolveAlert}
        onDismiss={dismissAlert}
      />
    </div>
  );
};

// Wrap App with AuthProvider
const AppWithAuth = () => {
  return (
    <AuthProvider>
      <ProtectedRoute>
        <App />
      </ProtectedRoute>
    </AuthProvider>
  );
};

export default AppWithAuth;
