/**
 * Tests for localStorage utility functions
 */

import {
  getStorageItem,
  setStorageItem,
  removeStorageItem,
  STORAGE_KEYS,
  cleanupOldConversations,
  isStorageAvailable,
  exportChatData,
  importChatData
} from '../localStorage';

// Mock localStorage
const localStorageMock = (() => {
  let store = {};
  return {
    getItem: (key) => store[key] || null,
    setItem: (key, value) => {
      store[key] = value.toString();
    },
    removeItem: (key) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    }
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('localStorage utilities', () => {
  beforeEach(() => {
    localStorage.clear();
  });

  test('should store and retrieve items correctly', () => {
    const testData = { test: 'value', number: 42 };
    
    const success = setStorageItem('test-key', testData);
    expect(success).toBe(true);
    
    const retrieved = getStorageItem('test-key');
    expect(retrieved).toEqual(testData);
  });

  test('should return default value for non-existent keys', () => {
    const defaultValue = { default: true };
    const result = getStorageItem('non-existent', defaultValue);
    expect(result).toEqual(defaultValue);
  });

  test('should remove items correctly', () => {
    setStorageItem('test-key', 'test-value');
    expect(getStorageItem('test-key')).toBe('test-value');
    
    const success = removeStorageItem('test-key');
    expect(success).toBe(true);
    expect(getStorageItem('test-key')).toBe(null);
  });

  test('should check storage availability', () => {
    expect(isStorageAvailable()).toBe(true);
  });

  test('should export and import chat data', () => {
    const testConversations = [
      ['conv1', { id: 'conv1', title: 'Test 1', messages: [] }],
      ['conv2', { id: 'conv2', title: 'Test 2', messages: [] }]
    ];
    const testSettings = { model: 'gpt-4', temperature: 0.8 };
    
    setStorageItem(STORAGE_KEYS.CONVERSATIONS, testConversations);
    setStorageItem(STORAGE_KEYS.CHAT_SETTINGS, testSettings);
    setStorageItem(STORAGE_KEYS.ACTIVE_CONVERSATION, 'conv1');
    
    const exported = exportChatData();
    expect(exported.conversations).toEqual(testConversations);
    expect(exported.settings).toEqual(testSettings);
    expect(exported.activeConversation).toBe('conv1');
    expect(exported.exportedAt).toBeDefined();
    
    // Clear storage and import
    localStorage.clear();
    const success = importChatData(exported);
    expect(success).toBe(true);
    
    expect(getStorageItem(STORAGE_KEYS.CONVERSATIONS)).toEqual(testConversations);
    expect(getStorageItem(STORAGE_KEYS.CHAT_SETTINGS)).toEqual(testSettings);
    expect(getStorageItem(STORAGE_KEYS.ACTIVE_CONVERSATION)).toBe('conv1');
  });

  test('should cleanup old conversations', () => {
    // Create 60 conversations (more than MAX_CONVERSATIONS = 50)
    const conversations = [];
    for (let i = 0; i < 60; i++) {
      conversations.push([
        `conv${i}`,
        {
          id: `conv${i}`,
          title: `Conversation ${i}`,
          messages: [],
          updatedAt: new Date(Date.now() - i * 1000).toISOString() // Older conversations have earlier timestamps
        }
      ]);
    }
    
    setStorageItem(STORAGE_KEYS.CONVERSATIONS, conversations);
    
    cleanupOldConversations();
    
    const remaining = getStorageItem(STORAGE_KEYS.CONVERSATIONS, []);
    expect(remaining.length).toBe(50);
    
    // Check that the most recent conversations are kept
    const remainingIds = remaining.map(([id]) => id);
    expect(remainingIds).toContain('conv0'); // Most recent
    expect(remainingIds).toContain('conv49'); // 50th most recent
    expect(remainingIds).not.toContain('conv59'); // Oldest should be removed
  });
});
