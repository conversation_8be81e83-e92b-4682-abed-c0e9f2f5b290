/**
 * Utility functions for localStorage operations with error handling
 */

// Storage keys
export const STORAGE_KEYS = {
  CONVERSATIONS: 'ai_chat_conversations',
  ACTIVE_CONVERSATION: 'ai_chat_active_conversation',
  CHAT_SETTINGS: 'ai_chat_settings',
};

// Maximum number of conversations to keep in localStorage
const MAX_CONVERSATIONS = 50;

/**
 * Safely get an item from localStorage
 * @param {string} key - The localStorage key
 * @param {*} defaultValue - Default value if key doesn't exist or parsing fails
 * @returns {*} The parsed value or default value
 */
export const getStorageItem = (key, defaultValue = null) => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.warn(`Failed to get item from localStorage (${key}):`, error);
    return defaultValue;
  }
};

/**
 * Safely set an item in localStorage
 * @param {string} key - The localStorage key
 * @param {*} value - The value to store
 * @returns {boolean} True if successful, false otherwise
 */
export const setStorageItem = (key, value) => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.warn(`Failed to set item in localStorage (${key}):`, error);
    
    // If quota exceeded, try to clean up old conversations
    if (error.name === 'QuotaExceededError' && key === STORAGE_KEYS.CONVERSATIONS) {
      cleanupOldConversations();
      // Try again after cleanup
      try {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
      } catch (retryError) {
        console.error('Failed to save even after cleanup:', retryError);
      }
    }
    return false;
  }
};

/**
 * Safely remove an item from localStorage
 * @param {string} key - The localStorage key
 * @returns {boolean} True if successful, false otherwise
 */
export const removeStorageItem = (key) => {
  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.warn(`Failed to remove item from localStorage (${key}):`, error);
    return false;
  }
};

/**
 * Clean up old conversations to free up storage space
 */
export const cleanupOldConversations = () => {
  try {
    const conversationsArray = getStorageItem(STORAGE_KEYS.CONVERSATIONS, []);
    
    if (conversationsArray.length > MAX_CONVERSATIONS) {
      // Sort by updatedAt and keep only the most recent conversations
      const sortedConversations = conversationsArray
        .sort((a, b) => new Date(b[1].updatedAt) - new Date(a[1].updatedAt))
        .slice(0, MAX_CONVERSATIONS);
      
      setStorageItem(STORAGE_KEYS.CONVERSATIONS, sortedConversations);
      console.log(`Cleaned up old conversations, kept ${sortedConversations.length} most recent`);
    }
  } catch (error) {
    console.warn('Failed to cleanup old conversations:', error);
  }
};

/**
 * Get the size of localStorage usage in bytes (approximate)
 * @returns {number} Approximate size in bytes
 */
export const getStorageSize = () => {
  try {
    let total = 0;
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        total += localStorage[key].length + key.length;
      }
    }
    return total;
  } catch (error) {
    console.warn('Failed to calculate storage size:', error);
    return 0;
  }
};

/**
 * Check if localStorage is available
 * @returns {boolean} True if localStorage is available
 */
export const isStorageAvailable = () => {
  try {
    const test = '__storage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Export all chat data for backup
 * @returns {Object} All chat-related data
 */
export const exportChatData = () => {
  return {
    conversations: getStorageItem(STORAGE_KEYS.CONVERSATIONS, []),
    activeConversation: getStorageItem(STORAGE_KEYS.ACTIVE_CONVERSATION),
    settings: getStorageItem(STORAGE_KEYS.CHAT_SETTINGS, {}),
    exportedAt: new Date().toISOString(),
  };
};

/**
 * Import chat data from backup
 * @param {Object} data - The data to import
 * @returns {boolean} True if successful
 */
export const importChatData = (data) => {
  try {
    if (data.conversations) {
      setStorageItem(STORAGE_KEYS.CONVERSATIONS, data.conversations);
    }
    if (data.activeConversation) {
      setStorageItem(STORAGE_KEYS.ACTIVE_CONVERSATION, data.activeConversation);
    }
    if (data.settings) {
      setStorageItem(STORAGE_KEYS.CHAT_SETTINGS, data.settings);
    }
    return true;
  } catch (error) {
    console.error('Failed to import chat data:', error);
    return false;
  }
};
