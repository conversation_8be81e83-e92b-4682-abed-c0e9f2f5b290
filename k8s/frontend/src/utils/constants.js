// API endpoints
// Support both Vite and React environment variables for compatibility
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || import.meta.env.REACT_APP_API_BASE_URL || '';
export const DASHBOARD_API_PREFIX = `${API_BASE_URL}/api`;
export const POLICY_MANAGER_API_PREFIX = `${API_BASE_URL}/api`;
export const PLANNING_API_PREFIX = `${API_BASE_URL}/api/planning/v1`;
export const MULTI_AGENT_API_PREFIX = `${API_BASE_URL}/api/multi-agent/v1`;
export const GOVERNANCE_API_PREFIX = `${API_BASE_URL}/governance`;

// User configuration
export const DEFAULT_USER_ID = 'dashboard-user'; // Consistent user ID across all services

// Governance API endpoints
export const GOVERNANCE_POLICIES_ENDPOINT = `${GOVERNANCE_API_PREFIX}/policies`;
export const GOVERNANCE_ALERTS_ENDPOINT = `${GOVERNANCE_API_PREFIX}/alerts`;
export const GOVERNANCE_COMPLIANCE_ENDPOINT = `${GOVERNANCE_API_PREFIX}/compliance-status`;
export const GOVERNANCE_AUDIT_ENDPOINT = `${GOVERNANCE_API_PREFIX}/audit-logs`;
export const GOVERNANCE_METRICS_ENDPOINT = `${GOVERNANCE_API_PREFIX}/metrics`;

// Ensure consistent endpoint naming
export const POLICIES_ENDPOINT = `${POLICY_MANAGER_API_PREFIX}/policies`;
export const MODEL_PROFILES_ENDPOINT = `${POLICY_MANAGER_API_PREFIX}/model-profiles`; // Use hyphenated version consistently

// Planning API endpoints
export const GOALS_ENDPOINT = `${PLANNING_API_PREFIX}/goals`;
export const TEMPLATES_ENDPOINT = `${PLANNING_API_PREFIX}/templates`;

// Model Profile Constants
export const MODEL_PROFILE_CAPABILITIES = [
  'text-generation',
  'chat',
  'code-generation',
  'summarization',
  'complex-reasoning',
  'financial-analysis',
  'customer-support',
  'multi-modal',
  'vision',
  'text-classification',
  'long-context',
  'report-generation',
  'FAQ-answering'
];

export const MODEL_PROFILE_PRICING_TIERS = [
  'free',
  'basic',
  'standard',
  'premium',
  'enterprise',
  'custom'
];

export const MODEL_PROFILE_DATA_SENSITIVITY_LEVELS = [
  'low',
  'medium',
  'high'
];

export const MODEL_PROFILE_BACKEND_TYPES = [
  'openai',
  'openai-external',
  'anthropic',
  'anthropic-external',
  'google',
  'google-external',
  'vllm',
  'llama',
  'mistral',
  'custom'
];

// Planning Constants
export const GOAL_STATUSES = [
  'pending',
  'planning',
  'ready',
  'executing',
  'completed',
  'failed',
  'cancelled'
];

export const TASK_STATUSES = [
  'pending',
  'ready',
  'running',
  'completed',
  'failed',
  'skipped'
];

export const CONSTRAINT_TYPES = [
  'time',
  'cost',
  'quality',
  'resource'
];

export const CONSTRAINT_OPERATORS = [
  '<=',
  '>=',
  '==',
  '!=',
  '<',
  '>'
];

export const CONSTRAINT_SEVERITIES = [
  'soft',
  'hard',
  'preference'
];

export const SUCCESS_CRITERIA_OPERATORS = [
  '>=',
  '<=',
  '==',
  '!=',
  '>',
  '<',
  'contains',
  'not_contains'
];

export const GOAL_PRIORITIES = [
  { value: 1, label: 'Low (1)' },
  { value: 2, label: 'Low (2)' },
  { value: 3, label: 'Low (3)' },
  { value: 4, label: 'Medium (4)' },
  { value: 5, label: 'Medium (5)' },
  { value: 6, label: 'Medium (6)' },
  { value: 7, label: 'High (7)' },
  { value: 8, label: 'High (8)' },
  { value: 9, label: 'Critical (9)' },
  { value: 10, label: 'Critical (10)' }
];

// Governance Constants
export const GOVERNANCE_POLICY_TYPES = [
  'access_control',
  'data_usage',
  'model_deployment',
  'compliance',
  'security',
  'privacy'
];

export const GOVERNANCE_POLICY_STATUSES = [
  'active',
  'inactive',
  'draft'
];

export const GOVERNANCE_ALERT_SEVERITIES = [
  'critical',
  'high',
  'medium',
  'low'
];

export const GOVERNANCE_ALERT_STATUSES = [
  'open',
  'acknowledged',
  'resolved',
  'dismissed'
];

export const GOVERNANCE_ENFORCEMENT_MODES = [
  'strict',
  'warn',
  'log'
];

export const GOVERNANCE_RULE_ACTIONS = [
  'allow',
  'deny',
  'require_approval',
  'log',
  'alert'
];

export const COMPLIANCE_FRAMEWORKS = [
  'EU_AI_Act',
  'NIST_AI_RMF',
  'ISO_23053',
  'GDPR',
  'CCPA',
  'SOX',
  'HIPAA'
];

export const COMPLIANCE_STATUSES = [
  'compliant',
  'non_compliant',
  'partial',
  'pending',
  'unknown'
];
