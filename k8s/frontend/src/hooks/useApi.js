import { useCallback, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';

// API prefixes
// These prefixes are relative to window.location.origin, relying on proxying in dev/ingress in prod.
// Support both Vite and React environment variables for compatibility
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || import.meta.env.REACT_APP_API_BASE_URL || '';
export const DASHBOARD_API_PREFIX = `${API_BASE_URL}/api`;
export const POLICY_MANAGER_PREFIX = `${API_BASE_URL}/api`;

// Create the hook
const useApi = () => {
  const [apiLoading, setApiLoading] = useState(false);
  const [apiError, setApiError] = useState(null);
  const { getAuthHeaders } = useAuth();

  // Default options for fetch
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    credentials: 'same-origin'
  };

  const callApi = useCallback(async (endpoint, options = {}) => {
    console.log(`[API] Calling: ${endpoint}`);
    setApiLoading(true);
    setApiError(null); // Clear previous errors
    
    try {
      // Log the full URL being called
      const fullUrl = endpoint.startsWith('http') 
        ? endpoint 
        : new URL(endpoint, window.location.origin).toString();
      console.log(`[API] Full URL: ${fullUrl}`);
      
      // Make the API call with authentication headers
      const authHeaders = getAuthHeaders();
      console.log('[API] Auth headers for', fullUrl, ':', authHeaders);

      const response = await fetch(fullUrl, {
        ...defaultOptions,
        ...options,
        headers: {
          ...defaultOptions.headers,
          ...authHeaders, // Include authentication headers
          ...options.headers,
          'Cache-Control': 'no-cache', // Ensure fresh data
          'X-Debug-Timestamp': Date.now().toString() // Useful for debugging proxies
        }
      });
      
      console.log(`[API] Response status: ${response.status} for ${endpoint}`);
      
      // Process response
      if (response.ok) {
        // Read the response body only once as text
        const responseText = await response.text();

        // Try to parse as JSON first
        try {
          const data = JSON.parse(responseText);
          console.log(`[API] Response data for ${endpoint}:`, data);
          return data;
        } catch (parseError) {
          // If JSON parsing fails, return the raw text
          console.log(`[API] Non-JSON response from ${endpoint}: ${responseText}`);
          return responseText;
        }
      } else {
        console.error(`[API] Error response ${response.status} for ${endpoint}`);
        let errorText = 'Unknown error';
        try {
          errorText = await response.text() || `HTTP Error ${response.status}`;
        } catch (e) {
          errorText = `HTTP Error ${response.status}`;
        }
        console.error(`[API] Error details: ${errorText}`);
        const errorMessage = `API error: ${response.status} - ${errorText}`;
        setApiError(errorMessage);
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error(`[API] Call failed for ${endpoint}:`, error);
      const errorMessage = error?.message || 'Unknown API error';
      setApiError(errorMessage);
      throw error;
    } finally {
      setApiLoading(false);
    }
  }, [getAuthHeaders]);

  return {
    callApi,
    apiLoading,
    apiError,
    setApiLoading,
    setApiError
  };
};

// Export both as default and named export for backward compatibility
export default useApi;
export { useApi };
