import { useState, useCallback } from 'react';

// Support both Vite and React environment variables for compatibility
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || import.meta.env.REACT_APP_API_BASE_URL || 'https://scale-llm.com';

export const useMCPData = ({ showNotification }) => {
  const [mcpStatus, setMcpStatus] = useState({});
  const [connectedClients, setConnectedClients] = useState([]);
  const [availableServers, setAvailableServers] = useState([]);
  const [connectionHistory, setConnectionHistory] = useState([]);
  const [serverMetrics, setServerMetrics] = useState({});
  const [loading, setLoading] = useState(false);

  // Fetch MCP status from proxy-gateway
  const fetchMCPStatus = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/mcp/status`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error('Response is not JSON');
      }

      const data = await response.json();
      setMcpStatus(data);
      setConnectedClients(data.clients || []);
      setAvailableServers(data.servers || []);
      return data;
    } catch (error) {
      console.error('Error fetching MCP status:', error);
      // Only show notification for manual refresh, not auto-refresh
      if (error.message.includes('manual')) {
        showNotification('Failed to fetch MCP status', 'error');
      }
      // Set default state with mock data for development
      setMcpStatus({
        host: null,
        stats: { active_connections: 0, available_servers: 0, total_requests: 0, total_errors: 0 }
      });
      setConnectedClients([]);
      setAvailableServers([
        {
          id: 'ai-optimizer',
          name: 'AI Optimizer',
          url: 'wss://scale-llm.com/ai-optimizer/mcp',
          status: 'unknown',
          enabled: true,
          capabilities: ['tools', 'resources'],
          last_seen: new Date(),
          version: '1.0.0',
          tools_count: 4,
          resources_count: 3
        },
        {
          id: 'planning-service',
          name: 'Planning Service',
          url: 'wss://scale-llm.com/planning-service/mcp',
          status: 'unknown',
          enabled: true,
          capabilities: ['tools', 'prompts', 'resources'],
          last_seen: new Date(),
          version: '1.0.0',
          tools_count: 4,
          resources_count: 3
        },
        {
          id: 'evaluation-service',
          name: 'Evaluation Service',
          url: 'wss://scale-llm.com/evaluation-service/mcp',
          status: 'unknown',
          enabled: true,
          capabilities: ['tools', 'resources'],
          last_seen: new Date(),
          version: '1.0.0',
          tools_count: 4,
          resources_count: 3
        },
        {
          id: 'integration-service',
          name: 'Integration Service',
          url: 'wss://scale-llm.com/integration-service/mcp',
          status: 'unknown',
          enabled: true,
          capabilities: ['tools', 'resources'],
          last_seen: new Date(),
          version: '1.0.0',
          tools_count: 4,
          resources_count: 3
        }
      ]);
      throw error;
    }
  }, [showNotification]);

  // Fetch server metrics from individual MCP servers
  const fetchServerMetrics = useCallback(async () => {
    try {
      // Use the dashboard API endpoint that aggregates metrics from all services
      const response = await fetch(`${API_BASE_URL}/api/mcp/metrics`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        console.warn('MCP metrics response is not JSON, using empty metrics');
        setServerMetrics({});
        return {};
      }

      const metrics = await response.json();
      setServerMetrics(metrics);
      return metrics;
    } catch (error) {
      console.error('Error fetching server metrics:', error);
      // Only show notification for manual refresh, not auto-refresh
      if (error.message.includes('manual')) {
        showNotification('Failed to fetch server metrics', 'error');
      }
      setServerMetrics({});
      throw error;
    }
  }, [showNotification]);

  // Fetch connection history (placeholder - would come from analytics service)
  const fetchConnectionHistory = useCallback(async (clients = []) => {
    try {
      // For now, generate mock data based on current connections
      const history = clients.map((client, index) => ({
        id: `conn-${index}`,
        client_id: client.id,
        user_id: client.user_id,
        connected_at: client.connected_at,
        disconnected_at: null,
        duration: Date.now() - new Date(client.connected_at).getTime(),
        status: 'active'
      }));

      setConnectionHistory(history);
      return history;
    } catch (error) {
      console.error('Error fetching connection history:', error);
      showNotification('Failed to fetch connection history', 'error');
      throw error;
    }
  }, [showNotification]);

  // Main fetch function that gets all MCP data
  const fetchMCPData = useCallback(async () => {
    setLoading(true);
    try {
      // Fetch status first, then metrics and history
      const statusData = await fetchMCPStatus();

      // Only fetch metrics and history if status fetch was successful
      try {
        await fetchServerMetrics();
      } catch (error) {
        console.warn('Failed to fetch server metrics, continuing...', error);
      }

      try {
        // Pass the clients from status data to avoid dependency issues
        await fetchConnectionHistory(statusData?.clients || []);
      } catch (error) {
        console.warn('Failed to fetch connection history, continuing...', error);
      }
    } catch (error) {
      console.error('Error fetching MCP data:', error);
      // Don't show notification here to avoid spam during auto-refresh
    } finally {
      setLoading(false);
    }
  }, [fetchMCPStatus, fetchServerMetrics, fetchConnectionHistory]);

  // Connect to a specific MCP server
  const connectToServer = useCallback(async (serverId, config = {}) => {
    try {
      const response = await fetch(`${API_BASE_URL}/mcp/servers/${serverId}/connect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      showNotification(`Connected to ${serverId} successfully`, 'success');
      
      // Refresh data
      await fetchMCPData();
      
      return result;
    } catch (error) {
      console.error(`Error connecting to server ${serverId}:`, error);
      showNotification(`Failed to connect to ${serverId}`, 'error');
      throw error;
    }
  }, [showNotification, fetchMCPData]);

  // Disconnect from a specific MCP server
  const disconnectFromServer = useCallback(async (serverId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/mcp/servers/${serverId}/disconnect`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      showNotification(`Disconnected from ${serverId} successfully`, 'success');
      
      // Refresh data
      await fetchMCPData();
      
      return result;
    } catch (error) {
      console.error(`Error disconnecting from server ${serverId}:`, error);
      showNotification(`Failed to disconnect from ${serverId}`, 'error');
      throw error;
    }
  }, [showNotification, fetchMCPData]);

  // Restart a specific MCP server
  const restartServer = useCallback(async (serverId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/mcp/servers/${serverId}/restart`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      showNotification(`Server ${serverId} restarted successfully`, 'success');
      
      // Refresh data after a short delay
      setTimeout(() => fetchMCPData(), 2000);
      
      return result;
    } catch (error) {
      console.error(`Error restarting server ${serverId}:`, error);
      showNotification(`Failed to restart ${serverId}`, 'error');
      throw error;
    }
  }, [showNotification, fetchMCPData]);

  // Update server configuration
  const updateServerConfig = useCallback(async (serverId, config) => {
    try {
      const response = await fetch(`${API_BASE_URL}/mcp/servers/${serverId}/config`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      showNotification(`Server ${serverId} configuration updated`, 'success');
      
      // Refresh data
      await fetchMCPData();
      
      return result;
    } catch (error) {
      console.error(`Error updating server ${serverId} config:`, error);
      showNotification(`Failed to update ${serverId} configuration`, 'error');
      throw error;
    }
  }, [showNotification, fetchMCPData]);

  // Disconnect a specific client
  const disconnectClient = useCallback(async (clientId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/mcp/clients/${clientId}/disconnect`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      showNotification(`Client ${clientId} disconnected successfully`, 'success');
      
      // Refresh data
      await fetchMCPData();
      
      return result;
    } catch (error) {
      console.error(`Error disconnecting client ${clientId}:`, error);
      showNotification(`Failed to disconnect client ${clientId}`, 'error');
      throw error;
    }
  }, [showNotification, fetchMCPData]);

  // Test MCP server connection
  const testServerConnection = useCallback(async (serverId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/mcp/servers/${serverId}/test`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      showNotification(`Server ${serverId} connection test successful`, 'success');
      
      return result;
    } catch (error) {
      console.error(`Error testing server ${serverId} connection:`, error);
      showNotification(`Server ${serverId} connection test failed`, 'error');
      throw error;
    }
  }, [showNotification]);

  return {
    // State
    mcpStatus,
    connectedClients,
    availableServers,
    connectionHistory,
    serverMetrics,
    loading,

    // Actions
    fetchMCPData,
    connectToServer,
    disconnectFromServer,
    restartServer,
    updateServerConfig,
    disconnectClient,
    testServerConnection,
  };
};
