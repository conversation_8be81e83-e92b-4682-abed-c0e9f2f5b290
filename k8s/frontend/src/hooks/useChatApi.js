import { useState, useCallback, useRef, useEffect } from 'react';
import { API_BASE_URL } from '../utils/constants';
import { useAuth } from '../contexts/AuthContext';
import {
  getStorageItem,
  setStorageItem,
  removeStorageItem,
  STORAGE_KEYS,
  cleanupOldConversations,
  isStorageAvailable
} from '../utils/localStorage';

// Helper functions for conversation storage
const saveConversationsToStorage = (conversations) => {
  const conversationsArray = Array.from(conversations.entries());
  const success = setStorageItem(STORAGE_KEYS.CONVERSATIONS, conversationsArray);

  // If save failed due to storage issues, try cleanup and retry
  if (!success && conversations.size > 10) {
    cleanupOldConversations();
  }
};

const loadConversationsFromStorage = () => {
  const conversationsArray = getStorageItem(STORAGE_KEYS.CONVERSATIONS, []);
  return new Map(conversationsArray);
};

const saveActiveConversationToStorage = (conversationId) => {
  if (conversationId) {
    setStorageItem(STORAGE_KEYS.ACTIVE_CONVERSATION, conversationId);
  } else {
    removeStorageItem(STORAGE_KEYS.ACTIVE_CONVERSATION);
  }
};

const loadActiveConversationFromStorage = () => {
  return getStorageItem(STORAGE_KEYS.ACTIVE_CONVERSATION);
};

const useChatApi = ({ showNotification }) => {
  const { getAuthHeaders } = useAuth();
  const [conversations, setConversations] = useState(new Map());
  const [activeConversationId, setActiveConversationId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const abortControllerRef = useRef(null);

  // Load conversations from localStorage on mount
  useEffect(() => {
    // Check if localStorage is available
    if (!isStorageAvailable()) {
      showNotification?.('Browser storage is not available. Conversation history will not be saved.', 'warning');
      return;
    }

    const storedConversations = loadConversationsFromStorage();
    const storedActiveConversationId = loadActiveConversationFromStorage();

    setConversations(storedConversations);

    // Only set active conversation if it exists in stored conversations
    if (storedActiveConversationId && storedConversations.has(storedActiveConversationId)) {
      setActiveConversationId(storedActiveConversationId);
    }

    // Show notification if conversations were restored
    if (storedConversations.size > 0) {
      showNotification?.(`Restored ${storedConversations.size} conversation${storedConversations.size === 1 ? '' : 's'} from previous session`, 'success');
    }

    // Clean up old conversations on startup if needed
    if (storedConversations.size > 50) {
      cleanupOldConversations();
    }
  }, [showNotification]);

  // Get active conversation
  const activeConversation = activeConversationId ? conversations.get(activeConversationId) : null;

  // Create a new conversation
  const createConversation = useCallback((title = 'New Chat') => {
    const conversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newConversation = {
      id: conversationId,
      title,
      messages: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      model: 'gpt-3.5-turbo', // Default model
    };

    setConversations(prev => {
      const newConversations = new Map(prev).set(conversationId, newConversation);
      saveConversationsToStorage(newConversations);
      return newConversations;
    });
    setActiveConversationId(conversationId);
    saveActiveConversationToStorage(conversationId);
    return { conversationId, conversation: newConversation };
  }, []);

  // Switch to a different conversation
  const switchConversation = useCallback((conversationId) => {
    if (conversations.has(conversationId)) {
      setActiveConversationId(conversationId);
      saveActiveConversationToStorage(conversationId);
    }
  }, [conversations]);

  // Delete a conversation
  const deleteConversation = useCallback((conversationId) => {
    setConversations(prev => {
      const newConversations = new Map(prev);
      newConversations.delete(conversationId);
      saveConversationsToStorage(newConversations);
      return newConversations;
    });

    if (activeConversationId === conversationId) {
      const remainingConversations = Array.from(conversations.keys()).filter(id => id !== conversationId);
      const newActiveId = remainingConversations.length > 0 ? remainingConversations[0] : null;
      setActiveConversationId(newActiveId);
      saveActiveConversationToStorage(newActiveId);
    }
  }, [activeConversationId, conversations]);

  // Update conversation title
  const updateConversationTitle = useCallback((conversationId, title) => {
    setConversations(prev => {
      const newConversations = new Map(prev);
      const conversation = newConversations.get(conversationId);
      if (conversation) {
        newConversations.set(conversationId, {
          ...conversation,
          title,
          updatedAt: new Date().toISOString(),
        });
        saveConversationsToStorage(newConversations);
      }
      return newConversations;
    });
  }, []);

  // Add a message to a conversation
  const addMessage = useCallback((conversationId, message) => {
    setConversations(prev => {
      const newConversations = new Map(prev);
      const conversation = newConversations.get(conversationId);
      if (conversation) {
        const updatedConversation = {
          ...conversation,
          messages: [...conversation.messages, message],
          updatedAt: new Date().toISOString(),
        };
        newConversations.set(conversationId, updatedConversation);
        saveConversationsToStorage(newConversations);
      }
      return newConversations;
    });
  }, []);

  // Update the last message in a conversation (for streaming)
  const updateLastMessage = useCallback((conversationId, updates) => {
    setConversations(prev => {
      const newConversations = new Map(prev);
      const conversation = newConversations.get(conversationId);
      if (conversation && conversation.messages.length > 0) {
        const messages = [...conversation.messages];
        const lastMessage = messages[messages.length - 1];

        // Handle function updates
        const newMessage = typeof updates === 'function'
          ? updates(lastMessage)
          : { ...lastMessage, ...updates };

        messages[messages.length - 1] = newMessage;

        const updatedConversation = {
          ...conversation,
          messages,
          updatedAt: new Date().toISOString(),
        };

        newConversations.set(conversationId, updatedConversation);
        saveConversationsToStorage(newConversations);
      }
      return newConversations;
    });
  }, []);

  // Send a chat message
  const sendMessage = useCallback(async (message, options = {}) => {
    if (!message.trim()) return;

    let conversationId = activeConversationId;
    let conversation;

    // Create new conversation if none exists
    if (!conversationId) {
      const result = createConversation();
      conversationId = result.conversationId;
      conversation = result.conversation;
    } else {
      conversation = conversations.get(conversationId);
      if (!conversation) return;
    }

    // Add user message
    const userMessage = {
      id: `msg_${Date.now()}_user`,
      role: 'user',
      content: message,
      timestamp: new Date().toISOString(),
    };

    addMessage(conversationId, userMessage);

    // Prepare messages for API
    const messages = [...conversation.messages, userMessage].map(msg => ({
      role: msg.role,
      content: msg.content,
    }));

    setIsLoading(true);
    setIsStreaming(options.stream || false);

    // Create abort controller for this request
    abortControllerRef.current = new AbortController();

    try {
      const requestBody = {
        model: options.model || conversation.model || 'gpt-3.5-turbo',
        messages,
        conversation_id: conversationId,
        stream: options.stream || false,
        temperature: options.temperature || 0.7,
        max_tokens: options.max_tokens || 2048,
        ...options.additionalParams,
      };

      const authHeaders = getAuthHeaders();
      console.log('[useChatApi] Auth headers for chat completions:', authHeaders);

      const response = await fetch(`${API_BASE_URL}/api/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders,
          ...(options.preferredLlmId && { 'X-Preferred-LLM-ID': options.preferredLlmId }),
        },
        body: JSON.stringify(requestBody),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (options.stream) {
        // Handle streaming response
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        // Add initial assistant message
        const assistantMessage = {
          id: `msg_${Date.now()}_assistant`,
          role: 'assistant',
          content: '',
          timestamp: new Date().toISOString(),
          isStreaming: true,
        };

        addMessage(conversationId, assistantMessage);

        let buffer = '';
        
        try {
          while (true) {
            const { done, value } = await reader.read();
            
            if (done) break;
            
            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') {
                  updateLastMessage(conversationId, { isStreaming: false });
                  break;
                }

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content || '';
                  if (content) {
                    updateLastMessage(conversationId, (prev) => ({
                      ...prev,
                      content: (prev.content || '') + content,
                    }));
                  }
                } catch (e) {
                  console.warn('Failed to parse streaming data:', data);
                }
              }
            }
          }
        } finally {
          reader.releaseLock();
        }
      } else {
        // Handle non-streaming response
        const data = await response.json();
        
        const assistantMessage = {
          id: `msg_${Date.now()}_assistant`,
          role: 'assistant',
          content: data.choices?.[0]?.message?.content || 'No response received',
          timestamp: new Date().toISOString(),
          model: data.model,
          usage: data.usage,
        };

        addMessage(conversationId, assistantMessage);

        // Auto-generate conversation title if this is the first exchange
        if (conversation.messages.length === 0 && conversation.title === 'New Chat') {
          const title = message.length > 50 ? message.substring(0, 47) + '...' : message;
          updateConversationTitle(conversationId, title);
        }
      }

    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('Request was aborted');
        return;
      }

      console.error('Error sending message:', error);
      showNotification?.('Failed to send message: ' + error.message, 'error');

      // Add error message
      const errorMessage = {
        id: `msg_${Date.now()}_error`,
        role: 'assistant',
        content: 'Sorry, I encountered an error while processing your request. Please try again.',
        timestamp: new Date().toISOString(),
        isError: true,
      };

      addMessage(conversationId, errorMessage);
    } finally {
      setIsLoading(false);
      setIsStreaming(false);
      abortControllerRef.current = null;
    }
  }, [activeConversationId, conversations, createConversation, addMessage, updateLastMessage, updateConversationTitle, showNotification, getAuthHeaders]);

  // Cancel ongoing request
  const cancelRequest = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setIsLoading(false);
      setIsStreaming(false);
    }
  }, []);

  // Clear all conversations
  const clearAllConversations = useCallback(() => {
    const emptyConversations = new Map();
    setConversations(emptyConversations);
    setActiveConversationId(null);
    saveConversationsToStorage(emptyConversations);
    saveActiveConversationToStorage(null);
  }, []);

  return {
    // State
    conversations: Array.from(conversations.values()).sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt)),
    activeConversation,
    activeConversationId,
    isLoading,
    isStreaming,

    // Actions
    createConversation,
    switchConversation,
    deleteConversation,
    updateConversationTitle,
    sendMessage,
    cancelRequest,
    clearAllConversations,
  };
};

export default useChatApi;
