import { useState, useCallback, useRef } from 'react';
import useApi from './useApi';
import { GOVERNANCE_API_PREFIX } from '../utils/constants';

const useGovernanceData = ({ showNotification }) => {
  const { callApi, apiLoading: loading, apiError: error } = useApi();
  const [governancePolicies, setGovernancePolicies] = useState([]);
  const [governanceAlerts, setGovernanceAlerts] = useState([]);
  const [complianceStatus, setComplianceStatus] = useState([]);
  const [auditLogs, setAuditLogs] = useState([]);
  const [governanceMetrics, setGovernanceMetrics] = useState({});
  const [realTimeAlerts, setRealTimeAlerts] = useState([]);
  
  // WebSocket connection for real-time updates
  const wsRef = useRef(null);

  // Fetch all governance data
  const fetchGovernanceData = useCallback(async (timeframe = '7d') => {
    try {
      // Fetch governance policies
      const policiesResponse = await callApi(`${GOVERNANCE_API_PREFIX}/policies`);
      if (policiesResponse) {
        setGovernancePolicies(policiesResponse);
      }

      // Fetch governance alerts
      const alertsResponse = await callApi(`${GOVERNANCE_API_PREFIX}/alerts?timeframe=${timeframe}`);
      if (alertsResponse) {
        setGovernanceAlerts(alertsResponse);
      }

      // Fetch compliance status
      const complianceResponse = await callApi(`${GOVERNANCE_API_PREFIX}/compliance-status?timeframe=${timeframe}`);
      if (complianceResponse) {
        setComplianceStatus(complianceResponse);
      }

      // Fetch audit logs
      const auditResponse = await callApi(`${GOVERNANCE_API_PREFIX}/audit-logs?timeframe=${timeframe}`);
      if (auditResponse) {
        setAuditLogs(auditResponse);
      }

      // Fetch governance metrics
      const metricsResponse = await callApi(`${GOVERNANCE_API_PREFIX}/metrics?timeframe=${timeframe}`);
      if (metricsResponse) {
        setGovernanceMetrics(metricsResponse);
      }

    } catch (err) {
      console.error('Failed to fetch governance data:', err);
      showNotification?.('Failed to fetch governance data', 'error');
    }
  }, [callApi, showNotification]);

  // Policy management functions
  const createPolicy = useCallback(async (policyData) => {
    try {
      const response = await callApi(`${GOVERNANCE_API_PREFIX}/policies`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(policyData)
      });
      
      if (response) {
        setGovernancePolicies(prev => [...prev, response]);
        showNotification?.('Policy created successfully', 'success');
        return response;
      }
    } catch (err) {
      console.error('Failed to create policy:', err);
      showNotification?.('Failed to create policy', 'error');
      throw err;
    }
  }, [callApi, showNotification]);

  const updatePolicy = useCallback(async (policyId, policyData) => {
    try {
      const response = await callApi(`${GOVERNANCE_API_PREFIX}/policies/${policyId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(policyData)
      });
      
      if (response) {
        setGovernancePolicies(prev => 
          prev.map(policy => policy.id === policyId ? response : policy)
        );
        showNotification?.('Policy updated successfully', 'success');
        return response;
      }
    } catch (err) {
      console.error('Failed to update policy:', err);
      showNotification?.('Failed to update policy', 'error');
      throw err;
    }
  }, [callApi, showNotification]);

  const deletePolicy = useCallback(async (policyId) => {
    try {
      await callApi(`${GOVERNANCE_API_PREFIX}/policies/${policyId}`, {
        method: 'DELETE'
      });
      
      setGovernancePolicies(prev => prev.filter(policy => policy.id !== policyId));
      showNotification?.('Policy deleted successfully', 'success');
    } catch (err) {
      console.error('Failed to delete policy:', err);
      showNotification?.('Failed to delete policy', 'error');
      throw err;
    }
  }, [callApi, showNotification]);

  // Alert management functions
  const acknowledgeAlert = useCallback(async (alertId) => {
    try {
      const response = await callApi(`${GOVERNANCE_API_PREFIX}/alerts/${alertId}/acknowledge`, {
        method: 'POST'
      });
      
      if (response) {
        setGovernanceAlerts(prev => 
          prev.map(alert => alert.id === alertId ? { ...alert, status: 'acknowledged' } : alert)
        );
        showNotification?.('Alert acknowledged', 'success');
      }
    } catch (err) {
      console.error('Failed to acknowledge alert:', err);
      showNotification?.('Failed to acknowledge alert', 'error');
    }
  }, [callApi, showNotification]);

  const resolveAlert = useCallback(async (alertId, resolution) => {
    try {
      const response = await callApi(`${GOVERNANCE_API_PREFIX}/alerts/${alertId}/resolve`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ resolution })
      });
      
      if (response) {
        setGovernanceAlerts(prev => 
          prev.map(alert => alert.id === alertId ? { ...alert, status: 'resolved' } : alert)
        );
        showNotification?.('Alert resolved', 'success');
      }
    } catch (err) {
      console.error('Failed to resolve alert:', err);
      showNotification?.('Failed to resolve alert', 'error');
    }
  }, [callApi, showNotification]);

  const dismissAlert = useCallback(async (alertId, reason) => {
    try {
      const response = await callApi(`${GOVERNANCE_API_PREFIX}/alerts/${alertId}/dismiss`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reason })
      });
      
      if (response) {
        setGovernanceAlerts(prev => 
          prev.map(alert => alert.id === alertId ? { ...alert, status: 'dismissed' } : alert)
        );
        showNotification?.('Alert dismissed', 'success');
      }
    } catch (err) {
      console.error('Failed to dismiss alert:', err);
      showNotification?.('Failed to dismiss alert', 'error');
    }
  }, [callApi, showNotification]);

  // Compliance functions
  const runComplianceCheck = useCallback(async (modelId, framework) => {
    try {
      const response = await callApi(`${GOVERNANCE_API_PREFIX}/compliance/check`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ model_id: modelId, framework })
      });
      
      if (response) {
        showNotification?.('Compliance check initiated', 'success');
        // Refresh compliance status after a delay
        setTimeout(() => fetchGovernanceData(), 2000);
        return response;
      }
    } catch (err) {
      console.error('Failed to run compliance check:', err);
      showNotification?.('Failed to run compliance check', 'error');
    }
  }, [callApi, showNotification, fetchGovernanceData]);

  // Export functions
  const exportAuditReport = useCallback(async (timeframe, format = 'json') => {
    try {
      const response = await callApi(`${GOVERNANCE_API_PREFIX}/audit/export?timeframe=${timeframe}&format=${format}`, {
        method: 'GET'
      });
      
      if (response) {
        // Create download link
        const blob = new Blob([JSON.stringify(response, null, 2)], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `audit-report-${timeframe}-${new Date().toISOString().split('T')[0]}.${format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
        showNotification?.('Audit report exported successfully', 'success');
      }
    } catch (err) {
      console.error('Failed to export audit report:', err);
      showNotification?.('Failed to export audit report', 'error');
    }
  }, [callApi, showNotification]);

  // Real-time WebSocket connection (disabled until WebSocket server is implemented)
  const connectWebSocket = useCallback(() => {
    // WebSocket connection disabled to prevent connection errors
    // TODO: Implement governance WebSocket server endpoint at /ws/governance
    console.log('Governance WebSocket connection disabled - using polling instead');
    return;

    /* Commented out until WebSocket server is implemented
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      // Use wss:// for HTTPS sites, ws:// for HTTP sites
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/ws/governance`;
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('Governance WebSocket connected');
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          switch (data.type) {
            case 'alert':
              setRealTimeAlerts(prev => [data.payload, ...prev.slice(0, 9)]);
              setGovernanceAlerts(prev => [data.payload, ...prev]);
              showNotification?.(
                `New governance alert: ${data.payload.title}`,
                data.payload.severity === 'critical' ? 'error' : 'warning'
              );
              break;
            case 'policy_update':
              setGovernancePolicies(prev =>
                prev.map(policy => policy.id === data.payload.id ? data.payload : policy)
              );
              break;
            case 'compliance_update':
              setComplianceStatus(prev =>
                prev.map(status => status.id === data.payload.id ? data.payload : status)
              );
              break;
            default:
              console.log('Unknown WebSocket message type:', data.type);
          }
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };

      wsRef.current.onclose = () => {
        console.log('Governance WebSocket disconnected');
        // Attempt to reconnect after 5 seconds
        setTimeout(connectWebSocket, 5000);
      };

      wsRef.current.onerror = (error) => {
        console.error('Governance WebSocket error:', error);
      };
    } catch (err) {
      console.error('Failed to connect to governance WebSocket:', err);
    }
    */
  }, [showNotification]);

  const disconnectWebSocket = useCallback(() => {
    // WebSocket connection disabled - no cleanup needed
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
  }, []);

  return {
    // Data
    governancePolicies,
    governanceAlerts,
    complianceStatus,
    auditLogs,
    governanceMetrics,
    realTimeAlerts,
    loading,
    error,
    
    // Functions
    fetchGovernanceData,
    createPolicy,
    updatePolicy,
    deletePolicy,
    acknowledgeAlert,
    resolveAlert,
    dismissAlert,
    runComplianceCheck,
    exportAuditReport,
    connectWebSocket,
    disconnectWebSocket
  };
};

export default useGovernanceData;
