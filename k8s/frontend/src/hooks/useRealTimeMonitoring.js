import { useState, useEffect, useCallback, useRef } from 'react';
import { GoalStatus } from '../types/planning';

/**
 * Custom hook for real-time monitoring of goal execution
 * Provides polling-based updates for active goals
 */
const useRealTimeMonitoring = (planningHook, showNotification) => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [monitoringGoals, setMonitoringGoals] = useState(new Set());
  const [lastUpdates, setLastUpdates] = useState(new Map());
  const intervalRef = useRef(null);
  const notificationTimeouts = useRef(new Map());

  const {
    goals,
    getGoal,
    getExecutionStatus,
    getTasks,
    activeGoal
  } = planningHook;

  // Check if a goal should be monitored
  const shouldMonitorGoal = useCallback((goal) => {
    return goal && [
      GoalStatus.PLANNING,
      GoalStatus.EXECUTING
    ].includes(goal.status);
  }, []);

  // Add goal to monitoring
  const startMonitoring = useCallback((goalId) => {
    setMonitoringGoals(prev => new Set([...prev, goalId]));
    console.log(`[RealTimeMonitoring] Started monitoring goal: ${goalId}`);
  }, []);

  // Remove goal from monitoring
  const stopMonitoring = useCallback((goalId) => {
    setMonitoringGoals(prev => {
      const newSet = new Set(prev);
      newSet.delete(goalId);
      return newSet;
    });
    
    // Clear any pending notifications for this goal
    if (notificationTimeouts.current.has(goalId)) {
      clearTimeout(notificationTimeouts.current.get(goalId));
      notificationTimeouts.current.delete(goalId);
    }
    
    console.log(`[RealTimeMonitoring] Stopped monitoring goal: ${goalId}`);
  }, []);

  // Update monitoring based on goals list
  const updateMonitoringList = useCallback(() => {
    const activeGoalIds = goals
      .filter(shouldMonitorGoal)
      .map(goal => goal.id);

    // Add new active goals to monitoring
    activeGoalIds.forEach(goalId => {
      if (!monitoringGoals.has(goalId)) {
        startMonitoring(goalId);
      }
    });

    // Remove completed/failed goals from monitoring
    monitoringGoals.forEach(goalId => {
      const goal = goals.find(g => g.id === goalId);
      if (!goal || !shouldMonitorGoal(goal)) {
        stopMonitoring(goalId);
      }
    });
  }, [goals, monitoringGoals, shouldMonitorGoal, startMonitoring, stopMonitoring]);

  // Fetch updates for a specific goal
  const fetchGoalUpdates = useCallback(async (goalId) => {
    try {
      const [goalData, executionStatus, tasks] = await Promise.all([
        getGoal(goalId).catch(() => null),
        getExecutionStatus(goalId).catch(() => null),
        getTasks(goalId).catch(() => [])
      ]);

      const lastUpdate = lastUpdates.get(goalId);
      const currentTime = Date.now();

      // Check for status changes
      if (goalData && lastUpdate) {
        if (goalData.status !== lastUpdate.status) {
          const statusMessage = getStatusChangeMessage(goalData.status, lastUpdate.status);
          if (statusMessage && showNotification) {
            // Debounce notifications to avoid spam
            if (notificationTimeouts.current.has(goalId)) {
              clearTimeout(notificationTimeouts.current.get(goalId));
            }
            
            const timeout = setTimeout(() => {
              showNotification(statusMessage, getNotificationType(goalData.status));
              notificationTimeouts.current.delete(goalId);
            }, 1000);
            
            notificationTimeouts.current.set(goalId, timeout);
          }
        }

        // Check for task completion
        if (tasks && lastUpdate.tasks) {
          const newCompletedTasks = tasks.filter(task => 
            task.status === 'completed' && 
            !lastUpdate.tasks.some(oldTask => 
              oldTask.id === task.id && oldTask.status === 'completed'
            )
          );

          newCompletedTasks.forEach(task => {
            if (showNotification) {
              showNotification(`Task completed: ${task.name}`, 'success');
            }
          });
        }
      }

      // Update last known state
      setLastUpdates(prev => new Map(prev.set(goalId, {
        status: goalData?.status,
        tasks: tasks || [],
        executionStatus,
        timestamp: currentTime
      })));

      return { goalData, executionStatus, tasks };
    } catch (error) {
      console.error(`[RealTimeMonitoring] Error fetching updates for goal ${goalId}:`, error);
      return null;
    }
  }, [getGoal, getExecutionStatus, getTasks, lastUpdates, showNotification]);

  // Get status change message
  const getStatusChangeMessage = (newStatus, oldStatus) => {
    switch (newStatus) {
      case GoalStatus.EXECUTING:
        return 'Goal execution started';
      case GoalStatus.COMPLETED:
        return 'Goal completed successfully!';
      case GoalStatus.FAILED:
        return 'Goal execution failed';
      case GoalStatus.CANCELLED:
        return 'Goal was cancelled';
      case GoalStatus.READY:
        if (oldStatus === GoalStatus.PLANNING) {
          return 'Plan generated successfully - ready to execute';
        }
        break;
      default:
        return null;
    }
    return null;
  };

  // Get notification type based on status
  const getNotificationType = (status) => {
    switch (status) {
      case GoalStatus.COMPLETED:
        return 'success';
      case GoalStatus.FAILED:
      case GoalStatus.CANCELLED:
        return 'error';
      case GoalStatus.EXECUTING:
      case GoalStatus.READY:
        return 'info';
      default:
        return 'info';
    }
  };

  // Polling function
  const pollUpdates = useCallback(async () => {
    if (monitoringGoals.size === 0) return;

    console.log(`[RealTimeMonitoring] Polling updates for ${monitoringGoals.size} goals`);
    
    const updatePromises = Array.from(monitoringGoals).map(goalId => 
      fetchGoalUpdates(goalId)
    );

    await Promise.allSettled(updatePromises);
  }, [monitoringGoals, fetchGoalUpdates]);

  // Start/stop monitoring
  const startRealTimeMonitoring = useCallback(() => {
    if (isMonitoring) return;

    setIsMonitoring(true);
    console.log('[RealTimeMonitoring] Starting real-time monitoring');

    // Initial update
    updateMonitoringList();

    // Set up polling interval (every 5 seconds)
    intervalRef.current = setInterval(pollUpdates, 5000);
  }, [isMonitoring, updateMonitoringList, pollUpdates]);

  const stopRealTimeMonitoring = useCallback(() => {
    if (!isMonitoring) return;

    setIsMonitoring(false);
    console.log('[RealTimeMonitoring] Stopping real-time monitoring');

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Clear all notification timeouts
    notificationTimeouts.current.forEach(timeout => clearTimeout(timeout));
    notificationTimeouts.current.clear();

    // Clear monitoring goals
    setMonitoringGoals(new Set());
    setLastUpdates(new Map());
  }, [isMonitoring]);

  // Auto-start monitoring when there are active goals
  useEffect(() => {
    const hasActiveGoals = goals.some(shouldMonitorGoal);
    
    if (hasActiveGoals && !isMonitoring) {
      startRealTimeMonitoring();
    } else if (!hasActiveGoals && isMonitoring) {
      stopRealTimeMonitoring();
    } else if (isMonitoring) {
      updateMonitoringList();
    }
  }, [goals, isMonitoring, shouldMonitorGoal, startRealTimeMonitoring, stopRealTimeMonitoring, updateMonitoringList]);

  // Auto-monitor active goal when viewing details
  useEffect(() => {
    if (activeGoal && shouldMonitorGoal(activeGoal)) {
      if (!monitoringGoals.has(activeGoal.id)) {
        startMonitoring(activeGoal.id);
      }
      if (!isMonitoring) {
        startRealTimeMonitoring();
      }
    }
  }, [activeGoal, shouldMonitorGoal, monitoringGoals, isMonitoring, startMonitoring, startRealTimeMonitoring]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      notificationTimeouts.current.forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  // Manual refresh function
  const refreshGoal = useCallback(async (goalId) => {
    if (goalId) {
      return await fetchGoalUpdates(goalId);
    }
  }, [fetchGoalUpdates]);

  // Get monitoring status for a specific goal
  const isGoalBeingMonitored = useCallback((goalId) => {
    return monitoringGoals.has(goalId);
  }, [monitoringGoals]);

  // Get last update time for a goal
  const getLastUpdateTime = useCallback((goalId) => {
    const lastUpdate = lastUpdates.get(goalId);
    return lastUpdate ? new Date(lastUpdate.timestamp) : null;
  }, [lastUpdates]);

  return {
    // State
    isMonitoring,
    monitoringGoals: Array.from(monitoringGoals),
    
    // Actions
    startRealTimeMonitoring,
    stopRealTimeMonitoring,
    startMonitoring,
    stopMonitoring,
    refreshGoal,
    
    // Utilities
    isGoalBeingMonitored,
    getLastUpdateTime,
    shouldMonitorGoal
  };
};

export default useRealTimeMonitoring;
