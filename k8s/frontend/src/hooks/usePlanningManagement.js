import { useState, useCallback, useEffect } from 'react';
import { GOALS_ENDPOINT, TEMPLATES_ENDPOINT, DEFAULT_USER_ID } from '../utils/constants';
import { GoalStatus, TaskStatus } from '../types/planning';

/**
 * Custom hook for managing planning operations
 * Handles goals, plans, tasks, and execution monitoring
 */
const usePlanningManagement = (showNotification) => {
  // State for goals
  const [goals, setGoals] = useState([]);
  const [goalsLoading, setGoalsLoading] = useState(false);
  const [goalsError, setGoalsError] = useState(null);

  // State for active goal details
  const [activeGoal, setActiveGoal] = useState(null);
  const [activePlan, setActivePlan] = useState(null);
  const [activeTasks, setActiveTasks] = useState([]);
  const [executionStatus, setExecutionStatus] = useState(null);

  // State for templates
  const [templates, setTemplates] = useState([]);
  const [templatesLoading, setTemplatesLoading] = useState(false);

  // State for planning metrics
  const [planningMetrics, setPlanningMetrics] = useState(null);
  const [metricsLoading, setMetricsLoading] = useState(false);

  // API call helper
  const callPlanningApi = useCallback(async (endpoint, options = {}) => {
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-User-ID': DEFAULT_USER_ID // TODO: Get from auth context
      },
      credentials: 'same-origin'
    };

    const response = await fetch(endpoint, {
      ...defaultOptions,
      ...options,
      headers: { ...defaultOptions.headers, ...options.headers }
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error: ${response.status} - ${errorText}`);
    }

    if (response.status === 204) {
      return Promise.resolve(null);
    }

    return response.json();
  }, []);

  // Fetch all goals
  const fetchGoals = useCallback(async () => {
    setGoalsLoading(true);
    setGoalsError(null);
    try {
      const data = await callPlanningApi(GOALS_ENDPOINT);
      setGoals(Array.isArray(data) ? data : []);
      console.log('[usePlanningManagement] Fetched goals:', data);
    } catch (error) {
      console.error('[usePlanningManagement] Error fetching goals:', error);
      setGoalsError(error.message);
      if (showNotification) {
        showNotification(`Failed to fetch goals: ${error.message}`, 'error');
      }
      setGoals([]);
    } finally {
      setGoalsLoading(false);
    }
  }, [callPlanningApi, showNotification]);

  // Create a new goal
  const createGoal = useCallback(async (goalRequest) => {
    try {
      const data = await callPlanningApi(GOALS_ENDPOINT, {
        method: 'POST',
        body: JSON.stringify(goalRequest)
      });
      
      console.log('[usePlanningManagement] Created goal:', data);
      if (showNotification) {
        showNotification('Goal created successfully!', 'success');
      }
      
      // Refresh goals list
      await fetchGoals();
      return data;
    } catch (error) {
      console.error('[usePlanningManagement] Error creating goal:', error);
      if (showNotification) {
        showNotification(`Failed to create goal: ${error.message}`, 'error');
      }
      throw error;
    }
  }, [callPlanningApi, showNotification, fetchGoals]);

  // Get goal details
  const getGoal = useCallback(async (goalId) => {
    try {
      const data = await callPlanningApi(`${GOALS_ENDPOINT}/${goalId}`);
      setActiveGoal(data);
      return data;
    } catch (error) {
      console.error('[usePlanningManagement] Error fetching goal:', error);
      if (showNotification) {
        showNotification(`Failed to fetch goal: ${error.message}`, 'error');
      }
      throw error;
    }
  }, [callPlanningApi, showNotification]);

  // Update goal
  const updateGoal = useCallback(async (goalId, updates) => {
    try {
      const data = await callPlanningApi(`${GOALS_ENDPOINT}/${goalId}`, {
        method: 'PUT',
        body: JSON.stringify(updates)
      });
      
      if (showNotification) {
        showNotification('Goal updated successfully!', 'success');
      }
      
      // Refresh goals list and active goal if it's the same
      await fetchGoals();
      if (activeGoal && activeGoal.id === goalId) {
        setActiveGoal(data);
      }
      return data;
    } catch (error) {
      console.error('[usePlanningManagement] Error updating goal:', error);
      if (showNotification) {
        showNotification(`Failed to update goal: ${error.message}`, 'error');
      }
      throw error;
    }
  }, [callPlanningApi, showNotification, fetchGoals, activeGoal]);

  // Delete/Cancel goal
  const deleteGoal = useCallback(async (goalId) => {
    try {
      await callPlanningApi(`${GOALS_ENDPOINT}/${goalId}`, {
        method: 'DELETE'
      });
      
      if (showNotification) {
        showNotification('Goal cancelled successfully!', 'success');
      }
      
      // Refresh goals list and clear active goal if it's the same
      await fetchGoals();
      if (activeGoal && activeGoal.id === goalId) {
        setActiveGoal(null);
        setActivePlan(null);
        setActiveTasks([]);
        setExecutionStatus(null);
      }
    } catch (error) {
      console.error('[usePlanningManagement] Error deleting goal:', error);
      if (showNotification) {
        showNotification(`Failed to cancel goal: ${error.message}`, 'error');
      }
      throw error;
    }
  }, [callPlanningApi, showNotification, fetchGoals, activeGoal]);

  // Generate plan for goal
  const generatePlan = useCallback(async (goalId) => {
    try {
      const data = await callPlanningApi(`${GOALS_ENDPOINT}/${goalId}/plan`, {
        method: 'POST'
      });
      
      setActivePlan(data);
      if (showNotification) {
        showNotification('Plan generated successfully!', 'success');
      }
      
      // Refresh goal to get updated status
      await getGoal(goalId);
      return data;
    } catch (error) {
      console.error('[usePlanningManagement] Error generating plan:', error);
      if (showNotification) {
        showNotification(`Failed to generate plan: ${error.message}`, 'error');
      }
      throw error;
    }
  }, [callPlanningApi, showNotification, getGoal]);

  // Get plan for goal
  const getPlan = useCallback(async (goalId) => {
    try {
      const data = await callPlanningApi(`${GOALS_ENDPOINT}/${goalId}/plan`);
      setActivePlan(data);
      return data;
    } catch (error) {
      console.error('[usePlanningManagement] Error fetching plan:', error);
      // Don't show notification for this as it might be called frequently
      throw error;
    }
  }, [callPlanningApi]);

  // Execute goal
  const executeGoal = useCallback(async (goalId) => {
    try {
      const data = await callPlanningApi(`${GOALS_ENDPOINT}/${goalId}/execute`, {
        method: 'POST'
      });
      
      if (showNotification) {
        showNotification('Goal execution started!', 'success');
      }
      
      // Refresh goal and start monitoring
      await getGoal(goalId);
      return data;
    } catch (error) {
      console.error('[usePlanningManagement] Error executing goal:', error);
      if (showNotification) {
        showNotification(`Failed to start execution: ${error.message}`, 'error');
      }
      throw error;
    }
  }, [callPlanningApi, showNotification, getGoal]);

  // Get execution status
  const getExecutionStatus = useCallback(async (goalId) => {
    try {
      const data = await callPlanningApi(`${GOALS_ENDPOINT}/${goalId}/status`);
      setExecutionStatus(data);
      return data;
    } catch (error) {
      console.error('[usePlanningManagement] Error fetching execution status:', error);
      // Don't show notification for this as it might be called frequently
      return null;
    }
  }, [callPlanningApi]);

  // Get tasks for goal
  const getTasks = useCallback(async (goalId) => {
    try {
      const data = await callPlanningApi(`${GOALS_ENDPOINT}/${goalId}/tasks`);
      setActiveTasks(Array.isArray(data) ? data : []);
      return data;
    } catch (error) {
      console.error('[usePlanningManagement] Error fetching tasks:', error);
      setActiveTasks([]);
      return [];
    }
  }, [callPlanningApi]);

  // Get execution results
  const getResults = useCallback(async (goalId) => {
    try {
      const data = await callPlanningApi(`${GOALS_ENDPOINT}/${goalId}/results`);
      return data;
    } catch (error) {
      console.error('[usePlanningManagement] Error fetching results:', error);
      throw error;
    }
  }, [callPlanningApi]);

  // Fetch templates
  const fetchTemplates = useCallback(async () => {
    setTemplatesLoading(true);
    try {
      const data = await callPlanningApi(TEMPLATES_ENDPOINT);
      setTemplates(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('[usePlanningManagement] Error fetching templates:', error);
      setTemplates([]);
    } finally {
      setTemplatesLoading(false);
    }
  }, [callPlanningApi]);

  // Calculate planning metrics from goals
  const calculateMetrics = useCallback(() => {
    if (!goals.length) {
      setPlanningMetrics(null);
      return;
    }

    const totalGoals = goals.length;
    const activeGoals = goals.filter(g => [GoalStatus.PLANNING, GoalStatus.READY, GoalStatus.EXECUTING].includes(g.status)).length;
    const completedGoals = goals.filter(g => g.status === GoalStatus.COMPLETED).length;
    const failedGoals = goals.filter(g => g.status === GoalStatus.FAILED).length;
    const successRate = totalGoals > 0 ? (completedGoals / totalGoals) * 100 : 0;

    const statusBreakdown = goals.reduce((acc, goal) => {
      acc[goal.status] = (acc[goal.status] || 0) + 1;
      return acc;
    }, {});

    setPlanningMetrics({
      total_goals: totalGoals,
      active_goals: activeGoals,
      completed_goals: completedGoals,
      failed_goals: failedGoals,
      success_rate: successRate,
      status_breakdown: statusBreakdown
    });
  }, [goals]);

  // Auto-calculate metrics when goals change
  useEffect(() => {
    calculateMetrics();
  }, [calculateMetrics]);

  // Load initial data
  useEffect(() => {
    fetchGoals();
    fetchTemplates();
  }, [fetchGoals, fetchTemplates]);

  return {
    // Goals
    goals,
    goalsLoading,
    goalsError,
    fetchGoals,
    createGoal,
    getGoal,
    updateGoal,
    deleteGoal,

    // Active goal details
    activeGoal,
    setActiveGoal,
    activePlan,
    activeTasks,
    executionStatus,

    // Plan operations
    generatePlan,
    getPlan,
    executeGoal,
    getExecutionStatus,
    getTasks,
    getResults,

    // Templates
    templates,
    templatesLoading,
    fetchTemplates,

    // Metrics
    planningMetrics,
    metricsLoading,

    // Utility
    callPlanningApi
  };
};

export default usePlanningManagement;
