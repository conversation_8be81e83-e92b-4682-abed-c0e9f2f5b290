import { useState, useCallback } from 'react';
import { useApi } from './useApi';

const usePromptOps = () => {
  const { callApi } = useApi();
  const [prompts, setPrompts] = useState([]);
  const [abTests, setAbTests] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(false);

  // Extract variables from template content - defined early to avoid hoisting issues
  const extractVariables = useCallback((content) => {
    const variables = [];
    const variableMap = new Map();

    // Match {{variable_name}} pattern
    const regex = /\{\{([^}]+)\}\}/g;
    let match;

    while ((match = regex.exec(content)) !== null) {
      const varName = match[1].trim();
      if (!variableMap.has(varName)) {
        variables.push({
          name: varName,
          type: 'string',
          description: `Variable: ${varName}`,
          required: true,
          default_value: ''
        });
        variableMap.set(varName, true);
      }
    }

    return variables;
  }, []);

  // Create a new prompt with enhanced versioning
  const createPrompt = useCallback(async (promptData) => {
    try {
      setLoading(true);

      // Extract variables from content
      const variables = extractVariables(promptData.content);

      // Create prompt via backend API with enhanced fields
      const newPrompt = {
        id: promptData.id || `prompt_${Date.now()}`,
        name: promptData.name,
        description: promptData.description,
        content: promptData.content,
        version: promptData.version || '1.0.0', // Semantic versioning
        status: promptData.status || 'draft',
        tags: promptData.tags || [],
        owner: promptData.owner || 'user',
        model_targets: promptData.model_targets || [],
        variables: variables,
        change_log: promptData.change_log || 'Initial version',
        metadata: {
          created_by: promptData.owner || 'user',
          use_case: promptData.use_case || 'general',
          complexity: promptData.complexity || 'medium',
          ...promptData.metadata
        }
      };

      const response = await callApi('/api/prompts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newPrompt)
      });

      if (response) {
        setPrompts(prev => [...prev, response]);
        return response;
      }

      return newPrompt;
    } catch (error) {
      console.error('Error creating prompt:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [callApi]);

  // Create a new template
  const createTemplate = useCallback(async (templateData) => {
    try {
      setLoading(true);

      const newTemplate = {
        id: templateData.id || `template_${Date.now()}`,
        name: templateData.name,
        description: templateData.description || `Template for ${templateData.use_case || 'general'}`,
        content: templateData.content,
        version: templateData.version || '1.0.0',
        status: templateData.status || 'active',
        tags: templateData.tags || ['template'],
        owner: templateData.owner || 'user',
        model_targets: templateData.model_targets || [],
        variables: templateData.variables || [],
        change_log: templateData.change_log || 'Initial template version',
        metadata: {
          created_by: templateData.owner || 'user',
          use_case: templateData.use_case || 'general',
          complexity: templateData.complexity || 'medium',
          ...templateData.metadata
        }
      };

      // Create template as a prompt in the backend
      const response = await callApi('/api/prompts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newTemplate)
      });

      if (response) {
        setTemplates(prev => [...prev, response]);
        return response;
      }

      return newTemplate;
    } catch (error) {
      console.error('Error creating template:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [callApi]);

  // Start an A/B test
  const startAbTest = useCallback(async (testData) => {
    try {
      setLoading(true);

      // Create A/B test object that matches backend API expectations
      const newTest = {
        id: testData.id || `abtest_${Date.now()}`,
        name: testData.name,
        prompt_a_id: testData.prompt_a_id || 'default-prompt-a',
        prompt_b_id: testData.prompt_b_id || 'default-prompt-b',
        traffic_split: testData.traffic_split || 50,
        status: 'running',
        start_date: new Date().toISOString(),
        end_date: null,
        variant_a_score: 0,
        variant_b_score: 0,
        variant_a_usage: 0,
        variant_b_usage: 0
      };

      // Create A/B test via backend API
      const response = await callApi('/api/prompts/ab-tests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newTest)
      });

      if (response) {
        setAbTests(prev => [...prev, response]);
        // Simulate test execution
        simulateAbTest(response);
        return response;
      }

      setAbTests(prev => [...prev, newTest]);
      simulateAbTest(newTest);
      return newTest;
    } catch (error) {
      console.error('Error starting A/B test:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [callApi]);

  // Execute a prompt against a specific model
  const executePrompt = useCallback(async (promptId, modelId, inputData = {}) => {
    try {
      // This would integrate with your proxy-gateway to execute the prompt
      // For now, we'll simulate the execution
      
      const prompt = prompts.find(p => p.id === promptId);
      if (!prompt) {
        throw new Error('Prompt not found');
      }

      // Simulate API call to proxy-gateway
      const response = await callApi('/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Preferred-LLM-ID': modelId
        },
        body: JSON.stringify({
          model: modelId,
          messages: [
            {
              role: 'user',
              content: interpolatePrompt(prompt.content, inputData)
            }
          ],
          max_tokens: 1000
        })
      });

      return response;
    } catch (error) {
      console.error('Error executing prompt:', error);
      throw error;
    }
  }, [callApi, prompts]);

  // NOTE: extractVariables function moved below to avoid duplication

  // Helper function to interpolate prompt with variables
  const interpolatePrompt = (template, variables) => {
    let result = template;
    Object.entries(variables).forEach(([key, value]) => {
      result = result.replace(new RegExp(`\\{\\{${key}\\}\\}`, 'g'), value);
    });
    return result;
  };

  // Simulate A/B test execution
  const simulateAbTest = async (test) => {
    // This would integrate with your infrastructure to route traffic
    // For now, we'll simulate some test results with progressive updates

    let updateCount = 0;
    const maxUpdates = 5; // Number of simulation updates

    const updateTest = async () => {
      updateCount++;
      const baseUsage = updateCount * 20; // Progressive usage increase

      const updatedData = {
        variant_a_usage: Math.floor(Math.random() * 50) + baseUsage,
        variant_b_usage: Math.floor(Math.random() * 50) + baseUsage,
        variant_a_score: Math.random() * 0.3 + 0.7, // Random score between 0.7-1.0
        variant_b_score: Math.random() * 0.3 + 0.7,
        status: 'running'
      };

      try {
        // Update backend first
        await callApi(`/api/prompts/ab-tests/${test.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            ...test,
            ...updatedData
          })
        });

        // Then update frontend state
        setAbTests(prev => prev.map(t => {
          if (t.id === test.id && t.status === 'running') {
            return { ...t, ...updatedData };
          }
          return t;
        }));
      } catch (error) {
        console.error('Error updating A/B test:', error);
        // Still update frontend even if backend fails
        setAbTests(prev => prev.map(t => {
          if (t.id === test.id && t.status === 'running') {
            return { ...t, ...updatedData };
          }
          return t;
        }));
      }

      // Schedule next update if test is still running and we haven't reached max updates
      if (updateCount < maxUpdates) {
        setTimeout(updateTest, 10000); // Update every 10 seconds
      }
    };

    // Start first update after 3 seconds
    setTimeout(updateTest, 3000);
  };

  // Stop an A/B test
  const stopAbTest = useCallback(async (testId) => {
    try {
      setLoading(true);

      // Call backend API to stop the test
      const response = await callApi(`/api/prompts/ab-tests/${testId}/stop`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response) {
        // Update local state
        setAbTests(prev => prev.map(test =>
          test.id === testId
            ? { ...test, status: 'completed', end_date: new Date().toISOString() }
            : test
        ));
        return response;
      }
    } catch (error) {
      console.error('Error stopping A/B test:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [callApi]);

  // Deploy the winner of an A/B test
  const deployWinner = useCallback(async (testId) => {
    try {
      setLoading(true);

      // Get the test details first
      const test = abTests.find(t => t.id === testId);
      if (!test) {
        throw new Error('Test not found');
      }

      // Determine winner based on scores, with fallback logic
      const aScore = test.variant_a_score || 0;
      const bScore = test.variant_b_score || 0;
      const aUsage = test.variant_a_usage || 0;
      const bUsage = test.variant_b_usage || 0;

      let winner, winnerPromptId, winnerScore;

      if (aScore === 0 && bScore === 0) {
        // If no scores available, use usage as a fallback or default to A
        if (aUsage > 0 || bUsage > 0) {
          winner = aUsage >= bUsage ? 'A' : 'B';
          winnerPromptId = aUsage >= bUsage ? test.prompt_a_id : test.prompt_b_id;
          winnerScore = aUsage >= bUsage ? aUsage : bUsage;
        } else {
          // Default to variant A if no data available
          winner = 'A';
          winnerPromptId = test.prompt_a_id;
          winnerScore = 'default';
        }
      } else {
        // Use scores to determine winner
        winner = aScore > bScore ? 'A' : 'B';
        winnerPromptId = aScore > bScore ? test.prompt_a_id : test.prompt_b_id;
        winnerScore = aScore > bScore ? aScore : bScore;
      }

      // REAL DEPLOYMENT: Deploy the winning prompt to production
      console.log(`Deploying winner: Variant ${winner} (${winnerPromptId}) with score/metric ${winnerScore}`);

      // Step 1: Get the winning prompt details
      const winnerPrompt = await callApi(`/api/prompts/${winnerPromptId}`);
      if (!winnerPrompt) {
        throw new Error(`Could not fetch winning prompt ${winnerPromptId}`);
      }

      // Step 2: Create a production version of the winning prompt
      const productionPrompt = {
        ...winnerPrompt,
        id: `${winnerPrompt.id}-production`,
        name: `${winnerPrompt.name} (Production)`,
        version: `${winnerPrompt.version}-prod`,
        status: 'production',
        tags: [...(winnerPrompt.tags || []), 'production', 'ab-test-winner'],
        change_log: `Deployed as A/B test winner from test ${testId}. Original variant: ${winner}`,
        metadata: {
          ...winnerPrompt.metadata,
          deployed_from_ab_test: testId,
          ab_test_variant: winner,
          deployment_date: new Date().toISOString(),
          previous_production_id: winnerPrompt.metadata?.production_id || null
        }
      };

      // Step 3: Deploy to production via policy-manager
      const deploymentResult = await callApi('/api/prompts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(productionPrompt)
      });

      if (!deploymentResult) {
        throw new Error('Failed to create production prompt');
      }

      // Step 4: Update routing rules to use the winning prompt/model combination
      await updateProductionRouting(winnerPromptId, test.prompt_a_id === winnerPromptId ? 'A' : 'B', test);

      // Step 5: Export to GitOps repository (if integration is enabled)
      try {
        await exportToGitOps(productionPrompt, testId);
      } catch (gitError) {
        console.warn('GitOps export failed (non-critical):', gitError);
      }

      // Update test status to indicate winner has been deployed
      const response = await callApi(`/api/prompts/ab-tests/${testId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...test,
          status: 'deployed',
          winner: winner,
          deployed_prompt_id: winnerPromptId
        })
      });

      if (response) {
        // Update local state
        setAbTests(prev => prev.map(t =>
          t.id === testId
            ? { ...t, status: 'deployed', winner: winner, deployed_prompt_id: winnerPromptId }
            : t
        ));
        return { winner, promptId: winnerPromptId, score: winnerScore };
      }
    } catch (error) {
      console.error('Error deploying winner:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [callApi, abTests]);

  // Fetch prompts from backend
  const fetchPrompts = useCallback(async () => {
    try {
      setLoading(true);
      const response = await callApi('/api/prompts');
      if (response) {
        // Check if this is a standard edition fallback response
        if (response.status === 'unavailable' && response.edition === 'standard') {
          console.log('PromptOps features not available in Standard Edition');
          setPrompts([]);
          setTemplates([]);
        } else {
          setPrompts(response);
          // Filter templates (prompts with 'template' tag)
          const templatePrompts = response.filter(p => p.tags && p.tags.includes('template'));
          setTemplates(templatePrompts);
        }
      }
    } catch (error) {
      console.error('Error fetching prompts:', error);
      // Set empty arrays on error to prevent UI issues
      setPrompts([]);
      setTemplates([]);
    } finally {
      setLoading(false);
    }
  }, [callApi]);

  // Fetch A/B tests from backend
  const fetchAbTests = useCallback(async () => {
    try {
      setLoading(true);
      const response = await callApi('/api/prompts/ab-tests');
      if (response) {
        setAbTests(response);

        // Start simulation for any running tests that don't have scores yet
        response.forEach(test => {
          if (test.status === 'running' &&
              (test.variant_a_score === 0 || test.variant_b_score === 0)) {
            simulateAbTest(test);
          }
        });
      }
    } catch (error) {
      console.error('Error fetching A/B tests:', error);
      // Set empty array on error to prevent UI issues
      setAbTests([]);
    } finally {
      setLoading(false);
    }
  }, [callApi]);

  // Enhanced PromptOps functions
  const getPromptVersions = useCallback(async (promptId) => {
    try {
      const response = await callApi(`/api/prompts/${promptId}/versions`);
      return response || [];
    } catch (error) {
      console.error('Error fetching prompt versions:', error);
      return [];
    }
  }, [callApi]);

  const getPromptDiff = useCallback(async (promptId, fromVersion, toVersion) => {
    try {
      const response = await callApi(`/api/prompts/${promptId}/diff?from=${fromVersion}&to=${toVersion}`);
      return response;
    } catch (error) {
      console.error('Error fetching prompt diff:', error);
      throw error;
    }
  }, [callApi]);

  const rollbackPrompt = useCallback(async (promptId, toVersion, changeLog) => {
    try {
      const response = await callApi(`/api/prompts/${promptId}/rollback`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ to_version: toVersion, change_log: changeLog })
      });

      if (response) {
        // Refresh prompts list
        await fetchPrompts();
      }

      return response;
    } catch (error) {
      console.error('Error rolling back prompt:', error);
      throw error;
    }
  }, [callApi, fetchPrompts]);

  const clonePrompt = useCallback(async (promptId, newId, newName, description) => {
    try {
      const response = await callApi(`/api/prompts/${promptId}/clone`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          new_id: newId,
          new_name: newName,
          description: description
        })
      });

      if (response) {
        setPrompts(prev => [...prev, response]);
      }

      return response;
    } catch (error) {
      console.error('Error cloning prompt:', error);
      throw error;
    }
  }, [callApi]);

  const extractPromptVariables = useCallback(async (content) => {
    try {
      const response = await callApi('/api/prompts/variables/extract', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content })
      });
      return response?.variables || [];
    } catch (error) {
      console.error('Error extracting variables:', error);
      return [];
    }
  }, [callApi]);

  const testPrompt = useCallback(async (promptId, testData) => {
    try {
      const response = await callApi(`/api/prompts/${promptId}/test`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testData)
      });
      return response;
    } catch (error) {
      console.error('Error testing prompt:', error);
      throw error;
    }
  }, [callApi]);

  const executePromptWithModel = useCallback(async (promptId, modelId, variables = {}) => {
    try {
      const response = await callApi(`/api/prompts/${promptId}/execute`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model_id: modelId,
          variables: variables
        })
      });
      return response;
    } catch (error) {
      console.error('Error executing prompt:', error);
      throw error;
    }
  }, [callApi]);

  // Helper function to extract variables from content locally
  // NOTE: extractVariables function moved to top of hook to avoid hoisting issues

  // Update an existing prompt
  const updatePrompt = useCallback(async (promptId, promptData) => {
    try {
      setLoading(true);

      // Find the existing prompt to get current version
      const existingPrompt = prompts.find(p => p.id === promptId);

      // Ensure the data has the correct field names and required fields for the backend
      const updateData = {
        id: promptData.id || promptId,
        name: promptData.name,
        description: promptData.description,
        content: promptData.content,
        version: promptData.version || existingPrompt?.version || '1.0.0',
        status: promptData.status || existingPrompt?.status || 'draft',
        tags: promptData.tags || existingPrompt?.tags || [],
        owner: promptData.owner || existingPrompt?.owner || 'user',
        model_targets: promptData.model_targets || existingPrompt?.model_targets || [],
        variables: promptData.variables || existingPrompt?.variables || [],
        change_log: promptData.change_log || 'Updated prompt',
        metadata: {
          ...existingPrompt?.metadata,
          ...promptData.metadata,
          updated_by: promptData.owner || 'user',
          use_case: promptData.use_case || existingPrompt?.metadata?.use_case || 'general'
        }
      };

      const response = await callApi(`/api/prompts/${promptId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (response) {
        setPrompts(prev => prev.map(p => p.id === promptId ? response : p));
        return response;
      }
    } catch (error) {
      console.error('Error updating prompt:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [callApi, prompts]);

  return {
    prompts,
    abTests,
    templates,
    loading,
    createPrompt,
    createTemplate,
    startAbTest,
    stopAbTest,
    deployWinner,
    executePrompt,
    fetchPrompts,
    fetchAbTests,
    setPrompts,
    setAbTests,
    setTemplates,
    // Enhanced PromptOps functions
    updatePrompt,
    getPromptVersions,
    getPromptDiff,
    rollbackPrompt,
    clonePrompt,
    extractPromptVariables,
    testPrompt,
    executePromptWithModel
  };

  // Helper function to update production routing rules
  const updateProductionRouting = async (winnerPromptId, variant, test) => {
    try {
      // Create a new routing policy that prioritizes the winning prompt/model combination
      const routingPolicy = {
        id: `ab-test-winner-${test.id}`,
        name: `A/B Test Winner - ${test.name}`,
        description: `Auto-generated routing policy for A/B test winner. Variant ${variant} won with prompt ${winnerPromptId}`,
        type: 'ROUTE',
        priority: 100, // High priority to override other policies
        criteria: {
          prompt_pattern: winnerPromptId,
          use_case: test.metadata?.use_case || 'general'
        },
        action: {
          backend_id: getModelForPrompt(winnerPromptId), // Use the winning model
          reason: `A/B test winner deployment from test ${test.id}`
        },
        status: 'active',
        metadata: {
          source: 'ab_test_deployment',
          test_id: test.id,
          winning_variant: variant,
          deployment_date: new Date().toISOString()
        }
      };

      // Create the routing policy
      await callApi('/api/policies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(routingPolicy)
      });

      console.log(`Created production routing policy for A/B test winner: ${routingPolicy.id}`);
    } catch (error) {
      console.error('Failed to update production routing:', error);
      throw new Error(`Failed to update production routing: ${error.message}`);
    }
  };

  // Helper function to get model for prompt (simplified mapping)
  const getModelForPrompt = (promptId) => {
    // This should match the mapping in proxy-gateway
    const promptModelMap = {
      "prompt-a-*************": "gemini-2.5-flash-preview-05-20",
      "prompt-b-*************": "gpt-3.5-turbo",
      "prompt-a-*************": "gpt-4o-mini",
      "prompt-b-*************": "claude-3-haiku",
      "summarization-v1": "gpt-3.5-turbo",
      "summarization-v2": "gemini-2.5-flash-preview-05-20",
    };

    return promptModelMap[promptId] || "gemini-2.5-flash-preview-05-20";
  };

  // Helper function to export to GitOps repository
  const exportToGitOps = async (productionPrompt, testId) => {
    try {
      // Export the production prompt to GitOps repository
      const gitOpsPayload = {
        prompts: [productionPrompt],
        environment: 'production',
        deployment_reason: `A/B test winner deployment from test ${testId}`,
        auto_deploy: true
      };

      await callApi('/api/integration/deploy-prompts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(gitOpsPayload)
      });

      console.log(`Exported winning prompt to GitOps repository: ${productionPrompt.id}`);
    } catch (error) {
      console.error('GitOps export failed:', error);
      throw error;
    }
  };
};

export default usePromptOps;
