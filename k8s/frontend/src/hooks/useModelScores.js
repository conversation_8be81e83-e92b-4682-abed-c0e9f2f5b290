import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';

const useModelScores = () => {
  const [modelProfiles, setModelProfiles] = useState([]);
  const [modelScores, setModelScores] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { getAuthHeaders } = useAuth();

  // API endpoints - use environment-aware base URL
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || import.meta.env.REACT_APP_API_BASE_URL || '';
  const POLICY_MANAGER_API_PREFIX = `${API_BASE_URL}/api`;
  const AI_OPTIMIZER_API_PREFIX = `${API_BASE_URL}/api`;

  const fetchModelProfiles = useCallback(async () => {
    try {
      const authHeaders = getAuthHeaders();
      console.log('[useModelScores] Auth headers for model-profiles:', authHeaders);

      const response = await fetch(`${POLICY_MANAGER_API_PREFIX}/model-profiles`, {
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders
        }
      });
      if (!response.ok) {
        throw new Error(`Failed to fetch model profiles: ${response.status}`);
      }
      const profiles = await response.json();
      setModelProfiles(Array.isArray(profiles) ? profiles : []);
      return profiles;
    } catch (err) {
      console.error('Error fetching model profiles:', err);
      setError(err.message);
      return [];
    }
  }, [getAuthHeaders]);

  const fetchModelScores = useCallback(async () => {
    try {
      // Try to fetch capability scores from AI optimizer
      const authHeaders = getAuthHeaders();
      console.log('[useModelScores] Auth headers for model-capabilities:', authHeaders);

      const response = await fetch(`${AI_OPTIMIZER_API_PREFIX}/model-capabilities`, {
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders
        }
      });
      if (!response.ok) {
        throw new Error(`Failed to fetch model capabilities: ${response.status}`);
      }
      const capabilities = await response.json();
      setModelScores(capabilities || {});
      return capabilities;
    } catch (err) {
      console.error('Error fetching model scores:', err);
      // Fallback to hardcoded scores if API is not available
      const fallbackScores = {
        'gpt-4o-mini': {
          code_generation: 0.90,
          reasoning: 0.88,
          creative_writing: 0.85,
          factual_qa: 0.90,
          analysis: 0.88,
          summarization: 0.85,
          translation: 0.82,
          mathematical: 0.88,
          conversational: 0.85,
          overall: 0.87
        },
        'gpt-3.5-turbo': {
          code_generation: 0.80,
          reasoning: 0.75,
          creative_writing: 0.78,
          factual_qa: 0.82,
          analysis: 0.75,
          summarization: 0.80,
          translation: 0.75,
          mathematical: 0.70,
          conversational: 0.85,
          overall: 0.78
        },
        'claude-3-haiku': {
          code_generation: 0.85,
          reasoning: 0.88,
          creative_writing: 0.90,
          factual_qa: 0.85,
          analysis: 0.88,
          summarization: 0.85,
          translation: 0.80,
          mathematical: 0.82,
          conversational: 0.88,
          overall: 0.86
        },
        'gemini-2.5-flash-preview-05-20': {
          code_generation: 0.85,
          reasoning: 0.88,
          creative_writing: 0.80,
          factual_qa: 0.90,
          analysis: 0.85,
          summarization: 0.82,
          translation: 0.88,
          mathematical: 0.88,
          conversational: 0.82,
          multimodal: 0.95,
          overall: 0.86
        },
        'command-r-plus': {
          code_generation: 0.75,
          reasoning: 0.85,
          creative_writing: 0.80,
          factual_qa: 0.92,
          analysis: 0.88,
          summarization: 0.90,
          translation: 0.90,
          mathematical: 0.75,
          conversational: 0.85,
          rag: 0.95,
          overall: 0.85
        },
        'command-r': {
          code_generation: 0.70,
          reasoning: 0.80,
          creative_writing: 0.75,
          factual_qa: 0.88,
          analysis: 0.82,
          summarization: 0.85,
          translation: 0.88,
          mathematical: 0.70,
          conversational: 0.80,
          rag: 0.90,
          overall: 0.81
        },
        'mistral-large-latest': {
          code_generation: 0.88,
          reasoning: 0.90,
          creative_writing: 0.80,
          factual_qa: 0.82,
          analysis: 0.85,
          summarization: 0.80,
          translation: 0.82,
          mathematical: 0.85,
          conversational: 0.78,
          function_calling: 0.90,
          overall: 0.84
        },
        'mistral-medium-latest': {
          code_generation: 0.82,
          reasoning: 0.80,
          creative_writing: 0.75,
          factual_qa: 0.78,
          analysis: 0.80,
          summarization: 0.78,
          translation: 0.80,
          mathematical: 0.78,
          conversational: 0.75,
          function_calling: 0.82,
          overall: 0.79
        },
        'grok-1': {
          code_generation: 0.82,
          reasoning: 0.88,
          creative_writing: 0.85,
          factual_qa: 0.90,
          analysis: 0.85,
          summarization: 0.80,
          translation: 0.75,
          mathematical: 0.85,
          conversational: 0.88,
          real_time_data: 0.95,
          overall: 0.85
        },
        'grok-1.5-vision': {
          code_generation: 0.85,
          reasoning: 0.90,
          creative_writing: 0.85,
          factual_qa: 0.92,
          analysis: 0.88,
          summarization: 0.82,
          translation: 0.78,
          mathematical: 0.88,
          conversational: 0.90,
          multimodal: 0.90,
          real_time_data: 0.95,
          overall: 0.88
        },
        'meta-llama-3.1-70b-instruct': {
          code_generation: 0.88,
          reasoning: 0.85,
          creative_writing: 0.82,
          factual_qa: 0.80,
          analysis: 0.83,
          summarization: 0.80,
          translation: 0.75,
          mathematical: 0.82,
          conversational: 0.80,
          overall: 0.82
        }
      };
      setModelScores(fallbackScores);
      return fallbackScores;
    }
  }, [getAuthHeaders]);

  const loadData = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const [profiles, scores] = await Promise.all([
        fetchModelProfiles(),
        fetchModelScores()
      ]);
      
      // Merge profile data with scores
      const enrichedProfiles = profiles.map(profile => ({
        ...profile,
        scores: scores[profile.id] || {},
        overallScore: scores[profile.id]?.overall || 0
      }));
      
      setModelProfiles(enrichedProfiles);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [fetchModelProfiles, fetchModelScores]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const getModelScore = useCallback((modelId, capability = 'overall') => {
    return modelScores[modelId]?.[capability] || 0;
  }, [modelScores]);

  const getModelProfile = useCallback((modelId) => {
    return modelProfiles.find(profile => profile.id === modelId);
  }, [modelProfiles]);

  const getTopCapabilities = useCallback((modelId, limit = 3) => {
    const scores = modelScores[modelId];
    if (!scores) return [];
    
    return Object.entries(scores)
      .filter(([key]) => key !== 'overall')
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([capability, score]) => ({
        capability: capability.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        score
      }));
  }, [modelScores]);

  return {
    modelProfiles,
    modelScores,
    loading,
    error,
    getModelScore,
    getModelProfile,
    getTopCapabilities,
    refresh: loadData
  };
};

export default useModelScores;
