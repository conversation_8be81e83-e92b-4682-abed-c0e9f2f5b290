import React, { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

// Authentication imports
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/auth/ProtectedRoute';
import UserProfile from './components/auth/UserProfile';
import LoginButton from './components/auth/LoginButton';

// Icon imports - only what we need for startup edition
import {
  LineChart as LineChartIcon,
  DollarSign,
  Zap,
  MessageSquare,
  FileText,
  Info as InfoIcon,
  Settings,
  Crown,
  TrendingUp,
  Users,
  Clock,
  User
} from 'lucide-react';

// Import only core components needed for startup edition
import ChatDashboard from './components/ChatDashboard';
import PromptOpsDashboard from './components/PromptOpsDashboard';
import StartupLanding from './components/StartupLanding';

dayjs.extend(relativeTime);

const StandardApp = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showLanding, setShowLanding] = useState(false);
  const [apiStatus, setApiStatus] = useState({
    dashboard: 'loading',
    proxy: 'loading'
  });
  const [metrics, setMetrics] = useState({
    totalRequests: 0,
    costSavings: 0,
    avgResponseTime: 0,
    usageThisMonth: 0,
    usageLimit: 1000 // Default free tier
  });
  const [userTier, setUserTier] = useState('free'); // free, starter, growth

  // Simplified API status check for standard edition
  useEffect(() => {
    const checkApiStatus = async () => {
      try {
        const response = await fetch('/standard/api/health');
        setApiStatus(prev => ({
          ...prev,
          proxy: response.ok ? 'ok' : 'error'
        }));
      } catch (error) {
        setApiStatus(prev => ({ ...prev, proxy: 'error' }));
      }

      try {
        const response = await fetch('/standard/dashboard/health');
        setApiStatus(prev => ({
          ...prev,
          dashboard: response.ok ? 'ok' : 'error'
        }));
      } catch (error) {
        setApiStatus(prev => ({ ...prev, dashboard: 'error' }));
      }
    };

    checkApiStatus();
    const interval = setInterval(checkApiStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  // Fetch basic metrics for standard dashboard
  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const response = await fetch('/standard/dashboard/metrics');
        if (response.ok) {
          const data = await response.json();
          setMetrics(data);
        }
      } catch (error) {
        console.error('Failed to fetch metrics:', error);
      }
    };

    fetchMetrics();
    const interval = setInterval(fetchMetrics, 60000); // Update every minute
    return () => clearInterval(interval);
  }, []);

  const SidebarButton = ({ name, icon, label, isPro = false }) => (
    <button
      onClick={() => setActiveTab(name)}
      className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
        activeTab === name
          ? 'bg-indigo-100 text-indigo-700 border-r-2 border-indigo-500'
          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
      } ${isPro && userTier === 'free' ? 'opacity-50' : ''}`}
      disabled={isPro && userTier === 'free'}
    >
      {icon}
      <span className="ml-3">{label}</span>
      {isPro && userTier === 'free' && <Crown size={16} className="ml-auto text-yellow-500" />}
    </button>
  );

  const UsageIndicator = () => {
    const usagePercentage = (metrics.usageThisMonth / metrics.usageLimit) * 100;
    const isNearLimit = usagePercentage > 80;

    return (
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Monthly Usage</span>
          <span className="text-xs text-gray-500">
            {metrics.usageThisMonth.toLocaleString()} / {metrics.usageLimit.toLocaleString()}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              isNearLimit ? 'bg-red-500' : 'bg-indigo-500'
            }`}
            style={{ width: `${Math.min(usagePercentage, 100)}%` }}
          />
        </div>
        {isNearLimit && (
          <p className="text-xs text-red-600 mt-1">
            Approaching usage limit. Consider upgrading to continue.
          </p>
        )}
      </div>
    );
  };

  const TierBadge = () => {
    const tierConfig = {
      free: { label: 'Free', color: 'bg-gray-100 text-gray-800' },
      starter: { label: 'Starter', color: 'bg-blue-100 text-blue-800' },
      growth: { label: 'Growth', color: 'bg-purple-100 text-purple-800' }
    };

    const config = tierConfig[userTier];
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  // Show landing page for new users
  if (showLanding && activeTab === 'landing') {
    return (
      <StartupLanding
        onGetStarted={() => {
          setShowLanding(false);
          setActiveTab('chat');
        }}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Simplified Sidebar */}
      <div className="w-64 bg-white shadow-lg flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Zap className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="ml-3">
              <h1 className="text-xl font-bold text-gray-900">AI Operations Hub</h1>
              <p className="text-sm text-gray-500">Intelligent LLM Management</p>
            </div>
          </div>
          <div className="mt-4 flex items-center justify-between">
            <TierBadge />
            {userTier === 'free' && (
              <button className="text-sm bg-indigo-100 text-indigo-700 px-3 py-1 rounded-md hover:bg-indigo-200 font-medium transition-colors">
                Upgrade Plan
              </button>
            )}
          </div>
        </div>

        {/* Navigation */}
        <nav className="mt-6 flex-1">
          <div className="space-y-1 px-3">
            <SidebarButton name="dashboard" icon={<LineChartIcon size={20} />} label="Dashboard" />
            <SidebarButton name="chat" icon={<MessageSquare size={20} />} label="AI Chat" />
            <SidebarButton name="prompts" icon={<FileText size={20} />} label="PromptOps" />
            <SidebarButton name="profile" icon={<User size={20} />} label="Profile" />
            <SidebarButton name="settings" icon={<Settings size={20} />} label="Settings" isPro={userTier === 'free'} />
            <SidebarButton name="about" icon={<InfoIcon size={20} />} label="About" />
          </div>
        </nav>

        {/* Usage Indicator */}
        <div className="p-3">
          <UsageIndicator />
        </div>

        {/* API Status */}
        <div className="p-3 border-t border-gray-200">
          <div className="text-xs text-gray-500 mb-2">API Status</div>
          <div className="space-y-1">
            <div className={`flex items-center text-xs ${
              apiStatus.proxy === 'ok' ? 'text-green-600' : 'text-red-600'
            }`}>
              <div className={`w-2 h-2 rounded-full mr-2 ${
                apiStatus.proxy === 'ok' ? 'bg-green-500' : 'bg-red-500'
              }`} />
              Proxy Gateway
            </div>
            <div className={`flex items-center text-xs ${
              apiStatus.dashboard === 'ok' ? 'text-green-600' : 'text-red-600'
            }`}>
              <div className={`w-2 h-2 rounded-full mr-2 ${
                apiStatus.dashboard === 'ok' ? 'bg-green-500' : 'bg-red-500'
              }`} />
              Dashboard API
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        <main className="flex-1 p-6 overflow-auto">
          {activeTab === 'dashboard' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-3xl font-bold text-gray-900">Dashboard</h2>
                <div className="text-sm text-gray-500">
                  Last updated: {dayjs().format('MMM D, YYYY h:mm A')}
                </div>
              </div>

              {/* Key Metrics Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-white p-6 rounded-lg shadow-sm border">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <MessageSquare className="h-8 w-8 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500">Total Requests</p>
                      <p className="text-2xl font-semibold text-gray-900">{metrics.totalRequests.toLocaleString()}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-lg shadow-sm border">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <DollarSign className="h-8 w-8 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500">Cost Savings</p>
                      <p className="text-2xl font-semibold text-gray-900">${metrics.costSavings.toFixed(2)}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-lg shadow-sm border">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Clock className="h-8 w-8 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500">Avg Response Time</p>
                      <p className="text-2xl font-semibold text-gray-900">{metrics.avgResponseTime}ms</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-lg shadow-sm border">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <TrendingUp className="h-8 w-8 text-indigo-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500">This Month</p>
                      <p className="text-2xl font-semibold text-gray-900">{metrics.usageThisMonth.toLocaleString()}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Feature Preview Cards */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* AI Chat Preview */}
                <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
                  <div className="p-6 border-b border-gray-200">
                    <div className="flex items-center">
                      <MessageSquare className="h-6 w-6 text-blue-600 mr-3" />
                      <h3 className="text-lg font-semibold text-gray-900">AI Chat Interface</h3>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">Intelligent LLM routing with cost optimization</p>
                  </div>
                  <div className="p-4 bg-gray-50">
                    <div className="space-y-3">
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <Users className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="flex-1 bg-white rounded-lg p-3 shadow-sm">
                          <p className="text-sm text-gray-700">What's the best way to optimize my React app performance?</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                          <Zap className="h-4 w-4 text-indigo-600" />
                        </div>
                        <div className="flex-1 bg-indigo-50 rounded-lg p-3">
                          <p className="text-sm text-gray-700">Here are the top React performance optimization techniques...</p>
                          <div className="mt-2 text-xs text-indigo-600 font-medium">✨ Routed to GPT-4 • $0.003 cost</div>
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => setActiveTab('chat')}
                      className="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                    >
                      Try AI Chat →
                    </button>
                  </div>
                </div>

                {/* PromptOps Preview */}
                <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
                  <div className="p-6 border-b border-gray-200">
                    <div className="flex items-center">
                      <FileText className="h-6 w-6 text-purple-600 mr-3" />
                      <h3 className="text-lg font-semibold text-gray-900">PromptOps Management</h3>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">Version control and A/B testing for prompts</p>
                  </div>
                  <div className="p-4 bg-gray-50">
                    <div className="space-y-3">
                      <div className="bg-white rounded-lg p-3 border-l-4 border-purple-500">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-900">Customer Support v2.1</span>
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Active</span>
                        </div>
                        <p className="text-xs text-gray-600 mt-1">Conversion rate: 94.2% ↑ 12%</p>
                      </div>
                      <div className="bg-white rounded-lg p-3 border-l-4 border-gray-300">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-900">Code Review Assistant</span>
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Testing</span>
                        </div>
                        <p className="text-xs text-gray-600 mt-1">A/B test: 67% completion</p>
                      </div>
                    </div>
                    <button
                      onClick={() => setActiveTab('prompts')}
                      className="mt-4 w-full bg-purple-600 text-white py-2 px-4 rounded-md text-sm font-medium hover:bg-purple-700 transition-colors"
                    >
                      Manage Prompts →
                    </button>
                  </div>
                </div>
              </div>

              {/* Upgrade CTA for free users */}
              {userTier === 'free' && (
                <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold">Ready to scale your AI operations?</h3>
                      <p className="text-indigo-100 mt-1">Upgrade to Starter for 10x more API calls and advanced features.</p>
                    </div>
                    <button className="bg-white text-indigo-600 px-6 py-2 rounded-md font-medium hover:bg-gray-50 transition-colors">
                      Upgrade Now
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'chat' && (
            <ChatDashboard
              showNotification={(message, type) => console.log(message, type)}
              onModalStateChange={() => {}}
              isStartupEdition={true}
            />
          )}

          {activeTab === 'prompts' && (
            <PromptOpsDashboard
              showNotification={(message, type) => console.log(message, type)}
              onModalStateChange={() => {}}
              modelProfiles={{}}
              isStartupEdition={true}
            />
          )}

          {activeTab === 'settings' && userTier !== 'free' && (
            <div className="space-y-6">
              <h2 className="text-3xl font-bold text-gray-900">Settings</h2>
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <p className="text-gray-600">Settings panel for Starter and Growth tier users.</p>
              </div>
            </div>
          )}

          {activeTab === 'profile' && (
            <div className="space-y-6">
              <h2 className="text-3xl font-bold text-gray-900">User Profile</h2>
              <UserProfile />
            </div>
          )}

          {activeTab === 'about' && (
            <div className="space-y-6 bg-white p-6 rounded-lg shadow-sm border">
              <h2 className="text-3xl font-bold text-gray-900">About AI Operations Hub - Standard Edition</h2>
              <div className="space-y-4">
                <p className="text-gray-700">
                  AI Operations Hub Standard Edition provides intelligent LLM routing and cost optimization
                  specifically designed for startups and small businesses.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Core Features</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-1">
                      <li>Intelligent LLM routing</li>
                      <li>Cost optimization</li>
                      <li>Simple PromptOps</li>
                      <li>Usage tracking</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Pricing Tiers</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-1">
                      <li>Free: 1,000 calls/month</li>
                      <li>Starter: 10,000 calls/month ($29)</li>
                      <li>Growth: 50,000 calls/month ($99)</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

// Wrap StandardApp with AuthProvider
const StandardAppWithAuth = () => {
  return (
    <AuthProvider>
      <ProtectedRoute fallback={<StartupLanding onGetStarted={() => window.location.href = '/auth/google'} />}>
        <StandardApp />
      </ProtectedRoute>
    </AuthProvider>
  );
};

export default StandardAppWithAuth;
