# --------------------------
# Stage 1: Build the Vite App for Startup Edition
# --------------------------
FROM node:20-alpine AS build-step

WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .

# Standard Edition specific build configuration
ENV REACT_APP_EDITION=standard
ENV REACT_APP_FEATURES=basic

# Build with /standard/ base path for standard edition
# Set environment variable to use StandardApp
ENV VITE_APP_EDITION=standard
ENV VITE_API_BASE_URL=/standard
ENV REACT_APP_API_BASE_URL=/standard/api
RUN npm run build -- --mode production --base=/standard/

# --------------------------
# Stage 2: Serve with Nginx
# --------------------------
FROM nginx:alpine

# Copy built app to Nginx's HTML directory
COPY --from=build-step /app/dist /usr/share/nginx/html

# Copy standard-specific nginx configuration
COPY nginx-standard.conf /etc/nginx/nginx.conf

EXPOSE 80
ENTRYPOINT ["nginx", "-g", "daemon off;"]
