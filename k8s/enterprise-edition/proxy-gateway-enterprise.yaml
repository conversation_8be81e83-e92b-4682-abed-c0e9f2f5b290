apiVersion: apps/v1
kind: Deployment
metadata:
  name: proxy-gateway-enterprise
  labels:
    app: proxy-gateway-enterprise
    edition: enterprise
spec:
  replicas: 3
  selector:
    matchLabels:
      app: proxy-gateway-enterprise
  template:
    metadata:
      labels:
        app: proxy-gateway-enterprise
        edition: enterprise
    spec:
      containers:
      - name: proxy-gateway
        image: ${PROXY_GATEWAY_IMAGE}
        ports:
        - containerPort: 8080
        env:
        - name: PORT
          value: "8080"
        - name: REDIS_HOST
          value: "redis"
        - name: REDIS_PORT
          value: "6379"
        # Enterprise Edition specific settings
        - name: EDITION
          value: "enterprise"
        - name: ENABLE_FREEMIUM
          value: "false"
        - name: ENABLE_ENTERPRISE_FEATURES
          value: "true"
        - name: ENABLE_ADVANCED_ANALYTICS
          value: "true"
        - name: ENABLE_MULTI_AGENT
          value: "true"
        - name: ENABLE_GOVERNANCE
          value: "true"
        # Test mode for development/testing (allows bypassing authentication)
        - name: TEST_MODE
          value: "true"
        # Enterprise service connections
        - name: GOVERNANCE_SERVICE_URL
          value: "http://governance-service.enterprise-edition.svc.cluster.local:8088"
        - name: PLA<PERSON><PERSON>NG_SERVICE_URL
          value: "http://planning-service.enterprise-edition.svc.cluster.local:8082"
        - name: MULTI_AGENT_URL
          value: "http://multi-agent-orchestrator-service:8083"
        - name: EVALUATION_SERVICE_URL
          value: "http://evaluation-service.enterprise-edition.svc.cluster.local:8087"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: proxy-gateway-enterprise
  labels:
    app: proxy-gateway-enterprise
    edition: enterprise
spec:
  selector:
    app: proxy-gateway-enterprise
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: proxy-gateway-enterprise-ingress
  labels:
    app: proxy-gateway-enterprise
    edition: enterprise
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: scale-llm-tls
  rules:
  - host: scale-llm.com
    http:
      paths:
      - path: /enterprise/api(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: proxy-gateway-enterprise
            port:
              number: 8080
