apiVersion: apps/v1
kind: Deployment
metadata:
  name: dashboard-api-enterprise
  labels:
    app: dashboard-api-enterprise
    edition: enterprise
spec:
  replicas: 2
  selector:
    matchLabels:
      app: dashboard-api-enterprise
  template:
    metadata:
      labels:
        app: dashboard-api-enterprise
        edition: enterprise
    spec:
      containers:
      - name: dashboard-api
        image: ${DASHBOARD_API_IMAGE}
        ports:
        - containerPort: 8081
        env:
        - name: PORT
          value: "8081"
        - name: REDIS_HOST
          value: "redis"
        - name: REDIS_PORT
          value: "6379"
        # Enterprise Edition specific settings
        - name: EDITION
          value: "enterprise"
        - name: BASIC_METRICS_ONLY
          value: "false"
        - name: DISABLE_ADVANCED_ANALYTICS
          value: "false"
        - name: DISABLE_ENTERPRISE_FEATURES
          value: "false"
        # ClickHouse for enterprise analytics
        - name: CLICKHOUSE_HOST
          value: "clickhouse"
        - name: CLICKHOUSE_PORT
          value: "9000"
        - name: USE_REDIS_ONLY
          value: "false"
        # Enterprise service connections
        - name: GOVERNANCE_SERVICE_URL
          value: "http://governance-service.enterprise-edition.svc.cluster.local:8088"
        - name: EVALUATION_SERVICE_URL
          value: "http://evaluation-service.enterprise-edition.svc.cluster.local:8087"
        - name: PLANNING_SERVICE_URL
          value: "http://planning-service.enterprise-edition.svc.cluster.local:8082"
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: dashboard-api-enterprise
  labels:
    app: dashboard-api-enterprise
    edition: enterprise
spec:
  selector:
    app: dashboard-api-enterprise
  ports:
  - name: http
    port: 8081
    targetPort: 8081
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dashboard-api-enterprise-ingress
  labels:
    app: dashboard-api-enterprise
    edition: enterprise
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: scale-llm-tls
  rules:
  - host: scale-llm.com
    http:
      paths:
      - path: /enterprise/dashboard(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-enterprise
            port:
              number: 8081
