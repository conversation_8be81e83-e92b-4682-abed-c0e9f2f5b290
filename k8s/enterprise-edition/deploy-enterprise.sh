#!/bin/bash

# AI Operations Hub - Enterprise Edition Deployment Script
# Deploys full enterprise features including governance, planning, and multi-agent capabilities

set -e

echo "🏢 Deploying AI Operations Hub - Enterprise Edition"
echo "=================================================="

# Create enterprise namespace if it doesn't exist
echo "📦 Creating enterprise namespace..."
kubectl create namespace enterprise-edition --dry-run=client -o yaml | kubectl apply -f -

# Deploy Redis (required for caching and session management)
echo "🔴 Deploying Redis..."
kubectl apply -f k8s/redis/redis-k8s.yaml -n enterprise-edition

# Deploy ClickHouse (required for enterprise analytics)
echo "📊 Deploying ClickHouse..."
kubectl apply -f k8s/clickhouse/clickhouse-k8s.yaml -n enterprise-edition

# Deploy Kafka (required for enterprise event streaming)
echo "📨 Deploying Kafka..."
kubectl apply -f k8s/kafka/kafka-k8s.yaml -n enterprise-edition

# Wait for dependencies to be ready
echo "⏳ Waiting for dependencies to be ready..."
kubectl wait --for=condition=ready pod -l app=redis -n enterprise-edition --timeout=300s
kubectl wait --for=condition=ready pod -l app=clickhouse -n enterprise-edition --timeout=300s
kubectl wait --for=condition=ready pod -l app=kafka -n enterprise-edition --timeout=300s

# Deploy enterprise services
echo "🔧 Deploying enterprise services..."

# Deploy Proxy Gateway (full enterprise features)
echo "  → Deploying Proxy Gateway..."
envsubst < k8s/enterprise-edition/proxy-gateway-enterprise.yaml | kubectl apply -f - -n enterprise-edition

# Deploy AI Optimizer (full features)
echo "  → Deploying AI Optimizer..."
envsubst < k8s/ai-optimizer/ai-optimizer-k8s.yaml | kubectl apply -f - -n enterprise-edition

# Deploy Dashboard API (full analytics)
echo "  → Deploying Dashboard API..."
envsubst < k8s/enterprise-edition/dashboard-api-enterprise.yaml | kubectl apply -f - -n enterprise-edition

# Deploy Planning Service
echo "  → Deploying Planning Service..."
envsubst < k8s/planning-service/planning-service-k8s.yaml | kubectl apply -f - -n enterprise-edition

# Deploy Evaluation Service
echo "  → Deploying Evaluation Service..."
envsubst < k8s/evaluation-service/evaluation-service-k8s.yaml | kubectl apply -f - -n enterprise-edition

# Deploy Integration Service
echo "  → Deploying Integration Service..."
envsubst < k8s/integration-service/integration-service-k8s.yaml | kubectl apply -f - -n enterprise-edition

# Deploy Governance Service
echo "  → Deploying Governance Service..."
envsubst < k8s/governance-service/governance-service-k8s.yaml | kubectl apply -f - -n enterprise-edition

# Deploy Data Processor
echo "  → Deploying Data Processor..."
envsubst < k8s/data-processor/data-processor-k8s.yaml | kubectl apply -f - -n enterprise-edition

# Deploy Policy Manager
echo "  → Deploying Policy Manager..."
envsubst < k8s/policy-manager/policy-manager-k8s.yaml | kubectl apply -f - -n enterprise-edition

# Deploy Frontend (full enterprise interface)
echo "  → Deploying Frontend..."
envsubst < k8s/enterprise-edition/frontend-enterprise.yaml | kubectl apply -f - -n enterprise-edition

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
kubectl wait --for=condition=ready pod -l app=proxy-gateway-enterprise -n enterprise-edition --timeout=300s
kubectl wait --for=condition=ready pod -l app=dashboard-api-enterprise -n enterprise-edition --timeout=300s
kubectl wait --for=condition=ready pod -l app=frontend-enterprise -n enterprise-edition --timeout=300s

# Create LLM API keys secret for enterprise
echo "🔑 Creating LLM API keys secret for enterprise..."
bash k8s/scripts/create-llm-secrets.sh enterprise-edition

# Configure ingress for enterprise deployment
echo "🌐 Configuring ingress routes for enterprise edition..."
bash k8s/scripts/manage-ingress-routes.sh enterprise

echo ""
echo "✅ Enterprise Edition deployment completed!"
echo ""
echo "🌐 Access your AI Operations Hub Enterprise Edition at:"
echo "   https://scale-llm.com/enterprise"
echo ""
echo "📋 Enterprise Services Deployed:"
echo "   • Proxy Gateway (full enterprise features)"
echo "   • AI Optimizer (advanced routing)"
echo "   • Dashboard API (full analytics)"
echo "   • Planning Service (autonomous task planning)"
echo "   • Evaluation Service (model evaluation)"
echo "   • Integration Service (GitOps & CI/CD)"
echo "   • Governance Service (responsible AI)"
echo "   • Data Processor (advanced analytics)"
echo "   • Policy Manager (enterprise policies)"
echo "   • Frontend (full enterprise interface)"
echo "   • Redis (caching & sessions)"
echo "   • ClickHouse (analytics database)"
echo "   • Kafka (event streaming)"
echo ""
echo "🔧 Next Steps:"
echo "   1. Update API keys: kubectl edit secret llm-api-keys -n enterprise-edition"
echo "   2. Configure ClickHouse: kubectl edit configmap clickhouse-config -n enterprise-edition"
echo "   3. Test the deployment:"
echo "      - Frontend: https://scale-llm.com/enterprise/"
echo "      - API Health: curl https://scale-llm.com/enterprise/api/health"
echo "      - Dashboard: https://scale-llm.com/enterprise/dashboard/health"
