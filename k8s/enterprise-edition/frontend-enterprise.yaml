apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-enterprise
  labels:
    app: frontend-enterprise
    edition: enterprise
spec:
  replicas: 3
  selector:
    matchLabels:
      app: frontend-enterprise
  template:
    metadata:
      labels:
        app: frontend-enterprise
        edition: enterprise
    spec:
      containers:
      - name: frontend
        image: ${FRONTEND_IMAGE}
        ports:
        - containerPort: 80
        env:
        # Enterprise Edition specific environment variables
        - name: REACT_APP_EDITION
          value: "enterprise"
        - name: REACT_APP_FEATURES
          value: "full"
        - name: REACT_APP_API_BASE_URL
          value: "/enterprise/api"
        - name: REACT_APP_DASHBOARD_API_URL
          value: "/enterprise/dashboard"
        - name: REACT_APP_DISABLE_ENTERPRISE_TABS
          value: "false"
        - name: REACT_APP_ENABLE_FREEMIUM
          value: "false"
        - name: REACT_APP_PRODUCT_NAME
          value: "AI Operations Hub - Enterprise Edition"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: frontend-enterprise
  labels:
    app: frontend-enterprise
    edition: enterprise
spec:
  selector:
    app: frontend-enterprise
  ports:
  - name: http
    port: 80
    targetPort: 80
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: frontend-enterprise-ingress
  labels:
    app: frontend-enterprise
    edition: enterprise
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: scale-llm-tls
  rules:
  - host: scale-llm.com
    http:
      paths:
      - path: /enterprise(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: frontend-enterprise
            port:
              number: 80
