apiVersion: apps/v1
kind: Deployment
metadata:
  name: proxy-gateway-standard
  labels:
    app: proxy-gateway-standard
    edition: standard
spec:
  replicas: 2
  selector:
    matchLabels:
      app: proxy-gateway-standard
  template:
    metadata:
      labels:
        app: proxy-gateway-standard
        edition: standard
    spec:
      containers:
      - name: proxy-gateway
        image: ${PROXY_GATEWAY_IMAGE}
        ports:
        - containerPort: 8080
        env:
        - name: PORT
          value: "8080"
        - name: AI_OPTIMIZER_URL
          value: "http://ai-optimizer-standard:8085"
        - name: POLICY_MANAGER_URL
          value: "http://policy-manager:8083"
        - name: REDIS_HOST
          value: "redis"
        - name: REDIS_PORT
          value: "6379"
        # Standard Edition specific environment variables
        - name: EDITION
          value: "standard"
        - name: ENABLE_FREEMIUM
          value: "true"
        - name: FREE_TIER_LIMIT
          value: "1000"  # 1000 API calls per month
        - name: STARTER_TIER_LIMIT
          value: "10000"  # 10K API calls per month
        - name: GROWTH_TIER_LIMIT
          value: "50000"  # 50K API calls per month
        - name: ENABLE_USAGE_TRACKING
          value: "true"
        - name: DISABLE_ENTERPRISE_FEATURES
          value: "true"
        # Disable enterprise services
        - name: GOVERNANCE_SERVICE_URL
          value: ""
        - name: PLANNING_SERVICE_URL
          value: ""
        - name: MULTI_AGENT_URL
          value: ""
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: proxy-gateway-standard
  labels:
    app: proxy-gateway-standard
    edition: standard
spec:
  selector:
    app: proxy-gateway-standard
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  type: ClusterIP

# Ingress configuration removed - handled by existing api-rewrite-ingress in default namespace
