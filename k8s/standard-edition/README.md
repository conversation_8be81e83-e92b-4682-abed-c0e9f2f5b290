# AI Operations Hub - Standard Edition

## Overview

The Standard Edition is a simplified version of AI Operations Hub designed specifically for startups (1-50 employees) and small businesses (50-200 employees). It focuses on core value delivery with a no-code interface and freemium pricing model.

## Architecture

### Core Services (4 Components)
- **Proxy Gateway**: Intelligent LLM routing with usage limits and freemium logic
- **AI Optimizer**: Basic cost optimization and model selection
- **Dashboard API**: Essential metrics and analytics (Redis-only, no ClickHouse)
- **Frontend**: Simplified no-code interface focused on chat and basic PromptOps
- **Redis**: Caching, session management, and basic data storage

### Removed Enterprise Features
- Planning Service (autonomous task execution)
- Multi-Agent Orchestrator
- All Responsible AI services (bias detection, explainability, robustness testing)
- Governance Service
- Policy Manager
- Compliance Service
- Integration Service (initially)
- ClickHouse dependency
- Kafka dependency

## Deployment

### Quick Deploy
```bash
# Deploy startup edition
gcloud builds submit --config=cloudbuild-startup.yaml

# Or deploy manually
kubectl apply -f k8s/startup-edition/ -n startup-edition
```

### Manual Deployment
```bash
# Create namespace
kubectl create namespace startup-edition

# Deploy Redis first
kubectl apply -f k8s/redis/redis-k8s.yaml -n startup-edition

# Deploy core services
kubectl apply -f k8s/standard-edition/proxy-gateway-standard.yaml -n standard-edition
kubectl apply -f k8s/standard-edition/ai-optimizer-standard.yaml -n standard-edition
kubectl apply -f k8s/standard-edition/dashboard-api-standard.yaml -n standard-edition
kubectl apply -f k8s/standard-edition/frontend-standard.yaml -n standard-edition
```

## Access

- **Standard Edition URL**: `https://scale-llm.com/standard`
- **API Endpoint**: `https://scale-llm.com/standard/api`
- **Dashboard**: `https://scale-llm.com/standard/dashboard`

## Features

### ✅ Included in Startup Edition
- Intelligent LLM routing (OpenAI, Google, Anthropic)
- Cost optimization and model selection
- Basic chat interface
- Simple PromptOps management
- Usage tracking and limits
- Basic analytics dashboard
- Freemium pricing tiers

### ❌ Enterprise-Only Features
- Autonomous planning and task execution
- Multi-agent orchestration
- Bias detection and explainability
- Advanced governance and compliance
- Complex policy management
- Advanced integrations
- Comprehensive audit logs

## Pricing Tiers

### Free Tier
- 1,000 API calls/month
- Basic chat interface
- Simple analytics

### Starter ($29/month)
- 10,000 API calls/month
- Full PromptOps features
- 3 integrations
- Email support

### Growth ($99/month)
- 50,000 API calls/month
- Unlimited integrations
- Advanced analytics
- Priority support

## Environment Variables

### Startup-Specific Settings
```bash
EDITION=startup
ENABLE_FREEMIUM=true
FREE_TIER_LIMIT=1000
STARTER_TIER_LIMIT=10000
GROWTH_TIER_LIMIT=50000
DISABLE_ENTERPRISE_FEATURES=true
USE_REDIS_ONLY=true
BASIC_METRICS_ONLY=true
```

## Development

### Building Startup Images
```bash
# Build with startup-specific tags
docker build -t ai-optimizer-proxy-gateway-startup:latest k8s/proxy-gateway/
docker build -t ai-optimizer-ai-optimizer-startup:latest k8s/ai-optimizer/
docker build -t ai-optimizer-dashboard-api-startup:latest k8s/dashboard-api/
docker build --build-arg REACT_APP_EDITION=startup -t ai-optimizer-frontend-startup:latest k8s/frontend/
```

### Local Development
```bash
# Set startup environment
export EDITION=startup
export ENABLE_FREEMIUM=true
export DISABLE_ENTERPRISE_FEATURES=true

# Run services locally
cd k8s/proxy-gateway && go run main.go
cd k8s/ai-optimizer && go run main.go
cd k8s/dashboard-api && go run main.go
```

## Monitoring

### Health Checks
- Proxy Gateway: `https://scale-llm.com/startups/api/health`
- AI Optimizer: Internal health check
- Dashboard API: Internal health check
- Frontend: `https://scale-llm.com/startups/health`

### Key Metrics
- API usage per user/tier
- Cost savings achieved
- Response times
- Error rates
- User conversion (free → paid)

## Support

For startup edition support:
- Documentation: Available in frontend
- Email: <EMAIL>
- Community: Join our startup Slack channel

## Migration Path

Startups can upgrade to Enterprise Edition when they need:
- Advanced AI governance
- Multi-agent workflows
- Custom integrations
- Dedicated support
- SLA guarantees
