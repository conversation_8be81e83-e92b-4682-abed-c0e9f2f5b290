
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-standard
  labels:
    app: frontend-standard
    edition: standard
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend-standard
  template:
    metadata:
      labels:
        app: frontend-standard
        edition: standard
    spec:
      containers:
      - name: frontend
        image: ${FRONTEND_IMAGE}
        ports:
        - containerPort: 80
        env:
        # Standard Edition specific environment variables
        - name: REACT_APP_EDITION
          value: "standard"
        - name: REACT_APP_FEATURES
          value: "basic"
        - name: VITE_API_BASE_URL
          value: "/standard"
        - name: REACT_APP_API_BASE_URL
          value: "/standard/api"
        - name: REACT_APP_DASHBOARD_API_URL
          value: "/standard/dashboard"
        - name: REACT_APP_DISABLE_ENTERPRISE_TABS
          value: "true"
        - name: REACT_APP_ENABLE_FREEMIUM
          value: "true"
        - name: REACT_APP_PRODUCT_NAME
          value: "AI Operations Hub - Standard Edition"
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: frontend-standard
  labels:
    app: frontend-standard
    edition: standard
spec:
  selector:
    app: frontend-standard
  ports:
  - name: http
    port: 80
    targetPort: 80
  type: ClusterIP

# Ingress configuration removed - handled by existing api-rewrite-ingress in default namespace
