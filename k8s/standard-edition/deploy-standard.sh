#!/bin/bash

# AI Operations Hub - Startup Edition Deployment Script
# Deploys only core services needed for startup/SMB market

set -e

echo "🚀 Deploying AI Operations Hub - Standard Edition"
echo "================================================="

# Create standard namespace if it doesn't exist
echo "📦 Creating standard namespace..."
kubectl create namespace standard-edition --dry-run=client -o yaml | kubectl apply -f -

# Deploy Authentication Service first (required by other services)
echo "🔐 Deploying Authentication Service..."
kubectl apply -f k8s/standard-edition/auth-service-standard.yaml

# Wait for Auth Service to be ready
echo "⏳ Waiting for Authentication Service to be ready..."
if ! kubectl wait --for=condition=Available deployment/auth-service -n standard-edition --timeout=300s; then
    echo "⚠️  Authentication Service readiness check failed, but continuing deployment..."
    echo "   Auth Service may still be starting up. Checking pod status:"
    kubectl get pods -l app=auth-service -n standard-edition
fi

# Deploy Redis (required for caching and session management)
echo "🔴 Deploying Redis..."
kubectl apply -f k8s/redis/redis-k8s.yaml -n standard-edition

# Wait for Redis to be ready (with better error handling)
echo "⏳ Waiting for Redis to be ready..."
if ! kubectl wait --for=condition=ready pod -l app=redis -n standard-edition --timeout=300s; then
    echo "⚠️  Redis readiness check failed, but continuing deployment..."
    echo "   Redis may still be starting up. Checking pod status:"
    kubectl get pods -l app=redis -n standard-edition
fi

# Deploy Redis Populator Job for Standard Edition
echo "🔄 Running Redis populator for standard edition..."
kubectl apply -f k8s/standard-edition/redis-populator-standard.yaml
echo "⏳ Waiting for Redis populator to complete..."
if ! kubectl wait --for=condition=Complete job/redis-populator-standard -n standard-edition --timeout=120s; then
    echo "⚠️  Redis populator may still be running. Checking job status:"
    kubectl get job redis-populator-standard -n standard-edition
    kubectl logs job/redis-populator-standard -n standard-edition --tail=10
fi

# Create LLM API keys secret if it doesn't exist
echo "🔑 Creating LLM API keys secret..."
if ! kubectl get secret llm-api-keys -n standard-edition >/dev/null 2>&1; then
    echo "   Creating placeholder secret (update with real API keys later)..."
    kubectl create secret generic llm-api-keys \
        --from-literal=openai-api-key="your-openai-api-key-here" \
        --from-literal=google-api-key="your-google-api-key-here" \
        --from-literal=anthropic-api-key="your-anthropic-api-key-here" \
        --from-literal=cohere-api-key="your-cohere-api-key-here" \
        --from-literal=huggingface-api-key="your-huggingface-api-key-here" \
        --from-literal=mistral-api-key="your-mistral-api-key-here" \
        --from-literal=grok-api-key="your-grok-api-key-here" \
        -n standard-edition
else
    echo "   Secret already exists, skipping creation."
fi

# Deploy core services with startup-specific configurations
echo "🔧 Deploying core services..."

# Deploy Proxy Gateway (with freemium limits)
echo "  → Deploying Proxy Gateway..."
envsubst < k8s/standard-edition/proxy-gateway-standard.yaml | kubectl apply -f - -n standard-edition

# Deploy AI Optimizer (simplified)
echo "  → Deploying AI Optimizer..."
envsubst < k8s/standard-edition/ai-optimizer-standard.yaml | kubectl apply -f - -n standard-edition

# Deploy Policy Manager (for prompts and model profiles)
echo "  → Deploying Policy Manager..."
envsubst < k8s/standard-edition/policy-manager-standard.yaml | kubectl apply -f - -n standard-edition

# Deploy Dashboard API (basic metrics only)
echo "  → Deploying Dashboard API..."
envsubst < k8s/standard-edition/dashboard-api-standard.yaml | kubectl apply -f - -n standard-edition

# Deploy Frontend (simplified no-code interface)
echo "  → Deploying Frontend..."
envsubst < k8s/standard-edition/frontend-standard.yaml | kubectl apply -f - -n standard-edition

# Apply the standard edition ingress and proxy services
echo "  → Configuring proxy services for cross-namespace communication..."
kubectl apply -f k8s/standard-edition-proxy-services-fixed.yaml

echo "  → Configuring API ingress rules..."
kubectl apply -f k8s/standard-edition-api-ingress.yaml

echo "  → Configuring frontend ingress for static assets..."
kubectl apply -f k8s/standard-edition-frontend-ingress.yaml

echo "  → Configuring main ingress for enterprise routing..."
kubectl apply -f k8s/unified-ingress.yaml

# Wait for all deployments to be ready (with better error handling)
echo "⏳ Waiting for all services to be ready..."
if ! kubectl wait --for=condition=available deployment --all -n standard-edition --timeout=600s; then
    echo "⚠️  Some deployments may still be starting. Current status:"
    kubectl get deployments -n standard-edition
    echo ""
    echo "Pod status:"
    kubectl get pods -n standard-edition
    echo ""
    echo "Continuing with status check..."
fi

# Configure ingress for standard-only deployment
echo "🌐 Configuring ingress routes for standard edition..."
if [ -f "k8s/scripts/manage-ingress-routes.sh" ]; then
    ./k8s/scripts/manage-ingress-routes.sh standard
else
    echo "⚠️  Ingress management script not found, skipping..."
fi

# Get service status
echo "📊 Service Status:"
# Deploy Landing Page
echo "  → Deploying Landing Page..."
export LANDING_PAGE_IMAGE="${LANDING_PAGE_IMAGE:-$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/ai-cost-performance-optimizer-landing-page:latest}"
envsubst < k8s/landing-page/landing-page.yaml | kubectl apply -f - -n standard-edition

# Wait for landing page to be ready
echo "⏳ Waiting for landing page to be ready..."
kubectl wait --for=condition=ready pod -l app=landing-page -n standard-edition --timeout=120s

# Update external service mappings (after all services including landing page are deployed)
echo "🔗 Updating external service mappings..."
if [ -f "k8s/scripts/update-standard-external-services.sh" ]; then
    ./k8s/scripts/update-standard-external-services.sh
else
    echo "⚠️  External service update script not found, skipping..."
fi

# Skip Ingress Configuration (handled by existing api-rewrite-ingress in default namespace)
echo "  → Skipping Standard Edition Ingress (using existing api-rewrite-ingress)..."
echo "    Standard edition routes are already configured in default/api-rewrite-ingress"

kubectl get pods -n standard-edition
kubectl get services -n standard-edition
echo "  → Standard edition ingress routes (in default namespace):"
kubectl get ingress api-rewrite-ingress -n default

echo ""
echo "✅ Standard Edition deployment completed!"
echo ""
echo "🌐 Access your AI Operations Hub Standard Edition at:"
echo "   https://scale-llm.com/standard"
echo ""
echo "📋 Core Services Deployed:"
echo "   • Proxy Gateway (with usage limits)"
echo "   • AI Optimizer (basic routing)"
echo "   • Dashboard API (essential metrics)"
echo "   • Frontend (no-code interface)"
echo "   • Landing Page (marketing site)"
echo "   • Redis (caching & sessions)"
echo ""
echo "🔧 Next Steps:"
echo "   1. Update API keys: kubectl edit secret llm-api-keys -n standard-edition"
echo "   2. Test the deployment:"
echo "      - Frontend: https://scale-llm.com/standard"
echo "      - API Health: curl https://scale-llm.com/standard/api/health"
echo "      - Dashboard API: curl https://scale-llm.com/standard/dashboard/health"
echo "   3. Monitor services: kubectl get pods -n standard-edition"
echo ""
echo "🎯 Ready for startup and small business customers!"
