apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  namespace: standard-edition
  labels:
    app: auth-service
    edition: standard
spec:
  replicas: 1
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
        edition: standard
    spec:
      containers:
      - name: auth-service
        image: gcr.io/silken-zenith-460615/auth-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: PORT
          value: "8080"
        - name: CLICKHOUSE_HOST
          value: "clickhouse.default.svc.cluster.local"
        - name: CLICKHOUSE_PORT
          value: "9000"
        - name: CLICKHOUSE_DATABASE
          value: "default"
        - name: CLICKHOUSE_USERNAME
          value: "test"
        - name: CL<PERSON>KHOUSE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: clickhouse-secret
              key: password
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: google-oauth-secret
              key: client-id
        - name: GOOG<PERSON>_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: google-oauth-secret
              key: client-secret
        - name: GOOGLE_REDIRECT_URL
          value: "https://scale-llm.com/auth/callback"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        - name: ADMIN_EMAILS
          valueFrom:
            configMapKeyRef:
              name: auth-config
              key: admin-emails
        - name: FRONTEND_URL
          value: "https://scale-llm.com"
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: auth-service
  namespace: standard-edition
  labels:
    app: auth-service
    edition: standard
spec:
  selector:
    app: auth-service
  ports:
  - name: http
    port: 80
    targetPort: 8080
    protocol: TCP
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: auth-config
  namespace: standard-edition
data:
  admin-emails: "<EMAIL>,<EMAIL>"
---
apiVersion: v1
kind: Secret
metadata:
  name: google-oauth-secret
  namespace: standard-edition
type: Opaque
data:
  # Base64 encoded values - replace with your actual Google OAuth credentials
  client-id: ""
  client-secret: ""
---
apiVersion: v1
kind: Secret
metadata:
  name: jwt-secret
  namespace: standard-edition
type: Opaque
data:
  # Base64 encoded JWT secret - generate a strong random secret
  secret: ""
