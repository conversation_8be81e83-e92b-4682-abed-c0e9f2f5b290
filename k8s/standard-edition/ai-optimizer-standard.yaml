apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-optimizer-standard
  labels:
    app: ai-optimizer-standard
    edition: standard
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-optimizer-standard
  template:
    metadata:
      labels:
        app: ai-optimizer-standard
        edition: standard
    spec:
      containers:
      - name: ai-optimizer
        image: ${AI_OPTIMIZER_IMAGE}
        ports:
        - containerPort: 8085
        env:
        - name: PORT
          value: "8085"
        - name: REDIS_HOST
          value: "redis"
        - name: REDIS_PORT
          value: "6379"
        # Standard Edition specific settings
        - name: EDITION
          value: "standard"
        - name: SIMPLIFIED_ROUTING
          value: "true"
        - name: DISABLE_GOVERNANCE
          value: "true"
        - name: DISABLE_PLANNING
          value: "true"
        - name: BASIC_OPTIMIZATION_ONLY
          value: "true"
        # External LLM API Keys (from secrets)
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: openai-api-key
        - name: GOOGLE_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: google-api-key
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: anthropic-api-key
        - name: COHERE_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: cohere-api-key
        - name: HUGGINGFACE_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: huggingface-api-key
        - name: MISTRAL_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: mistral-api-key
        - name: GROK_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: grok-api-key
        # Standard edition configuration
        - name: EDITION
          value: "standard"
        # Remove enterprise service URLs
        - name: GOVERNANCE_SERVICE_URL
          value: ""
        - name: EVALUATION_SERVICE_URL
          value: ""
        - name: POLICY_MANAGER_URL
          value: ""
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8085
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8085
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: ai-optimizer-standard
  labels:
    app: ai-optimizer-standard
    edition: standard
spec:
  selector:
    app: ai-optimizer-standard
  ports:
  - name: http
    port: 8085
    targetPort: 8085
  type: ClusterIP
