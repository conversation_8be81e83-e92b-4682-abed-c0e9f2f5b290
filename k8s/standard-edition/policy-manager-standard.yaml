apiVersion: apps/v1
kind: Deployment
metadata:
  name: policy-manager-standard
  namespace: standard-edition
  labels:
    app: policy-manager-standard
    edition: standard
spec:
  replicas: 1
  selector:
    matchLabels:
      app: policy-manager-standard
  template:
    metadata:
      labels:
        app: policy-manager-standard
        edition: standard
    spec:
      initContainers:
      - name: wait-for-redis
        image: busybox:1.36
        imagePullPolicy: IfNotPresent
        command: ['sh', '-c', 'echo "Waiting for Redis..."; until nc -z redis 6379; do echo "Redis not ready, waiting..."; sleep 2; done; echo "Redis is ready."']
        resources:
          requests:
            memory: "10Mi"
            cpu: "10m"
          limits:
            memory: "20Mi"
            cpu: "20m"
      containers:
      - name: policy-manager
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-policy-manager:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8083
          name: http
        env:
        - name: REDIS_ADDR
          value: "redis:6379"
        - name: REDIS_PASSWORD
          value: ""
        - name: LISTEN_ADDR
          value: ":8083"
        # API Keys for external LLMs
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: openai-api-key
        - name: GOOGLE_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: google-api-key
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: anthropic-api-key
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "300m"
        readinessProbe:
          httpGet:
            path: /health
            port: 8083
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /health
            port: 8083
          initialDelaySeconds: 15
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: policy-manager
  namespace: standard-edition
  labels:
    app: policy-manager-standard
    edition: standard
spec:
  selector:
    app: policy-manager-standard
  ports:
  - name: http
    protocol: TCP
    port: 8083
    targetPort: 8083
  type: ClusterIP
