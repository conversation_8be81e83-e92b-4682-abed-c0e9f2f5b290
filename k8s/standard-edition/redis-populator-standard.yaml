apiVersion: batch/v1
kind: Job
metadata:
  name: redis-populator-standard
  namespace: standard-edition
  labels:
    app: redis-populator-standard
    component: data-initialization
    edition: standard
spec:
  template:
    metadata:
      labels:
        app: redis-populator-standard
        component: data-initialization
    spec:
      restartPolicy: OnFailure
      containers:
      - name: redis-populator
        image: redis:7-alpine
        command: ["/bin/sh"]
        args:
          - -c
          - |
            echo "Standard Edition Redis Populator - Initializing basic data..."
            
            # Wait for Redis to be available
            until redis-cli -h redis -p 6379 ping; do
              echo "Waiting for Redis to be ready..."
              sleep 2
            done
            
            echo "Redis is ready. Populating standard edition data..."
            
            # Create basic routing strategies for standard edition
            redis-cli -h redis -p 6379 SET "routing_strategy:cost_optimized" '{"id":"cost_optimized","name":"Cost Optimized","description":"Routes to the most cost-effective model","priority_factors":{"cost":0.7,"latency":0.2,"quality":0.1},"fallback_model":"gpt-3.5-turbo"}'
            
            redis-cli -h redis -p 6379 SET "routing_strategy:balanced" '{"id":"balanced","name":"Balanced","description":"Balances cost, speed, and quality","priority_factors":{"cost":0.4,"latency":0.3,"quality":0.3},"fallback_model":"gpt-4o-mini"}'
            
            redis-cli -h redis -p 6379 SET "routing_strategy:quality_first" '{"id":"quality_first","name":"Quality First","description":"Prioritizes response quality","priority_factors":{"cost":0.1,"latency":0.2,"quality":0.7},"fallback_model":"gpt-4o-mini"}'
            
            # Set default routing strategy
            redis-cli -h redis -p 6379 SET "default_routing_strategy" "balanced"
            
            # Create basic usage limits for standard edition
            redis-cli -h redis -p 6379 SET "usage_limits:standard" '{"daily_requests":1000,"monthly_cost_limit":50.0,"rate_limit_rpm":60,"concurrent_requests":5}'
            
            # Initialize basic metrics
            redis-cli -h redis -p 6379 SET "metrics:requests_today" "0"
            redis-cli -h redis -p 6379 SET "metrics:cost_today" "0.0"
            redis-cli -h redis -p 6379 SET "metrics:last_reset" "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
            
            # Create essential model profiles for standard edition
            echo "Creating model profiles..."

            # OpenAI GPT-4o Mini (cost-effective, good quality)
            redis-cli -h redis -p 6379 SET "model_profile:gpt-4o-mini" '{"id":"gpt-4o-mini","name":"GPT-4o Mini","backend_type":"openai","url":"https://api.openai.com/v1/chat/completions","expected_cost":0.00015,"expected_latency_ms":800,"capabilities":["chat","text-generation","reasoning"],"cost_per_input_token":0.00000015,"cost_per_output_token":0.0000006,"version":"gpt-4o-mini-2024-07-18","owner":"OpenAI","status":"active","provider":"OpenAI","description":"Cost-effective GPT-4 class model","tier":1}'

            # OpenAI GPT-3.5 Turbo (cheapest option)
            redis-cli -h redis -p 6379 SET "model_profile:gpt-3.5-turbo" '{"id":"gpt-3.5-turbo","name":"GPT-3.5 Turbo","backend_type":"openai","url":"https://api.openai.com/v1/chat/completions","expected_cost":0.0005,"expected_latency_ms":600,"capabilities":["chat","text-generation"],"cost_per_input_token":0.0000005,"cost_per_output_token":0.0000015,"version":"gpt-3.5-turbo-0125","owner":"OpenAI","status":"active","provider":"OpenAI","description":"Fast and cost-effective model","tier":1}'

            # Google Gemini Flash (good balance)
            redis-cli -h redis -p 6379 SET "model_profile:gemini-1.5-flash" '{"id":"gemini-1.5-flash","name":"Gemini 1.5 Flash","backend_type":"google","url":"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent","expected_cost":0.000075,"expected_latency_ms":700,"capabilities":["chat","text-generation","multimodal"],"cost_per_input_token":0.000000075,"cost_per_output_token":0.0000003,"version":"gemini-1.5-flash-001","owner":"Google","status":"active","provider":"Google","description":"Fast multimodal model","tier":1}'

            # Anthropic Claude Haiku (efficient)
            redis-cli -h redis -p 6379 SET "model_profile:claude-3-haiku" '{"id":"claude-3-haiku","name":"Claude 3 Haiku","backend_type":"anthropic","url":"https://api.anthropic.com/v1/messages","expected_cost":0.00025,"expected_latency_ms":750,"capabilities":["chat","text-generation","analysis"],"cost_per_input_token":0.00000025,"cost_per_output_token":0.00000125,"version":"claude-3-haiku-20240307","owner":"Anthropic","status":"active","provider":"Anthropic","description":"Fast and efficient Claude model","tier":1}'

            # Create essential prompt templates for standard edition
            echo "Creating prompt templates..."

            # Document Summarization Template
            redis-cli -h redis -p 6379 SET "prompt:summarization-template:1.0" '{"id":"summarization-template","name":"Document Summarization","version":"1.0","content":"Please provide a concise summary of the following document, highlighting the key points and main conclusions:\n\n{{document}}","description":"Template for summarizing documents and articles","tags":["summarization","template","document"],"owner":"system","status":"active","created_at":"2025-07-18T17:33:42.018384018Z","updated_at":"2025-07-18T17:33:42.018384127Z"}'

            # Code Review Template
            redis-cli -h redis -p 6379 SET "prompt:code-review-prompt:1.0" '{"id":"code-review-prompt","name":"Code Review Assistant","version":"1.0","content":"Please review the following code and provide feedback on:\n1. Code quality and best practices\n2. Potential bugs or issues\n3. Performance improvements\n4. Security considerations\n\nCode:\n```{{language}}\n{{code}}\n```","description":"Prompt for reviewing code and providing feedback","tags":["code-review","development","quality"],"owner":"system","status":"active","created_at":"2025-07-18T17:33:42.01838422Z","updated_at":"2025-07-18T17:33:42.018384314Z"}'

            # Customer Support Template
            redis-cli -h redis -p 6379 SET "prompt:customer-support-template:1.0" '{"id":"customer-support-template","name":"Customer Support Response","version":"1.0","content":"Generate a helpful and professional customer support response for the following inquiry:\n\nCustomer Message: {{customer_message}}\n\nContext: {{context}}\n\nPlease provide a solution-oriented response that is empathetic and addresses the customer'\''s concerns.","description":"Template for generating customer support responses","tags":["customer-support","template","communication"],"owner":"system","status":"active","created_at":"2025-07-18T17:33:42.018384405Z","updated_at":"2025-07-18T17:33:42.018384492Z"}'

            # Create basic policies for standard edition
            echo "Creating basic policies..."

            # Cost optimization policy
            redis-cli -h redis -p 6379 SET "policy:cost-optimization" '{"id":"cost-optimization","name":"Cost Optimization Policy","description":"Routes to cost-effective models for general tasks","criteria":{"task_type":"general"},"action":"ROUTE","backend_id":"gpt-4o-mini","priority":10,"status":"active","created_at":"2025-07-18T17:33:42.018384018Z","updated_at":"2025-07-18T17:33:42.018384127Z"}'

            # Create standard edition configuration
            redis-cli -h redis -p 6379 SET "config:edition" "standard"
            redis-cli -h redis -p 6379 SET "config:features" '{"ai_optimizer_scores":true,"intelligent_routing":true,"cost_optimization":true,"basic_analytics":true,"usage_limits":true,"enterprise_features":false}'

            echo "Standard edition Redis data populated successfully!"
            
            # Verify data was created
            echo "Verifying data..."
            redis-cli -h redis -p 6379 KEYS "*" | wc -l
            echo "Redis keys created."
            
            echo "Standard Edition Redis Populator completed successfully!"
        env:
        - name: REDIS_HOST
          value: "redis"
        - name: REDIS_PORT
          value: "6379"
        - name: EDITION
          value: "standard"
      # Add resource limits for standard edition
      resources:
        requests:
          memory: "64Mi"
          cpu: "50m"
        limits:
          memory: "128Mi"
          cpu: "100m"
