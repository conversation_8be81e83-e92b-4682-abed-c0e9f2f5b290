apiVersion: apps/v1
kind: Deployment
metadata:
  name: dashboard-api-standard
  labels:
    app: dashboard-api-standard
    edition: standard
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dashboard-api-standard
  template:
    metadata:
      labels:
        app: dashboard-api-standard
        edition: standard
    spec:
      containers:
      - name: dashboard-api
        image: ${DASHBOARD_API_IMAGE}
        ports:
        - containerPort: 8081
        env:
        - name: PORT
          value: "8081"
        - name: REDIS_HOST
          value: "redis"
        - name: REDIS_PORT
          value: "6379"
        # Standard Edition specific settings
        - name: EDITION
          value: "standard"
        - name: BASIC_METRICS_ONLY
          value: "true"
        - name: DISABLE_ADVANCED_ANALYTICS
          value: "true"
        - name: DISABLE_ENTERPRISE_FEATURES
          value: "true"
        # Remove ClickHouse dependency for startup edition
        - name: CLICKHOUSE_HOST
          value: ""
        - name: CLICKHOUSE_PORT
          value: ""
        - name: USE_REDIS_ONLY
          value: "true"
        # Remove enterprise service connections
        - name: GOVERNANCE_SERVICE_URL
          value: ""
        - name: EVALUATION_SERVICE_URL
          value: ""
        - name: PLA<PERSON>NING_SERVICE_URL
          value: ""
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "300m"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: dashboard-api-standard
  labels:
    app: dashboard-api-standard
    edition: standard
spec:
  selector:
    app: dashboard-api-standard
  ports:
  - name: http
    port: 8081
    targetPort: 8081
  type: ClusterIP

# Ingress configuration removed - handled by existing api-rewrite-ingress in default namespace
