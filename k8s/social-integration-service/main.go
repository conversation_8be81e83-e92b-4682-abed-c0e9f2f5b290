package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/gorilla/mux"
	"github.com/redis/go-redis/v9"
)

// SocialPlatform represents a social media platform
type SocialPlatform struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Type        string            `json:"type"` // twitter, facebook, instagram, linkedin, tiktok
	Enabled     bool              `json:"enabled"`
	Config      map[string]string `json:"config"`
	Credentials map[string]string `json:"credentials"`
	LastSync    time.Time         `json:"last_sync"`
	Status      string            `json:"status"` // active, error, disabled
}

// IntegrationPlatform represents business tool integrations
type IntegrationPlatform struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Type        string            `json:"type"` // crm, helpdesk, analytics, marketing
	Category    string            `json:"category"`
	Enabled     bool              `json:"enabled"`
	Config      map[string]string `json:"config"`
	Credentials map[string]string `json:"credentials"`
	LastSync    time.Time         `json:"last_sync"`
	Status      string            `json:"status"`
}

// SocialPost represents a social media post
type SocialPost struct {
	ID          string            `json:"id"`
	Platform    string            `json:"platform"`
	Content     string            `json:"content"`
	Author      string            `json:"author"`
	Timestamp   time.Time         `json:"timestamp"`
	Engagement  EngagementMetrics `json:"engagement"`
	Sentiment   SentimentData     `json:"sentiment"`
	Metadata    map[string]string `json:"metadata"`
}

// EngagementMetrics represents social media engagement data
type EngagementMetrics struct {
	Likes    int `json:"likes"`
	Shares   int `json:"shares"`
	Comments int `json:"comments"`
	Views    int `json:"views"`
	Clicks   int `json:"clicks"`
}

// SentimentData represents sentiment analysis results
type SentimentData struct {
	Positive   float64 `json:"positive"`
	Neutral    float64 `json:"neutral"`
	Negative   float64 `json:"negative"`
	Overall    string  `json:"overall"`
	Confidence float64 `json:"confidence"`
}

// IntegrationData represents data from business tool integrations
type IntegrationData struct {
	ID        string                 `json:"id"`
	Platform  string                 `json:"platform"`
	Type      string                 `json:"type"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
	Source    string                 `json:"source"`
}

// SocialIntegrationService handles social media and business integrations
type SocialIntegrationService struct {
	redisClient *redis.Client
	platforms   map[string]*SocialPlatform
	integrations map[string]*IntegrationPlatform
}

func main() {
	// Initialize Redis client
	redisClient := redis.NewClient(&redis.Options{
		Addr:     getEnv("REDIS_ADDR", "redis:6379"),
		Password: getEnv("REDIS_PASSWORD", ""),
		DB:       0,
	})

	// Test Redis connection
	ctx := context.Background()
	_, err := redisClient.Ping(ctx).Result()
	if err != nil {
		log.Printf("Warning: Redis connection failed: %v", err)
	}

	// Initialize service
	service := &SocialIntegrationService{
		redisClient:  redisClient,
		platforms:    make(map[string]*SocialPlatform),
		integrations: make(map[string]*IntegrationPlatform),
	}

	// Initialize default platforms and integrations
	service.initializeDefaultPlatforms()
	service.initializeDefaultIntegrations()

	// Set up HTTP routes
	router := mux.NewRouter()

	// Health check
	router.HandleFunc("/health", healthHandler).Methods("GET")

	// Social Media Platform Management
	router.HandleFunc("/platforms", service.listPlatformsHandler).Methods("GET")
	router.HandleFunc("/platforms/{id}", service.getPlatformHandler).Methods("GET")
	router.HandleFunc("/platforms/{id}", service.updatePlatformHandler).Methods("PUT")
	router.HandleFunc("/platforms/{id}/enable", service.enablePlatformHandler).Methods("POST")
	router.HandleFunc("/platforms/{id}/disable", service.disablePlatformHandler).Methods("POST")
	router.HandleFunc("/platforms/{id}/sync", service.syncPlatformHandler).Methods("POST")

	// Business Integration Management
	router.HandleFunc("/integrations", service.listIntegrationsHandler).Methods("GET")
	router.HandleFunc("/integrations/{id}", service.getIntegrationHandler).Methods("GET")
	router.HandleFunc("/integrations/{id}", service.updateIntegrationHandler).Methods("PUT")
	router.HandleFunc("/integrations/{id}/enable", service.enableIntegrationHandler).Methods("POST")
	router.HandleFunc("/integrations/{id}/disable", service.disableIntegrationHandler).Methods("POST")
	router.HandleFunc("/integrations/{id}/sync", service.syncIntegrationHandler).Methods("POST")

	// Data Collection and Analysis
	router.HandleFunc("/social/posts", service.getSocialPostsHandler).Methods("GET")
	router.HandleFunc("/social/analytics", service.getSocialAnalyticsHandler).Methods("GET")
	router.HandleFunc("/social/mentions", service.getMentionsHandler).Methods("GET")
	router.HandleFunc("/social/trends", service.getTrendsHandler).Methods("GET")

	// Integration Data
	router.HandleFunc("/data/{platform}", service.getIntegrationDataHandler).Methods("GET")
	router.HandleFunc("/data/sync", service.syncAllDataHandler).Methods("POST")

	// Webhook endpoints for real-time updates
	router.HandleFunc("/webhooks/{platform}", service.webhookHandler).Methods("POST")

	// Start background sync processes
	go service.startBackgroundSync()

	// Start the HTTP server
	port := getEnv("PORT", "8089")
	fmt.Printf("Social Integration Service listening on port %s...\n", port)
	log.Fatal(http.ListenAndServe(":"+port, router))
}

func healthHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":    "healthy",
		"service":   "social-integration-service",
		"timestamp": time.Now().UTC(),
		"version":   "1.0.0",
	})
}

// initializeDefaultPlatforms sets up default social media platforms
func (s *SocialIntegrationService) initializeDefaultPlatforms() {
	platforms := []*SocialPlatform{
		{
			ID:      "twitter",
			Name:    "Twitter/X",
			Type:    "twitter",
			Enabled: false,
			Config: map[string]string{
				"api_version": "v2",
				"rate_limit":  "300",
			},
			Status: "disabled",
		},
		{
			ID:      "facebook",
			Name:    "Facebook",
			Type:    "facebook",
			Enabled: false,
			Config: map[string]string{
				"api_version": "v18.0",
				"rate_limit":  "200",
			},
			Status: "disabled",
		},
		{
			ID:      "instagram",
			Name:    "Instagram",
			Type:    "instagram",
			Enabled: false,
			Config: map[string]string{
				"api_version": "v18.0",
				"rate_limit":  "200",
			},
			Status: "disabled",
		},
		{
			ID:      "linkedin",
			Name:    "LinkedIn",
			Type:    "linkedin",
			Enabled: false,
			Config: map[string]string{
				"api_version": "v2",
				"rate_limit":  "100",
			},
			Status: "disabled",
		},
		{
			ID:      "tiktok",
			Name:    "TikTok",
			Type:    "tiktok",
			Enabled: false,
			Config: map[string]string{
				"api_version": "v1",
				"rate_limit":  "100",
			},
			Status: "disabled",
		},
	}

	for _, platform := range platforms {
		s.platforms[platform.ID] = platform
	}
}

// initializeDefaultIntegrations sets up default business tool integrations
func (s *SocialIntegrationService) initializeDefaultIntegrations() {
	integrations := []*IntegrationPlatform{
		{
			ID:       "salesforce",
			Name:     "Salesforce",
			Type:     "crm",
			Category: "Customer Relationship Management",
			Enabled:  false,
			Config: map[string]string{
				"api_version": "v58.0",
				"sync_interval": "300",
			},
			Status: "disabled",
		},
		{
			ID:       "hubspot",
			Name:     "HubSpot",
			Type:     "crm",
			Category: "Customer Relationship Management",
			Enabled:  false,
			Config: map[string]string{
				"api_version": "v3",
				"sync_interval": "300",
			},
			Status: "disabled",
		},
		{
			ID:       "zendesk",
			Name:     "Zendesk",
			Type:     "helpdesk",
			Category: "Customer Support",
			Enabled:  false,
			Config: map[string]string{
				"api_version": "v2",
				"sync_interval": "180",
			},
			Status: "disabled",
		},
		{
			ID:       "intercom",
			Name:     "Intercom",
			Type:     "helpdesk",
			Category: "Customer Support",
			Enabled:  false,
			Config: map[string]string{
				"api_version": "2.9",
				"sync_interval": "180",
			},
			Status: "disabled",
		},
		{
			ID:       "google_analytics",
			Name:     "Google Analytics",
			Type:     "analytics",
			Category: "Web Analytics",
			Enabled:  false,
			Config: map[string]string{
				"api_version": "v4",
				"sync_interval": "600",
			},
			Status: "disabled",
		},
		{
			ID:       "mailchimp",
			Name:     "Mailchimp",
			Type:     "marketing",
			Category: "Email Marketing",
			Enabled:  false,
			Config: map[string]string{
				"api_version": "3.0",
				"sync_interval": "900",
			},
			Status: "disabled",
		},
		{
			ID:       "slack",
			Name:     "Slack",
			Type:     "communication",
			Category: "Team Communication",
			Enabled:  false,
			Config: map[string]string{
				"api_version": "v1",
				"sync_interval": "60",
			},
			Status: "disabled",
		},
		{
			ID:       "notion",
			Name:     "Notion",
			Type:     "productivity",
			Category: "Knowledge Management",
			Enabled:  false,
			Config: map[string]string{
				"api_version": "2022-06-28",
				"sync_interval": "300",
			},
			Status: "disabled",
		},
	}

	for _, integration := range integrations {
		s.integrations[integration.ID] = integration
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
