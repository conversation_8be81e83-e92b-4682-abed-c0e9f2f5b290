package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gorilla/mux"
)

// listPlatformsHandler returns all social media platforms
func (s *SocialIntegrationService) listPlatformsHandler(w http.ResponseWriter, r *http.Request) {
	platforms := make([]*SocialPlatform, 0, len(s.platforms))
	for _, platform := range s.platforms {
		platforms = append(platforms, platform)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]any{
		"platforms": platforms,
		"total":     len(platforms),
	})
}

// getPlatformHandler returns a specific platform
func (s *SocialIntegrationService) getPlatformHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	platformID := vars["id"]

	platform, exists := s.platforms[platformID]
	if !exists {
		http.Error(w, "Platform not found", http.StatusNotFound)
		return
	}

	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(platform)
}

// updatePlatformHandler updates platform configuration
func (s *SocialIntegrationService) updatePlatformHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	platformID := vars["id"]

	platform, exists := s.platforms[platformID]
	if !exists {
		http.Error(w, "Platform not found", http.StatusNotFound)
		return
	}

	var updateData map[string]any
	if err := json.NewDecoder(r.Body).Decode(&updateData); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Update platform configuration
	if config, ok := updateData["config"].(map[string]any); ok {
		for key, value := range config {
			if strValue, ok := value.(string); ok {
				platform.Config[key] = strValue
			}
		}
	}

	// Update credentials
	if credentials, ok := updateData["credentials"].(map[string]any); ok {
		if platform.Credentials == nil {
			platform.Credentials = make(map[string]string)
		}
		for key, value := range credentials {
			if strValue, ok := value.(string); ok {
				platform.Credentials[key] = strValue
			}
		}
	}

	// Save to Redis
	s.savePlatformToRedis(r.Context(), platform)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(platform)
}

// enablePlatformHandler enables a social media platform
func (s *SocialIntegrationService) enablePlatformHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	platformID := vars["id"]

	platform, exists := s.platforms[platformID]
	if !exists {
		http.Error(w, "Platform not found", http.StatusNotFound)
		return
	}

	// Validate credentials before enabling
	if err := s.validatePlatformCredentials(platform); err != nil {
		http.Error(w, fmt.Sprintf("Invalid credentials: %v", err), http.StatusBadRequest)
		return
	}

	platform.Enabled = true
	platform.Status = "active"
	platform.LastSync = time.Now().UTC()

	// Save to Redis
	s.savePlatformToRedis(r.Context(), platform)

	// Start monitoring for this platform
	go s.startPlatformMonitoring(platform)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"status": "enabled"})
}

// disablePlatformHandler disables a social media platform
func (s *SocialIntegrationService) disablePlatformHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	platformID := vars["id"]

	platform, exists := s.platforms[platformID]
	if !exists {
		http.Error(w, "Platform not found", http.StatusNotFound)
		return
	}

	platform.Enabled = false
	platform.Status = "disabled"

	// Save to Redis
	s.savePlatformToRedis(r.Context(), platform)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"status": "disabled"})
}

// syncPlatformHandler manually syncs data from a platform
func (s *SocialIntegrationService) syncPlatformHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	platformID := vars["id"]

	platform, exists := s.platforms[platformID]
	if !exists {
		http.Error(w, "Platform not found", http.StatusNotFound)
		return
	}

	if !platform.Enabled {
		http.Error(w, "Platform is not enabled", http.StatusBadRequest)
		return
	}

	// Perform sync
	syncResult, err := s.syncPlatformData(r.Context(), platform)
	if err != nil {
		http.Error(w, fmt.Sprintf("Sync failed: %v", err), http.StatusInternalServerError)
		return
	}

	platform.LastSync = time.Now().UTC()
	s.savePlatformToRedis(r.Context(), platform)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(syncResult)
}

// listIntegrationsHandler returns all business integrations
func (s *SocialIntegrationService) listIntegrationsHandler(w http.ResponseWriter, r *http.Request) {
	integrations := make([]*IntegrationPlatform, 0, len(s.integrations))
	for _, integration := range s.integrations {
		integrations = append(integrations, integration)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]any{
		"integrations": integrations,
		"total":        len(integrations),
	})
}

// getIntegrationHandler returns a specific integration
func (s *SocialIntegrationService) getIntegrationHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	integrationID := vars["id"]

	integration, exists := s.integrations[integrationID]
	if !exists {
		http.Error(w, "Integration not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(integration)
}

// updateIntegrationHandler updates integration configuration
func (s *SocialIntegrationService) updateIntegrationHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	integrationID := vars["id"]

	integration, exists := s.integrations[integrationID]
	if !exists {
		http.Error(w, "Integration not found", http.StatusNotFound)
		return
	}

	var updateData map[string]any
	if err := json.NewDecoder(r.Body).Decode(&updateData); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Update integration configuration
	if config, ok := updateData["config"].(map[string]any); ok {
		for key, value := range config {
			if strValue, ok := value.(string); ok {
				integration.Config[key] = strValue
			}
		}
	}

	// Update credentials
	if credentials, ok := updateData["credentials"].(map[string]any); ok {
		if integration.Credentials == nil {
			integration.Credentials = make(map[string]string)
		}
		for key, value := range credentials {
			if strValue, ok := value.(string); ok {
				integration.Credentials[key] = strValue
			}
		}
	}

	// Save to Redis
	s.saveIntegrationToRedis(r.Context(), integration)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(integration)
}

// enableIntegrationHandler enables a business integration
func (s *SocialIntegrationService) enableIntegrationHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	integrationID := vars["id"]

	integration, exists := s.integrations[integrationID]
	if !exists {
		http.Error(w, "Integration not found", http.StatusNotFound)
		return
	}

	// Validate credentials before enabling
	if err := s.validateIntegrationCredentials(integration); err != nil {
		http.Error(w, fmt.Sprintf("Invalid credentials: %v", err), http.StatusBadRequest)
		return
	}

	integration.Enabled = true
	integration.Status = "active"
	integration.LastSync = time.Now().UTC()

	// Save to Redis
	s.saveIntegrationToRedis(r.Context(), integration)

	// Start monitoring for this integration
	go s.startIntegrationMonitoring(integration)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"status": "enabled"})
}

// disableIntegrationHandler disables a business integration
func (s *SocialIntegrationService) disableIntegrationHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	integrationID := vars["id"]

	integration, exists := s.integrations[integrationID]
	if !exists {
		http.Error(w, "Integration not found", http.StatusNotFound)
		return
	}

	integration.Enabled = false
	integration.Status = "disabled"

	// Save to Redis
	s.saveIntegrationToRedis(r.Context(), integration)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"status": "disabled"})
}

// syncIntegrationHandler manually syncs data from an integration
func (s *SocialIntegrationService) syncIntegrationHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	integrationID := vars["id"]

	integration, exists := s.integrations[integrationID]
	if !exists {
		http.Error(w, "Integration not found", http.StatusNotFound)
		return
	}

	if !integration.Enabled {
		http.Error(w, "Integration is not enabled", http.StatusBadRequest)
		return
	}

	// Perform sync
	syncResult, err := s.syncIntegrationData(r.Context(), integration)
	if err != nil {
		http.Error(w, fmt.Sprintf("Sync failed: %v", err), http.StatusInternalServerError)
		return
	}

	integration.LastSync = time.Now().UTC()
	s.saveIntegrationToRedis(r.Context(), integration)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(syncResult)
}

// getSocialPostsHandler returns social media posts
func (s *SocialIntegrationService) getSocialPostsHandler(w http.ResponseWriter, r *http.Request) {
	platform := r.URL.Query().Get("platform")
	limitStr := r.URL.Query().Get("limit")

	limit := 50
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil {
			limit = l
		}
	}

	posts, err := s.getSocialPosts(r.Context(), platform, limit)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get posts: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]any{
		"posts": posts,
		"total": len(posts),
	})
}

// getSocialAnalyticsHandler returns social media analytics
func (s *SocialIntegrationService) getSocialAnalyticsHandler(w http.ResponseWriter, r *http.Request) {
	platform := r.URL.Query().Get("platform")
	period := r.URL.Query().Get("period")

	if period == "" {
		period = "7d"
	}

	analytics, err := s.getSocialAnalytics(r.Context(), platform, period)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get analytics: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(analytics)
}

// getMentionsHandler returns brand mentions across platforms
func (s *SocialIntegrationService) getMentionsHandler(w http.ResponseWriter, r *http.Request) {
	keyword := r.URL.Query().Get("keyword")
	if keyword == "" {
		keyword = "AI Operations Hub"
	}

	mentions, err := s.getBrandMentions(r.Context(), keyword)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get mentions: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]any{
		"mentions": mentions,
		"keyword":  keyword,
		"total":    len(mentions),
	})
}

// getTrendsHandler returns trending topics and hashtags
func (s *SocialIntegrationService) getTrendsHandler(w http.ResponseWriter, r *http.Request) {
	platform := r.URL.Query().Get("platform")

	trends, err := s.getTrendingTopics(r.Context(), platform)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get trends: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]any{
		"trends":   trends,
		"platform": platform,
	})
}

// getIntegrationDataHandler returns data from business integrations
func (s *SocialIntegrationService) getIntegrationDataHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	platform := vars["platform"]

	data, err := s.getIntegrationData(r.Context(), platform)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get integration data: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(data)
}

// syncAllDataHandler syncs data from all enabled platforms and integrations
func (s *SocialIntegrationService) syncAllDataHandler(w http.ResponseWriter, r *http.Request) {
	results := make(map[string]any)

	// Sync social platforms
	for _, platform := range s.platforms {
		if platform.Enabled {
			result, err := s.syncPlatformData(r.Context(), platform)
			if err != nil {
				results[platform.ID] = map[string]string{"error": err.Error()}
			} else {
				results[platform.ID] = result
			}
		}
	}

	// Sync business integrations
	for _, integration := range s.integrations {
		if integration.Enabled {
			result, err := s.syncIntegrationData(r.Context(), integration)
			if err != nil {
				results[integration.ID] = map[string]string{"error": err.Error()}
			} else {
				results[integration.ID] = result
			}
		}
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]any{
		"sync_results": results,
		"timestamp":    time.Now().UTC(),
	})
}

// webhookHandler handles incoming webhooks from platforms
func (s *SocialIntegrationService) webhookHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	platform := vars["platform"]

	var webhookData map[string]any
	if err := json.NewDecoder(r.Body).Decode(&webhookData); err != nil {
		http.Error(w, "Invalid webhook data", http.StatusBadRequest)
		return
	}

	// Process webhook data
	err := s.processWebhookData(r.Context(), platform, webhookData)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to process webhook: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"status": "processed"})
}
