# Standard Edition API Ingress with proper path rewriting
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: standard-edition-api-ingress
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /api/$1$2$3
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Standard Edition Policy Manager API Routes - rewrite /standard/api/X to /api/X
      - path: /standard/api/(prompts|policies|model-profiles)(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: policy-manager-standard-proxy-fixed
            port:
              number: 8083

---
# Standard Edition Dashboard API Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: standard-edition-dashboard-ingress
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Standard Edition Dashboard API - rewrite /standard/dashboard/X to /X
      - path: /standard/dashboard(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-proxy-fixed
            port:
              number: 8081

---
# Standard Edition Fallback API Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: standard-edition-fallback-ingress
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Standard Edition Enterprise-only API fallbacks
      - path: /standard/api/(integration|planning|evaluation|multi-agent)(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: api-fallback-standard-proxy-fixed
            port:
              number: 8080

---
# Standard Edition General API Ingress (proxy-gateway)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: standard-edition-general-api-ingress
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Standard Edition General API (proxy-gateway) - MUST be last, catches remaining /api calls
      - path: /standard/api(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: proxy-gateway-standard-proxy-fixed
            port:
              number: 8080
