package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/gorilla/mux"
)

// --- Constants ---
const (
	redisAddr                       = "redis:6379"
	REDIS_POLICIES_KEY_PREFIX       = "policy:"
	REDIS_MODEL_PROFILES_KEY_PREFIX = "model_profile:"
	REDIS_PROMPT_KEY_PREFIX         = "prompt:"      // Redis key prefix for prompts
	REDIS_AB_TEST_KEY_PREFIX        = "ab_test:"     // Redis key prefix for A/B tests
	REDIS_PERFORMANCE_KEY_PREFIX    = "performance:" // Redis key prefix for performance metrics
	POLICY_UPDATES_CHANNEL          = "policy_updates"
	MODEL_PROFILE_UPDATES_CHANNEL   = "model_profile_updates"
	PROMPT_UPDATES_CHANNEL          = "prompt_updates" // Channel for prompt updates
)

// --- Structs ---

// Policy defines a routing policy.
// It's crucial for the AI Optimizer to enforce governance.
// ENHANCED for RBAC: Added Effect and Subjects fields.
type Policy struct {
	ID          string          `json:"id"`
	Name        string          `json:"name"`
	Description string          `json:"description"`
	Criteria    json.RawMessage `json:"criteria"`             // JSON string for specific criteria (e.g., {"user_id": "abc"}, {"model_id": "gpt-4"})
	Action      string          `json:"action"`               // "ROUTE", "OPTIMIZE", "BLOCK", "ALLOW_ACCESS", "DENY_ACCESS", etc.
	BackendID   string          `json:"backend_id,omitempty"` // Only if Action is ROUTE or OPTIMIZE
	Priority    int             `json:"priority"`             // Lower number means higher priority
	Rules       json.RawMessage `json:"rules,omitempty"`      // For more complex routing rules (could be a more structured type)
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
	Metadata    json.RawMessage `json:"metadata,omitempty"`   // Generic field for additional data
	RateLimit   int             `json:"rate_limit,omitempty"` // Requests per minute
	Budget      float64         `json:"budget,omitempty"`     // Max cost per time period

	Effect       string   `json:"effect,omitempty"`        // "ALLOW" or "DENY"
	Subjects     []string `json:"subjects,omitempty"`      // List of user IDs, role IDs, or group IDs
	ResourceType string   `json:"resource_type,omitempty"` // e.g., "model", "feature", "log"
	ResourceIDs  []string `json:"resource_ids,omitempty"`  // List of specific resource IDs (e.g., model IDs)
	Permissions  []string `json:"permissions,omitempty"`   // List of actions (e.g., "read", "write", "execute", "access_llm")
}

// RoutingStrategy defines how requests should be routed
type RoutingStrategy struct {
	ID                string                     `json:"id"`
	Name              string                     `json:"name"`
	Strategy          string                     `json:"strategy"` // ROUTING_STRATEGY_* constants
	ModelRequirements ModelCapabilityRequirement `json:"model_requirements,omitempty"`
	ModelPriorities   []string                   `json:"model_priorities,omitempty"`  // For cascade routing
	ParallelModels    []string                   `json:"parallel_models,omitempty"`   // For parallel routing
	ComparisonMethod  string                     `json:"comparison_method,omitempty"` // For parallel routing
	EnableFallback    bool                       `json:"enable_fallback"`             // Whether to use fallback routing
	FallbackModelID   string                     `json:"fallback_model_id,omitempty"` // Specific fallback model ID
	PolicyID          string                     `json:"policy_id,omitempty"`
	Priority          int                        `json:"priority"`
	TaskType          string                     `json:"task_type,omitempty"`
	Permissions       []string                   `json:"permissions,omitempty"` // List of actions (e.g., "read", "write", "execute", "access_llm")
}

// ModelCapabilityRequirement defines what capabilities are needed for a specific task
type ModelCapabilityRequirement struct {
	RequiredCapabilities []string `json:"required_capabilities"`
	MinimumTier          int      `json:"minimum_tier"`
	PreferredProvider    string   `json:"preferred_provider,omitempty"`
}

// ModelProfile defines the structure for an LLM model's profile, including its cost and performance characteristics.
// This struct is enhanced to serve as the Centralized LLM Registry.
type ModelProfile struct {
	ID                 string    `json:"id"`
	Name               string    `json:"name"`
	Aliases            []string  `json:"aliases"`
	Capabilities       []string  `json:"capabilities"` // Legacy field for backward compatibility
	PricingTier        string    `json:"pricing_tier"`
	DataSensitivity    string    `json:"data_sensitivity"`
	ExpectedLatencyMs  float64   `json:"expected_latency_ms"`
	ExpectedCost       float64   `json:"expected_cost"`
	BackendURL         string    `json:"url"` // *** FIX: Changed to "url" for consistency with other services and populate_redis.py ***
	BackendType        string    `json:"backend_type"`
	CostPerInputToken  float64   `json:"cost_per_input_token"`
	CostPerOutputToken float64   `json:"cost_per_output_token"`
	CPUCostPerHour     float64   `json:"cpu_cost_per_hour"`
	MemoryCostPerHour  float64   `json:"memory_cost_per_hour"`
	APIKey             string    `json:"api_key,omitempty"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`

	// Enhanced capability scoring system
	CapabilityScores map[string]float64 `json:"capability_scores"`  // Task type -> capability score (0-1)
	OptimalUseCases  []string           `json:"optimal_use_cases"`  // List of tasks this model excels at
	Strengths        []string           `json:"strengths"`          // Model's key strengths
	Weaknesses       []string           `json:"weaknesses"`         // Model's limitations
	MaxContextLength int                `json:"max_context_length"` // Maximum context window
	QualityScore     float64            `json:"quality_score"`      // Overall quality rating (0-1)
	RobustnessScore  float64            `json:"robustness_score"`   // Reliability and consistency (0-1)
	SafetyScore      float64            `json:"safety_score"`       // Safety and alignment rating (0-1)

	Version             string          `json:"version,omitempty"`
	Owner               string          `json:"owner,omitempty"`
	Status              string          `json:"status,omitempty"`
	DocumentationURL    string          `json:"documentation_url,omitempty"`
	License             string          `json:"license,omitempty"`
	FineTuningDetails   string          `json:"fine_tuning_details,omitempty"`
	InputContextLength  int             `json:"input_context_length,omitempty"`
	OutputContextLength int             `json:"output_context_length,omitempty"`
	TrainingDataInfo    string          `json:"training_data_info,omitempty"`
	LastEvaluatedAt     time.Time       `json:"last_evaluated_at,omitempty"`
	EvaluationMetrics   json.RawMessage `json:"evaluation_metrics,omitempty"`
	ComplianceTags      []string        `json:"compliance_tags,omitempty"`
	Region              string          `json:"region,omitempty"`
	Provider            string          `json:"provider,omitempty"`
	Tier                int             `json:"tier"`
}

// Prompt defines the structure for a managed LLM prompt with enhanced versioning.
type Prompt struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Version     string                 `json:"version"` // Semantic version: "1.0.0", "2.1.3"
	Content     string                 `json:"content"` // The actual prompt text
	Description string                 `json:"description,omitempty"`
	Tags        []string               `json:"tags,omitempty"`     // e.g., "stable", "experimental", "production"
	Owner       string                 `json:"owner,omitempty"`    // Creator or responsible team
	Status      string                 `json:"status,omitempty"`   // "draft", "active", "deprecated", "archived"
	Metadata    map[string]interface{} `json:"metadata,omitempty"` // Enhanced metadata
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`

	// Enhanced versioning fields
	ParentVersion   string             `json:"parent_version,omitempty"`   // Previous version for diff tracking
	ChangeLog       string             `json:"change_log,omitempty"`       // Description of changes
	ModelTargets    []string           `json:"model_targets,omitempty"`    // Compatible models
	TokenCount      int                `json:"token_count,omitempty"`      // Estimated token count
	Variables       []PromptVariable   `json:"variables,omitempty"`        // Template variables
	TestCases       []PromptTestCase   `json:"test_cases,omitempty"`       // Associated test cases
	PerformanceData *PromptPerformance `json:"performance_data,omitempty"` // Performance metrics
}

// PromptVariable defines template variables in prompts
type PromptVariable struct {
	Name         string      `json:"name"`
	Type         string      `json:"type"` // "string", "number", "boolean", "array", "object"
	Description  string      `json:"description,omitempty"`
	Required     bool        `json:"required"`
	DefaultValue interface{} `json:"default_value,omitempty"`
	Validation   string      `json:"validation,omitempty"` // Regex or validation rule
}

// PromptTestCase defines test cases for prompt validation
type PromptTestCase struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	Description  string                 `json:"description,omitempty"`
	InputData    map[string]interface{} `json:"input_data"`
	ExpectedType string                 `json:"expected_type"` // "contains", "exact", "regex", "custom"
	Expected     string                 `json:"expected"`
	ModelTargets []string               `json:"model_targets,omitempty"`
	CreatedAt    time.Time              `json:"created_at"`
}

// PromptPerformance tracks performance metrics for prompts (enhanced version)
type PromptPerformance struct {
	PromptID        string    `json:"prompt_id"`
	Version         string    `json:"version"`
	TotalExecutions int64     `json:"total_executions"`
	SuccessRate     float64   `json:"success_rate"`
	AvgLatency      float64   `json:"avg_latency_ms"`
	AvgCost         float64   `json:"avg_cost"`
	AvgTokens       int       `json:"avg_tokens"`
	QualityScore    float64   `json:"quality_score"`
	LastUsed        time.Time `json:"last_used"`
	LastUpdated     time.Time `json:"last_updated"`
}

// PromptABTest defines the structure for A/B testing prompts
type PromptABTest struct {
	ID            string     `json:"id"`
	Name          string     `json:"name"`
	PromptAID     string     `json:"prompt_a_id"`
	PromptBID     string     `json:"prompt_b_id"`
	TrafficSplit  int        `json:"traffic_split"` // Percentage for variant B (0-100)
	Status        string     `json:"status"`        // "running", "paused", "completed"
	StartDate     time.Time  `json:"start_date"`
	EndDate       *time.Time `json:"end_date,omitempty"`
	VariantAScore float64    `json:"variant_a_score"`
	VariantBScore float64    `json:"variant_b_score"`
	VariantAUsage int64      `json:"variant_a_usage"`
	VariantBUsage int64      `json:"variant_b_usage"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
}

// NOTE: PromptPerformance type moved above to avoid duplication

// --- Global Variables ---
var (
	redisClient       *redis.Client
	policies          map[string]Policy
	modelProfiles     map[string]ModelProfile
	prompts           map[string]Prompt            // Cache for managed prompts
	promptABTests     map[string]PromptABTest      // A/B tests storage
	promptPerformance map[string]PromptPerformance // Performance metrics storage
	mu                sync.RWMutex                 // Mutex for protecting all maps
)

// init function runs once on startup to initialize connections and load initial data.
func init() {
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	// Initialize Redis client
	redisClient = redis.NewClient(&redis.Options{
		Addr: redisAddr,
		DB:   0, // Default DB
	})

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := redisClient.Ping(ctx).Result()
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	log.Println("Successfully connected to Redis.")

	// Initialize caches
	policies = make(map[string]Policy)
	modelProfiles = make(map[string]ModelProfile)
	prompts = make(map[string]Prompt)                      // Initialize prompts cache
	promptABTests = make(map[string]PromptABTest)          // Initialize A/B tests cache
	promptPerformance = make(map[string]PromptPerformance) // Initialize performance cache

	// Load initial data from Redis
	if err := loadPoliciesFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load policies from Redis on startup: %v", err)
	}
	if err := loadModelProfilesFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load model profiles from Redis on startup: %v", err)
	}
	// Load initial prompts
	if err := loadPromptsFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load prompts from Redis on startup: %v", err)
	}

	// Create sample prompts if none exist
	if len(prompts) == 0 {
		createSamplePrompts(ctx)
	}

	// Create sample model profiles with enhanced capabilities if none exist
	if len(modelProfiles) == 0 {
		createSampleModelProfiles(ctx)
	}

	// Create sample policies if none exist
	if len(policies) == 0 {
		createSamplePolicies(ctx)
	}

	// Start goroutines to listen for Redis Pub/Sub updates
	go listenForRedisPolicyUpdates()
	go listenForRedisModelProfileUpdates()
	go listenForRedisPromptUpdates() // Start listening for prompt updates
}

func main() {
	defer redisClient.Close()

	router := mux.NewRouter()

	// Add health check endpoint
	router.HandleFunc("/health", healthCheck).Methods("GET")

	// API prefix for all endpoints
	apiRouter := router.PathPrefix("/api").Subrouter()

	// Policies API Endpoints - with both underscore and hyphen versions
	apiRouter.HandleFunc("/policies", createPolicy).Methods("POST")
	apiRouter.HandleFunc("/policies/", createPolicy).Methods("POST") // With trailing slash
	apiRouter.HandleFunc("/policies", getPolicies).Methods("GET")
	apiRouter.HandleFunc("/policies/", getPolicies).Methods("GET") // With trailing slash
	apiRouter.HandleFunc("/policies/{id}", getPolicy).Methods("GET")
	apiRouter.HandleFunc("/policies/{id}/", getPolicy).Methods("GET") // With trailing slash
	apiRouter.HandleFunc("/policies/{id}", updatePolicy).Methods("PUT")
	apiRouter.HandleFunc("/policies/{id}/", updatePolicy).Methods("PUT") // With trailing slash
	apiRouter.HandleFunc("/policies/{id}", deletePolicy).Methods("DELETE")
	apiRouter.HandleFunc("/policies/{id}/", deletePolicy).Methods("DELETE") // With trailing slash

	// Model Profiles API Endpoints - with both underscore and hyphen versions
	apiRouter.HandleFunc("/model_profiles", createModelProfile).Methods("POST")
	apiRouter.HandleFunc("/model-profiles", createModelProfile).Methods("POST")  // Hyphenated version
	apiRouter.HandleFunc("/model_profiles/", createModelProfile).Methods("POST") // With trailing slash
	apiRouter.HandleFunc("/model-profiles/", createModelProfile).Methods("POST") // Hyphenated with trailing slash

	apiRouter.HandleFunc("/model_profiles", getModelProfiles).Methods("GET")
	apiRouter.HandleFunc("/model-profiles", getModelProfiles).Methods("GET")  // Hyphenated version
	apiRouter.HandleFunc("/model_profiles/", getModelProfiles).Methods("GET") // With trailing slash
	apiRouter.HandleFunc("/model-profiles/", getModelProfiles).Methods("GET") // Hyphenated with trailing slash

	apiRouter.HandleFunc("/model_profiles/{id}", getModelProfile).Methods("GET")
	apiRouter.HandleFunc("/model-profiles/{id}", getModelProfile).Methods("GET")  // Hyphenated version
	apiRouter.HandleFunc("/model_profiles/{id}/", getModelProfile).Methods("GET") // With trailing slash
	apiRouter.HandleFunc("/model-profiles/{id}/", getModelProfile).Methods("GET") // Hyphenated with trailing slash

	apiRouter.HandleFunc("/model_profiles/{id}", updateModelProfile).Methods("PUT")
	apiRouter.HandleFunc("/model-profiles/{id}", updateModelProfile).Methods("PUT")  // Hyphenated version
	apiRouter.HandleFunc("/model_profiles/{id}/", updateModelProfile).Methods("PUT") // With trailing slash
	apiRouter.HandleFunc("/model-profiles/{id}/", updateModelProfile).Methods("PUT") // Hyphenated with trailing slash

	apiRouter.HandleFunc("/model_profiles/{id}", deleteModelProfile).Methods("DELETE")
	apiRouter.HandleFunc("/model-profiles/{id}", deleteModelProfile).Methods("DELETE")  // Hyphenated version
	apiRouter.HandleFunc("/model_profiles/{id}/", deleteModelProfile).Methods("DELETE") // With trailing slash
	apiRouter.HandleFunc("/model-profiles/{id}/", deleteModelProfile).Methods("DELETE") // Hyphenated with trailing slash

	// Prompt Management API Endpoints with /api prefix
	// IMPORTANT: More specific routes must come before general ones!

	// NEW: Prompt A/B Testing Endpoints (must come before /prompts/{id})
	apiRouter.HandleFunc("/prompts/ab-tests", createABTest).Methods("POST")
	apiRouter.HandleFunc("/prompts/ab-tests", getABTests).Methods("GET")
	apiRouter.HandleFunc("/prompts/ab-tests/{id}", getABTest).Methods("GET")
	apiRouter.HandleFunc("/prompts/ab-tests/{id}", updateABTest).Methods("PUT")
	apiRouter.HandleFunc("/prompts/ab-tests/{id}", deleteABTest).Methods("DELETE")
	apiRouter.HandleFunc("/prompts/ab-tests/{id}/stop", stopABTest).Methods("POST")

	// NEW: Prompt Performance Endpoints (must come before /prompts/{id})
	apiRouter.HandleFunc("/prompts/performance", getPromptPerformance).Methods("GET")
	apiRouter.HandleFunc("/prompts/performance/{id}", getPromptPerformanceByID).Methods("GET")
	apiRouter.HandleFunc("/prompts/performance/{id}", updatePromptPerformance).Methods("PUT")
	apiRouter.HandleFunc("/prompts/analytics", getPromptAnalytics).Methods("GET")

	// ENHANCED: Prompt Versioning & Advanced Operations (must come before /prompts/{id})
	apiRouter.HandleFunc("/prompts/templates", getPromptTemplates).Methods("GET")
	apiRouter.HandleFunc("/prompts/variables/extract", extractPromptVariables).Methods("POST")

	// Basic Prompt CRUD operations
	apiRouter.HandleFunc("/prompts", createPrompt).Methods("POST")
	apiRouter.HandleFunc("/prompts/", createPrompt).Methods("POST") // With trailing slash
	apiRouter.HandleFunc("/prompts", getPrompts).Methods("GET")
	apiRouter.HandleFunc("/prompts/", getPrompts).Methods("GET") // With trailing slash

	// Prompt operations with {id} parameter (must come after specific routes)
	apiRouter.HandleFunc("/prompts/{id}/versions", getPromptVersions).Methods("GET")
	apiRouter.HandleFunc("/prompts/{id}/diff", getPromptDiff).Methods("GET")
	apiRouter.HandleFunc("/prompts/{id}/rollback", rollbackPrompt).Methods("POST")
	apiRouter.HandleFunc("/prompts/{id}/clone", clonePrompt).Methods("POST")
	apiRouter.HandleFunc("/prompts/{id}/test", testPrompt).Methods("POST")
	apiRouter.HandleFunc("/prompts/{id}/execute", executePrompt).Methods("POST")
	apiRouter.HandleFunc("/prompts/{id}", getPrompt).Methods("GET")
	apiRouter.HandleFunc("/prompts/{id}/", getPrompt).Methods("GET") // With trailing slash
	apiRouter.HandleFunc("/prompts/{id}", updatePrompt).Methods("PUT")
	apiRouter.HandleFunc("/prompts/{id}/", updatePrompt).Methods("PUT") // With trailing slash
	apiRouter.HandleFunc("/prompts/{id}", deletePrompt).Methods("DELETE")
	apiRouter.HandleFunc("/prompts/{id}/", deletePrompt).Methods("DELETE") // With trailing slash

	// Also keep the original non-prefixed endpoints for backward compatibility
	router.HandleFunc("/policies", createPolicy).Methods("POST")
	router.HandleFunc("/policies", getPolicies).Methods("GET")
	router.HandleFunc("/policies/{id}", getPolicy).Methods("GET")
	router.HandleFunc("/policies/{id}", updatePolicy).Methods("PUT")
	router.HandleFunc("/policies/{id}", deletePolicy).Methods("DELETE")

	router.HandleFunc("/model_profiles", createModelProfile).Methods("POST")
	router.HandleFunc("/model_profiles", getModelProfiles).Methods("GET")
	router.HandleFunc("/model_profiles/{id}", getModelProfile).Methods("GET")
	router.HandleFunc("/model_profiles/{id}", updateModelProfile).Methods("PUT")
	router.HandleFunc("/model_profiles/{id}", deleteModelProfile).Methods("DELETE")

	// NEW: Prompt Management API Endpoints
	router.HandleFunc("/prompts", createPrompt).Methods("POST")
	router.HandleFunc("/prompts", getPrompts).Methods("GET")
	router.HandleFunc("/prompts/{id}", getPrompt).Methods("GET")
	router.HandleFunc("/prompts/{id}", updatePrompt).Methods("PUT")
	router.HandleFunc("/prompts/{id}", deletePrompt).Methods("DELETE")

	// Set up CORS for all routes (to allow frontend dashboard to access)
	corsHandler := func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			// Make sure to include X-User-ID, X-User-Roles if you plan to re-enable RBAC
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, X-LLM-API-Key, X-Preferred-LLM-ID, X-Conversation-ID, X-User-ID, X-User-Roles")
			if r.Method == "OPTIONS" {
				w.WriteHeader(http.StatusOK)
				return
			}
			next.ServeHTTP(w, r)
		})
	}
	router.Use(corsHandler)

	port := os.Getenv("PORT")
	if port == "" {
		port = "8083" // Default port for policy-manager
	}
	log.Printf("Policy Manager starting on port %s...", port)
	log.Fatal(http.ListenAndServe(":"+port, router))
}

// loadPoliciesFromRedis fetches all policies from Redis into the cache.
func loadPoliciesFromRedis(ctx context.Context) error {
	mu.Lock()
	defer mu.Unlock()

	newPolicies := make(map[string]Policy)
	keys, err := redisClient.Keys(ctx, REDIS_POLICIES_KEY_PREFIX+"*").Result()
	if err != nil && err != redis.Nil {
		return fmt.Errorf("failed to get policy keys from Redis: %w", err)
	}

	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("Error getting policy %s from Redis: %v", key, err)
			continue
		}
		var policy Policy
		if err := json.Unmarshal([]byte(val), &policy); err != nil {
			log.Printf("Error unmarshalling policy %s: %v", key, err)
			continue
		}
		newPolicies[policy.ID] = policy
	}
	policies = newPolicies
	log.Printf("Loaded %d policies from Redis.", len(policies))
	return nil
}

// loadModelProfilesFromRedis fetches all model profiles from Redis into the cache.
func loadModelProfilesFromRedis(ctx context.Context) error {
	mu.Lock()
	defer mu.Unlock()

	newModelProfiles := make(map[string]ModelProfile)
	keys, err := redisClient.Keys(ctx, REDIS_MODEL_PROFILES_KEY_PREFIX+"*").Result()
	if err != nil && err != redis.Nil {
		return fmt.Errorf("failed to get model profile keys from Redis: %w", err)
	}

	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("Error getting model profile %s from Redis: %v", key, err)
			continue
		}
		var profile ModelProfile
		// *** FIX: Ensure ModelProfile.BackendURL uses `json:"url"` for unmarshalling
		if err := json.Unmarshal([]byte(val), &profile); err != nil {
			log.Printf("Error unmarshalling model profile %s: %v", key, err)
			continue
		}
		newModelProfiles[profile.ID] = profile
	}
	modelProfiles = newModelProfiles
	log.Printf("Loaded %d model profiles from Redis.", len(modelProfiles))
	return nil
}

// NEW: loadPromptsFromRedis fetches all prompts from Redis into the cache.
func loadPromptsFromRedis(ctx context.Context) error {
	mu.Lock()
	defer mu.Unlock()

	newPrompts := make(map[string]Prompt)
	keys, err := redisClient.Keys(ctx, REDIS_PROMPT_KEY_PREFIX+"*").Result()
	if err != nil && err != redis.Nil {
		return fmt.Errorf("failed to get prompt keys from Redis: %w", err)
	}

	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("Error getting prompt %s from Redis: %v", key, err)
			continue
		}
		var prompt Prompt
		if err := json.Unmarshal([]byte(val), &prompt); err != nil {
			log.Printf("Error unmarshalling prompt %s: %v", key, err)
			continue
		}
		// Use the same key format as createPrompt and updatePrompt: ID:Version
		promptKey := fmt.Sprintf("%s:%s", prompt.ID, prompt.Version)
		newPrompts[promptKey] = prompt
	}
	prompts = newPrompts
	log.Printf("Loaded %d prompts from Redis.", len(prompts))
	return nil
}

// listenForRedisPolicyUpdates listens for Redis Pub/Sub messages for policy updates.
func listenForRedisPolicyUpdates() {
	pubsub := redisClient.Subscribe(context.Background(), POLICY_UPDATES_CHANNEL)
	defer pubsub.Close()
	log.Println("Listening for Redis policy updates...")

	for msg := range pubsub.Channel() {
		log.Printf("Received Redis Pub/Sub message: Channel=%s, Payload=%s", msg.Channel, msg.Payload)
		if err := loadPoliciesFromRedis(context.Background()); err != nil {
			log.Printf("Error reloading policies from Redis: %v", err)
		} else {
			log.Println("Policies cache refreshed due to pending updates...")
		}
	}
}

// listenForRedisModelProfileUpdates listens for Redis Pub/Sub messages for model profile updates.
func listenForRedisModelProfileUpdates() {
	pubsub := redisClient.Subscribe(context.Background(), MODEL_PROFILE_UPDATES_CHANNEL)
	defer pubsub.Close()
	log.Println("Listening for Redis model profile updates...")

	for msg := range pubsub.Channel() {
		log.Printf("Received Redis Pub/Sub message: Channel=%s, Payload=%s", msg.Channel, msg.Payload)
		if err := loadModelProfilesFromRedis(context.Background()); err != nil {
			log.Printf("Error reloading model profiles from Redis: %v", err)
		} else {
			log.Println("Model profiles cache refreshed due to pending updates...")
		}
	}
}

// createSamplePrompts creates sample prompts for demonstration purposes
func createSamplePrompts(ctx context.Context) {
	samplePrompts := []Prompt{
		{
			ID:          "summarization-template",
			Name:        "Document Summarization",
			Description: "Template for summarizing documents and articles",
			Content:     "Please provide a concise summary of the following document, highlighting the key points and main conclusions:\n\n{{document}}",
			Version:     "1.0",
			Status:      "active",
			Tags:        []string{"summarization", "template", "document"},
			Owner:       "system",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          "code-review-prompt",
			Name:        "Code Review Assistant",
			Description: "Prompt for reviewing code and providing feedback",
			Content:     "Please review the following code and provide feedback on:\n1. Code quality and best practices\n2. Potential bugs or issues\n3. Performance improvements\n4. Security considerations\n\nCode:\n```{{language}}\n{{code}}\n```",
			Version:     "1.0",
			Status:      "active",
			Tags:        []string{"code-review", "development", "quality"},
			Owner:       "system",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          "customer-support-template",
			Name:        "Customer Support Response",
			Description: "Template for generating customer support responses",
			Content:     "Generate a helpful and professional customer support response for the following inquiry:\n\nCustomer Message: {{customer_message}}\n\nContext: {{context}}\n\nPlease provide a solution-oriented response that is empathetic and addresses the customer's concerns.",
			Version:     "1.0",
			Status:      "active",
			Tags:        []string{"customer-support", "template", "communication"},
			Owner:       "system",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	mu.Lock()
	defer mu.Unlock()

	for _, prompt := range samplePrompts {
		// Use the same key format as createPrompt and updatePrompt: ID:Version
		promptKey := fmt.Sprintf("%s:%s", prompt.ID, prompt.Version)
		prompts[promptKey] = prompt

		// Save to Redis
		promptJSON, err := json.Marshal(prompt)
		if err != nil {
			log.Printf("Error marshalling sample prompt %s: %v", prompt.ID, err)
			continue
		}

		key := REDIS_PROMPT_KEY_PREFIX + promptKey
		if err := redisClient.Set(ctx, key, promptJSON, 0).Err(); err != nil {
			log.Printf("Error saving sample prompt %s to Redis: %v", prompt.ID, err)
		} else {
			log.Printf("Created sample prompt: %s", prompt.Name)
		}
	}

	log.Printf("Created %d sample prompts", len(samplePrompts))
}

// createSampleModelProfiles creates sample model profiles with enhanced capability data
func createSampleModelProfiles(ctx context.Context) {
	sampleProfiles := []ModelProfile{
		{
			ID:                 "gpt-4",
			Name:               "GPT-4",
			Aliases:            []string{"gpt-4-0613", "gpt-4-32k"},
			Capabilities:       []string{"chat", "completion", "reasoning", "code"},
			PricingTier:        "premium",
			DataSensitivity:    "standard",
			ExpectedLatencyMs:  2500,
			ExpectedCost:       0.06,
			BackendURL:         "https://api.openai.com/v1/chat/completions",
			BackendType:        "openai",
			CostPerInputToken:  0.03,
			CostPerOutputToken: 0.06,
			APIKey:             "",
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
			CapabilityScores: map[string]float64{
				"code_generation":  0.95,
				"reasoning":        0.95,
				"creative_writing": 0.90,
				"factual_qa":       0.85,
				"analysis":         0.92,
				"summarization":    0.88,
				"translation":      0.80,
				"mathematical":     0.90,
				"conversational":   0.85,
				"other":            0.80,
			},
			OptimalUseCases:  []string{"code_generation", "complex_reasoning", "analysis", "technical_writing"},
			Strengths:        []string{"complex_reasoning", "code_understanding", "analytical_tasks", "structured_output"},
			Weaknesses:       []string{"cost", "speed", "real_time_data"},
			MaxContextLength: 8192,
			QualityScore:     0.92,
			RobustnessScore:  0.88,
			SafetyScore:      0.95,
			Tier:             3,
		},
		{
			ID:                 "gpt-3.5-turbo",
			Name:               "GPT-3.5 Turbo",
			Aliases:            []string{"gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k"},
			Capabilities:       []string{"chat", "completion", "general"},
			PricingTier:        "standard",
			DataSensitivity:    "standard",
			ExpectedLatencyMs:  800,
			ExpectedCost:       0.002,
			BackendURL:         "https://api.openai.com/v1/chat/completions",
			BackendType:        "openai",
			CostPerInputToken:  0.001,
			CostPerOutputToken: 0.002,
			APIKey:             "",
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
			CapabilityScores: map[string]float64{
				"code_generation":  0.80,
				"reasoning":        0.75,
				"creative_writing": 0.85,
				"factual_qa":       0.80,
				"analysis":         0.70,
				"summarization":    0.85,
				"translation":      0.75,
				"mathematical":     0.70,
				"conversational":   0.90,
				"other":            0.75,
			},
			OptimalUseCases:  []string{"general_tasks", "simple_qa", "basic_writing", "conversational"},
			Strengths:        []string{"speed", "cost_efficiency", "general_purpose", "conversational"},
			Weaknesses:       []string{"complex_reasoning", "specialized_knowledge", "long_context"},
			MaxContextLength: 4096,
			QualityScore:     0.78,
			RobustnessScore:  0.82,
			SafetyScore:      0.90,
			Tier:             2,
		},
		{
			ID:                 "claude-3-sonnet",
			Name:               "Claude-3 Sonnet",
			Aliases:            []string{"claude-3-sonnet-20240229"},
			Capabilities:       []string{"chat", "completion", "analysis", "safety"},
			PricingTier:        "premium",
			DataSensitivity:    "high",
			ExpectedLatencyMs:  1800,
			ExpectedCost:       0.015,
			BackendURL:         "https://api.anthropic.com/v1/messages",
			BackendType:        "anthropic",
			CostPerInputToken:  0.003,
			CostPerOutputToken: 0.015,
			APIKey:             "",
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
			CapabilityScores: map[string]float64{
				"code_generation":  0.88,
				"reasoning":        0.90,
				"creative_writing": 0.92,
				"factual_qa":       0.88,
				"analysis":         0.90,
				"summarization":    0.90,
				"translation":      0.85,
				"mathematical":     0.85,
				"conversational":   0.88,
				"other":            0.85,
			},
			OptimalUseCases:  []string{"long_context", "safety_critical", "creative_writing", "analysis"},
			Strengths:        []string{"safety", "long_context", "nuanced_understanding", "creative_tasks"},
			Weaknesses:       []string{"speed", "cost", "code_specificity"},
			MaxContextLength: 200000,
			QualityScore:     0.89,
			RobustnessScore:  0.92,
			SafetyScore:      0.98,
			Tier:             3,
		},
		{
			ID:                 "gemini-pro",
			Name:               "Gemini Pro",
			Aliases:            []string{"gemini-1.0-pro"},
			Capabilities:       []string{"chat", "completion", "multimodal", "code"},
			PricingTier:        "standard",
			DataSensitivity:    "standard",
			ExpectedLatencyMs:  1200,
			ExpectedCost:       0.0005,
			BackendURL:         "https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent",
			BackendType:        "google",
			CostPerInputToken:  0.00025,
			CostPerOutputToken: 0.0005,
			APIKey:             "",
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
			CapabilityScores: map[string]float64{
				"code_generation":  0.85,
				"reasoning":        0.88,
				"creative_writing": 0.80,
				"factual_qa":       0.90,
				"analysis":         0.85,
				"summarization":    0.82,
				"translation":      0.88,
				"mathematical":     0.88,
				"conversational":   0.82,
				"multimodal":       0.95,
				"other":            0.80,
			},
			OptimalUseCases:  []string{"multimodal_tasks", "factual_queries", "cost_efficient", "mathematical"},
			Strengths:        []string{"multimodal", "factual_accuracy", "cost_efficiency", "mathematical_reasoning"},
			Weaknesses:       []string{"creative_writing", "nuanced_conversation"},
			MaxContextLength: 32768,
			QualityScore:     0.83,
			RobustnessScore:  0.85,
			SafetyScore:      0.92,
			Tier:             2,
		},
	}

	mu.Lock()
	defer mu.Unlock()

	for _, profile := range sampleProfiles {
		modelProfiles[profile.ID] = profile

		// Save to Redis
		profileJSON, err := json.Marshal(profile)
		if err != nil {
			log.Printf("Error marshalling sample model profile %s: %v", profile.ID, err)
			continue
		}

		key := REDIS_MODEL_PROFILES_KEY_PREFIX + profile.ID
		if err := redisClient.Set(ctx, key, profileJSON, 0).Err(); err != nil {
			log.Printf("Error saving sample model profile %s to Redis: %v", profile.ID, err)
		} else {
			log.Printf("Created sample model profile: %s", profile.Name)
		}
	}

	log.Printf("Created %d sample model profiles with enhanced capabilities", len(sampleProfiles))
}

// createSamplePolicies creates sample routing policies for demonstration purposes
func createSamplePolicies(ctx context.Context) {
	samplePolicies := []Policy{
		{
			ID:          "default-routing",
			Name:        "Default Model Routing",
			Description: "Default routing policy for general requests",
			Criteria:    json.RawMessage(`{"default": true}`),
			Action:      "ROUTE",
			BackendID:   "gpt-3.5-turbo",
			Priority:    1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          "premium-routing",
			Name:        "Premium Model Routing",
			Description: "Route premium users to high-quality models",
			Criteria:    json.RawMessage(`{"user_tier": "premium"}`),
			Action:      "ROUTE",
			BackendID:   "gpt-4",
			Priority:    10,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          "code-generation-routing",
			Name:        "Code Generation Routing",
			Description: "Route code generation requests to specialized models",
			Criteria:    json.RawMessage(`{"task_type": "code_generation"}`),
			Action:      "ROUTE",
			BackendID:   "gpt-4",
			Priority:    8,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          "multimodal-routing",
			Name:        "Multimodal Routing",
			Description: "Route multimodal requests to Gemini",
			Criteria:    json.RawMessage(`{"task_type": "multimodal"}`),
			Action:      "ROUTE",
			BackendID:   "gemini-pro",
			Priority:    9,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          "cost-optimization",
			Name:        "Cost Optimization Policy",
			Description: "Optimize for cost-effective models for simple tasks",
			Criteria:    json.RawMessage(`{"complexity": "low"}`),
			Action:      "OPTIMIZE",
			BackendID:   "gpt-3.5-turbo",
			Priority:    5,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	mu.Lock()
	defer mu.Unlock()

	for _, policy := range samplePolicies {
		policies[policy.ID] = policy

		// Save to Redis
		policyJSON, err := json.Marshal(policy)
		if err != nil {
			log.Printf("Error marshalling sample policy %s: %v", policy.ID, err)
			continue
		}

		key := REDIS_POLICIES_KEY_PREFIX + policy.ID
		if err := redisClient.Set(ctx, key, policyJSON, 0).Err(); err != nil {
			log.Printf("Error saving sample policy %s to Redis: %v", policy.ID, err)
		} else {
			log.Printf("Created sample policy: %s", policy.Name)
		}
	}

	// Publish update event to notify other services
	redisClient.Publish(ctx, POLICY_UPDATES_CHANNEL, "sample_policies_created")

	log.Printf("Created %d sample policies", len(samplePolicies))
}

// NEW: listenForRedisPromptUpdates listens for Redis Pub/Sub messages for prompt updates.
func listenForRedisPromptUpdates() {
	pubsub := redisClient.Subscribe(context.Background(), PROMPT_UPDATES_CHANNEL)
	defer pubsub.Close()
	log.Println("Listening for Redis prompt updates...")

	for msg := range pubsub.Channel() {
		log.Printf("Received Redis Pub/Sub message: Channel=%s, Payload=%s", msg.Channel, msg.Payload)
		if err := loadPromptsFromRedis(context.Background()); err != nil {
			log.Printf("Error reloading prompts from Redis: %v", err)
		} else {
			log.Println("Prompts cache refreshed due to pending updates...")
		}
	}
}

// --- Policy API Handlers ---
func createPolicy(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	var policy Policy
	if err := json.NewDecoder(r.Body).Decode(&policy); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if policy.ID == "" {
		http.Error(w, "Policy ID is required.", http.StatusBadRequest)
		return
	}

	mu.Lock()
	defer mu.Unlock()

	if _, exists := policies[policy.ID]; exists {
		http.Error(w, "Policy with this ID already exists.", http.StatusConflict)
		return
	}

	policy.CreatedAt = time.Now()
	policy.UpdatedAt = time.Now()
	policies[policy.ID] = policy

	// Store in Redis - Check if it's a routing strategy or a policy
	policyJSON, _ := json.Marshal(policy)
	redisKey := REDIS_POLICIES_KEY_PREFIX + policy.ID
	if policy.Action == "ROUTE" || policy.Action == "OPTIMIZE" {
		redisKey = "routing_strategy:" + policy.ID // Store routing strategies with a different prefix
	}
	if err := redisClient.Set(context.Background(), redisKey, policyJSON, 0).Err(); err != nil {
		log.Printf("Error setting policy in Redis: %v", err)
		http.Error(w, "Failed to store policy", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), POLICY_UPDATES_CHANNEL, policy.ID)

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(policy)
	log.Printf("Policy '%s' created.", policy.ID)
}

func getPolicies(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	mu.RLock()
	defer mu.RUnlock()

	policyList := []Policy{}
	for _, p := range policies {
		policyList = append(policyList, p)
	}
	json.NewEncoder(w).Encode(policyList)
}

func getPolicy(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	mu.RLock()
	defer mu.RUnlock()

	policy, ok := policies[id]
	if !ok {
		http.Error(w, "Policy not found.", http.StatusNotFound)
		return
	}
	json.NewEncoder(w).Encode(policy)
}

func updatePolicy(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	var updatedPolicy Policy
	if err := json.NewDecoder(r.Body).Decode(&updatedPolicy); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if updatedPolicy.ID != "" && updatedPolicy.ID != id {
		http.Error(w, "Policy ID in body must match URL ID.", http.StatusBadRequest)
		return
	}
	updatedPolicy.ID = id // Ensure the ID from the URL is used

	mu.Lock()
	defer mu.Unlock()

	existingPolicy, ok := policies[id]
	if !ok {
		http.Error(w, "Policy not found.", http.StatusNotFound)
		return
	}

	updatedPolicy.CreatedAt = existingPolicy.CreatedAt // Preserve original creation timestamp
	updatedPolicy.UpdatedAt = time.Now()
	policies[id] = updatedPolicy

	// Update in Redis
	policyJSON, _ := json.Marshal(updatedPolicy)
	if err := redisClient.Set(context.Background(), REDIS_POLICIES_KEY_PREFIX+id, policyJSON, 0).Err(); err != nil {
		log.Printf("Error updating policy in Redis: %v", err)
		http.Error(w, "Failed to update policy in Redis", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), POLICY_UPDATES_CHANNEL, id)

	json.NewEncoder(w).Encode(updatedPolicy)
	log.Printf("Policy '%s' updated.", id)
}

func deletePolicy(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	mu.Lock()
	defer mu.Unlock()

	if _, ok := policies[id]; !ok {
		http.Error(w, "Policy not found.", http.StatusNotFound)
		return
	}

	delete(policies, id)

	// Delete from Redis
	if err := redisClient.Del(context.Background(), REDIS_POLICIES_KEY_PREFIX+id).Err(); err != nil {
		log.Printf("Error deleting policy from Redis: %v", err)
		http.Error(w, "Failed to delete policy from Redis", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), POLICY_UPDATES_CHANNEL, id)

	w.WriteHeader(http.StatusNoContent) // 204 No Content for successful deletion
	log.Printf("Policy '%s' deleted.", id)
}

// --- Model Profile (LLM Registry) API Handlers ---
func createModelProfile(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	var profile ModelProfile
	if err := json.NewDecoder(r.Body).Decode(&profile); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if profile.ID == "" {
		http.Error(w, "Model Profile ID is required.", http.StatusBadRequest)
		return
	}

	mu.Lock()
	defer mu.Unlock()

	if _, exists := modelProfiles[profile.ID]; exists {
		http.Error(w, "Model Profile with this ID already exists.", http.StatusConflict)
		return
	}

	profile.CreatedAt = time.Now()
	profile.UpdatedAt = time.Now()
	modelProfiles[profile.ID] = profile

	// Store in Redis
	profileJSON, _ := json.Marshal(profile)
	if err := redisClient.Set(context.Background(), REDIS_MODEL_PROFILES_KEY_PREFIX+profile.ID, profileJSON, 0).Err(); err != nil {
		log.Printf("Error setting model profile in Redis: %v", err)
		http.Error(w, "Failed to store model profile", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), MODEL_PROFILE_UPDATES_CHANNEL, profile.ID)

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(profile)
	log.Printf("Model Profile '%s' created.", profile.ID)
}

func getModelProfiles(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	mu.RLock()
	defer mu.RUnlock()

	profileList := []ModelProfile{}
	for _, mp := range modelProfiles {
		profileList = append(profileList, mp)
	}
	json.NewEncoder(w).Encode(profileList)
}

func getModelProfile(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	mu.RLock()
	defer mu.RUnlock()

	profile, ok := modelProfiles[id]
	if !ok {
		http.Error(w, "Model Profile not found.", http.StatusNotFound)
		return
	}
	json.NewEncoder(w).Encode(profile)
}

func updateModelProfile(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	var updatedProfile ModelProfile
	if err := json.NewDecoder(r.Body).Decode(&updatedProfile); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if updatedProfile.ID != "" && updatedProfile.ID != id {
		http.Error(w, "Model Profile ID in body must match URL ID.", http.StatusBadRequest)
		return
	}
	updatedProfile.ID = id // Ensure the ID from the URL is used

	mu.Lock()
	defer mu.Unlock()

	_, ok := modelProfiles[id]
	if !ok {
		http.Error(w, "Model Profile not found.", http.StatusNotFound)
		return
	}

	// updatedProfile.CreatedAt = time.Now() // Update creation timestamp
	updatedProfile.UpdatedAt = time.Now()
	modelProfiles[id] = updatedProfile

	// Update in Redis
	profileJSON, _ := json.Marshal(updatedProfile)
	if err := redisClient.Set(context.Background(), REDIS_MODEL_PROFILES_KEY_PREFIX+id, profileJSON, 0).Err(); err != nil {
		log.Printf("Error updating model profile in Redis: %v", err)
		http.Error(w, "Failed to update model profile in Redis", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), MODEL_PROFILE_UPDATES_CHANNEL, id)

	json.NewEncoder(w).Encode(updatedProfile)
	log.Printf("Model Profile '%s' updated.", id)
}

func deleteModelProfile(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	mu.Lock()
	defer mu.Unlock()

	if _, ok := modelProfiles[id]; !ok {
		http.Error(w, "Model Profile not found.", http.StatusNotFound)
		return
	}

	delete(modelProfiles, id)

	// Delete from Redis
	if err := redisClient.Del(context.Background(), REDIS_MODEL_PROFILES_KEY_PREFIX+id).Err(); err != nil {
		log.Printf("Error deleting model profile from Redis: %v", err)
		http.Error(w, "Failed to delete model profile from Redis", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), MODEL_PROFILE_UPDATES_CHANNEL, id)

	w.WriteHeader(http.StatusNoContent) // 204 No Content for successful deletion
	log.Printf("Model Profile '%s' deleted.", id)
}

// --- NEW: Prompt Management API Handlers ---
func createPrompt(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	// Read the raw body for debugging
	body, err := io.ReadAll(r.Body)
	if err != nil {
		log.Printf("Error reading request body: %v", err)
		http.Error(w, "Error reading request body", http.StatusBadRequest)
		return
	}

	log.Printf("Received prompt JSON: %s", string(body))

	var prompt Prompt
	if err := json.Unmarshal(body, &prompt); err != nil {
		log.Printf("Error decoding prompt JSON: %v", err)
		log.Printf("Raw JSON: %s", string(body))
		http.Error(w, fmt.Sprintf("Invalid JSON: %v", err), http.StatusBadRequest)
		return
	}

	// Basic validation for required fields
	if prompt.ID == "" || prompt.Name == "" || prompt.Version == "" || prompt.Content == "" {
		http.Error(w, "Prompt ID, Name, Version, and Content are required.", http.StatusBadRequest)
		return
	}

	// Combine ID and Version to create a unique key for the prompt
	// This supports versioning and allows for multiple versions of the same logical prompt name
	promptKey := fmt.Sprintf("%s:%s", prompt.ID, prompt.Version)

	mu.Lock()
	defer mu.Unlock()

	if _, exists := prompts[promptKey]; exists {
		http.Error(w, fmt.Sprintf("Prompt with ID '%s' and Version '%s' already exists.", prompt.ID, prompt.Version), http.StatusConflict)
		return
	}

	prompt.CreatedAt = time.Now()
	prompt.UpdatedAt = time.Now()
	prompts[promptKey] = prompt

	// Store in Redis
	promptJSON, _ := json.Marshal(prompt)
	if err := redisClient.Set(context.Background(), REDIS_PROMPT_KEY_PREFIX+promptKey, promptJSON, 0).Err(); err != nil {
		log.Printf("Error setting prompt in Redis: %v", err)
		http.Error(w, "Failed to store prompt", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), PROMPT_UPDATES_CHANNEL, promptKey)

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(prompt)
	log.Printf("Prompt '%s' (Version: %s) created.", prompt.ID, prompt.Version)
}

func getPrompts(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	mu.RLock()
	defer mu.RUnlock()

	promptList := []Prompt{}
	for _, p := range prompts {
		promptList = append(promptList, p)
	}
	json.NewEncoder(w).Encode(promptList)
}

func getPrompt(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"] // This ID refers to the prompt ID (e.g., "summarization-template")

	// Allow querying by prompt ID (returns all versions) or specific ID:Version
	version := r.URL.Query().Get("version")

	mu.RLock()
	defer mu.RUnlock()

	foundPrompts := []Prompt{}
	if version != "" {
		// Specific version requested: use the exact key "ID:Version"
		promptKey := fmt.Sprintf("%s:%s", id, version)
		if p, ok := prompts[promptKey]; ok {
			foundPrompts = append(foundPrompts, p)
		}
	} else {
		// No specific version, return all versions for this ID
		for key, p := range prompts {
			if !strings.Contains(key, ":") {
				continue
			}
			// Check if the key starts with the desired ID and a colon for the version
			if strings.HasPrefix(key, id+":") {
				foundPrompts = append(foundPrompts, p)
			}
			if !strings.Contains(key, ":") {
				continue
			}
			// Check if the key starts with the desired ID and a colon for the version
			if strings.HasPrefix(key, id+":") {
				foundPrompts = append(foundPrompts, p)
			}
			// Check if the key starts with the desired ID and a colon for the version
			if strings.HasPrefix(key, id+":") {
				foundPrompts = append(foundPrompts, p)
			}
		}
	}

	if len(foundPrompts) == 0 {
		http.Error(w, fmt.Sprintf("Prompt with ID '%s' (and version '%s' if specified) not found.", id, version), http.StatusNotFound)
		return
	}
	json.NewEncoder(w).Encode(foundPrompts)
}

func updatePrompt(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	var updatedPrompt Prompt
	if err := json.NewDecoder(r.Body).Decode(&updatedPrompt); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Ensure ID and Version in body match the URL ID and the prompt's own version
	if updatedPrompt.ID != "" && updatedPrompt.ID != id {
		http.Error(w, "Prompt ID in body must match URL ID.", http.StatusBadRequest)
		return
	}
	updatedPrompt.ID = id // Ensure the ID from the URL is used

	if updatedPrompt.Version == "" {
		http.Error(w, "Prompt Version is required for update.", http.StatusBadRequest)
		return
	}

	promptKey := fmt.Sprintf("%s:%s", id, updatedPrompt.Version)

	mu.Lock()
	defer mu.Unlock()

	existingPrompt, ok := prompts[promptKey]
	if !ok {
		http.Error(w, fmt.Sprintf("Prompt with ID '%s' and Version '%s' not found.", id, updatedPrompt.Version), http.StatusNotFound)
		return
	}

	updatedPrompt.CreatedAt = existingPrompt.CreatedAt // Preserve original creation timestamp
	updatedPrompt.UpdatedAt = time.Now()
	prompts[promptKey] = updatedPrompt

	// Update in Redis
	promptJSON, _ := json.Marshal(updatedPrompt)
	if err := redisClient.Set(context.Background(), REDIS_PROMPT_KEY_PREFIX+promptKey, promptJSON, 0).Err(); err != nil {
		log.Printf("Error updating prompt in Redis: %v", err)
		http.Error(w, "Failed to update prompt in Redis", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), PROMPT_UPDATES_CHANNEL, promptKey)

	json.NewEncoder(w).Encode(updatedPrompt)
	log.Printf("Prompt '%s' (Version: %s) updated.", id, updatedPrompt.Version)
}

func deletePrompt(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	version := r.URL.Query().Get("version") // Allow deleting specific version via query param

	mu.Lock()
	defer mu.Unlock()

	if version != "" {
		// Delete specific version
		promptKey := fmt.Sprintf("%s:%s", id, version)
		if _, ok := prompts[promptKey]; !ok {
			http.Error(w, fmt.Sprintf("Prompt with ID '%s' and Version '%s' not found.", id, version), http.StatusNotFound)
			return
		}
		delete(prompts, promptKey)
		if err := redisClient.Del(context.Background(), REDIS_PROMPT_KEY_PREFIX+promptKey).Err(); err != nil {
			log.Printf("Error deleting specific prompt from Redis: %v", err)
			http.Error(w, "Failed to delete prompt from Redis", http.StatusInternalServerError)
			return
		}
		redisClient.Publish(context.Background(), PROMPT_UPDATES_CHANNEL, promptKey)
		log.Printf("Prompt '%s' (Version: %s) deleted.", id, version)

	} else {
		// Delete all versions for this ID (use with caution!)
		deletedCount := 0
		keysToDelete := []string{}
		for key := range prompts {
			// Check if the key starts with the desired ID and a colon, indicating a versioned prompt
			if strings.HasPrefix(key, id+":") {
				keysToDelete = append(keysToDelete, key)
				delete(prompts, key)
				deletedCount++
			}
		}

		if deletedCount == 0 {
			http.Error(w, fmt.Sprintf("No prompts found with ID '%s' to delete.", id), http.StatusNotFound)
			return
		}

		redisKeys := make([]string, len(keysToDelete))
		for i, k := range keysToDelete {
			redisKeys[i] = REDIS_PROMPT_KEY_PREFIX + k
		}

		if len(redisKeys) > 0 {
			if err := redisClient.Del(context.Background(), redisKeys...).Err(); err != nil {
				log.Printf("Error deleting multiple prompts from Redis: %v", err)
				http.Error(w, "Failed to delete prompts from Redis", http.StatusInternalServerError)
				return
			}
		}

		// Publish updates for each deleted key (or a single batch update signal)
		for _, k := range keysToDelete {
			redisClient.Publish(context.Background(), PROMPT_UPDATES_CHANNEL, k)
		}
		log.Printf("Deleted %d versions of prompt '%s'.", deletedCount, id)
	}

	w.WriteHeader(http.StatusNoContent)
}

// Add a simple health check endpoint
func healthCheck(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{
		"status":    "ok",
		"service":   "policy-manager",
		"timestamp": time.Now().Format(time.RFC3339),
	})
	log.Printf("Health check request received from %s", r.RemoteAddr)
}

// --- A/B Testing Handlers ---

func createABTest(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	var abTest PromptABTest
	if err := json.NewDecoder(r.Body).Decode(&abTest); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Basic validation
	if abTest.ID == "" || abTest.Name == "" || abTest.PromptAID == "" || abTest.PromptBID == "" {
		http.Error(w, "ID, Name, PromptAID, and PromptBID are required.", http.StatusBadRequest)
		return
	}

	if abTest.TrafficSplit < 0 || abTest.TrafficSplit > 100 {
		http.Error(w, "TrafficSplit must be between 0 and 100.", http.StatusBadRequest)
		return
	}

	mu.Lock()
	defer mu.Unlock()

	if _, exists := promptABTests[abTest.ID]; exists {
		http.Error(w, fmt.Sprintf("A/B test with ID '%s' already exists.", abTest.ID), http.StatusConflict)
		return
	}

	abTest.CreatedAt = time.Now()
	abTest.UpdatedAt = time.Now()
	abTest.StartDate = time.Now()
	abTest.Status = "running"
	promptABTests[abTest.ID] = abTest

	// Store in Redis
	abTestJSON, _ := json.Marshal(abTest)
	if err := redisClient.Set(context.Background(), REDIS_AB_TEST_KEY_PREFIX+abTest.ID, abTestJSON, 0).Err(); err != nil {
		log.Printf("Error setting A/B test in Redis: %v", err)
		http.Error(w, "Failed to store A/B test", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(abTest)
	log.Printf("A/B test '%s' created.", abTest.ID)
}

func getABTests(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	mu.RLock()
	defer mu.RUnlock()

	abTestList := []PromptABTest{}
	for _, test := range promptABTests {
		abTestList = append(abTestList, test)
	}
	json.NewEncoder(w).Encode(abTestList)
}

func getABTest(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	mu.RLock()
	defer mu.RUnlock()

	abTest, exists := promptABTests[id]
	if !exists {
		http.Error(w, fmt.Sprintf("A/B test with ID '%s' not found.", id), http.StatusNotFound)
		return
	}

	json.NewEncoder(w).Encode(abTest)
}

func updateABTest(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	var updatedTest PromptABTest
	if err := json.NewDecoder(r.Body).Decode(&updatedTest); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	mu.Lock()
	defer mu.Unlock()

	existingTest, exists := promptABTests[id]
	if !exists {
		http.Error(w, fmt.Sprintf("A/B test with ID '%s' not found.", id), http.StatusNotFound)
		return
	}

	updatedTest.ID = id
	updatedTest.CreatedAt = existingTest.CreatedAt
	updatedTest.UpdatedAt = time.Now()
	promptABTests[id] = updatedTest

	// Update in Redis
	abTestJSON, _ := json.Marshal(updatedTest)
	if err := redisClient.Set(context.Background(), REDIS_AB_TEST_KEY_PREFIX+id, abTestJSON, 0).Err(); err != nil {
		log.Printf("Error updating A/B test in Redis: %v", err)
		http.Error(w, "Failed to update A/B test in Redis", http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(updatedTest)
	log.Printf("A/B test '%s' updated.", id)
}

func deleteABTest(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	mu.Lock()
	defer mu.Unlock()

	if _, exists := promptABTests[id]; !exists {
		http.Error(w, fmt.Sprintf("A/B test with ID '%s' not found.", id), http.StatusNotFound)
		return
	}

	delete(promptABTests, id)
	if err := redisClient.Del(context.Background(), REDIS_AB_TEST_KEY_PREFIX+id).Err(); err != nil {
		log.Printf("Error deleting A/B test from Redis: %v", err)
		http.Error(w, "Failed to delete A/B test from Redis", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
	log.Printf("A/B test '%s' deleted.", id)
}

func stopABTest(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	mu.Lock()
	defer mu.Unlock()

	abTest, exists := promptABTests[id]
	if !exists {
		http.Error(w, fmt.Sprintf("A/B test with ID '%s' not found.", id), http.StatusNotFound)
		return
	}

	abTest.Status = "completed"
	endTime := time.Now()
	abTest.EndDate = &endTime
	abTest.UpdatedAt = time.Now()
	promptABTests[id] = abTest

	// Update in Redis
	abTestJSON, _ := json.Marshal(abTest)
	redisClient.Set(context.Background(), REDIS_AB_TEST_KEY_PREFIX+id, abTestJSON, 0)

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"status": "stopped"})
	log.Printf("A/B test '%s' stopped.", id)
}

// --- Performance Tracking Handlers ---

func getPromptPerformance(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	mu.RLock()
	defer mu.RUnlock()

	performanceList := []PromptPerformance{}
	for _, perf := range promptPerformance {
		performanceList = append(performanceList, perf)
	}
	json.NewEncoder(w).Encode(performanceList)
}

func getPromptPerformanceByID(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	mu.RLock()
	defer mu.RUnlock()

	perf, exists := promptPerformance[id]
	if !exists {
		http.Error(w, fmt.Sprintf("Performance data for prompt '%s' not found.", id), http.StatusNotFound)
		return
	}

	json.NewEncoder(w).Encode(perf)
}

func getPromptAnalytics(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	mu.RLock()
	defer mu.RUnlock()

	// Combine prompt data with performance metrics
	type PromptAnalytics struct {
		Prompt      Prompt            `json:"prompt"`
		Performance PromptPerformance `json:"performance"`
	}

	analytics := []PromptAnalytics{}
	for promptID, prompt := range prompts {
		perf, exists := promptPerformance[promptID]
		if !exists {
			// Create default performance data if none exists
			perf = PromptPerformance{
				PromptID:        promptID,
				Version:         prompt.Version,
				TotalExecutions: 0,
				SuccessRate:     0.0,
				AvgLatency:      0.0,
				AvgCost:         0.0,
				AvgTokens:       0,
				QualityScore:    0.0,
				LastUsed:        time.Time{},
				LastUpdated:     time.Now(),
			}
		}

		analytics = append(analytics, PromptAnalytics{
			Prompt:      prompt,
			Performance: perf,
		})
	}

	json.NewEncoder(w).Encode(analytics)
}

// updatePromptPerformance updates performance metrics for a prompt (called by other services)
func updatePromptPerformance(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	promptID := vars["id"]

	var perfUpdate PromptPerformance
	if err := json.NewDecoder(r.Body).Decode(&perfUpdate); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	mu.Lock()
	defer mu.Unlock()

	perfUpdate.PromptID = promptID
	perfUpdate.LastUpdated = time.Now()
	promptPerformance[promptID] = perfUpdate

	// Store in Redis
	perfJSON, _ := json.Marshal(perfUpdate)
	if err := redisClient.Set(context.Background(), REDIS_PERFORMANCE_KEY_PREFIX+promptID, perfJSON, 0).Err(); err != nil {
		log.Printf("Error setting performance data in Redis: %v", err)
		http.Error(w, "Failed to store performance data", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(perfUpdate)
	log.Printf("Performance data updated for prompt '%s'.", promptID)
}

// --- ENHANCED: Prompt Versioning & Advanced Operations ---

// getPromptVersions returns all versions of a specific prompt
func getPromptVersions(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	mu.RLock()
	defer mu.RUnlock()

	var versions []Prompt
	for key, prompt := range prompts {
		// Check if this prompt belongs to the requested ID
		if strings.HasPrefix(key, id+":") || prompt.ID == id {
			versions = append(versions, prompt)
		}
	}

	if len(versions) == 0 {
		http.Error(w, fmt.Sprintf("No versions found for prompt ID '%s'", id), http.StatusNotFound)
		return
	}

	// Sort versions by creation date (newest first)
	sort.Slice(versions, func(i, j int) bool {
		return versions[i].CreatedAt.After(versions[j].CreatedAt)
	})

	json.NewEncoder(w).Encode(versions)
}

// getPromptDiff returns the difference between two prompt versions
func getPromptDiff(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	fromVersion := r.URL.Query().Get("from")
	toVersion := r.URL.Query().Get("to")

	if fromVersion == "" || toVersion == "" {
		http.Error(w, "Both 'from' and 'to' version parameters are required", http.StatusBadRequest)
		return
	}

	mu.RLock()
	defer mu.RUnlock()

	fromKey := fmt.Sprintf("%s:%s", id, fromVersion)
	toKey := fmt.Sprintf("%s:%s", id, toVersion)

	fromPrompt, fromExists := prompts[fromKey]
	toPrompt, toExists := prompts[toKey]

	if !fromExists {
		http.Error(w, fmt.Sprintf("Version '%s' not found for prompt '%s'", fromVersion, id), http.StatusNotFound)
		return
	}
	if !toExists {
		http.Error(w, fmt.Sprintf("Version '%s' not found for prompt '%s'", toVersion, id), http.StatusNotFound)
		return
	}

	diff := map[string]interface{}{
		"prompt_id":    id,
		"from_version": fromVersion,
		"to_version":   toVersion,
		"changes": map[string]interface{}{
			"content_changed":     fromPrompt.Content != toPrompt.Content,
			"description_changed": fromPrompt.Description != toPrompt.Description,
			"tags_changed":        !equalStringSlices(fromPrompt.Tags, toPrompt.Tags),
			"variables_changed":   !equalVariables(fromPrompt.Variables, toPrompt.Variables),
		},
		"from_prompt":  fromPrompt,
		"to_prompt":    toPrompt,
		"generated_at": time.Now(),
	}

	json.NewEncoder(w).Encode(diff)
}

// rollbackPrompt creates a new version based on an older version
func rollbackPrompt(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	var rollbackReq struct {
		ToVersion string `json:"to_version"`
		ChangeLog string `json:"change_log"`
	}

	if err := json.NewDecoder(r.Body).Decode(&rollbackReq); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if rollbackReq.ToVersion == "" {
		http.Error(w, "to_version is required", http.StatusBadRequest)
		return
	}

	mu.Lock()
	defer mu.Unlock()

	// Find the version to rollback to
	rollbackKey := fmt.Sprintf("%s:%s", id, rollbackReq.ToVersion)
	sourcePrompt, exists := prompts[rollbackKey]
	if !exists {
		http.Error(w, fmt.Sprintf("Version '%s' not found for prompt '%s'", rollbackReq.ToVersion, id), http.StatusNotFound)
		return
	}

	// Create new version based on the rollback target
	newVersion := incrementVersion(getLatestVersion(id))
	newPrompt := sourcePrompt
	newPrompt.Version = newVersion
	newPrompt.ParentVersion = getLatestVersion(id)
	newPrompt.ChangeLog = rollbackReq.ChangeLog
	if newPrompt.ChangeLog == "" {
		newPrompt.ChangeLog = fmt.Sprintf("Rollback to version %s", rollbackReq.ToVersion)
	}
	newPrompt.CreatedAt = time.Now()
	newPrompt.UpdatedAt = time.Now()

	newKey := fmt.Sprintf("%s:%s", id, newVersion)
	prompts[newKey] = newPrompt

	// Store in Redis
	promptJSON, _ := json.Marshal(newPrompt)
	if err := redisClient.Set(context.Background(), REDIS_PROMPT_KEY_PREFIX+newKey, promptJSON, 0).Err(); err != nil {
		log.Printf("Error setting prompt in Redis: %v", err)
		http.Error(w, "Failed to store prompt", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), PROMPT_UPDATES_CHANNEL, newKey)

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(newPrompt)
	log.Printf("Prompt '%s' rolled back to version '%s' as new version '%s'", id, rollbackReq.ToVersion, newVersion)
}

// clonePrompt creates a copy of an existing prompt with a new ID
func clonePrompt(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	sourceID := vars["id"]

	var cloneReq struct {
		NewID       string `json:"new_id"`
		NewName     string `json:"new_name"`
		Version     string `json:"version,omitempty"`
		Description string `json:"description,omitempty"`
	}

	if err := json.NewDecoder(r.Body).Decode(&cloneReq); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if cloneReq.NewID == "" {
		http.Error(w, "new_id is required", http.StatusBadRequest)
		return
	}

	mu.Lock()
	defer mu.Unlock()

	// Find the source prompt (latest version if no version specified)
	sourceVersion := cloneReq.Version
	if sourceVersion == "" {
		sourceVersion = getLatestVersion(sourceID)
	}

	sourceKey := fmt.Sprintf("%s:%s", sourceID, sourceVersion)
	sourcePrompt, exists := prompts[sourceKey]
	if !exists {
		http.Error(w, fmt.Sprintf("Source prompt '%s' version '%s' not found", sourceID, sourceVersion), http.StatusNotFound)
		return
	}

	// Create cloned prompt
	clonedPrompt := sourcePrompt
	clonedPrompt.ID = cloneReq.NewID
	clonedPrompt.Name = cloneReq.NewName
	if clonedPrompt.Name == "" {
		clonedPrompt.Name = sourcePrompt.Name + " (Copy)"
	}
	clonedPrompt.Version = "1.0.0" // Start fresh version for cloned prompt
	clonedPrompt.ParentVersion = ""
	clonedPrompt.ChangeLog = fmt.Sprintf("Cloned from %s:%s", sourceID, sourceVersion)
	if cloneReq.Description != "" {
		clonedPrompt.Description = cloneReq.Description
	}
	clonedPrompt.CreatedAt = time.Now()
	clonedPrompt.UpdatedAt = time.Now()

	newKey := fmt.Sprintf("%s:%s", clonedPrompt.ID, clonedPrompt.Version)

	// Check if the new ID already exists
	if _, exists := prompts[newKey]; exists {
		http.Error(w, fmt.Sprintf("Prompt with ID '%s' already exists", clonedPrompt.ID), http.StatusConflict)
		return
	}

	prompts[newKey] = clonedPrompt

	// Store in Redis
	promptJSON, _ := json.Marshal(clonedPrompt)
	if err := redisClient.Set(context.Background(), REDIS_PROMPT_KEY_PREFIX+newKey, promptJSON, 0).Err(); err != nil {
		log.Printf("Error setting cloned prompt in Redis: %v", err)
		http.Error(w, "Failed to store cloned prompt", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), PROMPT_UPDATES_CHANNEL, newKey)

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(clonedPrompt)
	log.Printf("Prompt '%s' cloned to '%s'", sourceID, clonedPrompt.ID)
}

// Placeholder implementations for remaining handlers
func testPrompt(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"message": "Prompt testing functionality - to be implemented",
		"status":  "not_implemented",
	})
}

func executePrompt(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	var execReq struct {
		ModelID     string                 `json:"model_id"`
		Variables   map[string]interface{} `json:"variables"`
		Temperature *float64               `json:"temperature,omitempty"`
		MaxTokens   *int                   `json:"max_tokens,omitempty"`
	}

	if err := json.NewDecoder(r.Body).Decode(&execReq); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Find the prompt (try to find the latest version if no specific version requested)
	mu.RLock()
	var prompt *Prompt
	version := r.URL.Query().Get("version")

	if version != "" {
		// Specific version requested
		promptKey := fmt.Sprintf("%s:%s", id, version)
		if p, exists := prompts[promptKey]; exists {
			prompt = &p
		}
	} else {
		// Find the latest version
		var latestVersion string
		for key, p := range prompts {
			if strings.HasPrefix(key, id+":") {
				if latestVersion == "" || p.Version > latestVersion {
					latestVersion = p.Version
					prompt = &p
				}
			}
		}
	}
	mu.RUnlock()

	if prompt == nil {
		http.Error(w, fmt.Sprintf("Prompt with ID '%s' not found", id), http.StatusNotFound)
		return
	}

	// Interpolate variables into the prompt content
	content := prompt.Content
	if execReq.Variables != nil {
		for key, value := range execReq.Variables {
			placeholder := fmt.Sprintf("{{%s}}", key)
			content = strings.ReplaceAll(content, placeholder, fmt.Sprintf("%v", value))
		}
	}

	// Prepare request to proxy-gateway
	proxyGatewayURL := os.Getenv("PROXY_GATEWAY_URL")
	if proxyGatewayURL == "" {
		proxyGatewayURL = "http://proxy-gateway:8080"
	}

	chatReq := map[string]interface{}{
		"model": execReq.ModelID,
		"messages": []map[string]string{
			{
				"role":    "user",
				"content": content,
			},
		},
	}

	// Add optional parameters
	if execReq.Temperature != nil {
		chatReq["temperature"] = *execReq.Temperature
	}
	if execReq.MaxTokens != nil {
		chatReq["max_tokens"] = *execReq.MaxTokens
	}

	chatReqJSON, err := json.Marshal(chatReq)
	if err != nil {
		http.Error(w, "Error preparing request", http.StatusInternalServerError)
		return
	}

	log.Printf("Calling proxy-gateway at %s with request: %s", proxyGatewayURL+"/v1/chat/completions", string(chatReqJSON))

	// Call proxy-gateway
	proxyReq, err := http.NewRequest("POST", proxyGatewayURL+"/v1/chat/completions", bytes.NewBuffer(chatReqJSON))
	if err != nil {
		http.Error(w, "Error creating request", http.StatusInternalServerError)
		return
	}

	proxyReq.Header.Set("Content-Type", "application/json")
	if execReq.ModelID != "" {
		proxyReq.Header.Set("X-Preferred-LLM-ID", execReq.ModelID)
	}

	// Copy relevant headers from original request
	if userID := r.Header.Get("X-User-ID"); userID != "" {
		proxyReq.Header.Set("X-User-ID", userID)
	}
	if userRoles := r.Header.Get("X-User-Roles"); userRoles != "" {
		proxyReq.Header.Set("X-User-Roles", userRoles)
	}

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(proxyReq)
	if err != nil {
		log.Printf("Error calling proxy-gateway: %v", err)
		http.Error(w, "Error calling proxy-gateway: "+err.Error(), http.StatusInternalServerError)
		return
	}
	defer resp.Body.Close()

	// Read response
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Error reading response: %v", err)
		http.Error(w, "Error reading response", http.StatusInternalServerError)
		return
	}

	// Check if the proxy-gateway returned an error status
	if resp.StatusCode != http.StatusOK {
		log.Printf("Proxy-gateway returned error status %d: %s", resp.StatusCode, string(respBody))
		http.Error(w, fmt.Sprintf("Proxy-gateway error: %s", string(respBody)), resp.StatusCode)
		return
	}

	// Parse response using the proper OpenAI response structure
	var chatResp struct {
		ID      string `json:"id"`
		Object  string `json:"object"`
		Created int64  `json:"created"`
		Model   string `json:"model"`
		Choices []struct {
			Index        int               `json:"index"`
			Message      map[string]string `json:"message"`
			LogProbs     interface{}       `json:"logprobs"`
			FinishReason string            `json:"finish_reason"`
		} `json:"choices"`
		Usage struct {
			PromptTokens     int `json:"prompt_tokens"`
			CompletionTokens int `json:"completion_tokens"`
			TotalTokens      int `json:"total_tokens"`
		} `json:"usage"`
	}

	if err := json.Unmarshal(respBody, &chatResp); err != nil {
		log.Printf("Error parsing response: %v. Response body: %s", err, string(respBody))
		http.Error(w, "Error parsing response", http.StatusInternalServerError)
		return
	}

	log.Printf("Parsed response: ID=%s, Model=%s, Choices=%d, Usage=%+v",
		chatResp.ID, chatResp.Model, len(chatResp.Choices), chatResp.Usage)

	// Extract response content
	var responseContent string
	if len(chatResp.Choices) > 0 {
		responseContent = chatResp.Choices[0].Message["content"]
	}

	// Extract usage information
	tokens := map[string]interface{}{
		"input":  chatResp.Usage.PromptTokens,
		"output": chatResp.Usage.CompletionTokens,
		"total":  chatResp.Usage.TotalTokens,
	}

	// Prepare execution result
	result := map[string]interface{}{
		"prompt_id": id,
		"model_id":  execReq.ModelID,
		"response":  responseContent,
		"tokens":    tokens,
		"status":    "success",
	}

	// Add cost calculation if available
	if chatResp.Usage.PromptTokens > 0 || chatResp.Usage.CompletionTokens > 0 {
		// This is a simplified cost calculation - in a real implementation,
		// you'd look up the model's pricing from the model profiles
		promptTokens := float64(chatResp.Usage.PromptTokens)
		completionTokens := float64(chatResp.Usage.CompletionTokens)

		// Example pricing (should be looked up from model profiles)
		inputCost := promptTokens * 0.000001      // $1 per 1M input tokens
		outputCost := completionTokens * 0.000002 // $2 per 1M output tokens
		result["cost"] = inputCost + outputCost
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(result)
	log.Printf("Prompt '%s' executed successfully with model '%s'", id, execReq.ModelID)
}

func getPromptTemplates(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	mu.RLock()
	defer mu.RUnlock()

	var templates []Prompt
	for _, prompt := range prompts {
		// Check if prompt has template tag
		for _, tag := range prompt.Tags {
			if tag == "template" {
				templates = append(templates, prompt)
				break
			}
		}
	}

	json.NewEncoder(w).Encode(templates)
}

func extractPromptVariables(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	var req struct {
		Content string `json:"content"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	variables := extractVariablesFromContent(req.Content)

	json.NewEncoder(w).Encode(map[string]interface{}{
		"variables": variables,
		"count":     len(variables),
	})
}

// --- Helper Functions ---

// getLatestVersion returns the latest version for a given prompt ID
func getLatestVersion(promptID string) string {
	latestVersion := "1.0.0"

	for key := range prompts {
		if strings.HasPrefix(key, promptID+":") {
			version := strings.Split(key, ":")[1]
			if compareVersions(version, latestVersion) > 0 {
				latestVersion = version
			}
		}
	}

	return latestVersion
}

// incrementVersion increments a semantic version string
func incrementVersion(version string) string {
	parts := strings.Split(version, ".")
	if len(parts) != 3 {
		return "1.0.1" // Default if invalid format
	}

	patch, err := strconv.Atoi(parts[2])
	if err != nil {
		return "1.0.1"
	}

	return fmt.Sprintf("%s.%s.%d", parts[0], parts[1], patch+1)
}

// compareVersions compares two semantic version strings
// Returns: -1 if v1 < v2, 0 if v1 == v2, 1 if v1 > v2
func compareVersions(v1, v2 string) int {
	parts1 := strings.Split(v1, ".")
	parts2 := strings.Split(v2, ".")

	// Ensure both have 3 parts
	for len(parts1) < 3 {
		parts1 = append(parts1, "0")
	}
	for len(parts2) < 3 {
		parts2 = append(parts2, "0")
	}

	for i := 0; i < 3; i++ {
		num1, _ := strconv.Atoi(parts1[i])
		num2, _ := strconv.Atoi(parts2[i])

		if num1 < num2 {
			return -1
		} else if num1 > num2 {
			return 1
		}
	}

	return 0
}

// equalStringSlices compares two string slices for equality
func equalStringSlices(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}

	for i, v := range a {
		if v != b[i] {
			return false
		}
	}

	return true
}

// equalVariables compares two PromptVariable slices for equality
func equalVariables(a, b []PromptVariable) bool {
	if len(a) != len(b) {
		return false
	}

	for i, v := range a {
		if v.Name != b[i].Name || v.Type != b[i].Type || v.Required != b[i].Required {
			return false
		}
	}

	return true
}

// extractVariablesFromContent extracts template variables from prompt content
func extractVariablesFromContent(content string) []PromptVariable {
	var variables []PromptVariable
	variableMap := make(map[string]bool)

	// Match {{variable_name}} pattern
	re := regexp.MustCompile(`\{\{([^}]+)\}\}`)
	matches := re.FindAllStringSubmatch(content, -1)

	for _, match := range matches {
		if len(match) > 1 {
			varName := strings.TrimSpace(match[1])
			if !variableMap[varName] {
				variables = append(variables, PromptVariable{
					Name:        varName,
					Type:        "string", // Default type
					Description: fmt.Sprintf("Variable: %s", varName),
					Required:    true, // Default to required
				})
				variableMap[varName] = true
			}
		}
	}

	return variables
}
