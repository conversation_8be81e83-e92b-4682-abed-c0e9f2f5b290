package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

// ModelFactsheet documents comprehensive model information including performance, ethics, and compliance
type ModelFactsheet struct {
	ID            string   `json:"id"`
	ModelName     string   `json:"model_name"`
	Version       string   `json:"version"`
	Description   string   `json:"description"`
	ModelType     string   `json:"model_type"` // "llm", "agent", "classifier", etc.
	IntendedUse   string   `json:"intended_use"`
	OutOfScopeUse []string `json:"out_of_scope_use"`

	// Model Details
	Architecture      string                 `json:"architecture"`
	Parameters        map[string]interface{} `json:"parameters"`
	TrainingData      TrainingDataInfo       `json:"training_data"`
	TrainingProcedure TrainingProcedure      `json:"training_procedure"`

	// Performance Metrics
	PerformanceMetrics map[string]interface{} `json:"performance_metrics"`
	BenchmarkResults   []BenchmarkResult      `json:"benchmark_results"`

	// Ethical and Bias Information
	BiasEvaluation        BiasEvaluation     `json:"bias_evaluation"`
	FairnessMetrics       map[string]float64 `json:"fairness_metrics"`
	EthicalConsiderations map[string]string  `json:"ethical_considerations"`

	// Risk and Safety
	RiskAssessment RiskAssessment `json:"risk_assessment"`
	SafetyMeasures []string       `json:"safety_measures"`
	Limitations    []string       `json:"limitations"`

	// Compliance and Governance
	ComplianceStatus    map[string]string `json:"compliance_status"`    // e.g., "EU_AI_Act": "compliant"
	GovernanceFramework []string          `json:"governance_framework"` // e.g., ["NIST_AI_RMF", "ISO_23053"]
	AuditTrail          []AuditEntry      `json:"audit_trail"`

	// Operational Information
	Lineage             map[string]string   `json:"lineage"`
	Dependencies        []string            `json:"dependencies"`
	EnvironmentalImpact EnvironmentalImpact `json:"environmental_impact"`

	// Metadata
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	CreatedBy      string    `json:"created_by"`
	LastReviewedAt time.Time `json:"last_reviewed_at"`
	NextReviewDue  time.Time `json:"next_review_due"`
}

// TrainingDataInfo contains information about training data
type TrainingDataInfo struct {
	Sources            []string               `json:"sources"`
	Size               map[string]interface{} `json:"size"` // e.g., {"tokens": 1000000, "documents": 50000}
	Demographics       map[string]interface{} `json:"demographics"`
	DataQuality        map[string]float64     `json:"data_quality"`
	PreprocessingSteps []string               `json:"preprocessing_steps"`
	PrivacyMeasures    []string               `json:"privacy_measures"`
}

// TrainingProcedure contains training methodology information
type TrainingProcedure struct {
	Algorithm        string                 `json:"algorithm"`
	Hyperparameters  map[string]interface{} `json:"hyperparameters"`
	TrainingTime     string                 `json:"training_time"`
	ComputeResources map[string]interface{} `json:"compute_resources"`
	ValidationMethod string                 `json:"validation_method"`
}

// BenchmarkResult contains benchmark performance data
type BenchmarkResult struct {
	BenchmarkName      string    `json:"benchmark_name"`
	Score              float64   `json:"score"`
	Metric             string    `json:"metric"`
	ComparisonBaseline float64   `json:"comparison_baseline"`
	TestDate           time.Time `json:"test_date"`
}

// BiasEvaluation contains bias assessment information
type BiasEvaluation struct {
	EvaluationDate       time.Time          `json:"evaluation_date"`
	MethodsUsed          []string           `json:"methods_used"` // e.g., ["AIF360", "Fairlearn"]
	ProtectedAttributes  []string           `json:"protected_attributes"`
	BiasMetrics          map[string]float64 `json:"bias_metrics"`
	MitigationStrategies []string           `json:"mitigation_strategies"`
}

// RiskAssessment contains risk analysis information
type RiskAssessment struct {
	RiskLevel          string    `json:"risk_level"` // "low", "medium", "high", "critical"
	IdentifiedRisks    []Risk    `json:"identified_risks"`
	MitigationMeasures []string  `json:"mitigation_measures"`
	ResidualRisk       string    `json:"residual_risk"`
	AssessmentDate     time.Time `json:"assessment_date"`
}

// Risk represents an identified risk
type Risk struct {
	Type        string `json:"type"` // e.g., "bias", "privacy", "security"
	Description string `json:"description"`
	Likelihood  string `json:"likelihood"` // "low", "medium", "high"
	Impact      string `json:"impact"`     // "low", "medium", "high"
	Severity    string `json:"severity"`   // "low", "medium", "high", "critical"
}

// AuditEntry represents an audit log entry
type AuditEntry struct {
	Timestamp time.Time              `json:"timestamp"`
	Action    string                 `json:"action"`
	User      string                 `json:"user"`
	Changes   map[string]interface{} `json:"changes"`
	Reason    string                 `json:"reason"`
}

// EnvironmentalImpact contains environmental impact metrics
type EnvironmentalImpact struct {
	CarbonFootprint   map[string]interface{} `json:"carbon_footprint"` // e.g., {"training": "50kg CO2", "inference": "0.1g CO2/query"}
	EnergyConsumption map[string]interface{} `json:"energy_consumption"`
	ComputeEfficiency map[string]float64     `json:"compute_efficiency"`
}

// --- New Governance Structures ---

// GovernancePolicy represents a governance policy that can be enforced
type GovernancePolicy struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Type        string                 `json:"type"`   // "access_control", "data_usage", "model_deployment", "compliance"
	Status      string                 `json:"status"` // "active", "inactive", "draft"
	Priority    int                    `json:"priority"`
	Rules       []PolicyRule           `json:"rules"`
	Scope       PolicyScope            `json:"scope"`
	Enforcement PolicyEnforcement      `json:"enforcement"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	CreatedBy   string                 `json:"created_by"`
}

// PolicyRule defines a specific rule within a policy
type PolicyRule struct {
	ID          string                 `json:"id"`
	Condition   string                 `json:"condition"` // e.g., "model_type == 'llm' AND data_sensitivity == 'high'"
	Action      string                 `json:"action"`    // "allow", "deny", "require_approval", "log", "alert"
	Parameters  map[string]interface{} `json:"parameters"`
	Description string                 `json:"description"`
}

// PolicyScope defines what the policy applies to
type PolicyScope struct {
	Models     []string `json:"models"`     // Model IDs or patterns
	Users      []string `json:"users"`      // User IDs or roles
	Services   []string `json:"services"`   // Service names
	Operations []string `json:"operations"` // API operations
	DataTypes  []string `json:"data_types"` // Data sensitivity levels
}

// PolicyEnforcement defines how the policy is enforced
type PolicyEnforcement struct {
	Mode           string   `json:"mode"` // "strict", "warn", "log"
	AutoRemediate  bool     `json:"auto_remediate"`
	NotifyUsers    []string `json:"notify_users"`
	EscalationPath []string `json:"escalation_path"`
}

// GovernanceAlert represents a governance violation or warning
type GovernanceAlert struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`     // "violation", "warning", "info"
	Severity    string                 `json:"severity"` // "critical", "high", "medium", "low"
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	PolicyID    string                 `json:"policy_id"`
	ModelID     string                 `json:"model_id"`
	UserID      string                 `json:"user_id"`
	ServiceID   string                 `json:"service_id"`
	Context     map[string]interface{} `json:"context"`
	Status      string                 `json:"status"` // "open", "acknowledged", "resolved", "dismissed"
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	ResolvedAt  *time.Time             `json:"resolved_at,omitempty"`
	ResolvedBy  string                 `json:"resolved_by,omitempty"`
}

// ComplianceStatus represents the compliance status of a model or service
type ComplianceStatus struct {
	ID           string                  `json:"id"`
	ModelID      string                  `json:"model_id"`
	ServiceID    string                  `json:"service_id"`
	Framework    string                  `json:"framework"` // "EU_AI_Act", "NIST_AI_RMF", "ISO_23053"
	Status       string                  `json:"status"`    // "compliant", "non_compliant", "pending", "unknown"
	Score        float64                 `json:"score"`     // 0.0 to 1.0
	Requirements []ComplianceRequirement `json:"requirements"`
	LastAudit    time.Time               `json:"last_audit"`
	NextAudit    time.Time               `json:"next_audit"`
	Evidence     []ComplianceEvidence    `json:"evidence"`
	Gaps         []ComplianceGap         `json:"gaps"`
	CreatedAt    time.Time               `json:"created_at"`
	UpdatedAt    time.Time               `json:"updated_at"`
}

// ComplianceRequirement represents a specific compliance requirement
type ComplianceRequirement struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Status      string `json:"status"` // "met", "not_met", "partial", "not_applicable"
	Evidence    string `json:"evidence"`
	Notes       string `json:"notes"`
}

// ComplianceEvidence represents evidence of compliance
type ComplianceEvidence struct {
	ID          string     `json:"id"`
	Type        string     `json:"type"` // "document", "test_result", "audit_log", "certification"
	Title       string     `json:"title"`
	Description string     `json:"description"`
	URL         string     `json:"url"`
	CreatedAt   time.Time  `json:"created_at"`
	ValidUntil  *time.Time `json:"valid_until,omitempty"`
}

// ComplianceGap represents a compliance gap that needs to be addressed
type ComplianceGap struct {
	ID            string    `json:"id"`
	RequirementID string    `json:"requirement_id"`
	Description   string    `json:"description"`
	Severity      string    `json:"severity"` // "critical", "high", "medium", "low"
	Impact        string    `json:"impact"`
	Remediation   string    `json:"remediation"`
	DueDate       time.Time `json:"due_date"`
	AssignedTo    string    `json:"assigned_to"`
	Status        string    `json:"status"` // "open", "in_progress", "resolved"
}

// WebSocket message types
type WebSocketMessage struct {
	Type    string      `json:"type"`
	Payload interface{} `json:"payload"`
}

// WebSocket client connection
type WebSocketClient struct {
	conn *websocket.Conn
	send chan WebSocketMessage
	hub  *WebSocketHub
	id   string
}

// WebSocket hub manages all client connections
type WebSocketHub struct {
	clients    map[*WebSocketClient]bool
	broadcast  chan WebSocketMessage
	register   chan *WebSocketClient
	unregister chan *WebSocketClient
	mu         sync.RWMutex
}

// GovernanceDatabase provides thread-safe, in-memory storage for all governance data.
type GovernanceDatabase struct {
	factsheets       map[string]ModelFactsheet
	policies         map[string]GovernancePolicy
	alerts           map[string]GovernanceAlert
	complianceStatus map[string]ComplianceStatus
	mu               sync.RWMutex

	// WebSocket hub for real-time updates
	wsHub *WebSocketHub

	// Service URLs for integration
	biasServiceURL           string
	explainabilityServiceURL string
	robustnessServiceURL     string
	complianceServiceURL     string
}

// NewWebSocketHub creates a new WebSocket hub
func NewWebSocketHub() *WebSocketHub {
	return &WebSocketHub{
		clients:    make(map[*WebSocketClient]bool),
		broadcast:  make(chan WebSocketMessage),
		register:   make(chan *WebSocketClient),
		unregister: make(chan *WebSocketClient),
	}
}

// Run starts the WebSocket hub
func (h *WebSocketHub) Run() {
	for {
		select {
		case client := <-h.register:
			h.mu.Lock()
			h.clients[client] = true
			h.mu.Unlock()
			log.Printf("WebSocket client connected: %s", client.id)

		case client := <-h.unregister:
			h.mu.Lock()
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)
				h.mu.Unlock()
				log.Printf("WebSocket client disconnected: %s", client.id)
			} else {
				h.mu.Unlock()
			}

		case message := <-h.broadcast:
			h.mu.RLock()
			for client := range h.clients {
				select {
				case client.send <- message:
				default:
					close(client.send)
					delete(h.clients, client)
				}
			}
			h.mu.RUnlock()
		}
	}
}

// NewGovernanceDatabase initializes a new in-memory database with sample data.
func NewGovernanceDatabase() *GovernanceDatabase {
	wsHub := NewWebSocketHub()
	go wsHub.Run()
	db := &GovernanceDatabase{
		factsheets:               make(map[string]ModelFactsheet),
		policies:                 make(map[string]GovernancePolicy),
		alerts:                   make(map[string]GovernanceAlert),
		complianceStatus:         make(map[string]ComplianceStatus),
		wsHub:                    wsHub,
		biasServiceURL:           "http://bias-detection-service:8084",
		explainabilityServiceURL: "http://explainability-service:8085",
		robustnessServiceURL:     "http://robustness-testing-service:8086",
		complianceServiceURL:     "http://compliance-service:8087",
	}

	// Initialize with comprehensive sample data
	db.initializeSampleFactsheets()
	db.initializeSamplePolicies()
	db.initializeSampleAlerts()
	db.initializeSampleComplianceStatus()

	return db
}

// initializeSampleFactsheets creates comprehensive sample model factsheets
func (db *GovernanceDatabase) initializeSampleFactsheets() {
	sampleFactsheets := []ModelFactsheet{
		{
			ID:            "factsheet-001",
			ModelName:     "GPT-4",
			Version:       "v1.0.0",
			Description:   "Large language model for general-purpose text generation and reasoning",
			ModelType:     "llm",
			IntendedUse:   "Text generation, question answering, code generation, and reasoning tasks",
			OutOfScopeUse: []string{"Medical diagnosis", "Legal advice", "Financial recommendations"},

			Architecture: "Transformer-based autoregressive language model",
			Parameters: map[string]interface{}{
				"total_parameters": "175B",
				"layers":           96,
				"attention_heads":  96,
				"embedding_dim":    12288,
			},
			TrainingData: TrainingDataInfo{
				Sources:            []string{"Web crawl", "Books", "Academic papers", "Reference materials"},
				Size:               map[string]interface{}{"tokens": "300B+", "documents": "millions"},
				Demographics:       map[string]interface{}{"languages": []string{"English", "Spanish", "French", "German", "Chinese"}},
				DataQuality:        map[string]float64{"completeness": 0.95, "accuracy": 0.92, "consistency": 0.89},
				PreprocessingSteps: []string{"Deduplication", "Quality filtering", "PII removal", "Content filtering"},
				PrivacyMeasures:    []string{"Differential privacy", "Data anonymization", "PII scrubbing"},
			},
			TrainingProcedure: TrainingProcedure{
				Algorithm:        "Autoregressive language modeling with reinforcement learning from human feedback",
				Hyperparameters:  map[string]interface{}{"learning_rate": 0.0001, "batch_size": 3200, "sequence_length": 8192},
				TrainingTime:     "Several months",
				ComputeResources: map[string]interface{}{"gpus": "thousands", "compute_hours": "millions"},
				ValidationMethod: "Held-out validation set with human evaluation",
			},

			PerformanceMetrics: map[string]interface{}{
				"perplexity":  15.2,
				"bleu_score":  0.85,
				"rouge_score": 0.82,
				"human_eval":  0.78,
			},
			BenchmarkResults: []BenchmarkResult{
				{BenchmarkName: "MMLU", Score: 86.4, Metric: "accuracy", ComparisonBaseline: 25.0, TestDate: time.Now().Add(-30 * 24 * time.Hour)},
				{BenchmarkName: "HellaSwag", Score: 95.3, Metric: "accuracy", ComparisonBaseline: 25.0, TestDate: time.Now().Add(-30 * 24 * time.Hour)},
				{BenchmarkName: "HumanEval", Score: 67.0, Metric: "pass@1", ComparisonBaseline: 0.0, TestDate: time.Now().Add(-30 * 24 * time.Hour)},
			},

			BiasEvaluation: BiasEvaluation{
				EvaluationDate:       time.Now().Add(-15 * 24 * time.Hour),
				MethodsUsed:          []string{"Fairlearn", "AIF360", "Custom bias probes"},
				ProtectedAttributes:  []string{"gender", "race", "religion", "nationality"},
				BiasMetrics:          map[string]float64{"gender_bias": 0.12, "racial_bias": 0.08, "religious_bias": 0.06},
				MitigationStrategies: []string{"Diverse training data", "Bias-aware fine-tuning", "Output filtering"},
			},
			FairnessMetrics: map[string]float64{
				"demographic_parity": 0.88,
				"equal_opportunity":  0.92,
				"disparate_impact":   1.15,
			},
			EthicalConsiderations: map[string]string{
				"transparency":   "Model architecture and training process documented",
				"accountability": "Clear ownership and responsibility chains established",
				"privacy":        "Training data privacy measures implemented",
				"safety":         "Content filtering and safety measures in place",
			},

			RiskAssessment: RiskAssessment{
				RiskLevel: "medium",
				IdentifiedRisks: []Risk{
					{Type: "bias", Description: "Potential gender and racial biases in outputs", Likelihood: "medium", Impact: "medium", Severity: "medium"},
					{Type: "misinformation", Description: "May generate false or misleading information", Likelihood: "medium", Impact: "high", Severity: "high"},
					{Type: "privacy", Description: "Potential memorization of training data", Likelihood: "low", Impact: "high", Severity: "medium"},
				},
				MitigationMeasures: []string{"Bias monitoring", "Fact-checking integration", "Privacy audits", "Content filtering"},
				ResidualRisk:       "low",
				AssessmentDate:     time.Now().Add(-7 * 24 * time.Hour),
			},
			SafetyMeasures: []string{"Content filtering", "Toxicity detection", "Prompt injection protection", "Rate limiting"},
			Limitations:    []string{"Knowledge cutoff date", "Potential hallucinations", "Computational requirements", "Language limitations"},

			ComplianceStatus: map[string]string{
				"EU_AI_Act":   "compliant",
				"GDPR":        "compliant",
				"NIST_AI_RMF": "aligned",
				"ISO_23053":   "in_progress",
			},
			GovernanceFramework: []string{"NIST_AI_RMF", "ISO_23053", "IEEE_2857"},
			AuditTrail: []AuditEntry{
				{Timestamp: time.Now().Add(-7 * 24 * time.Hour), Action: "created", User: "system", Changes: map[string]interface{}{"status": "created"}, Reason: "Initial factsheet creation"},
				{Timestamp: time.Now().Add(-3 * 24 * time.Hour), Action: "updated", User: "admin", Changes: map[string]interface{}{"bias_evaluation": "updated"}, Reason: "Quarterly bias assessment"},
			},

			Lineage: map[string]string{
				"base_model":         "GPT-3.5",
				"training_framework": "PyTorch",
				"data_pipeline":      "Custom preprocessing pipeline v2.1",
			},
			Dependencies: []string{"PyTorch", "CUDA", "Custom tokenizer", "Safety classifiers"},
			EnvironmentalImpact: EnvironmentalImpact{
				CarbonFootprint:   map[string]interface{}{"training": "552 tons CO2", "inference": "0.047g CO2/query"},
				EnergyConsumption: map[string]interface{}{"training": "1287 MWh", "inference": "0.0012 kWh/query"},
				ComputeEfficiency: map[string]float64{"flops_per_parameter": 6.0, "energy_efficiency": 0.85},
			},

			CreatedAt:      time.Now().Add(-30 * 24 * time.Hour),
			UpdatedAt:      time.Now().Add(-3 * 24 * time.Hour),
			CreatedBy:      "AI Governance Team",
			LastReviewedAt: time.Now().Add(-7 * 24 * time.Hour),
			NextReviewDue:  time.Now().Add(83 * 24 * time.Hour), // 90 days from last review
		},
		{
			ID:            "factsheet-002",
			ModelName:     "Planning Agent v2",
			Version:       "v2.1.0",
			Description:   "Specialized agent for autonomous task decomposition and planning",
			ModelType:     "agent",
			IntendedUse:   "Task planning, goal decomposition, workflow orchestration",
			OutOfScopeUse: []string{"Real-time safety-critical decisions", "Financial trading", "Medical treatment planning"},

			Architecture: "Hierarchical planning with reinforcement learning",
			Parameters: map[string]interface{}{
				"planning_depth":    10,
				"action_space_size": 1000,
				"state_dimensions":  512,
			},

			PerformanceMetrics: map[string]interface{}{
				"success_rate":      0.94,
				"planning_accuracy": 0.89,
				"efficiency_score":  0.87,
			},
			BenchmarkResults: []BenchmarkResult{
				{BenchmarkName: "Planning Benchmark Suite", Score: 89.2, Metric: "success_rate", ComparisonBaseline: 65.0, TestDate: time.Now().Add(-15 * 24 * time.Hour)},
			},

			BiasEvaluation: BiasEvaluation{
				EvaluationDate:       time.Now().Add(-10 * 24 * time.Hour),
				MethodsUsed:          []string{"Custom fairness metrics", "Task distribution analysis"},
				ProtectedAttributes:  []string{"task_type", "user_domain"},
				BiasMetrics:          map[string]float64{"task_type_bias": 0.05, "domain_bias": 0.03},
				MitigationStrategies: []string{"Balanced training scenarios", "Domain-agnostic design"},
			},

			RiskAssessment: RiskAssessment{
				RiskLevel: "low",
				IdentifiedRisks: []Risk{
					{Type: "performance", Description: "May fail on highly complex or novel tasks", Likelihood: "medium", Impact: "medium", Severity: "low"},
					{Type: "resource", Description: "High computational requirements for complex planning", Likelihood: "high", Impact: "low", Severity: "low"},
				},
				MitigationMeasures: []string{"Fallback mechanisms", "Resource monitoring", "Performance thresholds"},
				ResidualRisk:       "very_low",
				AssessmentDate:     time.Now().Add(-5 * 24 * time.Hour),
			},

			ComplianceStatus: map[string]string{
				"EU_AI_Act":   "compliant",
				"NIST_AI_RMF": "aligned",
			},
			GovernanceFramework: []string{"NIST_AI_RMF"},

			CreatedAt:      time.Now().Add(-60 * 24 * time.Hour),
			UpdatedAt:      time.Now().Add(-5 * 24 * time.Hour),
			CreatedBy:      "Planning Team",
			LastReviewedAt: time.Now().Add(-5 * 24 * time.Hour),
			NextReviewDue:  time.Now().Add(85 * 24 * time.Hour),
		},
	}

	for _, factsheet := range sampleFactsheets {
		db.factsheets[factsheet.ID] = factsheet
	}
}

// initializeSamplePolicies creates sample governance policies
func (db *GovernanceDatabase) initializeSamplePolicies() {
	samplePolicies := []GovernancePolicy{
		{
			ID:          "policy-001",
			Name:        "High-Risk AI Model Approval",
			Description: "Requires approval for deployment of high-risk AI models",
			Type:        "model_deployment",
			Status:      "active",
			Priority:    1,
			Rules: []PolicyRule{
				{
					ID:          "rule-001",
					Condition:   "risk_level == 'high' OR risk_level == 'critical'",
					Action:      "require_approval",
					Parameters:  map[string]interface{}{"approvers": []string{"ai-governance-team", "legal-team"}},
					Description: "High-risk models require approval from governance and legal teams",
				},
			},
			Scope: PolicyScope{
				Models:     []string{"*"},
				Users:      []string{"*"},
				Services:   []string{"*"},
				Operations: []string{"deploy", "update"},
				DataTypes:  []string{"high", "critical"},
			},
			Enforcement: PolicyEnforcement{
				Mode:           "strict",
				AutoRemediate:  false,
				NotifyUsers:    []string{"<EMAIL>"},
				EscalationPath: []string{"governance-lead", "cto"},
			},
			Metadata:  map[string]interface{}{"category": "risk_management", "version": "1.0"},
			CreatedAt: time.Now().Add(-30 * 24 * time.Hour),
			UpdatedAt: time.Now().Add(-5 * 24 * time.Hour),
			CreatedBy: "AI Governance Team",
		},
		{
			ID:          "policy-002",
			Name:        "Bias Monitoring Requirement",
			Description: "Mandatory bias monitoring for all LLM models",
			Type:        "compliance",
			Status:      "active",
			Priority:    2,
			Rules: []PolicyRule{
				{
					ID:          "rule-002",
					Condition:   "model_type == 'llm'",
					Action:      "require_bias_monitoring",
					Parameters:  map[string]interface{}{"frequency": "weekly", "thresholds": map[string]float64{"disparate_impact": 1.2}},
					Description: "All LLM models must undergo weekly bias monitoring",
				},
			},
			Scope: PolicyScope{
				Models:     []string{"gpt-*", "claude-*", "gemini-*"},
				Users:      []string{"*"},
				Services:   []string{"proxy-gateway", "planning-service"},
				Operations: []string{"inference", "training"},
				DataTypes:  []string{"*"},
			},
			Enforcement: PolicyEnforcement{
				Mode:           "warn",
				AutoRemediate:  true,
				NotifyUsers:    []string{"<EMAIL>"},
				EscalationPath: []string{"responsible-ai-lead"},
			},
			Metadata:  map[string]interface{}{"category": "bias_prevention", "version": "1.1"},
			CreatedAt: time.Now().Add(-60 * 24 * time.Hour),
			UpdatedAt: time.Now().Add(-10 * 24 * time.Hour),
			CreatedBy: "Responsible AI Team",
		},
	}

	for _, policy := range samplePolicies {
		db.policies[policy.ID] = policy
	}
}

// initializeSampleAlerts creates sample governance alerts
func (db *GovernanceDatabase) initializeSampleAlerts() {
	sampleAlerts := []GovernanceAlert{
		{
			ID:          "alert-001",
			Type:        "violation",
			Severity:    "high",
			Title:       "Bias Threshold Exceeded",
			Description: "Model GPT-4 has exceeded the bias threshold for gender discrimination",
			PolicyID:    "policy-002",
			ModelID:     "gpt-4",
			UserID:      "system",
			ServiceID:   "bias-detection-service",
			Context: map[string]interface{}{
				"bias_metric":    "disparate_impact",
				"threshold":      1.2,
				"actual_value":   1.35,
				"protected_attr": "gender",
			},
			Status:    "open",
			CreatedAt: time.Now().Add(-2 * time.Hour),
			UpdatedAt: time.Now().Add(-2 * time.Hour),
		},
		{
			ID:          "alert-002",
			Type:        "warning",
			Severity:    "medium",
			Title:       "Model Performance Degradation",
			Description: "Planning Agent v2 showing decreased performance in robustness tests",
			PolicyID:    "",
			ModelID:     "planning-agent-v2",
			UserID:      "system",
			ServiceID:   "robustness-testing-service",
			Context: map[string]interface{}{
				"test_type":       "adversarial",
				"success_rate":    0.72,
				"baseline":        0.85,
				"degradation_pct": 15.3,
			},
			Status:    "acknowledged",
			CreatedAt: time.Now().Add(-6 * time.Hour),
			UpdatedAt: time.Now().Add(-1 * time.Hour),
		},
	}

	for _, alert := range sampleAlerts {
		db.alerts[alert.ID] = alert
	}
}

// initializeSampleComplianceStatus creates sample compliance status records
func (db *GovernanceDatabase) initializeSampleComplianceStatus() {
	sampleComplianceStatus := []ComplianceStatus{
		{
			ID:        "compliance-001",
			ModelID:   "gpt-4",
			ServiceID: "proxy-gateway",
			Framework: "EU_AI_Act",
			Status:    "compliant",
			Score:     0.92,
			Requirements: []ComplianceRequirement{
				{
					ID:          "req-001",
					Name:        "Risk Assessment",
					Description: "Comprehensive risk assessment documentation",
					Status:      "met",
					Evidence:    "Risk assessment document v2.1 completed",
					Notes:       "Updated quarterly as required",
				},
				{
					ID:          "req-002",
					Name:        "Human Oversight",
					Description: "Human oversight mechanisms in place",
					Status:      "met",
					Evidence:    "Human-in-the-loop validation system implemented",
					Notes:       "Automated escalation for high-risk decisions",
				},
			},
			LastAudit: time.Now().Add(-30 * 24 * time.Hour),
			NextAudit: time.Now().Add(60 * 24 * time.Hour),
			Evidence: []ComplianceEvidence{
				{
					ID:          "evidence-001",
					Type:        "document",
					Title:       "EU AI Act Compliance Report",
					Description: "Comprehensive compliance assessment for GPT-4",
					URL:         "/compliance/reports/gpt-4-eu-ai-act-2024.pdf",
					CreatedAt:   time.Now().Add(-30 * 24 * time.Hour),
				},
			},
			Gaps:      []ComplianceGap{},
			CreatedAt: time.Now().Add(-90 * 24 * time.Hour),
			UpdatedAt: time.Now().Add(-30 * 24 * time.Hour),
		},
	}

	for _, status := range sampleComplianceStatus {
		db.complianceStatus[status.ID] = status
	}
}

// --- Database CRUD Operations ---

// Factsheet operations
func (db *GovernanceDatabase) CreateFactsheet(factsheet ModelFactsheet) (ModelFactsheet, error) {
	db.mu.Lock()
	defer db.mu.Unlock()

	factsheet.ID = uuid.New().String()
	factsheet.CreatedAt = time.Now()
	factsheet.UpdatedAt = time.Now()
	db.factsheets[factsheet.ID] = factsheet

	log.Printf("Created factsheet %s for model '%s'", factsheet.ID, factsheet.ModelName)
	return factsheet, nil
}

func (db *GovernanceDatabase) ReadFactsheet(id string) (ModelFactsheet, bool) {
	db.mu.RLock()
	defer db.mu.RUnlock()
	factsheet, ok := db.factsheets[id]
	return factsheet, ok
}

func (db *GovernanceDatabase) ReadAllFactsheets() []ModelFactsheet {
	db.mu.RLock()
	defer db.mu.RUnlock()
	all := make([]ModelFactsheet, 0, len(db.factsheets))
	for _, f := range db.factsheets {
		all = append(all, f)
	}
	return all
}

func (db *GovernanceDatabase) UpdateFactsheet(id string, factsheet ModelFactsheet) (ModelFactsheet, bool) {
	db.mu.Lock()
	defer db.mu.Unlock()

	if _, ok := db.factsheets[id]; !ok {
		return ModelFactsheet{}, false
	}
	factsheet.ID = id
	factsheet.UpdatedAt = time.Now()
	db.factsheets[id] = factsheet
	log.Printf("Updated factsheet %s", id)
	return factsheet, true
}

func (db *GovernanceDatabase) DeleteFactsheet(id string) bool {
	db.mu.Lock()
	defer db.mu.Unlock()

	if _, ok := db.factsheets[id]; !ok {
		return false
	}
	delete(db.factsheets, id)
	log.Printf("Deleted factsheet %s", id)
	return true
}

// Policy operations
func (db *GovernanceDatabase) CreatePolicy(policy GovernancePolicy) (GovernancePolicy, error) {
	db.mu.Lock()
	defer db.mu.Unlock()

	policy.ID = uuid.New().String()
	policy.CreatedAt = time.Now()
	policy.UpdatedAt = time.Now()
	db.policies[policy.ID] = policy

	log.Printf("Created governance policy %s: %s", policy.ID, policy.Name)
	return policy, nil
}

func (db *GovernanceDatabase) ReadPolicy(id string) (GovernancePolicy, bool) {
	db.mu.RLock()
	defer db.mu.RUnlock()
	policy, ok := db.policies[id]
	return policy, ok
}

func (db *GovernanceDatabase) ReadAllPolicies() []GovernancePolicy {
	db.mu.RLock()
	defer db.mu.RUnlock()
	all := make([]GovernancePolicy, 0, len(db.policies))
	for _, p := range db.policies {
		all = append(all, p)
	}
	return all
}

func (db *GovernanceDatabase) UpdatePolicy(id string, policy GovernancePolicy) (GovernancePolicy, bool) {
	db.mu.Lock()
	defer db.mu.Unlock()

	if _, ok := db.policies[id]; !ok {
		return GovernancePolicy{}, false
	}
	policy.ID = id
	policy.UpdatedAt = time.Now()
	db.policies[id] = policy
	log.Printf("Updated governance policy %s", id)

	// Broadcast policy update to WebSocket clients
	go db.broadcastMessage("policy_update", policy)

	return policy, true
}

func (db *GovernanceDatabase) DeletePolicy(id string) bool {
	db.mu.Lock()
	defer db.mu.Unlock()

	if _, ok := db.policies[id]; !ok {
		return false
	}
	delete(db.policies, id)
	log.Printf("Deleted governance policy %s", id)
	return true
}

// Alert operations
func (db *GovernanceDatabase) CreateAlert(alert GovernanceAlert) (GovernanceAlert, error) {
	db.mu.Lock()
	defer db.mu.Unlock()

	alert.ID = uuid.New().String()
	alert.CreatedAt = time.Now()
	alert.UpdatedAt = time.Now()
	db.alerts[alert.ID] = alert

	log.Printf("Created governance alert %s: %s", alert.ID, alert.Title)

	// Broadcast alert to WebSocket clients
	go db.broadcastMessage("alert", alert)

	return alert, nil
}

func (db *GovernanceDatabase) ReadAlert(id string) (GovernanceAlert, bool) {
	db.mu.RLock()
	defer db.mu.RUnlock()
	alert, ok := db.alerts[id]
	return alert, ok
}

func (db *GovernanceDatabase) ReadAllAlerts() []GovernanceAlert {
	db.mu.RLock()
	defer db.mu.RUnlock()
	all := make([]GovernanceAlert, 0, len(db.alerts))
	for _, a := range db.alerts {
		all = append(all, a)
	}
	return all
}

func (db *GovernanceDatabase) UpdateAlert(id string, alert GovernanceAlert) (GovernanceAlert, bool) {
	db.mu.Lock()
	defer db.mu.Unlock()

	if _, ok := db.alerts[id]; !ok {
		return GovernanceAlert{}, false
	}
	alert.ID = id
	alert.UpdatedAt = time.Now()
	db.alerts[id] = alert
	log.Printf("Updated governance alert %s", id)

	// Broadcast alert update to WebSocket clients
	go db.broadcastMessage("alert_update", alert)

	return alert, true
}

func (db *GovernanceDatabase) DeleteAlert(id string) bool {
	db.mu.Lock()
	defer db.mu.Unlock()

	if _, ok := db.alerts[id]; !ok {
		return false
	}
	delete(db.alerts, id)
	log.Printf("Deleted governance alert %s", id)
	return true
}

// Compliance Status operations
func (db *GovernanceDatabase) CreateComplianceStatus(status ComplianceStatus) (ComplianceStatus, error) {
	db.mu.Lock()
	defer db.mu.Unlock()

	status.ID = uuid.New().String()
	status.CreatedAt = time.Now()
	status.UpdatedAt = time.Now()
	db.complianceStatus[status.ID] = status

	log.Printf("Created compliance status %s for model %s", status.ID, status.ModelID)

	// Broadcast compliance update to WebSocket clients
	go db.broadcastMessage("compliance_update", status)

	return status, nil
}

func (db *GovernanceDatabase) ReadComplianceStatus(id string) (ComplianceStatus, bool) {
	db.mu.RLock()
	defer db.mu.RUnlock()
	status, ok := db.complianceStatus[id]
	return status, ok
}

func (db *GovernanceDatabase) ReadAllComplianceStatus() []ComplianceStatus {
	db.mu.RLock()
	defer db.mu.RUnlock()
	all := make([]ComplianceStatus, 0, len(db.complianceStatus))
	for _, s := range db.complianceStatus {
		all = append(all, s)
	}
	return all
}

// --- HTTP Handlers ---

func factsheetHandler(db *GovernanceDatabase) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		switch r.Method {
		case http.MethodGet:
			queryID := r.URL.Query().Get("id")
			if queryID != "" {
				// Get a single factsheet by ID
				factsheet, ok := db.ReadFactsheet(queryID)
				if !ok {
					http.Error(w, `{"error": "Factsheet not found"}`, http.StatusNotFound)
					return
				}
				json.NewEncoder(w).Encode(factsheet)
			} else {
				// Get all factsheets
				allFactsheets := db.ReadAllFactsheets()
				json.NewEncoder(w).Encode(allFactsheets)
			}
		case http.MethodPost:
			var factsheet ModelFactsheet
			if err := json.NewDecoder(r.Body).Decode(&factsheet); err != nil {
				http.Error(w, fmt.Sprintf(`{"error": "Invalid request body: %v"}`, err), http.StatusBadRequest)
				return
			}
			createdFactsheet, _ := db.CreateFactsheet(factsheet)
			w.WriteHeader(http.StatusCreated)
			json.NewEncoder(w).Encode(createdFactsheet)
		case http.MethodPut:
			queryID := r.URL.Query().Get("id")
			if queryID == "" {
				http.Error(w, `{"error": "Missing 'id' query parameter for update"}`, http.StatusBadRequest)
				return
			}
			var factsheet ModelFactsheet
			if err := json.NewDecoder(r.Body).Decode(&factsheet); err != nil {
				http.Error(w, fmt.Sprintf(`{"error": "Invalid request body: %v"}`, err), http.StatusBadRequest)
				return
			}
			updatedFactsheet, ok := db.UpdateFactsheet(queryID, factsheet)
			if !ok {
				http.Error(w, `{"error": "Factsheet not found"}`, http.StatusNotFound)
				return
			}
			json.NewEncoder(w).Encode(updatedFactsheet)
		case http.MethodDelete:
			queryID := r.URL.Query().Get("id")
			if queryID == "" {
				http.Error(w, `{"error": "Missing 'id' query parameter for delete"}`, http.StatusBadRequest)
				return
			}
			if !db.DeleteFactsheet(queryID) {
				http.Error(w, `{"error": "Factsheet not found"}`, http.StatusNotFound)
				return
			}
			w.WriteHeader(http.StatusNoContent)
		default:
			http.Error(w, `{"error": "Method not allowed"}`, http.StatusMethodNotAllowed)
		}
	}
}

// Policy handlers
func policyHandler(db *GovernanceDatabase) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		switch r.Method {
		case http.MethodGet:
			queryID := r.URL.Query().Get("id")
			if queryID != "" {
				policy, ok := db.ReadPolicy(queryID)
				if !ok {
					http.Error(w, `{"error": "Policy not found"}`, http.StatusNotFound)
					return
				}
				json.NewEncoder(w).Encode(policy)
			} else {
				allPolicies := db.ReadAllPolicies()
				json.NewEncoder(w).Encode(allPolicies)
			}
		case http.MethodPost:
			var policy GovernancePolicy
			if err := json.NewDecoder(r.Body).Decode(&policy); err != nil {
				http.Error(w, fmt.Sprintf(`{"error": "Invalid request body: %v"}`, err), http.StatusBadRequest)
				return
			}
			createdPolicy, _ := db.CreatePolicy(policy)
			w.WriteHeader(http.StatusCreated)
			json.NewEncoder(w).Encode(createdPolicy)
		case http.MethodPut:
			queryID := r.URL.Query().Get("id")
			if queryID == "" {
				http.Error(w, `{"error": "Missing 'id' query parameter for update"}`, http.StatusBadRequest)
				return
			}
			var policy GovernancePolicy
			if err := json.NewDecoder(r.Body).Decode(&policy); err != nil {
				http.Error(w, fmt.Sprintf(`{"error": "Invalid request body: %v"}`, err), http.StatusBadRequest)
				return
			}
			updatedPolicy, ok := db.UpdatePolicy(queryID, policy)
			if !ok {
				http.Error(w, `{"error": "Policy not found"}`, http.StatusNotFound)
				return
			}
			json.NewEncoder(w).Encode(updatedPolicy)
		case http.MethodDelete:
			queryID := r.URL.Query().Get("id")
			if queryID == "" {
				http.Error(w, `{"error": "Missing 'id' query parameter for delete"}`, http.StatusBadRequest)
				return
			}
			if !db.DeletePolicy(queryID) {
				http.Error(w, `{"error": "Policy not found"}`, http.StatusNotFound)
				return
			}
			w.WriteHeader(http.StatusNoContent)
		default:
			http.Error(w, `{"error": "Method not allowed"}`, http.StatusMethodNotAllowed)
		}
	}
}

// Alert handlers
func alertHandler(db *GovernanceDatabase) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		switch r.Method {
		case http.MethodGet:
			queryID := r.URL.Query().Get("id")
			if queryID != "" {
				alert, ok := db.ReadAlert(queryID)
				if !ok {
					http.Error(w, `{"error": "Alert not found"}`, http.StatusNotFound)
					return
				}
				json.NewEncoder(w).Encode(alert)
			} else {
				allAlerts := db.ReadAllAlerts()
				json.NewEncoder(w).Encode(allAlerts)
			}
		case http.MethodPost:
			var alert GovernanceAlert
			if err := json.NewDecoder(r.Body).Decode(&alert); err != nil {
				http.Error(w, fmt.Sprintf(`{"error": "Invalid request body: %v"}`, err), http.StatusBadRequest)
				return
			}
			createdAlert, _ := db.CreateAlert(alert)
			w.WriteHeader(http.StatusCreated)
			json.NewEncoder(w).Encode(createdAlert)
		case http.MethodPut:
			queryID := r.URL.Query().Get("id")
			if queryID == "" {
				http.Error(w, `{"error": "Missing 'id' query parameter for update"}`, http.StatusBadRequest)
				return
			}
			var alert GovernanceAlert
			if err := json.NewDecoder(r.Body).Decode(&alert); err != nil {
				http.Error(w, fmt.Sprintf(`{"error": "Invalid request body: %v"}`, err), http.StatusBadRequest)
				return
			}
			updatedAlert, ok := db.UpdateAlert(queryID, alert)
			if !ok {
				http.Error(w, `{"error": "Alert not found"}`, http.StatusNotFound)
				return
			}
			json.NewEncoder(w).Encode(updatedAlert)
		case http.MethodDelete:
			queryID := r.URL.Query().Get("id")
			if queryID == "" {
				http.Error(w, `{"error": "Missing 'id' query parameter for delete"}`, http.StatusBadRequest)
				return
			}
			if !db.DeleteAlert(queryID) {
				http.Error(w, `{"error": "Alert not found"}`, http.StatusNotFound)
				return
			}
			w.WriteHeader(http.StatusNoContent)
		default:
			http.Error(w, `{"error": "Method not allowed"}`, http.StatusMethodNotAllowed)
		}
	}
}

// Compliance status handlers
func complianceStatusHandler(db *GovernanceDatabase) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		switch r.Method {
		case http.MethodGet:
			queryID := r.URL.Query().Get("id")
			if queryID != "" {
				status, ok := db.ReadComplianceStatus(queryID)
				if !ok {
					http.Error(w, `{"error": "Compliance status not found"}`, http.StatusNotFound)
					return
				}
				json.NewEncoder(w).Encode(status)
			} else {
				allStatus := db.ReadAllComplianceStatus()
				json.NewEncoder(w).Encode(allStatus)
			}
		case http.MethodPost:
			var status ComplianceStatus
			if err := json.NewDecoder(r.Body).Decode(&status); err != nil {
				http.Error(w, fmt.Sprintf(`{"error": "Invalid request body: %v"}`, err), http.StatusBadRequest)
				return
			}
			createdStatus, _ := db.CreateComplianceStatus(status)
			w.WriteHeader(http.StatusCreated)
			json.NewEncoder(w).Encode(createdStatus)
		default:
			http.Error(w, `{"error": "Method not allowed"}`, http.StatusMethodNotAllowed)
		}
	}
}

// WebSocket upgrader
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // Allow all origins for development
	},
}

// WebSocket client read pump
func (c *WebSocketClient) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	for {
		_, _, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}
	}
}

// WebSocket client write pump
func (c *WebSocketClient) writePump() {
	defer c.conn.Close()

	for message := range c.send {
		if err := c.conn.WriteJSON(message); err != nil {
			log.Printf("WebSocket write error: %v", err)
			return
		}
	}
}

// WebSocket handler
func (db *GovernanceDatabase) handleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade error: %v", err)
		return
	}

	client := &WebSocketClient{
		conn: conn,
		send: make(chan WebSocketMessage, 256),
		hub:  db.wsHub,
		id:   uuid.New().String(),
	}

	client.hub.register <- client

	go client.writePump()
	go client.readPump()
}

// Broadcast message to all WebSocket clients
func (db *GovernanceDatabase) broadcastMessage(msgType string, payload interface{}) {
	message := WebSocketMessage{
		Type:    msgType,
		Payload: payload,
	}

	select {
	case db.wsHub.broadcast <- message:
	default:
		log.Printf("WebSocket broadcast channel full, dropping message")
	}
}

// CORS middleware
func enableCORS(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "*")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next(w, r)
	}
}

// Audit logs handler
func auditLogsHandler(db *GovernanceDatabase) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")

		// Get timeframe parameter
		timeframe := r.URL.Query().Get("timeframe")
		if timeframe == "" {
			timeframe = "7d"
		}

		// Generate mock audit logs based on timeframe
		auditLogs := []map[string]interface{}{
			{
				"id":        "audit-001",
				"timestamp": time.Now().Add(-time.Hour * 2).Format(time.RFC3339),
				"action":    "policy_created",
				"user":      "<EMAIL>",
				"resource":  "governance-policy-001",
				"details":   "Created new bias detection policy",
				"severity":  "info",
			},
			{
				"id":        "audit-002",
				"timestamp": time.Now().Add(-time.Hour * 6).Format(time.RFC3339),
				"action":    "compliance_check",
				"user":      "system",
				"resource":  "model-gpt-4",
				"details":   "Automated compliance check completed",
				"severity":  "info",
			},
			{
				"id":        "audit-003",
				"timestamp": time.Now().Add(-time.Hour * 12).Format(time.RFC3339),
				"action":    "alert_triggered",
				"user":      "system",
				"resource":  "bias-detection",
				"details":   "Bias threshold exceeded for model predictions",
				"severity":  "warning",
			},
		}

		json.NewEncoder(w).Encode(auditLogs)
	}
}

// Metrics handler
func metricsHandler(db *GovernanceDatabase) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")

		// Get timeframe parameter
		timeframe := r.URL.Query().Get("timeframe")
		if timeframe == "" {
			timeframe = "7d"
		}

		// Generate governance metrics
		metrics := map[string]interface{}{
			"compliance_score":   85.5,
			"active_policies":    len(db.ReadAllPolicies()),
			"total_alerts":       len(db.ReadAllAlerts()),
			"resolved_alerts":    12,
			"pending_alerts":     3,
			"models_monitored":   15,
			"bias_checks_passed": 142,
			"bias_checks_failed": 8,
			"audit_events":       156,
			"policy_violations":  2,
			"timeframe":          timeframe,
			"last_updated":       time.Now().Format(time.RFC3339),
		}

		json.NewEncoder(w).Encode(metrics)
	}
}

func main() {
	db := NewGovernanceDatabase()
	fmt.Println("Enhanced Governance service starting with comprehensive AI governance capabilities...")

	// API endpoints with CORS
	http.HandleFunc("/factsheets", enableCORS(factsheetHandler(db)))
	http.HandleFunc("/policies", enableCORS(policyHandler(db)))
	http.HandleFunc("/alerts", enableCORS(alertHandler(db)))
	http.HandleFunc("/compliance-status", enableCORS(complianceStatusHandler(db)))
	http.HandleFunc("/audit-logs", enableCORS(auditLogsHandler(db)))
	http.HandleFunc("/metrics", enableCORS(metricsHandler(db)))

	// WebSocket endpoint
	http.HandleFunc("/ws/governance", db.handleWebSocket)

	// Health check endpoint
	http.HandleFunc("/health", enableCORS(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":    "healthy",
			"service":   "governance-service",
			"timestamp": time.Now(),
			"features": []string{
				"model_factsheets",
				"governance_policies",
				"compliance_monitoring",
				"alert_management",
				"responsible_ai_integration",
			},
		})
	}))

	// Root handler
	http.HandleFunc("/", enableCORS(func(w http.ResponseWriter, r *http.Request) {
		fmt.Fprint(w, "Enhanced Governance service is running with comprehensive AI governance capabilities.")
	}))

	// Set the port for the HTTP server
	port := ":8080"
	fmt.Printf("Listening on port %s...\n", port)

	// Start the HTTP server
	log.Fatal(http.ListenAndServe(port, nil))
}
