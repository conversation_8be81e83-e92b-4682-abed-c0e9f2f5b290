# Build Stage
FROM golang:1.24.2-alpine AS builder

WORKDIR /app

# Install dependencies for private repositories if needed
# RUN apk add --no-cache git openssh

# Copy module files and download dependencies
COPY go.mod go.sum ./
RUN go mod download

# Copy the source code
COPY . .

# Build the application
# CGO_ENABLED=0 is for static linking
# -o /governance-service specifies the output binary name
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o /governance-service .

# Final Stage
FROM alpine:latest

# It's good practice to run as a non-root user
RUN addgroup -S appgroup && adduser -S appuser -G appgroup
USER appuser

# Copy the binary from the builder stage
COPY --from=builder /governance-service /governance-service

# Expose the port the application runs on
EXPOSE 8080

# Run the application
CMD ["/governance-service"]