package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/gorilla/mux"
	"github.com/rs/cors"
)

// MultiAgentOrchestrator is the main service for coordinating multiple AI agents
type MultiAgentOrchestrator struct {
	agentRegistry    *AgentRegistry
	workflowDesigner *WorkflowDesigner
	executionEngine  *MultiAgentExecutionEngine
	communicationHub *CommunicationHub
	agentSelector    *AgentSelector
	securityManager  *SecurityManager
	storage          *MemoryStorage
	server           *http.Server
}

// MemoryStorage provides in-memory storage for all components
type MemoryStorage struct {
	agentStorage    *MemoryAgentStorage
	workflowStorage *MemoryWorkflowStorage
}

// NewMultiAgentOrchestrator creates a new multi-agent orchestrator
func NewMultiAgentOrchestrator() *MultiAgentOrchestrator {
	// Initialize storage
	storage := &MemoryStorage{
		agentStorage:    NewMemoryAgentStorage(),
		workflowStorage: NewMemoryWorkflowStorage(),
	}

	// Initialize agent registry
	governanceServiceURL := os.Getenv("GOVERNANCE_SERVICE_URL")
	if governanceServiceURL == "" {
		governanceServiceURL = "http://governance-service:8080" // Default value
		log.Printf("GOVERNANCE_SERVICE_URL not set, using default: %s", governanceServiceURL)
	}

	agentRegistry := NewAgentRegistry(storage.agentStorage, governanceServiceURL)

	// Initialize workflow designer
	workflowDesigner := NewWorkflowDesigner(agentRegistry, storage.workflowStorage)

	// Initialize communication hub
	communicationHub := NewCommunicationHub()

	// Initialize execution engine
	executionEngine := NewMultiAgentExecutionEngine(agentRegistry, communicationHub, storage.workflowStorage)

	// Initialize advanced agent selector
	agentSelector := NewAgentSelector(agentRegistry)

	// Initialize security manager
	securityManager := NewSecurityManager()

	return &MultiAgentOrchestrator{
		agentRegistry:    agentRegistry,
		workflowDesigner: workflowDesigner,
		executionEngine:  executionEngine,
		communicationHub: communicationHub,
		agentSelector:    agentSelector,
		securityManager:  securityManager,
		storage:          storage,
	}
}

// Start starts the multi-agent orchestrator service
func (mao *MultiAgentOrchestrator) Start() error {
	router := mux.NewRouter()

	// Health check endpoint (no auth required)
	router.HandleFunc("/health", mao.healthCheck).Methods("GET")

	// Authentication endpoints (no auth required)
	router.HandleFunc("/auth/login", mao.login).Methods("POST")
	router.HandleFunc("/auth/logout", mao.logout).Methods("POST")

	// API routes
	api := router.PathPrefix("/v1").Subrouter()

	// Agent management endpoints
	api.HandleFunc("/agents", mao.listAgents).Methods("GET")
	api.HandleFunc("/agents", mao.registerAgent).Methods("POST")
	api.HandleFunc("/agents/{id}", mao.getAgent).Methods("GET")
	api.HandleFunc("/agents/{id}", mao.updateAgent).Methods("PUT")
	api.HandleFunc("/agents/{id}", mao.unregisterAgent).Methods("DELETE")
	api.HandleFunc("/agents/{id}/status", mao.updateAgentStatus).Methods("PUT")
	api.HandleFunc("/agents/{id}/health", mao.checkAgentHealth).Methods("POST")
	api.HandleFunc("/agents/{id}/insights", mao.getAgentInsights).Methods("GET")
	api.HandleFunc("/agents/select", mao.selectOptimalAgent).Methods("POST")

	// Workflow management endpoints
	api.HandleFunc("/workflows", mao.listWorkflows).Methods("GET")
	api.HandleFunc("/workflows", mao.createWorkflow).Methods("POST")
	api.HandleFunc("/workflows/{id}", mao.getWorkflow).Methods("GET")
	api.HandleFunc("/workflows/{id}", mao.updateWorkflow).Methods("PUT")
	api.HandleFunc("/workflows/{id}", mao.deleteWorkflow).Methods("DELETE")
	api.HandleFunc("/workflows/{id}/validate", mao.validateWorkflow).Methods("POST")

	// Workflow execution endpoints
	api.HandleFunc("/workflows/{id}/execute", mao.executeWorkflow).Methods("POST")
	api.HandleFunc("/workflows/{id}/pause", mao.pauseWorkflow).Methods("POST")
	api.HandleFunc("/workflows/{id}/resume", mao.resumeWorkflow).Methods("POST")
	api.HandleFunc("/workflows/{id}/cancel", mao.cancelWorkflow).Methods("POST")
	api.HandleFunc("/workflows/{id}/status", mao.getWorkflowStatus).Methods("GET")

	// Template endpoints
	api.HandleFunc("/templates", mao.listTemplates).Methods("GET")
	api.HandleFunc("/templates/{id}", mao.getTemplate).Methods("GET")

	// Analytics and monitoring endpoints
	api.HandleFunc("/analytics/agents", mao.getAgentAnalytics).Methods("GET")
	api.HandleFunc("/analytics/workflows", mao.getWorkflowAnalytics).Methods("GET")
	api.HandleFunc("/analytics/collaboration", mao.getCollaborationAnalytics).Methods("GET")

	// Health check
	router.HandleFunc("/health", mao.healthCheck).Methods("GET")

	// Setup CORS
	c := cors.New(cors.Options{
		AllowedOrigins: []string{"*"},
		AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders: []string{"*"},
	})

	handler := c.Handler(router)

	// Create HTTP server
	mao.server = &http.Server{
		Addr:         ":8083",
		Handler:      handler,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	log.Println("Starting Multi-Agent Orchestrator on :8083")
	return mao.server.ListenAndServe()
}

// Stop gracefully stops the orchestrator
func (mao *MultiAgentOrchestrator) Stop() error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	log.Println("Shutting down Multi-Agent Orchestrator...")

	// Stop execution engine
	mao.executionEngine.Stop()

	// Stop communication hub
	mao.communicationHub.Stop()

	// Stop HTTP server
	return mao.server.Shutdown(ctx)
}

// Agent management handlers

func (mao *MultiAgentOrchestrator) listAgents(w http.ResponseWriter, r *http.Request) {
	agents := mao.agentRegistry.ListAgents()
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(agents)
}

func (mao *MultiAgentOrchestrator) registerAgent(w http.ResponseWriter, r *http.Request) {
	var agent Agent
	if err := json.NewDecoder(r.Body).Decode(&agent); err != nil {
		http.Error(w, fmt.Sprintf("Invalid request body: %v", err), http.StatusBadRequest)
		return
	}

	if err := mao.agentRegistry.RegisterAgent(agent); err != nil {
		http.Error(w, fmt.Sprintf("Failed to register agent: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(agent)
}

func (mao *MultiAgentOrchestrator) getAgent(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	agentID := vars["id"]

	agent, err := mao.agentRegistry.GetAgent(agentID)
	if err != nil {
		http.Error(w, fmt.Sprintf("Agent not found: %v", err), http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(agent)
}

func (mao *MultiAgentOrchestrator) updateAgent(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	agentID := vars["id"]

	var updates map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&updates); err != nil {
		http.Error(w, fmt.Sprintf("Invalid request body: %v", err), http.StatusBadRequest)
		return
	}

	// For now, we'll implement basic status updates
	if status, ok := updates["status"].(string); ok {
		if err := mao.agentRegistry.UpdateAgentStatus(agentID, AgentStatus(status)); err != nil {
			http.Error(w, fmt.Sprintf("Failed to update agent: %v", err), http.StatusInternalServerError)
			return
		}
	}

	w.WriteHeader(http.StatusOK)
}

func (mao *MultiAgentOrchestrator) unregisterAgent(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	agentID := vars["id"]

	if err := mao.agentRegistry.UnregisterAgent(agentID); err != nil {
		http.Error(w, fmt.Sprintf("Failed to unregister agent: %v", err), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

func (mao *MultiAgentOrchestrator) updateAgentStatus(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	agentID := vars["id"]

	var statusUpdate struct {
		Status AgentStatus `json:"status"`
	}

	if err := json.NewDecoder(r.Body).Decode(&statusUpdate); err != nil {
		http.Error(w, fmt.Sprintf("Invalid request body: %v", err), http.StatusBadRequest)
		return
	}

	if err := mao.agentRegistry.UpdateAgentStatus(agentID, statusUpdate.Status); err != nil {
		http.Error(w, fmt.Sprintf("Failed to update agent status: %v", err), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

func (mao *MultiAgentOrchestrator) checkAgentHealth(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	agentID := vars["id"]

	if err := mao.agentRegistry.healthChecker.ForceHealthCheck(agentID); err != nil {
		http.Error(w, fmt.Sprintf("Failed to perform health check: %v", err), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

func (mao *MultiAgentOrchestrator) getAgentInsights(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	agentID := vars["id"]

	insights := mao.agentSelector.GetPerformanceInsights(agentID)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(insights)
}

func (mao *MultiAgentOrchestrator) selectOptimalAgent(w http.ResponseWriter, r *http.Request) {
	var request AgentSelectionRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, fmt.Sprintf("Invalid request body: %v", err), http.StatusBadRequest)
		return
	}

	result, err := mao.agentSelector.SelectOptimalAgent(request)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to select agent: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(result)
}

// Authentication handlers

func (mao *MultiAgentOrchestrator) login(w http.ResponseWriter, r *http.Request) {
	var authRequest AuthRequest
	if err := json.NewDecoder(r.Body).Decode(&authRequest); err != nil {
		http.Error(w, fmt.Sprintf("Invalid request body: %v", err), http.StatusBadRequest)
		return
	}

	response, err := mao.securityManager.Authenticate(authRequest, r.RemoteAddr, r.UserAgent())
	if err != nil {
		http.Error(w, "Authentication failed", http.StatusUnauthorized)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (mao *MultiAgentOrchestrator) logout(w http.ResponseWriter, r *http.Request) {
	// Extract token from Authorization header
	authHeader := r.Header.Get("Authorization")
	if authHeader != "" {
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) == 2 && parts[0] == "Bearer" {
			token := parts[1]
			// Remove session
			mao.securityManager.mu.Lock()
			delete(mao.securityManager.sessions, token)
			mao.securityManager.mu.Unlock()
		}
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"message": "Logged out successfully"})
}

// Workflow management handlers

func (mao *MultiAgentOrchestrator) listWorkflows(w http.ResponseWriter, r *http.Request) {
	workflows := mao.workflowDesigner.ListWorkflows()
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(workflows)
}

func (mao *MultiAgentOrchestrator) createWorkflow(w http.ResponseWriter, r *http.Request) {
	var request WorkflowCreationRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, fmt.Sprintf("Invalid request body: %v", err), http.StatusBadRequest)
		return
	}

	// Log agent assignments for debugging
	log.Printf("Received agent assignments: %v", request.AgentAssignments)

	workflow, err := mao.workflowDesigner.CreateWorkflow(request)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to create workflow: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(workflow)
}

func (mao *MultiAgentOrchestrator) getWorkflow(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	workflowID := vars["id"]

	workflow, err := mao.workflowDesigner.GetWorkflow(workflowID)
	if err != nil {
		http.Error(w, fmt.Sprintf("Workflow not found: %v", err), http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(workflow)
}

func (mao *MultiAgentOrchestrator) updateWorkflow(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	workflowID := vars["id"]

	var updates map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&updates); err != nil {
		http.Error(w, fmt.Sprintf("Invalid request body: %v", err), http.StatusBadRequest)
		return
	}

	if err := mao.workflowDesigner.UpdateWorkflow(workflowID, updates); err != nil {
		http.Error(w, fmt.Sprintf("Failed to update workflow: %v", err), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

func (mao *MultiAgentOrchestrator) deleteWorkflow(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	workflowID := vars["id"]

	if err := mao.workflowDesigner.DeleteWorkflow(workflowID); err != nil {
		http.Error(w, fmt.Sprintf("Failed to delete workflow: %v", err), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

func (mao *MultiAgentOrchestrator) validateWorkflow(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	workflowID := vars["id"]

	issues, err := mao.workflowDesigner.ValidateWorkflow(workflowID)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to validate workflow: %v", err), http.StatusInternalServerError)
		return
	}

	response := map[string]interface{}{
		"valid":  len(issues) == 0,
		"issues": issues,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// Workflow execution handlers

func (mao *MultiAgentOrchestrator) executeWorkflow(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	workflowID := vars["id"]

	execution, err := mao.executionEngine.ExecuteWorkflow(workflowID)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to execute workflow: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusAccepted)
	json.NewEncoder(w).Encode(execution)
}

func (mao *MultiAgentOrchestrator) pauseWorkflow(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	workflowID := vars["id"]

	if err := mao.executionEngine.PauseWorkflow(workflowID); err != nil {
		http.Error(w, fmt.Sprintf("Failed to pause workflow: %v", err), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

func (mao *MultiAgentOrchestrator) resumeWorkflow(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	workflowID := vars["id"]

	if err := mao.executionEngine.ResumeWorkflow(workflowID); err != nil {
		http.Error(w, fmt.Sprintf("Failed to resume workflow: %v", err), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

func (mao *MultiAgentOrchestrator) cancelWorkflow(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	workflowID := vars["id"]

	if err := mao.executionEngine.CancelWorkflow(workflowID); err != nil {
		http.Error(w, fmt.Sprintf("Failed to cancel workflow: %v", err), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

func (mao *MultiAgentOrchestrator) getWorkflowStatus(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	workflowID := vars["id"]

	execution, err := mao.executionEngine.GetWorkflowExecution(workflowID)
	if err != nil {
		http.Error(w, fmt.Sprintf("Workflow execution not found: %v", err), http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(execution)
}

// Template handlers

func (mao *MultiAgentOrchestrator) listTemplates(w http.ResponseWriter, r *http.Request) {
	templates := mao.workflowDesigner.GetTemplates()
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(templates)
}

func (mao *MultiAgentOrchestrator) getTemplate(w http.ResponseWriter, r *http.Request) {
	// Implementation would retrieve specific template
	w.WriteHeader(http.StatusNotImplemented)
}

// Analytics handlers

// Health check handler

func (mao *MultiAgentOrchestrator) healthCheck(w http.ResponseWriter, r *http.Request) {
	status := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now(),
		"version":   "1.0.0",
		"components": map[string]string{
			"agent_registry":    "healthy",
			"workflow_designer": "healthy",
			"execution_engine":  "healthy",
			"communication_hub": "healthy",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(status)
}

func main() {
	orchestrator := NewMultiAgentOrchestrator()

	// Handle graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		log.Println("Received shutdown signal")
		if err := orchestrator.Stop(); err != nil {
			log.Printf("Error during shutdown: %v", err)
		}
		os.Exit(0)
	}()

	// Start the orchestrator
	if err := orchestrator.Start(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("Failed to start orchestrator: %v", err)
	}
}
