#!/bin/bash

# Multi-Agent Orchestrator Build Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="multi-agent-orchestrator"
IMAGE_TAG="latest"
REGISTRY=""
PUSH_IMAGE=false
RUN_TESTS=false
DEPLOY=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --registry)
            REGISTRY="$2"
            shift 2
            ;;
        --push)
            PUSH_IMAGE=true
            shift
            ;;
        --test)
            RUN_TESTS=true
            shift
            ;;
        --deploy)
            DEPLOY=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --tag TAG        Set image tag (default: latest)"
            echo "  --registry REG   Set registry prefix"
            echo "  --push           Push image to registry"
            echo "  --test           Run tests before building"
            echo "  --deploy         Deploy to Kubernetes after building"
            echo "  --help           Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Set full image name
if [ -n "$REGISTRY" ]; then
    FULL_IMAGE_NAME="$REGISTRY/$IMAGE_NAME:$IMAGE_TAG"
else
    FULL_IMAGE_NAME="$IMAGE_NAME:$IMAGE_TAG"
fi

echo -e "${BLUE}Building Multi-Agent Orchestrator${NC}"
echo -e "${BLUE}Image: $FULL_IMAGE_NAME${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
print_status "Checking prerequisites..."

if ! command -v go &> /dev/null; then
    print_error "Go is not installed"
    exit 1
fi

if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed"
    exit 1
fi

if [ "$DEPLOY" = true ] && ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed (required for deployment)"
    exit 1
fi

# Check Go version
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
REQUIRED_VERSION="1.21"
if ! printf '%s\n%s\n' "$REQUIRED_VERSION" "$GO_VERSION" | sort -V -C; then
    print_warning "Go version $GO_VERSION is older than recommended $REQUIRED_VERSION"
fi

print_status "Prerequisites check passed"

# Run tests if requested
if [ "$RUN_TESTS" = true ]; then
    print_status "Running tests..."
    
    # Unit tests
    go test -v ./... -short
    if [ $? -ne 0 ]; then
        print_error "Unit tests failed"
        exit 1
    fi
    
    # Integration tests (if service is running)
    if curl -s http://localhost:8083/health > /dev/null 2>&1; then
        print_status "Running integration tests..."
        go test -v ./... -run TestMultiAgentOrchestrationIntegration
        if [ $? -ne 0 ]; then
            print_warning "Integration tests failed (service may not be running)"
        fi
    else
        print_warning "Skipping integration tests (service not running)"
    fi
    
    print_status "Tests completed"
fi

# Download dependencies
print_status "Downloading Go dependencies..."
go mod download
go mod tidy

# Build the application
print_status "Building Go application..."
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo -o main .
if [ $? -ne 0 ]; then
    print_error "Go build failed"
    exit 1
fi

print_status "Go build completed"

# Build Docker image
print_status "Building Docker image..."
docker build -t "$FULL_IMAGE_NAME" .
if [ $? -ne 0 ]; then
    print_error "Docker build failed"
    exit 1
fi

print_status "Docker image built successfully"

# Push image if requested
if [ "$PUSH_IMAGE" = true ]; then
    if [ -z "$REGISTRY" ]; then
        print_error "Registry must be specified when pushing"
        exit 1
    fi
    
    print_status "Pushing image to registry..."
    docker push "$FULL_IMAGE_NAME"
    if [ $? -ne 0 ]; then
        print_error "Docker push failed"
        exit 1
    fi
    
    print_status "Image pushed successfully"
fi

# Deploy to Kubernetes if requested
if [ "$DEPLOY" = true ]; then
    print_status "Deploying to Kubernetes..."
    
    # Check if deployment exists
    if kubectl get deployment multi-agent-orchestrator > /dev/null 2>&1; then
        print_status "Updating existing deployment..."
        kubectl set image deployment/multi-agent-orchestrator multi-agent-orchestrator="$FULL_IMAGE_NAME"
        kubectl rollout status deployment/multi-agent-orchestrator
    else
        print_status "Creating new deployment..."
        kubectl apply -f multi-agent-orchestrator.yaml
        kubectl rollout status deployment/multi-agent-orchestrator
    fi
    
    # Wait for deployment to be ready
    kubectl wait --for=condition=available --timeout=300s deployment/multi-agent-orchestrator
    
    print_status "Deployment completed"
    
    # Show service status
    print_status "Service status:"
    kubectl get pods -l app=multi-agent-orchestrator
    kubectl get services multi-agent-orchestrator
fi

# Cleanup
print_status "Cleaning up build artifacts..."
rm -f main

# Summary
echo ""
echo -e "${GREEN}Build completed successfully!${NC}"
echo -e "${BLUE}Image:${NC} $FULL_IMAGE_NAME"

if [ "$PUSH_IMAGE" = true ]; then
    echo -e "${BLUE}Status:${NC} Pushed to registry"
fi

if [ "$DEPLOY" = true ]; then
    echo -e "${BLUE}Status:${NC} Deployed to Kubernetes"
    echo ""
    echo -e "${YELLOW}To access the service:${NC}"
    echo "kubectl port-forward service/multi-agent-orchestrator 8083:8083"
    echo "curl http://localhost:8083/health"
fi

echo ""
echo -e "${GREEN}Multi-Agent Orchestrator is ready!${NC}"
