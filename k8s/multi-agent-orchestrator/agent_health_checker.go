package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"net/http"
	"sync"
	"time"
)

// AgentHealthChecker monitors the health and availability of registered agents
type AgentHealthChecker struct {
	registry      *AgentRegistry
	checkInterval time.Duration
	timeout       time.Duration
	maxRetries    int
	stopChan      chan struct{}
	wg            sync.WaitGroup
	httpClient    *http.Client
}

// HealthCheckResponse represents the response from an agent health check
type HealthCheckResponse struct {
	Status       AgentStatus            `json:"status"`
	Timestamp    time.Time              `json:"timestamp"`
	Version      string                 `json:"version,omitempty"`
	Capabilities []AgentCapability      `json:"capabilities,omitempty"`
	Performance  AgentPerformance       `json:"performance,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// NewAgentHealthChecker creates a new agent health checker
func NewAgentHealthChecker(registry *AgentRegistry) *AgentHealthChecker {
	return &AgentHealthChecker{
		registry:      registry,
		checkInterval: 30 * time.Second,
		timeout:       10 * time.Second,
		maxRetries:    3,
		stop<PERSON>han:      make(chan struct{}),
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// Start begins the health checking routine
func (ahc *AgentHealthChecker) Start() {
	log.Println("Starting agent health checker")

	ticker := time.NewTicker(ahc.checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			ahc.performHealthChecks()
		case <-ahc.stopChan:
			log.Println("Stopping agent health checker")
			return
		}
	}
}

// Stop stops the health checking routine
func (ahc *AgentHealthChecker) Stop() {
	close(ahc.stopChan)
	ahc.wg.Wait()
}

// performHealthChecks checks the health of all registered agents
func (ahc *AgentHealthChecker) performHealthChecks() {
	agents := ahc.registry.ListAgents()

	log.Printf("Performing health checks for %d agents", len(agents))

	// Use a semaphore to limit concurrent health checks
	semaphore := make(chan struct{}, 10)

	for _, agent := range agents {
		ahc.wg.Add(1)
		go func(a Agent) {
			defer ahc.wg.Done()

			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			ahc.checkAgentHealth(a)
		}(agent)
	}

	ahc.wg.Wait()
}

// checkAgentHealth performs a health check on a specific agent
func (ahc *AgentHealthChecker) checkAgentHealth(agent Agent) {
	ctx, cancel := context.WithTimeout(context.Background(), ahc.timeout)
	defer cancel()

	healthEndpoint := fmt.Sprintf("%s/health", agent.Endpoint)

	var lastErr error
	for attempt := 0; attempt < ahc.maxRetries; attempt++ {
		if attempt > 0 {
			// Wait before retry
			time.Sleep(time.Duration(attempt) * time.Second)
		}

		response, err := ahc.performHealthRequest(ctx, healthEndpoint, agent)
		if err != nil {
			lastErr = err
			log.Printf("Health check attempt %d failed for agent %s: %v", attempt+1, agent.ID, err)
			continue
		}

		// Health check successful
		ahc.processHealthResponse(agent, response)
		return
	}

	// All retries failed
	log.Printf("All health check attempts failed for agent %s: %v", agent.ID, lastErr)
	ahc.markAgentUnhealthy(agent, lastErr)
}

// performHealthRequest makes the actual HTTP request to the agent
func (ahc *AgentHealthChecker) performHealthRequest(ctx context.Context, endpoint string, agent Agent) (*HealthCheckResponse, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add authentication if API key is available
	if agent.APIKey != "" {
		req.Header.Set("Authorization", "Bearer "+agent.APIKey)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "AI-Orchestrator-HealthChecker/1.0")

	resp, err := ahc.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("health check returned status %d", resp.StatusCode)
	}

	var healthResponse HealthCheckResponse
	if err := json.NewDecoder(resp.Body).Decode(&healthResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &healthResponse, nil
}

// processHealthResponse processes a successful health check response
func (ahc *AgentHealthChecker) processHealthResponse(agent Agent, response *HealthCheckResponse) {
	// Update agent status if it changed
	if agent.Status != response.Status {
		log.Printf("Agent %s status changed from %s to %s", agent.ID, agent.Status, response.Status)
		if err := ahc.registry.UpdateAgentStatus(agent.ID, response.Status); err != nil {
			log.Printf("Failed to update agent status: %v", err)
		}
	}

	// Update performance metrics if provided
	if response.Performance.LastUpdated.After(agent.Performance.LastUpdated) {
		log.Printf("Updating performance metrics for agent %s", agent.ID)
		if err := ahc.registry.UpdateAgentPerformance(agent.ID, response.Performance); err != nil {
			log.Printf("Failed to update agent performance: %v", err)
		}
	}

	// Update capabilities if they changed
	if len(response.Capabilities) > 0 {
		ahc.updateAgentCapabilities(agent, response.Capabilities)
	}

	log.Printf("Health check successful for agent %s (status: %s)", agent.ID, response.Status)
}

// markAgentUnhealthy marks an agent as unhealthy after failed health checks
func (ahc *AgentHealthChecker) markAgentUnhealthy(agent Agent, err error) {
	newStatus := AgentStatusError

	// Determine appropriate status based on error type
	if isTimeoutError(err) {
		newStatus = AgentStatusOffline
	}

	log.Printf("Marking agent %s as %s due to health check failure: %v", agent.ID, newStatus, err)

	if updateErr := ahc.registry.UpdateAgentStatus(agent.ID, newStatus); updateErr != nil {
		log.Printf("Failed to update agent status to %s: %v", newStatus, updateErr)
	}
}

// updateAgentCapabilities updates an agent's capabilities if they have changed
func (ahc *AgentHealthChecker) updateAgentCapabilities(agent Agent, newCapabilities []AgentCapability) {
	// For now, we'll just log capability changes
	// In a full implementation, we would update the agent registry
	if len(newCapabilities) != len(agent.Capabilities) {
		log.Printf("Agent %s capabilities changed: %d -> %d", agent.ID, len(agent.Capabilities), len(newCapabilities))
	}
}

// isTimeoutError checks if an error is a timeout error
func isTimeoutError(err error) bool {
	if err == nil {
		return false
	}

	// Check for context deadline exceeded
	if err == context.DeadlineExceeded {
		return true
	}

	// Check for HTTP timeout errors
	if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
		return true
	}

	return false
}

// GetHealthStatus returns the current health status of all agents
func (ahc *AgentHealthChecker) GetHealthStatus() map[string]AgentHealthStatus {
	agents := ahc.registry.ListAgents()
	status := make(map[string]AgentHealthStatus)

	for _, agent := range agents {
		status[agent.ID] = AgentHealthStatus{
			AgentID:     agent.ID,
			AgentName:   agent.Name,
			Status:      agent.Status,
			LastSeen:    agent.LastSeen,
			Performance: agent.Performance,
			IsHealthy:   agent.Status == AgentStatusIdle || agent.Status == AgentStatusBusy,
		}
	}

	return status
}

// AgentHealthStatus represents the health status of an agent
type AgentHealthStatus struct {
	AgentID     string           `json:"agent_id"`
	AgentName   string           `json:"agent_name"`
	Status      AgentStatus      `json:"status"`
	LastSeen    time.Time        `json:"last_seen"`
	Performance AgentPerformance `json:"performance"`
	IsHealthy   bool             `json:"is_healthy"`
}

// GetUnhealthyAgents returns a list of agents that are currently unhealthy
func (ahc *AgentHealthChecker) GetUnhealthyAgents() []Agent {
	agents := ahc.registry.ListAgents()
	var unhealthyAgents []Agent

	for _, agent := range agents {
		if agent.Status == AgentStatusError || agent.Status == AgentStatusOffline {
			unhealthyAgents = append(unhealthyAgents, agent)
		}
	}

	return unhealthyAgents
}

// ForceHealthCheck immediately performs a health check on a specific agent
func (ahc *AgentHealthChecker) ForceHealthCheck(agentID string) error {
	agent, err := ahc.registry.GetAgent(agentID)
	if err != nil {
		return fmt.Errorf("agent not found: %w", err)
	}

	log.Printf("Forcing health check for agent %s", agentID)
	ahc.checkAgentHealth(*agent)

	return nil
}

// SetCheckInterval updates the health check interval
func (ahc *AgentHealthChecker) SetCheckInterval(interval time.Duration) {
	ahc.checkInterval = interval
	log.Printf("Health check interval updated to %v", interval)
}

// SetTimeout updates the health check timeout
func (ahc *AgentHealthChecker) SetTimeout(timeout time.Duration) {
	ahc.timeout = timeout
	ahc.httpClient.Timeout = timeout
	log.Printf("Health check timeout updated to %v", timeout)
}
