package main

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
)

// CommunicationHub manages inter-agent communication and message routing
type CommunicationHub struct {
	messageQueue    chan AgentMessage
	subscribers     map[string][]MessageHandler
	messageHistory  map[string][]AgentMessage
	activeWorkflows map[string]*WorkflowCommunicationContext
	mu              sync.RWMutex
	stopChan        chan struct{}
	wg              sync.WaitGroup
}

// MessageHandler defines the interface for handling messages
type MessageHandler interface {
	HandleMessage(ctx context.Context, message AgentMessage) error
	GetHandlerID() string
}

// WorkflowCommunicationContext maintains communication state for a workflow
type WorkflowCommunicationContext struct {
	WorkflowID      string                 `json:"workflow_id"`
	ActiveAgents    map[string]AgentState  `json:"active_agents"`
	MessageRules    []CommunicationRule    `json:"message_rules"`
	MessageHistory  []AgentMessage         `json:"message_history"`
	SharedVariables map[string]interface{} `json:"shared_variables"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
}

// MessageType constants for different types of inter-agent messages
const (
	MessageTypeTaskRequest  = "task_request"
	MessageTypeTaskResponse = "task_response"
	MessageTypeDataShare    = "data_share"
	MessageTypeStatusUpdate = "status_update"
	MessageTypeCoordination = "coordination"
	MessageTypeNotification = "notification"
	MessageTypeError        = "error"
	MessageTypeBroadcast    = "broadcast"
)

// NewCommunicationHub creates a new communication hub
func NewCommunicationHub() *CommunicationHub {
	hub := &CommunicationHub{
		messageQueue:    make(chan AgentMessage, 1000),
		subscribers:     make(map[string][]MessageHandler),
		messageHistory:  make(map[string][]AgentMessage),
		activeWorkflows: make(map[string]*WorkflowCommunicationContext),
		stopChan:        make(chan struct{}),
	}

	// Start message processing
	go hub.processMessages()

	return hub
}

// Start begins the communication hub operations
func (ch *CommunicationHub) Start() {
	log.Println("Communication Hub started")
}

// Stop gracefully stops the communication hub
func (ch *CommunicationHub) Stop() {
	close(ch.stopChan)
	ch.wg.Wait()
	log.Println("Communication Hub stopped")
}

// SendMessage sends a message between agents
func (ch *CommunicationHub) SendMessage(message AgentMessage) error {
	// Validate message
	if err := ch.validateMessage(message); err != nil {
		return fmt.Errorf("invalid message: %w", err)
	}

	// Set message ID and timestamp if not provided
	if message.ID == "" {
		message.ID = uuid.New().String()
	}
	if message.Timestamp.IsZero() {
		message.Timestamp = time.Now()
	}

	// Add to queue for processing
	select {
	case ch.messageQueue <- message:
		return nil
	default:
		return fmt.Errorf("message queue is full")
	}
}

// BroadcastMessage sends a message to all agents in a workflow
func (ch *CommunicationHub) BroadcastMessage(workflowID string, fromAgentID string, messageType string, content interface{}) error {
	ch.mu.RLock()
	context, exists := ch.activeWorkflows[workflowID]
	ch.mu.RUnlock()

	if !exists {
		return fmt.Errorf("workflow not found: %s", workflowID)
	}

	// Send to all active agents except the sender
	for agentID := range context.ActiveAgents {
		if agentID != fromAgentID {
			message := AgentMessage{
				ID:          uuid.New().String(),
				WorkflowID:  workflowID,
				FromAgentID: fromAgentID,
				ToAgentID:   agentID,
				Type:        messageType,
				Content:     content,
				Timestamp:   time.Now(),
				Processed:   false,
			}

			if err := ch.SendMessage(message); err != nil {
				log.Printf("Failed to broadcast message to agent %s: %v", agentID, err)
			}
		}
	}

	return nil
}

// Subscribe registers a message handler for specific message types
func (ch *CommunicationHub) Subscribe(messageType string, handler MessageHandler) {
	ch.mu.Lock()
	defer ch.mu.Unlock()

	ch.subscribers[messageType] = append(ch.subscribers[messageType], handler)
	log.Printf("Handler %s subscribed to message type: %s", handler.GetHandlerID(), messageType)
}

// Unsubscribe removes a message handler
func (ch *CommunicationHub) Unsubscribe(messageType string, handlerID string) {
	ch.mu.Lock()
	defer ch.mu.Unlock()

	handlers := ch.subscribers[messageType]
	for i, handler := range handlers {
		if handler.GetHandlerID() == handlerID {
			ch.subscribers[messageType] = append(handlers[:i], handlers[i+1:]...)
			log.Printf("Handler %s unsubscribed from message type: %s", handlerID, messageType)
			break
		}
	}
}

// CreateWorkflowContext creates a communication context for a workflow
func (ch *CommunicationHub) CreateWorkflowContext(workflowID string, agents []string, rules []CommunicationRule) error {
	ch.mu.Lock()
	defer ch.mu.Unlock()

	context := &WorkflowCommunicationContext{
		WorkflowID:      workflowID,
		ActiveAgents:    make(map[string]AgentState),
		MessageRules:    rules,
		MessageHistory:  make([]AgentMessage, 0),
		SharedVariables: make(map[string]interface{}),
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// Initialize agent states
	for _, agentID := range agents {
		context.ActiveAgents[agentID] = AgentState{
			AgentID:      agentID,
			Status:       AgentStatusIdle,
			LastActivity: time.Now(),
			Context:      make(map[string]interface{}),
		}
	}

	ch.activeWorkflows[workflowID] = context
	log.Printf("Created communication context for workflow: %s", workflowID)

	return nil
}

// UpdateAgentState updates the state of an agent in a workflow context
func (ch *CommunicationHub) UpdateAgentState(workflowID string, agentID string, state AgentState) error {
	ch.mu.Lock()
	defer ch.mu.Unlock()

	context, exists := ch.activeWorkflows[workflowID]
	if !exists {
		return fmt.Errorf("workflow context not found: %s", workflowID)
	}

	state.LastActivity = time.Now()
	context.ActiveAgents[agentID] = state
	context.UpdatedAt = time.Now()

	return nil
}

// GetWorkflowContext retrieves the communication context for a workflow
func (ch *CommunicationHub) GetWorkflowContext(workflowID string) (*WorkflowCommunicationContext, error) {
	ch.mu.RLock()
	defer ch.mu.RUnlock()

	context, exists := ch.activeWorkflows[workflowID]
	if !exists {
		return nil, fmt.Errorf("workflow context not found: %s", workflowID)
	}

	// Return a copy
	contextCopy := *context
	return &contextCopy, nil
}

// SetSharedVariable sets a shared variable in a workflow context
func (ch *CommunicationHub) SetSharedVariable(workflowID string, key string, value interface{}) error {
	ch.mu.Lock()
	defer ch.mu.Unlock()

	context, exists := ch.activeWorkflows[workflowID]
	if !exists {
		return fmt.Errorf("workflow context not found: %s", workflowID)
	}

	context.SharedVariables[key] = value
	context.UpdatedAt = time.Now()

	// Notify interested agents about the variable change
	ch.notifyVariableChange(workflowID, key, value)

	return nil
}

// GetSharedVariable retrieves a shared variable from a workflow context
func (ch *CommunicationHub) GetSharedVariable(workflowID string, key string) (interface{}, error) {
	ch.mu.RLock()
	defer ch.mu.RUnlock()

	context, exists := ch.activeWorkflows[workflowID]
	if !exists {
		return nil, fmt.Errorf("workflow context not found: %s", workflowID)
	}

	value, exists := context.SharedVariables[key]
	if !exists {
		return nil, fmt.Errorf("shared variable not found: %s", key)
	}

	return value, nil
}

// GetMessageHistory retrieves message history for a workflow
func (ch *CommunicationHub) GetMessageHistory(workflowID string, limit int) ([]AgentMessage, error) {
	ch.mu.RLock()
	defer ch.mu.RUnlock()

	context, exists := ch.activeWorkflows[workflowID]
	if !exists {
		return nil, fmt.Errorf("workflow context not found: %s", workflowID)
	}

	history := context.MessageHistory
	if limit > 0 && len(history) > limit {
		// Return the most recent messages
		start := len(history) - limit
		history = history[start:]
	}

	return history, nil
}

// CleanupWorkflowContext removes a workflow context
func (ch *CommunicationHub) CleanupWorkflowContext(workflowID string) error {
	ch.mu.Lock()
	defer ch.mu.Unlock()

	delete(ch.activeWorkflows, workflowID)
	delete(ch.messageHistory, workflowID)

	log.Printf("Cleaned up communication context for workflow: %s", workflowID)
	return nil
}

// processMessages processes messages from the queue
func (ch *CommunicationHub) processMessages() {
	ch.wg.Add(1)
	defer ch.wg.Done()

	for {
		select {
		case message := <-ch.messageQueue:
			ch.handleMessage(message)
		case <-ch.stopChan:
			return
		}
	}
}

// handleMessage processes a single message
func (ch *CommunicationHub) handleMessage(message AgentMessage) {
	ctx := context.Background()

	// Store message in history
	ch.storeMessage(message)

	// Apply communication rules
	if err := ch.applyCommunicationRules(message); err != nil {
		log.Printf("Failed to apply communication rules for message %s: %v", message.ID, err)
	}

	// Notify subscribers
	ch.mu.RLock()
	handlers := ch.subscribers[message.Type]
	ch.mu.RUnlock()

	for _, handler := range handlers {
		go func(h MessageHandler) {
			if err := h.HandleMessage(ctx, message); err != nil {
				log.Printf("Handler %s failed to process message %s: %v", h.GetHandlerID(), message.ID, err)
			}
		}(handler)
	}

	// Mark message as processed
	message.Processed = true
}

// storeMessage stores a message in the appropriate history
func (ch *CommunicationHub) storeMessage(message AgentMessage) {
	ch.mu.Lock()
	defer ch.mu.Unlock()

	// Store in workflow context if it exists
	if context, exists := ch.activeWorkflows[message.WorkflowID]; exists {
		context.MessageHistory = append(context.MessageHistory, message)
		context.UpdatedAt = time.Now()

		// Limit history size
		if len(context.MessageHistory) > 1000 {
			context.MessageHistory = context.MessageHistory[100:]
		}
	}

	// Store in global history
	ch.messageHistory[message.WorkflowID] = append(ch.messageHistory[message.WorkflowID], message)
}

// applyCommunicationRules applies workflow-specific communication rules
func (ch *CommunicationHub) applyCommunicationRules(message AgentMessage) error {
	ch.mu.RLock()
	context, exists := ch.activeWorkflows[message.WorkflowID]
	ch.mu.RUnlock()

	if !exists {
		return nil // No rules to apply
	}

	for _, rule := range context.MessageRules {
		if ch.ruleMatches(rule, message) {
			if err := ch.executeRule(rule, message); err != nil {
				return fmt.Errorf("failed to execute rule %s: %w", rule.ID, err)
			}
		}
	}

	return nil
}

// ruleMatches checks if a communication rule matches a message
func (ch *CommunicationHub) ruleMatches(rule CommunicationRule, message AgentMessage) bool {
	if rule.FromAgentID != "" && rule.FromAgentID != message.FromAgentID {
		return false
	}
	if rule.ToAgentID != "" && rule.ToAgentID != message.ToAgentID {
		return false
	}
	if rule.MessageType != "" && rule.MessageType != message.Type {
		return false
	}
	return true
}

// executeRule executes a communication rule
func (ch *CommunicationHub) executeRule(rule CommunicationRule, message AgentMessage) error {
	// Implementation would depend on the specific rule type
	log.Printf("Executing communication rule %s for message %s", rule.ID, message.ID)
	return nil
}

// notifyVariableChange notifies agents about shared variable changes
func (ch *CommunicationHub) notifyVariableChange(workflowID string, key string, value interface{}) {
	notification := AgentMessage{
		ID:         uuid.New().String(),
		WorkflowID: workflowID,
		Type:       MessageTypeNotification,
		Content: map[string]interface{}{
			"type":     "variable_change",
			"variable": key,
			"value":    value,
		},
		Timestamp: time.Now(),
	}

	// Broadcast to all agents in the workflow
	context := ch.activeWorkflows[workflowID]
	for agentID := range context.ActiveAgents {
		notification.ToAgentID = agentID
		notification.ID = uuid.New().String() // New ID for each recipient

		select {
		case ch.messageQueue <- notification:
		default:
			log.Printf("Failed to queue variable change notification for agent %s", agentID)
		}
	}
}

// validateMessage validates a message before processing
func (ch *CommunicationHub) validateMessage(message AgentMessage) error {
	if message.WorkflowID == "" {
		return fmt.Errorf("workflow ID is required")
	}
	if message.FromAgentID == "" {
		return fmt.Errorf("from agent ID is required")
	}
	if message.ToAgentID == "" {
		return fmt.Errorf("to agent ID is required")
	}
	if message.Type == "" {
		return fmt.Errorf("message type is required")
	}
	return nil
}

// GetActiveWorkflows returns a list of active workflow IDs
func (ch *CommunicationHub) GetActiveWorkflows() []string {
	ch.mu.RLock()
	defer ch.mu.RUnlock()

	workflows := make([]string, 0, len(ch.activeWorkflows))
	for workflowID := range ch.activeWorkflows {
		workflows = append(workflows, workflowID)
	}

	return workflows
}

// GetCommunicationStats returns statistics about communication activity
func (ch *CommunicationHub) GetCommunicationStats() map[string]interface{} {
	ch.mu.RLock()
	defer ch.mu.RUnlock()

	totalMessages := 0
	for _, history := range ch.messageHistory {
		totalMessages += len(history)
	}

	stats := map[string]interface{}{
		"active_workflows": len(ch.activeWorkflows),
		"total_messages":   totalMessages,
		"queue_size":       len(ch.messageQueue),
		"subscribers":      len(ch.subscribers),
	}

	return stats
}
