package main

import (
	"time"
)

// AgentType represents different types of specialized agents
type AgentType string

const (
	AgentTypeDataAnalyst   AgentType = "data_analyst"
	AgentTypeContentWriter AgentType = "content_writer"
	AgentTypeCodeGenerator AgentType = "code_generator"
	AgentTypeResearcher    AgentType = "researcher"
	AgentTypeValidator     AgentType = "validator"
	AgentTypeCoordinator   AgentType = "coordinator"
	AgentTypeSpecialist    AgentType = "specialist"
	AgentTypeGeneralist    AgentType = "generalist"
)

// AgentStatus represents the current status of an agent
type AgentStatus string

const (
	AgentStatusIdle        AgentStatus = "idle"
	AgentStatusBusy        AgentStatus = "busy"
	AgentStatusOffline     AgentStatus = "offline"
	AgentStatusMaintenance AgentStatus = "maintenance"
	AgentStatusError       AgentStatus = "error"
)

// AgentCapability represents a specific capability of an agent
type AgentCapability struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Category    string                 `json:"category"`
	Parameters  map[string]interface{} `json:"parameters"`
	Cost        float64                `json:"cost"`
	Quality     float64                `json:"quality"`
	Speed       float64                `json:"speed"`
}

// Agent represents a specialized AI agent in the system
type Agent struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	Type         AgentType              `json:"type"`
	Description  string                 `json:"description"`
	Capabilities []AgentCapability      `json:"capabilities"`
	Status       AgentStatus            `json:"status"`
	Endpoint     string                 `json:"endpoint"`
	APIKey       string                 `json:"api_key,omitempty"`
	Metadata     map[string]interface{} `json:"metadata"`
	Performance  AgentPerformance       `json:"performance"`
	Quality      AgentQualityMetrics    `json:"quality"`
	Trust        AgentTrustMetrics      `json:"trust"`
	Factsheet    ModelFactsheet         `json:"factsheet"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
	LastSeen     time.Time              `json:"last_seen"`
}

// AgentPerformance tracks performance metrics for an agent
type AgentPerformance struct {
	TasksCompleted int       `json:"tasks_completed"`
	TasksFailed    int       `json:"tasks_failed"`
	AverageLatency float64   `json:"average_latency"`
	SuccessRate    float64   `json:"success_rate"`
	QualityScore   float64   `json:"quality_score"`
	CostEfficiency float64   `json:"cost_efficiency"`
	LastUpdated    time.Time `json:"last_updated"`
}

// ModelFactsheet documents model lineage, performance, and ethical considerations.
type ModelFactsheet struct {
	ModelName             string            `json:"model_name"`
	Version               string            `json:"version"`
	Description           string            `json:"description"`
	Lineage               map[string]string `json:"lineage"` // e.g., training_data_source, training_algorithm
	Limitations           []string          `json:"limitations"`
	EthicalConsiderations map[string]string `json:"ethical_considerations"`
	LastUpdated           time.Time         `json:"last_updated"`
}

// AgentQualityMetrics tracks detailed quality metrics for an agent's underlying model.
type AgentQualityMetrics struct {
	// General ML Metrics
	Accuracy  float64 `json:"accuracy,omitempty"`
	Precision float64 `json:"precision,omitempty"`
	Recall    float64 `json:"recall,omitempty"`
	F1Score   float64 `json:"f1_score,omitempty"`
	AUC       float64 `json:"auc,omitempty"`
	MAE       float64 `json:"mae,omitempty"` // Mean Absolute Error
	MSE       float64 `json:"mse,omitempty"` // Mean Squared Error

	// Robustness Metrics
	AdversarialRobustnessScore float64 `json:"adversarial_robustness_score,omitempty"`
	NoiseRobustnessScore       float64 `json:"noise_robustness_score,omitempty"`
	DriftScore                 float64 `json:"drift_score,omitempty"` // From services like a "Distribution Shift Monitor"

	// Generalizability
	GeneralizabilityScore float64 `json:"generalizability_score,omitempty"`

	LastUpdated time.Time `json:"last_updated"`
}

// AgentTrustMetrics tracks trust-related metrics like fairness, explainability, and privacy.
type AgentTrustMetrics struct {
	// Fairness Metrics from tools like AI Fairness 360
	FairnessScore      float64           `json:"fairness_score,omitempty"`
	DisparateImpact    float64           `json:"disparate_impact,omitempty"`
	EqualOpportunity   float64           `json:"equal_opportunity,omitempty"`
	BiasMitigationInfo map[string]string `json:"bias_mitigation_info,omitempty"`

	// Explainability Metrics from tools like LIME/SHAP
	ExplainabilityScore float64 `json:"explainability_score,omitempty"`
	LIMEAvailability    bool    `json:"lime_availability"`
	SHAPAvailability    bool    `json:"shap_availability"`

	// Privacy Metrics
	PrivacyScore        float64 `json:"privacy_score,omitempty"`
	FederatedLearning   bool    `json:"federated_learning"`
	DifferentialPrivacy bool    `json:"differential_privacy"`

	LastUpdated time.Time `json:"last_updated"`
}

// WorkflowStatus represents the status of a multi-agent workflow
type WorkflowStatus string

const (
	WorkflowStatusDraft     WorkflowStatus = "draft"
	WorkflowStatusActive    WorkflowStatus = "active"
	WorkflowStatusExecuting WorkflowStatus = "executing"
	WorkflowStatusCompleted WorkflowStatus = "completed"
	WorkflowStatusFailed    WorkflowStatus = "failed"
	WorkflowStatusPaused    WorkflowStatus = "paused"
	WorkflowStatusCancelled WorkflowStatus = "cancelled"
)

// MultiAgentWorkflow represents a workflow involving multiple agents
type MultiAgentWorkflow struct {
	ID            string                 `json:"id"`
	Name          string                 `json:"name"`
	Description   string                 `json:"description"`
	GoalID        string                 `json:"goal_id,omitempty"`
	Agents        []string               `json:"agents"`
	Tasks         []WorkflowTask         `json:"tasks"`
	Dependencies  []WorkflowDependency   `json:"dependencies"`
	Communication []CommunicationRule    `json:"communication"`
	Status        WorkflowStatus         `json:"status"`
	EstimatedCost float64                `json:"estimated_cost"`
	EstimatedTime time.Duration          `json:"estimated_time"`
	ActualCost    float64                `json:"actual_cost"`
	ActualTime    time.Duration          `json:"actual_time"`
	Metadata      map[string]interface{} `json:"metadata"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
	StartedAt     *time.Time             `json:"started_at,omitempty"`
	CompletedAt   *time.Time             `json:"completed_at,omitempty"`
}

// WorkflowTask represents a task within a multi-agent workflow
type WorkflowTask struct {
	ID              string                 `json:"id"`
	WorkflowID      string                 `json:"workflow_id"`
	Name            string                 `json:"name"`
	Description     string                 `json:"description"`
	AssignedAgentID string                 `json:"assigned_agent_id"`
	Type            string                 `json:"type"`
	Parameters      map[string]interface{} `json:"parameters"`
	Status          string                 `json:"status"`
	Priority        int                    `json:"priority"`
	EstimatedCost   float64                `json:"estimated_cost"`
	EstimatedTime   time.Duration          `json:"estimated_time"`
	ActualCost      float64                `json:"actual_cost"`
	ActualTime      time.Duration          `json:"actual_time"`
	Result          interface{}            `json:"result,omitempty"`
	Error           string                 `json:"error,omitempty"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
	StartedAt       *time.Time             `json:"started_at,omitempty"`
	CompletedAt     *time.Time             `json:"completed_at,omitempty"`
}

// WorkflowDependency represents dependencies between workflow tasks
type WorkflowDependency struct {
	ID          string `json:"id"`
	WorkflowID  string `json:"workflow_id"`
	FromTaskID  string `json:"from_task_id"`
	ToTaskID    string `json:"to_task_id"`
	Type        string `json:"type"`
	Description string `json:"description"`
}

// CommunicationRule defines how agents should communicate during workflow execution
type CommunicationRule struct {
	ID          string                 `json:"id"`
	WorkflowID  string                 `json:"workflow_id"`
	FromAgentID string                 `json:"from_agent_id"`
	ToAgentID   string                 `json:"to_agent_id"`
	MessageType string                 `json:"message_type"`
	Trigger     string                 `json:"trigger"`
	Parameters  map[string]interface{} `json:"parameters"`
	Description string                 `json:"description"`
}

// AgentMessage represents a message between agents
type AgentMessage struct {
	ID          string                 `json:"id"`
	WorkflowID  string                 `json:"workflow_id"`
	FromAgentID string                 `json:"from_agent_id"`
	ToAgentID   string                 `json:"to_agent_id"`
	Type        string                 `json:"type"`
	Content     interface{}            `json:"content"`
	Metadata    map[string]interface{} `json:"metadata"`
	Timestamp   time.Time              `json:"timestamp"`
	Processed   bool                   `json:"processed"`
}

// WorkflowExecution represents the execution state of a multi-agent workflow
type WorkflowExecution struct {
	ID             string                 `json:"id"`
	WorkflowID     string                 `json:"workflow_id"`
	Status         WorkflowStatus         `json:"status"`
	CurrentTasks   []string               `json:"current_tasks"`
	CompletedTasks []string               `json:"completed_tasks"`
	FailedTasks    []string               `json:"failed_tasks"`
	AgentStates    map[string]AgentState  `json:"agent_states"`
	Messages       []AgentMessage         `json:"messages"`
	Variables      map[string]interface{} `json:"variables"`
	Results        map[string]interface{} `json:"results"`
	Metrics        WorkflowMetrics        `json:"metrics"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
}

// AgentState represents the current state of an agent within a workflow execution
type AgentState struct {
	AgentID      string                 `json:"agent_id"`
	Status       AgentStatus            `json:"status"`
	CurrentTask  string                 `json:"current_task,omitempty"`
	LastActivity time.Time              `json:"last_activity"`
	Context      map[string]interface{} `json:"context"`
	Performance  AgentPerformance       `json:"performance"`
}

// WorkflowMetrics tracks metrics for workflow execution
type WorkflowMetrics struct {
	TotalTasks         int           `json:"total_tasks"`
	CompletedTasks     int           `json:"completed_tasks"`
	FailedTasks        int           `json:"failed_tasks"`
	ActiveAgents       int           `json:"active_agents"`
	TotalCost          float64       `json:"total_cost"`
	ExecutionTime      time.Duration `json:"execution_time"`
	EfficiencyScore    float64       `json:"efficiency_score"`
	CollaborationScore float64       `json:"collaboration_score"`
	LastUpdated        time.Time     `json:"last_updated"`
}
