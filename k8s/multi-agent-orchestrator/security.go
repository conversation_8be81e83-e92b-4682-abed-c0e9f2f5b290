package main

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"log"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
)

// SecurityManager handles authentication, authorization, and audit logging
type SecurityManager struct {
	users          map[string]*User
	roles          map[string]*Role
	permissions    map[string]*Permission
	sessions       map[string]*Session
	auditLogger    *AuditLogger
	encryptionKeys map[string][]byte
	mu             sync.RWMutex
}

// User represents a system user
type User struct {
	ID           string                 `json:"id"`
	Username     string                 `json:"username"`
	Email        string                 `json:"email"`
	PasswordHash string                 `json:"password_hash,omitempty"`
	Roles        []string               `json:"roles"`
	Status       string                 `json:"status"` // active, suspended, disabled
	Metadata     map[string]interface{} `json:"metadata"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
	LastLogin    *time.Time             `json:"last_login,omitempty"`
}

// Role represents a user role with permissions
type Role struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Permissions []string  `json:"permissions"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Permission represents a specific permission
type Permission struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Resource    string `json:"resource"`
	Action      string `json:"action"`
}

// Session represents an active user session
type Session struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
	IPAddress string    `json:"ip_address"`
	UserAgent string    `json:"user_agent"`
}

// AuditLogger handles security audit logging
type AuditLogger struct {
	logs []AuditLog
	mu   sync.RWMutex
}

// AuditLog represents a security audit log entry
type AuditLog struct {
	ID        string                 `json:"id"`
	Timestamp time.Time              `json:"timestamp"`
	UserID    string                 `json:"user_id,omitempty"`
	Action    string                 `json:"action"`
	Resource  string                 `json:"resource"`
	Details   map[string]interface{} `json:"details"`
	IPAddress string                 `json:"ip_address"`
	UserAgent string                 `json:"user_agent"`
	Success   bool                   `json:"success"`
	Error     string                 `json:"error,omitempty"`
}

// AuthRequest represents an authentication request
type AuthRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// AuthResponse represents an authentication response
type AuthResponse struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	User      User      `json:"user"`
}

// NewSecurityManager creates a new security manager
func NewSecurityManager() *SecurityManager {
	sm := &SecurityManager{
		users:          make(map[string]*User),
		roles:          make(map[string]*Role),
		permissions:    make(map[string]*Permission),
		sessions:       make(map[string]*Session),
		auditLogger:    &AuditLogger{logs: make([]AuditLog, 0)},
		encryptionKeys: make(map[string][]byte),
	}

	// Initialize default roles and permissions
	sm.initializeDefaultSecurity()

	return sm
}

// Authenticate authenticates a user and returns a session token
func (sm *SecurityManager) Authenticate(request AuthRequest, ipAddress, userAgent string) (*AuthResponse, error) {
	sm.mu.RLock()
	var user *User
	for _, u := range sm.users {
		if u.Username == request.Username {
			user = u
			break
		}
	}
	sm.mu.RUnlock()

	if user == nil {
		sm.auditLogger.LogEvent("", "authentication_failed", "user", map[string]interface{}{
			"username": request.Username,
			"reason":   "user_not_found",
		}, ipAddress, userAgent, false, "User not found")
		return nil, fmt.Errorf("invalid credentials")
	}

	// Verify password
	if !sm.verifyPassword(request.Password, user.PasswordHash) {
		sm.auditLogger.LogEvent(user.ID, "authentication_failed", "user", map[string]interface{}{
			"username": request.Username,
			"reason":   "invalid_password",
		}, ipAddress, userAgent, false, "Invalid password")
		return nil, fmt.Errorf("invalid credentials")
	}

	// Check user status
	if user.Status != "active" {
		sm.auditLogger.LogEvent(user.ID, "authentication_failed", "user", map[string]interface{}{
			"username": request.Username,
			"reason":   "user_inactive",
			"status":   user.Status,
		}, ipAddress, userAgent, false, "User account inactive")
		return nil, fmt.Errorf("account inactive")
	}

	// Create session
	session := &Session{
		ID:        uuid.New().String(),
		UserID:    user.ID,
		Token:     sm.generateToken(),
		ExpiresAt: time.Now().Add(24 * time.Hour),
		CreatedAt: time.Now(),
		IPAddress: ipAddress,
		UserAgent: userAgent,
	}

	sm.mu.Lock()
	sm.sessions[session.Token] = session
	now := time.Now()
	user.LastLogin = &now
	sm.mu.Unlock()

	sm.auditLogger.LogEvent(user.ID, "authentication_success", "user", map[string]interface{}{
		"username":   request.Username,
		"session_id": session.ID,
	}, ipAddress, userAgent, true, "")

	// Return user without password hash
	userResponse := *user
	userResponse.PasswordHash = ""

	return &AuthResponse{
		Token:     session.Token,
		ExpiresAt: session.ExpiresAt,
		User:      userResponse,
	}, nil
}

// ValidateToken validates a session token and returns the user
func (sm *SecurityManager) ValidateToken(token string) (*User, error) {
	sm.mu.RLock()
	session, exists := sm.sessions[token]
	sm.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("invalid token")
	}

	if time.Now().After(session.ExpiresAt) {
		sm.mu.Lock()
		delete(sm.sessions, token)
		sm.mu.Unlock()
		return nil, fmt.Errorf("token expired")
	}

	sm.mu.RLock()
	user, exists := sm.users[session.UserID]
	sm.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("user not found")
	}

	if user.Status != "active" {
		return nil, fmt.Errorf("user inactive")
	}

	return user, nil
}

// CheckPermission checks if a user has a specific permission
func (sm *SecurityManager) CheckPermission(userID, resource, action string) bool {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	user, exists := sm.users[userID]
	if !exists {
		return false
	}

	// Check user roles and permissions
	for _, roleID := range user.Roles {
		role, exists := sm.roles[roleID]
		if !exists {
			continue
		}

		for _, permissionID := range role.Permissions {
			permission, exists := sm.permissions[permissionID]
			if !exists {
				continue
			}

			if (permission.Resource == "*" || permission.Resource == resource) &&
				(permission.Action == "*" || permission.Action == action) {
				return true
			}
		}
	}

	return false
}

// AuthMiddleware provides HTTP authentication middleware
func (sm *SecurityManager) AuthMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Extract token from Authorization header
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			http.Error(w, "Authorization header required", http.StatusUnauthorized)
			return
		}

		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			http.Error(w, "Invalid authorization header format", http.StatusUnauthorized)
			return
		}

		token := parts[1]
		user, err := sm.ValidateToken(token)
		if err != nil {
			http.Error(w, "Invalid or expired token", http.StatusUnauthorized)
			return
		}

		// Add user to request context
		r.Header.Set("X-User-ID", user.ID)
		r.Header.Set("X-Username", user.Username)

		next(w, r)
	}
}

// PermissionMiddleware checks specific permissions
func (sm *SecurityManager) PermissionMiddleware(resource, action string) func(http.HandlerFunc) http.HandlerFunc {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			userID := r.Header.Get("X-User-ID")
			if userID == "" {
				http.Error(w, "User not authenticated", http.StatusUnauthorized)
				return
			}

			if !sm.CheckPermission(userID, resource, action) {
				sm.auditLogger.LogEvent(userID, "authorization_failed", resource, map[string]interface{}{
					"action":   action,
					"resource": resource,
					"method":   r.Method,
					"path":     r.URL.Path,
				}, r.RemoteAddr, r.UserAgent(), false, "Insufficient permissions")

				http.Error(w, "Insufficient permissions", http.StatusForbidden)
				return
			}

			sm.auditLogger.LogEvent(userID, "authorization_success", resource, map[string]interface{}{
				"action":   action,
				"resource": resource,
				"method":   r.Method,
				"path":     r.URL.Path,
			}, r.RemoteAddr, r.UserAgent(), true, "")

			next(w, r)
		}
	}
}

// CreateUser creates a new user
func (sm *SecurityManager) CreateUser(username, email, password string, roles []string) (*User, error) {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	// Check if user already exists
	for _, user := range sm.users {
		if user.Username == username || user.Email == email {
			return nil, fmt.Errorf("user already exists")
		}
	}

	user := &User{
		ID:           uuid.New().String(),
		Username:     username,
		Email:        email,
		PasswordHash: sm.hashPassword(password),
		Roles:        roles,
		Status:       "active",
		Metadata:     make(map[string]interface{}),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	sm.users[user.ID] = user

	log.Printf("Created user: %s", username)
	return user, nil
}

// Helper methods

func (sm *SecurityManager) initializeDefaultSecurity() {
	// Create default permissions
	permissions := []*Permission{
		{ID: "agents_read", Name: "Read Agents", Description: "View agent information", Resource: "agents", Action: "read"},
		{ID: "agents_write", Name: "Write Agents", Description: "Create and modify agents", Resource: "agents", Action: "write"},
		{ID: "agents_delete", Name: "Delete Agents", Description: "Delete agents", Resource: "agents", Action: "delete"},
		{ID: "workflows_read", Name: "Read Workflows", Description: "View workflow information", Resource: "workflows", Action: "read"},
		{ID: "workflows_write", Name: "Write Workflows", Description: "Create and modify workflows", Resource: "workflows", Action: "write"},
		{ID: "workflows_execute", Name: "Execute Workflows", Description: "Execute workflows", Resource: "workflows", Action: "execute"},
		{ID: "workflows_delete", Name: "Delete Workflows", Description: "Delete workflows", Resource: "workflows", Action: "delete"},
		{ID: "analytics_read", Name: "Read Analytics", Description: "View analytics data", Resource: "analytics", Action: "read"},
		{ID: "admin_all", Name: "Admin All", Description: "Full administrative access", Resource: "*", Action: "*"},
	}

	for _, permission := range permissions {
		sm.permissions[permission.ID] = permission
	}

	// Create default roles
	roles := []*Role{
		{
			ID:          "viewer",
			Name:        "Viewer",
			Description: "Read-only access to agents and workflows",
			Permissions: []string{"agents_read", "workflows_read", "analytics_read"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          "operator",
			Name:        "Operator",
			Description: "Can create and execute workflows",
			Permissions: []string{"agents_read", "workflows_read", "workflows_write", "workflows_execute", "analytics_read"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          "admin",
			Name:        "Administrator",
			Description: "Full system access",
			Permissions: []string{"admin_all"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	for _, role := range roles {
		sm.roles[role.ID] = role
	}

	// Create default admin user
	adminUser, _ := sm.CreateUser("admin", "<EMAIL>", "admin123", []string{"admin"})
	log.Printf("Created default admin user: %s", adminUser.Username)
}

func (sm *SecurityManager) generateToken() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

func (sm *SecurityManager) hashPassword(password string) string {
	hash := sha256.Sum256([]byte(password + "salt"))
	return hex.EncodeToString(hash[:])
}

func (sm *SecurityManager) verifyPassword(password, hash string) bool {
	return sm.hashPassword(password) == hash
}

// LogEvent logs a security audit event
func (al *AuditLogger) LogEvent(userID, action, resource string, details map[string]interface{}, ipAddress, userAgent string, success bool, errorMsg string) {
	al.mu.Lock()
	defer al.mu.Unlock()

	logEntry := AuditLog{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		UserID:    userID,
		Action:    action,
		Resource:  resource,
		Details:   details,
		IPAddress: ipAddress,
		UserAgent: userAgent,
		Success:   success,
		Error:     errorMsg,
	}

	al.logs = append(al.logs, logEntry)

	// Keep only last 10000 logs
	if len(al.logs) > 10000 {
		al.logs = al.logs[len(al.logs)-10000:]
	}

	log.Printf("Audit: %s - %s on %s by %s (%t)", action, resource, userID, ipAddress, success)
}

// GetAuditLogs retrieves audit logs with filtering
func (al *AuditLogger) GetAuditLogs(userID string, limit int) []AuditLog {
	al.mu.RLock()
	defer al.mu.RUnlock()

	var filtered []AuditLog
	for i := len(al.logs) - 1; i >= 0 && len(filtered) < limit; i-- {
		log := al.logs[i]
		if userID == "" || log.UserID == userID {
			filtered = append(filtered, log)
		}
	}

	return filtered
}
