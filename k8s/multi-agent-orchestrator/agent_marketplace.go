package main

import (
	"fmt"
	"log"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
)

// AgentMarketplace manages discovery and integration of external AI agents
type AgentMarketplace struct {
	providers       map[string]*MarketplaceProvider
	agentCatalog    map[string]*MarketplaceAgent
	evaluationCache map[string]*AgentEvaluation
	registry        *AgentRegistry
	mu              sync.RWMutex
}

// MarketplaceProvider represents an external agent provider
type MarketplaceProvider struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	BaseURL     string                 `json:"base_url"`
	APIKey      string                 `json:"api_key,omitempty"`
	Type        string                 `json:"type"` // "api", "registry", "catalog"
	Metadata    map[string]interface{} `json:"metadata"`
	Status      string                 `json:"status"`
	LastSync    time.Time              `json:"last_sync"`
	AgentCount  int                    `json:"agent_count"`
}

// MarketplaceAgent represents an agent available in the marketplace
type MarketplaceAgent struct {
	ID            string             `json:"id"`
	ProviderID    string             `json:"provider_id"`
	Name          string             `json:"name"`
	Description   string             `json:"description"`
	Type          AgentType          `json:"type"`
	Categories    []string           `json:"categories"`
	Capabilities  []AgentCapability  `json:"capabilities"`
	Pricing       PricingModel       `json:"pricing"`
	Performance   PerformanceMetrics `json:"performance"`
	Compatibility CompatibilityInfo  `json:"compatibility"`
	Documentation string             `json:"documentation"`
	DemoEndpoint  string             `json:"demo_endpoint,omitempty"`
	Rating        float64            `json:"rating"`
	ReviewCount   int                `json:"review_count"`
	Downloads     int                `json:"downloads"`
	LastUpdated   time.Time          `json:"last_updated"`
	Tags          []string           `json:"tags"`
	License       string             `json:"license"`
	SupportLevel  string             `json:"support_level"`
}

// PricingModel defines how an agent is priced
type PricingModel struct {
	Type        string        `json:"type"` // "free", "per_request", "subscription", "usage_based"
	BasePrice   float64       `json:"base_price"`
	Currency    string        `json:"currency"`
	Unit        string        `json:"unit"` // "request", "hour", "month", "token"
	TierPricing []PricingTier `json:"tier_pricing,omitempty"`
	FreeTier    *FreeTier     `json:"free_tier,omitempty"`
}

// PricingTier represents tiered pricing
type PricingTier struct {
	Name         string  `json:"name"`
	MinUsage     int     `json:"min_usage"`
	MaxUsage     int     `json:"max_usage"`
	PricePerUnit float64 `json:"price_per_unit"`
}

// FreeTier represents free usage limits
type FreeTier struct {
	RequestsPerDay   int `json:"requests_per_day"`
	RequestsPerMonth int `json:"requests_per_month"`
}

// PerformanceMetrics represents agent performance data
type PerformanceMetrics struct {
	AverageLatency  float64   `json:"average_latency"`
	SuccessRate     float64   `json:"success_rate"`
	QualityScore    float64   `json:"quality_score"`
	Reliability     float64   `json:"reliability"`
	Scalability     float64   `json:"scalability"`
	LastBenchmarked time.Time `json:"last_benchmarked"`
}

// CompatibilityInfo describes agent compatibility
type CompatibilityInfo struct {
	Protocols     []string             `json:"protocols"`
	DataFormats   []string             `json:"data_formats"`
	AuthMethods   []string             `json:"auth_methods"`
	MinAPIVersion string               `json:"min_api_version"`
	Dependencies  []string             `json:"dependencies"`
	ResourceReqs  ResourceRequirements `json:"resource_requirements"`
}

// ResourceRequirements specifies resource needs
type ResourceRequirements struct {
	MinMemoryMB int     `json:"min_memory_mb"`
	MinCPU      float64 `json:"min_cpu"`
	GPURequired bool    `json:"gpu_required"`
	Storage     string  `json:"storage"`
}

// AgentEvaluation represents a comprehensive evaluation of an agent
type AgentEvaluation struct {
	AgentID            string             `json:"agent_id"`
	EvaluationID       string             `json:"evaluation_id"`
	Timestamp          time.Time          `json:"timestamp"`
	TestSuite          string             `json:"test_suite"`
	OverallScore       float64            `json:"overall_score"`
	CategoryScores     map[string]float64 `json:"category_scores"`
	BenchmarkResults   []BenchmarkResult  `json:"benchmark_results"`
	SecurityScan       SecurityScanResult `json:"security_scan"`
	Recommendations    []string           `json:"recommendations"`
	Warnings           []string           `json:"warnings"`
	CertificationLevel string             `json:"certification_level"`
}

// BenchmarkResult represents results from a specific benchmark
type BenchmarkResult struct {
	Name     string                 `json:"name"`
	Score    float64                `json:"score"`
	Metrics  map[string]interface{} `json:"metrics"`
	Duration time.Duration          `json:"duration"`
	Status   string                 `json:"status"`
	Details  string                 `json:"details"`
}

// SecurityScanResult represents security evaluation results
type SecurityScanResult struct {
	OverallRating   string    `json:"overall_rating"`
	Vulnerabilities []string  `json:"vulnerabilities"`
	Compliance      []string  `json:"compliance"`
	DataPrivacy     string    `json:"data_privacy"`
	Encryption      bool      `json:"encryption"`
	LastScanned     time.Time `json:"last_scanned"`
}

// AgentSearchRequest represents a search request for agents
type AgentSearchRequest struct {
	Query          string                `json:"query"`
	Categories     []string              `json:"categories"`
	Types          []AgentType           `json:"types"`
	Capabilities   []string              `json:"capabilities"`
	PriceRange     PriceRange            `json:"price_range"`
	PerformanceMin PerformanceThresholds `json:"performance_min"`
	Compatibility  []string              `json:"compatibility"`
	Rating         float64               `json:"min_rating"`
	SortBy         string                `json:"sort_by"`
	Limit          int                   `json:"limit"`
	Offset         int                   `json:"offset"`
}

// PriceRange defines price filtering
type PriceRange struct {
	MinPrice float64 `json:"min_price"`
	MaxPrice float64 `json:"max_price"`
	Currency string  `json:"currency"`
}

// PerformanceThresholds defines minimum performance requirements
type PerformanceThresholds struct {
	MinLatency      float64 `json:"min_latency"`
	MinSuccessRate  float64 `json:"min_success_rate"`
	MinQualityScore float64 `json:"min_quality_score"`
	MinReliability  float64 `json:"min_reliability"`
}

// AgentSearchResult represents search results
type AgentSearchResult struct {
	Agents      []MarketplaceAgent `json:"agents"`
	TotalCount  int                `json:"total_count"`
	HasMore     bool               `json:"has_more"`
	SearchTime  time.Duration      `json:"search_time"`
	Suggestions []string           `json:"suggestions"`
}

// NewAgentMarketplace creates a new agent marketplace
func NewAgentMarketplace(registry *AgentRegistry) *AgentMarketplace {
	marketplace := &AgentMarketplace{
		providers:       make(map[string]*MarketplaceProvider),
		agentCatalog:    make(map[string]*MarketplaceAgent),
		evaluationCache: make(map[string]*AgentEvaluation),
		registry:        registry,
	}

	// Initialize with default providers
	marketplace.initializeDefaultProviders()

	return marketplace
}

// AddProvider adds a new marketplace provider
func (am *AgentMarketplace) AddProvider(provider MarketplaceProvider) error {
	am.mu.Lock()
	defer am.mu.Unlock()

	if provider.ID == "" {
		provider.ID = uuid.New().String()
	}

	provider.Status = "active"
	provider.LastSync = time.Now()

	am.providers[provider.ID] = &provider

	// Sync agents from this provider
	go am.syncProviderAgents(provider.ID)

	log.Printf("Added marketplace provider: %s", provider.Name)
	return nil
}

// SearchAgents searches for agents in the marketplace
func (am *AgentMarketplace) SearchAgents(request AgentSearchRequest) (*AgentSearchResult, error) {
	startTime := time.Now()

	am.mu.RLock()
	defer am.mu.RUnlock()

	var matchingAgents []MarketplaceAgent

	// Filter agents based on search criteria
	for _, agent := range am.agentCatalog {
		if am.matchesSearchCriteria(agent, request) {
			matchingAgents = append(matchingAgents, *agent)
		}
	}

	// Sort results
	am.sortAgents(matchingAgents, request.SortBy)

	// Apply pagination
	totalCount := len(matchingAgents)
	start := request.Offset
	end := start + request.Limit
	if end > totalCount {
		end = totalCount
	}
	if start > totalCount {
		start = totalCount
	}

	paginatedAgents := matchingAgents[start:end]
	hasMore := end < totalCount

	// Generate suggestions
	suggestions := am.generateSearchSuggestions(request, matchingAgents)

	return &AgentSearchResult{
		Agents:      paginatedAgents,
		TotalCount:  totalCount,
		HasMore:     hasMore,
		SearchTime:  time.Since(startTime),
		Suggestions: suggestions,
	}, nil
}

// EvaluateAgent performs comprehensive evaluation of an agent
func (am *AgentMarketplace) EvaluateAgent(agentID string) (*AgentEvaluation, error) {
	am.mu.RLock()
	agent, exists := am.agentCatalog[agentID]
	am.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("agent not found: %s", agentID)
	}

	// Check cache first
	am.mu.RLock()
	if cached, exists := am.evaluationCache[agentID]; exists {
		// Return cached result if recent (within 24 hours)
		if time.Since(cached.Timestamp) < 24*time.Hour {
			am.mu.RUnlock()
			return cached, nil
		}
	}
	am.mu.RUnlock()

	// Perform new evaluation
	evaluation := &AgentEvaluation{
		AgentID:      agentID,
		EvaluationID: uuid.New().String(),
		Timestamp:    time.Now(),
		TestSuite:    "standard_v1.0",
	}

	// Run benchmarks
	evaluation.BenchmarkResults = am.runBenchmarks(agent)

	// Calculate category scores
	evaluation.CategoryScores = am.calculateCategoryScores(evaluation.BenchmarkResults)

	// Calculate overall score
	evaluation.OverallScore = am.calculateOverallScore(evaluation.CategoryScores)

	// Perform security scan
	evaluation.SecurityScan = am.performSecurityScan(agent)

	// Generate recommendations
	evaluation.Recommendations = am.generateRecommendations(agent, evaluation)

	// Determine certification level
	evaluation.CertificationLevel = am.determineCertificationLevel(evaluation)

	// Cache the evaluation
	am.mu.Lock()
	am.evaluationCache[agentID] = evaluation
	am.mu.Unlock()

	log.Printf("Evaluated agent %s with score %.2f", agent.Name, evaluation.OverallScore)
	return evaluation, nil
}

// IntegrateAgent integrates a marketplace agent into the local registry
func (am *AgentMarketplace) IntegrateAgent(agentID string, config map[string]interface{}) error {
	am.mu.RLock()
	marketplaceAgent, exists := am.agentCatalog[agentID]
	am.mu.RUnlock()

	if !exists {
		return fmt.Errorf("agent not found in marketplace: %s", agentID)
	}

	// Convert marketplace agent to registry agent
	registryAgent := Agent{
		Name:         marketplaceAgent.Name,
		Type:         marketplaceAgent.Type,
		Description:  marketplaceAgent.Description,
		Capabilities: marketplaceAgent.Capabilities,
		Status:       AgentStatusOffline, // Start as offline until verified
		Metadata: map[string]interface{}{
			"marketplace_id":   agentID,
			"provider_id":      marketplaceAgent.ProviderID,
			"integration_date": time.Now(),
			"config":           config,
		},
	}

	// Set endpoint from config
	if endpoint, ok := config["endpoint"].(string); ok {
		registryAgent.Endpoint = endpoint
	}

	// Set API key from config
	if apiKey, ok := config["api_key"].(string); ok {
		registryAgent.APIKey = apiKey
	}

	// Register the agent
	if err := am.registry.RegisterAgent(registryAgent); err != nil {
		return fmt.Errorf("failed to register agent: %w", err)
	}

	log.Printf("Successfully integrated marketplace agent: %s", marketplaceAgent.Name)
	return nil
}

// Helper methods

func (am *AgentMarketplace) initializeDefaultProviders() {
	// Add some default marketplace providers
	defaultProviders := []MarketplaceProvider{
		{
			ID:          "huggingface",
			Name:        "Hugging Face Hub",
			Description: "Open source AI models and agents",
			BaseURL:     "https://huggingface.co/api",
			Type:        "registry",
			Status:      "active",
		},
		{
			ID:          "openai",
			Name:        "OpenAI Platform",
			Description: "OpenAI's AI models and assistants",
			BaseURL:     "https://api.openai.com/v1",
			Type:        "api",
			Status:      "active",
		},
		{
			ID:          "anthropic",
			Name:        "Anthropic Claude",
			Description: "Anthropic's Claude AI assistants",
			BaseURL:     "https://api.anthropic.com/v1",
			Type:        "api",
			Status:      "active",
		},
	}

	for _, provider := range defaultProviders {
		am.providers[provider.ID] = &provider
	}
}

func (am *AgentMarketplace) syncProviderAgents(providerID string) {
	provider, exists := am.providers[providerID]
	if !exists {
		return
	}

	// This would implement actual syncing with external providers
	// For now, we'll add some mock agents
	mockAgents := am.generateMockAgents(providerID)

	am.mu.Lock()
	for _, agent := range mockAgents {
		am.agentCatalog[agent.ID] = &agent
	}
	provider.AgentCount = len(mockAgents)
	provider.LastSync = time.Now()
	am.mu.Unlock()

	log.Printf("Synced %d agents from provider %s", len(mockAgents), provider.Name)
}

func (am *AgentMarketplace) generateMockAgents(providerID string) []MarketplaceAgent {
	// Generate some mock agents for demonstration
	return []MarketplaceAgent{
		{
			ID:          fmt.Sprintf("%s-data-analyst", providerID),
			ProviderID:  providerID,
			Name:        "Advanced Data Analyst",
			Description: "Specialized agent for complex data analysis and visualization",
			Type:        AgentTypeDataAnalyst,
			Categories:  []string{"analytics", "data-science", "visualization"},
			Capabilities: []AgentCapability{
				{
					ID:          "statistical_analysis",
					Name:        "Statistical Analysis",
					Description: "Advanced statistical analysis capabilities",
					Quality:     0.95,
					Cost:        0.15,
					Speed:       0.85,
				},
			},
			Pricing: PricingModel{
				Type:      "per_request",
				BasePrice: 0.10,
				Currency:  "USD",
				Unit:      "request",
			},
			Performance: PerformanceMetrics{
				AverageLatency: 250.0,
				SuccessRate:    0.98,
				QualityScore:   0.95,
				Reliability:    0.99,
			},
			Rating:      4.8,
			ReviewCount: 156,
			Downloads:   2340,
			Tags:        []string{"data", "analytics", "statistics", "python"},
			License:     "MIT",
		},
	}
}

func (am *AgentMarketplace) matchesSearchCriteria(agent *MarketplaceAgent, request AgentSearchRequest) bool {
	// Query matching
	if request.Query != "" {
		query := strings.ToLower(request.Query)
		if !strings.Contains(strings.ToLower(agent.Name), query) &&
			!strings.Contains(strings.ToLower(agent.Description), query) {
			// Check tags
			found := false
			for _, tag := range agent.Tags {
				if strings.Contains(strings.ToLower(tag), query) {
					found = true
					break
				}
			}
			if !found {
				return false
			}
		}
	}

	// Category filtering
	if len(request.Categories) > 0 {
		found := false
		for _, reqCat := range request.Categories {
			for _, agentCat := range agent.Categories {
				if agentCat == reqCat {
					found = true
					break
				}
			}
			if found {
				break
			}
		}
		if !found {
			return false
		}
	}

	// Type filtering
	if len(request.Types) > 0 {
		found := false
		for _, reqType := range request.Types {
			if agent.Type == reqType {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Rating filtering
	if request.Rating > 0 && agent.Rating < request.Rating {
		return false
	}

	// Performance filtering
	if request.PerformanceMin.MinSuccessRate > 0 && agent.Performance.SuccessRate < request.PerformanceMin.MinSuccessRate {
		return false
	}

	return true
}

func (am *AgentMarketplace) sortAgents(agents []MarketplaceAgent, sortBy string) {
	switch sortBy {
	case "rating":
		sort.Slice(agents, func(i, j int) bool {
			return agents[i].Rating > agents[j].Rating
		})
	case "downloads":
		sort.Slice(agents, func(i, j int) bool {
			return agents[i].Downloads > agents[j].Downloads
		})
	case "price":
		sort.Slice(agents, func(i, j int) bool {
			return agents[i].Pricing.BasePrice < agents[j].Pricing.BasePrice
		})
	case "performance":
		sort.Slice(agents, func(i, j int) bool {
			return agents[i].Performance.QualityScore > agents[j].Performance.QualityScore
		})
	default: // relevance/name
		sort.Slice(agents, func(i, j int) bool {
			return agents[i].Name < agents[j].Name
		})
	}
}

func (am *AgentMarketplace) generateSearchSuggestions(request AgentSearchRequest, results []MarketplaceAgent) []string {
	// Generate search suggestions based on results and request
	suggestions := []string{}

	if len(results) == 0 {
		// No results found - suggest alternatives based on current search criteria
		if request.Query != "" {
			suggestions = append(suggestions, "Try broader search terms")
			suggestions = append(suggestions, "Check spelling of search terms")
		}
		if len(request.Categories) > 0 {
			suggestions = append(suggestions, "Try different categories")
		}
		if len(request.Types) > 0 {
			suggestions = append(suggestions, "Expand agent type selection")
		}
		if request.Rating > 0 {
			suggestions = append(suggestions, "Lower minimum rating requirement")
		}
		if len(request.Capabilities) > 0 {
			suggestions = append(suggestions, "Reduce required capabilities")
		}
		// Fallback suggestions
		if len(suggestions) == 0 {
			suggestions = append(suggestions, "Try broader search terms")
			suggestions = append(suggestions, "Check different categories")
		}
	} else if len(results) > 100 {
		// Too many results - suggest refinement based on available filters
		suggestions = append(suggestions, "Narrow your search with more specific terms")
		if len(request.Categories) == 0 {
			suggestions = append(suggestions, "Filter by specific categories")
		}
		if len(request.Types) == 0 {
			suggestions = append(suggestions, "Filter by agent type")
		}
		if request.Rating == 0 {
			suggestions = append(suggestions, "Set minimum rating filter")
		}
		if len(request.Capabilities) == 0 {
			suggestions = append(suggestions, "Filter by required capabilities")
		}
	}

	return suggestions
}

func (am *AgentMarketplace) runBenchmarks(agent *MarketplaceAgent) []BenchmarkResult {
	// Generate benchmark results based on agent's performance metrics and capabilities
	results := []BenchmarkResult{}

	// Performance benchmark based on agent's average latency
	responseTimeScore := 1.0 - (agent.Performance.AverageLatency / 1000.0) // Convert to 0-1 scale
	if responseTimeScore < 0 {
		responseTimeScore = 0
	}
	if responseTimeScore > 1 {
		responseTimeScore = 1
	}

	results = append(results, BenchmarkResult{
		Name:     "Response Time",
		Score:    responseTimeScore,
		Duration: time.Duration(agent.Performance.AverageLatency) * time.Millisecond,
		Status:   "completed",
		Details:  fmt.Sprintf("Average response time: %.0fms", agent.Performance.AverageLatency),
		Metrics: map[string]interface{}{
			"latency_ms": agent.Performance.AverageLatency,
			"target_ms":  500.0,
		},
	})

	// Accuracy benchmark based on agent's quality score
	results = append(results, BenchmarkResult{
		Name:     "Accuracy Test",
		Score:    agent.Performance.QualityScore,
		Duration: 2 * time.Second,
		Status:   "completed",
		Details:  fmt.Sprintf("Quality score: %.2f", agent.Performance.QualityScore),
		Metrics: map[string]interface{}{
			"quality_score": agent.Performance.QualityScore,
			"target_score":  0.85,
		},
	})

	// Reliability benchmark
	results = append(results, BenchmarkResult{
		Name:     "Reliability Test",
		Score:    agent.Performance.Reliability,
		Duration: 3 * time.Second,
		Status:   "completed",
		Details:  fmt.Sprintf("Reliability score: %.2f", agent.Performance.Reliability),
		Metrics: map[string]interface{}{
			"reliability": agent.Performance.Reliability,
			"uptime":      agent.Performance.SuccessRate,
		},
	})

	// Capability-specific benchmarks
	for _, capability := range agent.Capabilities {
		results = append(results, BenchmarkResult{
			Name:     fmt.Sprintf("%s Performance", capability.Name),
			Score:    capability.Quality,
			Duration: time.Duration(500+len(capability.Description)*10) * time.Millisecond,
			Status:   "completed",
			Details:  fmt.Sprintf("Capability quality: %.2f", capability.Quality),
			Metrics: map[string]interface{}{
				"capability_id":   capability.ID,
				"quality":         capability.Quality,
				"cost_efficiency": capability.Cost,
				"speed":           capability.Speed,
			},
		})
	}

	return results
}

func (am *AgentMarketplace) calculateCategoryScores(benchmarks []BenchmarkResult) map[string]float64 {
	if len(benchmarks) == 0 {
		return map[string]float64{
			"performance": 0.0,
			"reliability": 0.0,
			"accuracy":    0.0,
			"efficiency":  0.0,
		}
	}

	categoryScores := map[string]float64{
		"performance": 0.0,
		"reliability": 0.0,
		"accuracy":    0.0,
		"efficiency":  0.0,
	}

	categoryCounts := map[string]int{
		"performance": 0,
		"reliability": 0,
		"accuracy":    0,
		"efficiency":  0,
	}

	// Aggregate scores by category based on benchmark names
	for _, benchmark := range benchmarks {
		if benchmark.Status != "completed" {
			continue
		}

		// Categorize benchmarks based on their names
		switch {
		case strings.Contains(strings.ToLower(benchmark.Name), "performance") ||
			strings.Contains(strings.ToLower(benchmark.Name), "speed") ||
			strings.Contains(strings.ToLower(benchmark.Name), "throughput"):
			categoryScores["performance"] += benchmark.Score
			categoryCounts["performance"]++
		case strings.Contains(strings.ToLower(benchmark.Name), "reliability") ||
			strings.Contains(strings.ToLower(benchmark.Name), "stability") ||
			strings.Contains(strings.ToLower(benchmark.Name), "uptime"):
			categoryScores["reliability"] += benchmark.Score
			categoryCounts["reliability"]++
		case strings.Contains(strings.ToLower(benchmark.Name), "accuracy") ||
			strings.Contains(strings.ToLower(benchmark.Name), "precision") ||
			strings.Contains(strings.ToLower(benchmark.Name), "quality"):
			categoryScores["accuracy"] += benchmark.Score
			categoryCounts["accuracy"]++
		case strings.Contains(strings.ToLower(benchmark.Name), "efficiency") ||
			strings.Contains(strings.ToLower(benchmark.Name), "resource") ||
			strings.Contains(strings.ToLower(benchmark.Name), "memory"):
			categoryScores["efficiency"] += benchmark.Score
			categoryCounts["efficiency"]++
		default:
			// If benchmark doesn't match any category, distribute equally
			for category := range categoryScores {
				categoryScores[category] += benchmark.Score / 4.0
				categoryCounts[category]++
			}
		}
	}

	// Calculate average scores for each category
	for category := range categoryScores {
		if categoryCounts[category] > 0 {
			categoryScores[category] /= float64(categoryCounts[category])
		}
	}

	return categoryScores
}

func (am *AgentMarketplace) calculateOverallScore(categoryScores map[string]float64) float64 {
	var total float64
	for _, score := range categoryScores {
		total += score
	}
	return total / float64(len(categoryScores))
}

func (am *AgentMarketplace) performSecurityScan(agent *MarketplaceAgent) SecurityScanResult {
	// Determine security rating based on agent properties
	var overallRating string
	var vulnerabilities []string
	var compliance []string
	var dataPrivacy string
	encryption := true

	// Get provider information for security assessment
	am.mu.RLock()
	provider, providerExists := am.providers[agent.ProviderID]
	am.mu.RUnlock()

	// Base security rating on provider reputation and agent characteristics
	baseScore := 85.0 // Start with B+ rating

	// Provider-based security assessment
	if providerExists {
		switch provider.ID {
		case "openai", "anthropic":
			baseScore += 10 // Well-known providers get higher scores
			compliance = append(compliance, "SOC2", "GDPR", "CCPA")
		case "huggingface":
			baseScore += 5 // Open source platform, moderate trust
			compliance = append(compliance, "GDPR")
		default:
			// Unknown providers get lower scores
			vulnerabilities = append(vulnerabilities, "Unknown provider security practices")
		}
	} else {
		baseScore -= 15
		vulnerabilities = append(vulnerabilities, "Provider not verified")
	}

	// License-based security assessment
	switch agent.License {
	case "MIT", "Apache-2.0", "BSD":
		baseScore += 5 // Open source licenses are generally more transparent
	case "Commercial", "Proprietary":
		// No penalty, but no bonus either
	default:
		vulnerabilities = append(vulnerabilities, "Unclear licensing terms")
		baseScore -= 5
	}

	// Authentication method assessment
	hasSecureAuth := false
	for _, authMethod := range agent.Compatibility.AuthMethods {
		if authMethod == "oauth2" || authMethod == "api_key" || authMethod == "jwt" {
			hasSecureAuth = true
			break
		}
	}
	if !hasSecureAuth {
		vulnerabilities = append(vulnerabilities, "Weak authentication methods")
		baseScore -= 10
	}

	// Data format security assessment
	for _, format := range agent.Compatibility.DataFormats {
		if format == "encrypted" || format == "tls" {
			baseScore += 5
			break
		}
	}

	// Age and popularity assessment (newer or more popular = more scrutinized)
	if agent.Downloads > 1000 {
		baseScore += 5 // Popular agents are more likely to be vetted
	}
	if agent.ReviewCount > 50 {
		baseScore += 3 // More reviews indicate community scrutiny
	}

	// Determine overall rating from score
	switch {
	case baseScore >= 95:
		overallRating = "A+"
	case baseScore >= 90:
		overallRating = "A"
	case baseScore >= 85:
		overallRating = "A-"
	case baseScore >= 80:
		overallRating = "B+"
	case baseScore >= 75:
		overallRating = "B"
	case baseScore >= 70:
		overallRating = "B-"
	case baseScore >= 65:
		overallRating = "C+"
	case baseScore >= 60:
		overallRating = "C"
	default:
		overallRating = "D"
		vulnerabilities = append(vulnerabilities, "Multiple security concerns identified")
	}

	// Data privacy assessment
	if len(compliance) > 0 {
		dataPrivacy = "compliant"
	} else {
		dataPrivacy = "unknown"
		vulnerabilities = append(vulnerabilities, "Data privacy compliance unclear")
	}

	// Encryption assessment based on protocols
	encryption = false
	for _, protocol := range agent.Compatibility.Protocols {
		if strings.Contains(strings.ToLower(protocol), "https") ||
			strings.Contains(strings.ToLower(protocol), "tls") ||
			strings.Contains(strings.ToLower(protocol), "ssl") {
			encryption = true
			break
		}
	}
	if !encryption {
		vulnerabilities = append(vulnerabilities, "No encryption protocols detected")
	}

	return SecurityScanResult{
		OverallRating:   overallRating,
		Vulnerabilities: vulnerabilities,
		Compliance:      compliance,
		DataPrivacy:     dataPrivacy,
		Encryption:      encryption,
		LastScanned:     time.Now(),
	}
}

func (am *AgentMarketplace) generateRecommendations(agent *MarketplaceAgent, evaluation *AgentEvaluation) []string {
	recommendations := []string{}

	// Overall score-based recommendations
	if evaluation.OverallScore > 0.9 {
		recommendations = append(recommendations, "Excellent choice for production use")
	} else if evaluation.OverallScore > 0.7 {
		recommendations = append(recommendations, "Good for most use cases with monitoring")
	} else {
		recommendations = append(recommendations, "Consider for development/testing only")
	}

	// Agent-specific recommendations based on performance metrics
	if agent.Performance.AverageLatency > 1000 {
		recommendations = append(recommendations, "Consider optimizing for latency-sensitive applications")
	} else if agent.Performance.AverageLatency < 200 {
		recommendations = append(recommendations, "Excellent for real-time applications")
	}

	if agent.Performance.SuccessRate < 0.95 {
		recommendations = append(recommendations, "Implement robust error handling and retry logic")
	}

	if agent.Performance.Reliability < 0.9 {
		recommendations = append(recommendations, "Consider backup agents for critical workflows")
	}

	// Pricing-based recommendations
	if agent.Pricing.Type == "free" {
		recommendations = append(recommendations, "Great for prototyping and small-scale projects")
	} else if agent.Pricing.BasePrice > 1.0 {
		recommendations = append(recommendations, "Evaluate cost-benefit ratio for high-volume usage")
	}

	// Provider-based recommendations
	am.mu.RLock()
	provider, exists := am.providers[agent.ProviderID]
	am.mu.RUnlock()

	if exists {
		switch provider.ID {
		case "openai", "anthropic":
			recommendations = append(recommendations, "Enterprise-grade provider with strong support")
		case "huggingface":
			recommendations = append(recommendations, "Open-source option with community support")
		}
	}

	// Capability-based recommendations
	if len(agent.Capabilities) > 5 {
		recommendations = append(recommendations, "Multi-purpose agent suitable for diverse workflows")
	} else if len(agent.Capabilities) <= 2 {
		recommendations = append(recommendations, "Specialized agent - ideal for focused use cases")
	}

	// Security-based recommendations
	if evaluation.SecurityScan.OverallRating == "A+" || evaluation.SecurityScan.OverallRating == "A" {
		recommendations = append(recommendations, "Meets high security standards for sensitive data")
	} else if len(evaluation.SecurityScan.Vulnerabilities) > 0 {
		recommendations = append(recommendations, "Review security considerations before deployment")
	}

	// Popularity and maturity recommendations
	if agent.Downloads > 10000 && agent.ReviewCount > 100 {
		recommendations = append(recommendations, "Well-established agent with proven track record")
	} else if agent.Downloads < 100 {
		recommendations = append(recommendations, "New agent - consider thorough testing before production use")
	}

	// License-based recommendations
	switch agent.License {
	case "MIT", "Apache-2.0", "BSD":
		recommendations = append(recommendations, "Open-source license allows for flexible usage")
	case "Commercial", "Proprietary":
		recommendations = append(recommendations, "Review licensing terms for commercial usage")
	}

	// Category-specific recommendations
	for _, category := range agent.Categories {
		switch category {
		case "analytics", "data-science":
			recommendations = append(recommendations, "Ensure data privacy compliance for analytics workloads")
		case "content", "automation":
			recommendations = append(recommendations, "Consider content moderation and quality checks")
		case "research", "validation":
			recommendations = append(recommendations, "Verify accuracy with multiple sources for critical research")
		}
	}

	return recommendations
}

func (am *AgentMarketplace) determineCertificationLevel(evaluation *AgentEvaluation) string {
	if evaluation.OverallScore >= 0.95 {
		return "enterprise"
	} else if evaluation.OverallScore >= 0.85 {
		return "professional"
	} else if evaluation.OverallScore >= 0.7 {
		return "standard"
	}
	return "basic"
}
