package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"
)

const (
	baseURL = "http://localhost:8083"
)

// TestMultiAgentOrchestrationIntegration tests the complete multi-agent workflow
func TestMultiAgentOrchestrationIntegration(t *testing.T) {
	// Wait for service to be ready
	if !waitForService(baseURL+"/health", 30*time.Second) {
		t.Fatal("Multi-agent orchestrator service is not ready")
	}

	// Test 1: Register agents
	t.Run("RegisterAgents", func(t *testing.T) {
		agents := []Agent{
			{
				Name:        "Test Data Analyst",
				Type:        AgentTypeDataAnalyst,
				Description: "Test agent for data analysis",
				Endpoint:    "http://test-data-analyst:8080",
				Capabilities: []AgentCapability{
					{
						ID:          "data_analysis",
						Name:        "Data Analysis",
						Description: "Analyze data sets",
						Category:    "analytics",
						Cost:        0.10,
						Quality:     0.90,
						Speed:       0.80,
					},
				},
			},
			{
				Name:        "Test Content Writer",
				Type:        AgentTypeContentWriter,
				Description: "Test agent for content writing",
				Endpoint:    "http://test-content-writer:8080",
				Capabilities: []AgentCapability{
					{
						ID:          "content_writing",
						Name:        "Content Writing",
						Description: "Write content",
						Category:    "content",
						Cost:        0.15,
						Quality:     0.85,
						Speed:       0.75,
					},
				},
			},
		}

		var agentIDs []string
		for _, agent := range agents {
			agentID := registerAgent(t, agent)
			agentIDs = append(agentIDs, agentID)
		}

		// Verify agents are registered
		for _, agentID := range agentIDs {
			verifyAgent(t, agentID)
		}
	})

	// Test 2: Create and execute workflow
	t.Run("CreateAndExecuteWorkflow", func(t *testing.T) {
		// First, get available agents
		agents := listAgents(t)
		if len(agents) < 2 {
			t.Fatal("Need at least 2 agents for workflow test")
		}

		// Create workflow
		workflow := WorkflowCreationRequest{
			Name:        "Test Integration Workflow",
			Description: "Integration test workflow",
			CustomTasks: []WorkflowTask{
				{
					Name:            "Analyze Data",
					Description:     "Analyze test data",
					Type:            "data_analysis",
					AssignedAgentID: agents[0].ID,
					Parameters: map[string]interface{}{
						"data_source": "test_dataset.csv",
					},
					EstimatedCost: 0.10,
					EstimatedTime: 5 * time.Minute,
					Priority:      1,
				},
				{
					Name:            "Generate Report",
					Description:     "Generate analysis report",
					Type:            "content_writing",
					AssignedAgentID: agents[1].ID,
					Parameters: map[string]interface{}{
						"template": "analysis_report",
					},
					EstimatedCost: 0.15,
					EstimatedTime: 3 * time.Minute,
					Priority:      2,
				},
			},
		}

		workflowID := createWorkflow(t, workflow)
		
		// Validate workflow
		validateWorkflow(t, workflowID)

		// Execute workflow
		executeWorkflow(t, workflowID)

		// Monitor execution
		monitorWorkflowExecution(t, workflowID, 60*time.Second)
	})

	// Test 3: Test analytics
	t.Run("TestAnalytics", func(t *testing.T) {
		// Get agent analytics
		resp, err := http.Get(baseURL + "/v1/analytics/agents")
		if err != nil {
			t.Fatalf("Failed to get agent analytics: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}

		// Get workflow analytics
		resp, err = http.Get(baseURL + "/v1/analytics/workflows")
		if err != nil {
			t.Fatalf("Failed to get workflow analytics: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}
	})
}

// Helper functions

func waitForService(url string, timeout time.Duration) bool {
	client := &http.Client{Timeout: 5 * time.Second}
	deadline := time.Now().Add(timeout)

	for time.Now().Before(deadline) {
		resp, err := client.Get(url)
		if err == nil && resp.StatusCode == http.StatusOK {
			resp.Body.Close()
			return true
		}
		if resp != nil {
			resp.Body.Close()
		}
		time.Sleep(2 * time.Second)
	}
	return false
}

func registerAgent(t *testing.T, agent Agent) string {
	jsonData, err := json.Marshal(agent)
	if err != nil {
		t.Fatalf("Failed to marshal agent: %v", err)
	}

	resp, err := http.Post(baseURL+"/v1/agents", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		t.Fatalf("Failed to register agent: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		t.Errorf("Expected status 201, got %d", resp.StatusCode)
	}

	var registeredAgent Agent
	if err := json.NewDecoder(resp.Body).Decode(&registeredAgent); err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	if registeredAgent.ID == "" {
		t.Fatal("Agent ID should not be empty")
	}

	return registeredAgent.ID
}

func verifyAgent(t *testing.T, agentID string) {
	resp, err := http.Get(baseURL + "/v1/agents/" + agentID)
	if err != nil {
		t.Fatalf("Failed to get agent: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status 200, got %d", resp.StatusCode)
	}

	var agent Agent
	if err := json.NewDecoder(resp.Body).Decode(&agent); err != nil {
		t.Fatalf("Failed to decode agent: %v", err)
	}

	if agent.ID != agentID {
		t.Errorf("Expected agent ID %s, got %s", agentID, agent.ID)
	}
}

func listAgents(t *testing.T) []Agent {
	resp, err := http.Get(baseURL + "/v1/agents")
	if err != nil {
		t.Fatalf("Failed to list agents: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status 200, got %d", resp.StatusCode)
	}

	var agents []Agent
	if err := json.NewDecoder(resp.Body).Decode(&agents); err != nil {
		t.Fatalf("Failed to decode agents: %v", err)
	}

	return agents
}

func createWorkflow(t *testing.T, workflow WorkflowCreationRequest) string {
	jsonData, err := json.Marshal(workflow)
	if err != nil {
		t.Fatalf("Failed to marshal workflow: %v", err)
	}

	resp, err := http.Post(baseURL+"/v1/workflows", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		t.Fatalf("Failed to create workflow: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		t.Errorf("Expected status 201, got %d", resp.StatusCode)
	}

	var createdWorkflow MultiAgentWorkflow
	if err := json.NewDecoder(resp.Body).Decode(&createdWorkflow); err != nil {
		t.Fatalf("Failed to decode workflow: %v", err)
	}

	if createdWorkflow.ID == "" {
		t.Fatal("Workflow ID should not be empty")
	}

	return createdWorkflow.ID
}

func validateWorkflow(t *testing.T, workflowID string) {
	resp, err := http.Post(baseURL+"/v1/workflows/"+workflowID+"/validate", "application/json", nil)
	if err != nil {
		t.Fatalf("Failed to validate workflow: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status 200, got %d", resp.StatusCode)
	}

	var validation map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&validation); err != nil {
		t.Fatalf("Failed to decode validation response: %v", err)
	}

	if valid, ok := validation["valid"].(bool); !ok || !valid {
		issues := validation["issues"]
		t.Errorf("Workflow validation failed: %v", issues)
	}
}

func executeWorkflow(t *testing.T, workflowID string) {
	resp, err := http.Post(baseURL+"/v1/workflows/"+workflowID+"/execute", "application/json", nil)
	if err != nil {
		t.Fatalf("Failed to execute workflow: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusAccepted {
		t.Errorf("Expected status 202, got %d", resp.StatusCode)
	}
}

func monitorWorkflowExecution(t *testing.T, workflowID string, timeout time.Duration) {
	deadline := time.Now().Add(timeout)
	
	for time.Now().Before(deadline) {
		resp, err := http.Get(baseURL + "/v1/workflows/" + workflowID + "/status")
		if err != nil {
			t.Fatalf("Failed to get workflow status: %v", err)
		}

		var execution WorkflowExecution
		if err := json.NewDecoder(resp.Body).Decode(&execution); err != nil {
			resp.Body.Close()
			t.Fatalf("Failed to decode execution status: %v", err)
		}
		resp.Body.Close()

		switch execution.Status {
		case WorkflowStatusCompleted:
			t.Logf("Workflow completed successfully")
			return
		case WorkflowStatusFailed:
			t.Errorf("Workflow execution failed")
			return
		case WorkflowStatusCancelled:
			t.Errorf("Workflow was cancelled")
			return
		case WorkflowStatusExecuting:
			t.Logf("Workflow still executing, completed: %d, current: %d", 
				len(execution.CompletedTasks), len(execution.CurrentTasks))
		}

		time.Sleep(5 * time.Second)
	}

	t.Errorf("Workflow execution timed out after %v", timeout)
}

// Benchmark tests

func BenchmarkAgentRegistration(b *testing.B) {
	if !waitForService(baseURL+"/health", 10*time.Second) {
		b.Fatal("Service not ready")
	}

	agent := Agent{
		Name:        "Benchmark Agent",
		Type:        AgentTypeGeneralist,
		Description: "Agent for benchmarking",
		Endpoint:    "http://benchmark-agent:8080",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		agent.Name = fmt.Sprintf("Benchmark Agent %d", i)
		jsonData, _ := json.Marshal(agent)
		
		resp, err := http.Post(baseURL+"/v1/agents", "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			b.Fatalf("Failed to register agent: %v", err)
		}
		resp.Body.Close()
	}
}

func BenchmarkWorkflowCreation(b *testing.B) {
	if !waitForService(baseURL+"/health", 10*time.Second) {
		b.Fatal("Service not ready")
	}

	workflow := WorkflowCreationRequest{
		Name:        "Benchmark Workflow",
		Description: "Workflow for benchmarking",
		CustomTasks: []WorkflowTask{
			{
				Name:        "Benchmark Task",
				Description: "Task for benchmarking",
				Type:        "benchmark",
			},
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		workflow.Name = fmt.Sprintf("Benchmark Workflow %d", i)
		jsonData, _ := json.Marshal(workflow)
		
		resp, err := http.Post(baseURL+"/v1/workflows", "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			b.Fatalf("Failed to create workflow: %v", err)
		}
		resp.Body.Close()
	}
}
