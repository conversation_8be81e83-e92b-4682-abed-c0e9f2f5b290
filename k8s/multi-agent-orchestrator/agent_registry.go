package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/google/uuid"
)

// AgentRegistry manages the registration and discovery of AI agents
type AgentRegistry struct {
	agents               map[string]*Agent
	agentsByType         map[AgentType][]string
	agentsByCapability   map[string][]string
	mu                   sync.RWMutex
	healthChecker        *AgentHealthChecker
	storage              AgentStorage
	governanceServiceURL string
}

// AgentStorage interface for persisting agent data
type AgentStorage interface {
	SaveAgent(agent Agent) error
	LoadAgent(agentID string) (*Agent, error)
	LoadAllAgents() ([]Agent, error)
	DeleteAgent(agentID string) error
	UpdateAgentStatus(agentID string, status AgentStatus) error
	UpdateAgentPerformance(agentID string, performance AgentPerformance) error
}

// MemoryAgentStorage is an in-memory implementation of AgentStorage
type MemoryAgentStorage struct {
	agents map[string]Agent
	mu     sync.RWMutex
}

// NewAgentRegistry creates a new agent registry
// NewAgentRegistry creates a new agent registry
func NewAgentRegistry(storage AgentStorage, governanceServiceURL string) *AgentRegistry {
	registry := &AgentRegistry{
		agents:               make(map[string]*Agent),
		agentsByType:         make(map[AgentType][]string),
		agentsByCapability:   make(map[string][]string),
		storage:              storage,
		governanceServiceURL: governanceServiceURL,
	}

	// Initialize health checker
	registry.healthChecker = NewAgentHealthChecker(registry)

	// Load existing agents from storage
	if err := registry.loadAgentsFromStorage(); err != nil {
		log.Printf("Warning: Failed to load agents from storage: %v", err)
	}

	// Start health checking
	go registry.healthChecker.Start()

	return registry
}

// RegisterAgent registers a new agent in the system
func (ar *AgentRegistry) RegisterAgent(agent Agent) error {
	ar.mu.Lock()
	defer ar.mu.Unlock()

	// Validate agent
	if err := ar.validateAgent(agent); err != nil {
		return fmt.Errorf("agent validation failed: %w", err)
	}

	// Generate ID if not provided
	if agent.ID == "" {
		agent.ID = uuid.New().String()
	}

	// Set timestamps
	now := time.Now()
	agent.CreatedAt = now
	agent.UpdatedAt = now
	agent.LastSeen = now

	// Initialize performance metrics
	if agent.Performance.LastUpdated.IsZero() {
		agent.Performance.LastUpdated = now
	}

	// Store agent
	ar.agents[agent.ID] = &agent

	// Update indices
	ar.updateIndices(agent)

	// Persist to storage
	if err := ar.storage.SaveAgent(agent); err != nil {
		// Rollback in-memory changes
		delete(ar.agents, agent.ID)
		ar.removeFromIndices(agent)
		return fmt.Errorf("failed to persist agent: %w", err)
	}

	// Create factsheet in governance service
	if err := ar.createFactsheetForAgent(agent); err != nil {
		// This is a non-critical error, so we'll log it but not fail the registration
		log.Printf("WARNING: Failed to create factsheet for agent %s: %v", agent.ID, err)
	}

	log.Printf("Agent registered successfully: %s (%s)", agent.Name, agent.ID)
	return nil
}

// GetAgent retrieves an agent by ID
func (ar *AgentRegistry) GetAgent(agentID string) (*Agent, error) {
	ar.mu.RLock()
	defer ar.mu.RUnlock()

	agent, exists := ar.agents[agentID]
	if !exists {
		return nil, fmt.Errorf("agent not found: %s", agentID)
	}

	// Return a copy to prevent external modifications
	agentCopy := *agent
	return &agentCopy, nil
}

// ListAgents returns all registered agents
func (ar *AgentRegistry) ListAgents() []Agent {
	ar.mu.RLock()
	defer ar.mu.RUnlock()

	agents := make([]Agent, 0, len(ar.agents))
	for _, agent := range ar.agents {
		agents = append(agents, *agent)
	}

	return agents
}

// ListAgentsByType returns agents of a specific type
func (ar *AgentRegistry) ListAgentsByType(agentType AgentType) []Agent {
	ar.mu.RLock()
	defer ar.mu.RUnlock()

	agentIDs, exists := ar.agentsByType[agentType]
	if !exists {
		return []Agent{}
	}

	agents := make([]Agent, 0, len(agentIDs))
	for _, agentID := range agentIDs {
		if agent, exists := ar.agents[agentID]; exists {
			agents = append(agents, *agent)
		}
	}

	return agents
}

// FindAgentsByCapability finds agents that have a specific capability
func (ar *AgentRegistry) FindAgentsByCapability(capabilityID string) []Agent {
	ar.mu.RLock()
	defer ar.mu.RUnlock()

	agentIDs, exists := ar.agentsByCapability[capabilityID]
	if !exists {
		return []Agent{}
	}

	agents := make([]Agent, 0, len(agentIDs))
	for _, agentID := range agentIDs {
		if agent, exists := ar.agents[agentID]; exists {
			agents = append(agents, *agent)
		}
	}

	return agents
}

// UpdateAgentStatus updates the status of an agent
func (ar *AgentRegistry) UpdateAgentStatus(agentID string, status AgentStatus) error {
	ar.mu.Lock()
	defer ar.mu.Unlock()

	agent, exists := ar.agents[agentID]
	if !exists {
		return fmt.Errorf("agent not found: %s", agentID)
	}

	agent.Status = status
	agent.UpdatedAt = time.Now()
	agent.LastSeen = time.Now()

	// Persist to storage
	if err := ar.storage.UpdateAgentStatus(agentID, status); err != nil {
		return fmt.Errorf("failed to persist status update: %w", err)
	}

	return nil
}

// UpdateAgentPerformance updates the performance metrics of an agent
func (ar *AgentRegistry) UpdateAgentPerformance(agentID string, performance AgentPerformance) error {
	ar.mu.Lock()
	defer ar.mu.Unlock()

	agent, exists := ar.agents[agentID]
	if !exists {
		return fmt.Errorf("agent not found: %s", agentID)
	}

	performance.LastUpdated = time.Now()
	agent.Performance = performance
	agent.UpdatedAt = time.Now()

	// Persist to storage
	if err := ar.storage.UpdateAgentPerformance(agentID, performance); err != nil {
		return fmt.Errorf("failed to persist performance update: %w", err)
	}

	return nil
}

// UnregisterAgent removes an agent from the registry
func (ar *AgentRegistry) UnregisterAgent(agentID string) error {
	ar.mu.Lock()
	defer ar.mu.Unlock()

	agent, exists := ar.agents[agentID]
	if !exists {
		return fmt.Errorf("agent not found: %s", agentID)
	}

	// Remove from indices
	ar.removeFromIndices(*agent)

	// Remove from memory
	delete(ar.agents, agentID)

	// Remove from storage
	if err := ar.storage.DeleteAgent(agentID); err != nil {
		return fmt.Errorf("failed to delete agent from storage: %w", err)
	}

	log.Printf("Agent unregistered: %s (%s)", agent.Name, agentID)
	return nil
}

// GetAvailableAgents returns agents that are currently available (idle status)
func (ar *AgentRegistry) GetAvailableAgents() []Agent {
	ar.mu.RLock()
	defer ar.mu.RUnlock()

	var availableAgents []Agent
	for _, agent := range ar.agents {
		if agent.Status == AgentStatusIdle {
			availableAgents = append(availableAgents, *agent)
		}
	}

	return availableAgents
}

// SelectBestAgent selects the best agent for a given task based on capabilities and performance
// SelectBestAgent selects the best agent by filtering on capabilities and constraints, then scoring the candidates.
func (ar *AgentRegistry) SelectBestAgent(requiredCapabilities []string, preferences map[string]interface{}) (*Agent, error) {
	ar.mu.RLock()
	defer ar.mu.RUnlock()

	var candidates []*Agent
	log.Printf("Searching for agents with capabilities: %v and preferences: %v", requiredCapabilities, preferences)

	// 1. Initial Filtering based on capabilities and status
	for _, agent := range ar.agents {
		if agent.Status != AgentStatusIdle {
			continue // Skip non-idle agents
		}

		if hasAllRequiredCapabilities(agent, requiredCapabilities) {
			candidates = append(candidates, agent)
		}
	}

	if len(candidates) == 0 {
		return nil, fmt.Errorf("no available agents found with required capabilities")
	}
	log.Printf("Found %d initial candidates after capability filter.", len(candidates))

	// 2. Advanced Filtering based on Trust & Quality Constraints from preferences
	var filteredCandidates []*Agent
	for _, candidate := range candidates {
		if meetsTrustAndQualityConstraints(candidate, preferences) {
			filteredCandidates = append(filteredCandidates, candidate)
		}
	}

	if len(filteredCandidates) == 0 {
		log.Println("No agents met the advanced trust and quality constraints. Consider relaxing constraints.")
		return nil, fmt.Errorf("no available agents found that meet all quality and trust constraints")
	}
	log.Printf("Filtered down to %d candidates based on constraints.", len(filteredCandidates))

	// 3. Select best candidate from the filtered list based on scoring
	bestAgent := ar.selectBestCandidate(filteredCandidates, preferences)
	log.Printf("Selected best agent: %s (ID: %s)", bestAgent.Name, bestAgent.ID)

	// Return a copy to prevent external modifications
	bestAgentCopy := *bestAgent
	return &bestAgentCopy, nil
}

// Helper methods

func (ar *AgentRegistry) validateAgent(agent Agent) error {
	if agent.Name == "" {
		return fmt.Errorf("agent name is required")
	}
	if agent.Type == "" {
		return fmt.Errorf("agent type is required")
	}
	if agent.Endpoint == "" {
		return fmt.Errorf("agent endpoint is required")
	}
	return nil
}

func (ar *AgentRegistry) updateIndices(agent Agent) {
	// Update type index
	ar.agentsByType[agent.Type] = append(ar.agentsByType[agent.Type], agent.ID)

	// Update capability index
	for _, capability := range agent.Capabilities {
		ar.agentsByCapability[capability.ID] = append(ar.agentsByCapability[capability.ID], agent.ID)
	}
}

// createFactsheetForAgent calls the governance service to create a new model factsheet.
func (ar *AgentRegistry) createFactsheetForAgent(agent Agent) error {
	if ar.governanceServiceURL == "" {
		log.Println("Governance service URL not configured, skipping factsheet creation.")
		return nil
	}

	factsheet := agent.Factsheet
	if factsheet.ModelName == "" {
		factsheet.ModelName = agent.Name // Default to agent name
	}
	if factsheet.Description == "" {
		factsheet.Description = agent.Description
	}

	payload, err := json.Marshal(factsheet)
	if err != nil {
		return fmt.Errorf("failed to marshal factsheet payload: %w", err)
	}

	url := fmt.Sprintf("%s/factsheets", ar.governanceServiceURL)
	req, err := http.NewRequest(http.MethodPost, url, bytes.NewBuffer(payload))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request to governance service: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("governance service returned non-201 status: %s", resp.Status)
	}

	log.Printf("Successfully created factsheet for agent %s.", agent.ID)
	return nil
}

func (ar *AgentRegistry) removeFromIndices(agent Agent) {
	// Remove from type index
	if agentIDs, exists := ar.agentsByType[agent.Type]; exists {
		for i, id := range agentIDs {
			if id == agent.ID {
				ar.agentsByType[agent.Type] = append(agentIDs[:i], agentIDs[i+1:]...)
				break
			}
		}
	}

	// Remove from capability index
	for _, capability := range agent.Capabilities {
		if agentIDs, exists := ar.agentsByCapability[capability.ID]; exists {
			for i, id := range agentIDs {
				if id == agent.ID {
					ar.agentsByCapability[capability.ID] = append(agentIDs[:i], agentIDs[i+1:]...)
					break
				}
			}
		}
	}
}

func (ar *AgentRegistry) loadAgentsFromStorage() error {
	agents, err := ar.storage.LoadAllAgents()
	if err != nil {
		return err
	}

	for _, agent := range agents {
		ar.agents[agent.ID] = &agent
		ar.updateIndices(agent)
	}

	log.Printf("Loaded %d agents from storage", len(agents))
	return nil
}

// Helper function to check if an agent has all required capabilities.
func hasAllRequiredCapabilities(agent *Agent, requiredCapabilities []string) bool {
	agentCapabilities := make(map[string]bool)
	for _, cap := range agent.Capabilities {
		agentCapabilities[cap.ID] = true
	}

	for _, reqCap := range requiredCapabilities {
		if !agentCapabilities[reqCap] {
			return false
		}
	}
	return true
}

// Helper function to check if an agent meets the trust and quality constraints from preferences.
func meetsTrustAndQualityConstraints(agent *Agent, preferences map[string]interface{}) bool {
	if preferences == nil {
		return true // No constraints to apply
	}

	if minFairness, ok := preferences["min_fairness_score"].(float64); ok {
		if agent.Trust.FairnessScore < minFairness {
			log.Printf("Agent %s failed fairness constraint (%.2f < %.2f)", agent.Name, agent.Trust.FairnessScore, minFairness)
			return false
		}
	}
	if minRobustness, ok := preferences["min_robustness_score"].(float64); ok {
		// Using adversarial robustness as the key metric here, can be expanded.
		if agent.Quality.AdversarialRobustnessScore < minRobustness {
			log.Printf("Agent %s failed robustness constraint (%.2f < %.2f)", agent.Name, agent.Quality.AdversarialRobustnessScore, minRobustness)
			return false
		}
	}
	if requireLIME, ok := preferences["require_lime_explainability"].(bool); ok && requireLIME {
		if !agent.Trust.LIMEAvailability {
			log.Printf("Agent %s failed LIME explainability requirement", agent.Name)
			return false
		}
	}
	if requireSHAP, ok := preferences["require_shap_explainability"].(bool); ok && requireSHAP {
		if !agent.Trust.SHAPAvailability {
			log.Printf("Agent %s failed SHAP explainability requirement", agent.Name)
			return false
		}
	}

	return true // Agent meets all specified constraints
}

func (ar *AgentRegistry) selectBestCandidate(candidates []*Agent, preferences map[string]interface{}) *Agent {
	if len(candidates) == 1 {
		return candidates[0]
	}

	// Default selection based on performance metrics
	bestAgent := candidates[0]
	bestScore := ar.calculateAgentScore(bestAgent, preferences)

	for _, candidate := range candidates[1:] {
		score := ar.calculateAgentScore(candidate, preferences)
		if score > bestScore {
			bestScore = score
			bestAgent = candidate
		}
	}

	return bestAgent
}

// calculateAgentScore computes a holistic score based on performance, quality, and trust metrics.
func (ar *AgentRegistry) calculateAgentScore(agent *Agent, preferences map[string]interface{}) float64 {
	// Performance Score (weighted 40%)
	// Normalizing latency: assuming max latency is high, e.g., 5s. Anything higher gets 0.
	normalizedLatency := 1.0 - (agent.Performance.AverageLatency / 5000.0)
	if normalizedLatency < 0 {
		normalizedLatency = 0
	}
	performanceScore := agent.Performance.SuccessRate*0.5 +
		normalizedLatency*0.3 +
		agent.Performance.CostEfficiency*0.2

	// Quality Score (weighted 30%)
	// Using F1-score as the primary general metric. Can be switched with others.
	qualityScore := (agent.Quality.F1Score*0.4 +
		agent.Quality.AdversarialRobustnessScore*0.3 +
		agent.Quality.GeneralizabilityScore*0.3)

	// Trust Score (weighted 30%)
	trustScore := (agent.Trust.FairnessScore*0.5 +
		agent.Trust.ExplainabilityScore*0.3 +
		agent.Trust.PrivacyScore*0.2)

	// Base score combines Performance, Quality, and Trust
	// Weights can be adjusted based on overall platform strategy.
	baseScore := performanceScore*0.4 + qualityScore*0.3 + trustScore*0.3

	// Apply preferences for "priority"
	if preferences != nil {
		if priorityType, exists := preferences["priority"]; exists {
			switch priorityType {
			case "cost":
				baseScore = baseScore*0.5 + agent.Performance.CostEfficiency*0.5
			case "quality":
				// Prioritize the new detailed quality score
				baseScore = baseScore*0.5 + qualityScore*0.5
			case "speed":
				baseScore = baseScore*0.5 + normalizedLatency*0.5
			case "trust":
				baseScore = baseScore*0.5 + trustScore*0.5
			}
		}
	}

	log.Printf("Agent %s scoring -> Performance: %.2f, Quality: %.2f, Trust: %.2f, Final Score: %.2f",
		agent.Name, performanceScore, qualityScore, trustScore, baseScore)

	return baseScore
}

// Memory storage implementation

func NewMemoryAgentStorage() *MemoryAgentStorage {
	return &MemoryAgentStorage{
		agents: make(map[string]Agent),
	}
}

func (mas *MemoryAgentStorage) SaveAgent(agent Agent) error {
	mas.mu.Lock()
	defer mas.mu.Unlock()
	mas.agents[agent.ID] = agent
	return nil
}

func (mas *MemoryAgentStorage) LoadAgent(agentID string) (*Agent, error) {
	mas.mu.RLock()
	defer mas.mu.RUnlock()

	if agent, exists := mas.agents[agentID]; exists {
		return &agent, nil
	}
	return nil, fmt.Errorf("agent not found: %s", agentID)
}

func (mas *MemoryAgentStorage) LoadAllAgents() ([]Agent, error) {
	mas.mu.RLock()
	defer mas.mu.RUnlock()

	agents := make([]Agent, 0, len(mas.agents))
	for _, agent := range mas.agents {
		agents = append(agents, agent)
	}
	return agents, nil
}

func (mas *MemoryAgentStorage) DeleteAgent(agentID string) error {
	mas.mu.Lock()
	defer mas.mu.Unlock()
	delete(mas.agents, agentID)
	return nil
}

func (mas *MemoryAgentStorage) UpdateAgentStatus(agentID string, status AgentStatus) error {
	mas.mu.Lock()
	defer mas.mu.Unlock()

	if agent, exists := mas.agents[agentID]; exists {
		agent.Status = status
		agent.UpdatedAt = time.Now()
		mas.agents[agentID] = agent
		return nil
	}
	return fmt.Errorf("agent not found: %s", agentID)
}

func (mas *MemoryAgentStorage) UpdateAgentPerformance(agentID string, performance AgentPerformance) error {
	mas.mu.Lock()
	defer mas.mu.Unlock()

	if agent, exists := mas.agents[agentID]; exists {
		agent.Performance = performance
		agent.UpdatedAt = time.Now()
		mas.agents[agentID] = agent
		return nil
	}
	return fmt.Errorf("agent not found: %s", agentID)
}
