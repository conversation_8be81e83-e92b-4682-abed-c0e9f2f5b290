package main

import (
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/google/uuid"
)

// WorkflowTemplateLibrary manages a comprehensive collection of workflow templates
type WorkflowTemplateLibrary struct {
	templates  map[string]*WorkflowTemplate
	categories map[string][]string // category -> template IDs
	tags       map[string][]string // tag -> template IDs
}

// TemplateCategory represents a category of workflow templates
type TemplateCategory struct {
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	Description string   `json:"description"`
	Icon        string   `json:"icon"`
	Templates   []string `json:"template_ids"`
	Popular     bool     `json:"popular"`
}

// TemplateSearchRequest represents a search request for templates
type TemplateSearchRequest struct {
	Query      string   `json:"query"`
	Categories []string `json:"categories"`
	Tags       []string `json:"tags"`
	Complexity string   `json:"complexity"` // "simple", "intermediate", "advanced"
	SortBy     string   `json:"sort_by"`    // "popularity", "name", "created", "complexity"
	Limit      int      `json:"limit"`
}

// TemplateCustomizationOptions represents customization options for a template
type TemplateCustomizationOptions struct {
	TemplateID    string                 `json:"template_id"`
	Name          string                 `json:"name"`
	Description   string                 `json:"description"`
	AgentMappings map[string]string      `json:"agent_mappings"` // role -> agent ID
	Parameters    map[string]interface{} `json:"parameters"`     // custom parameters
	TaskOverrides []TaskOverride         `json:"task_overrides"` // task customizations
	RemoveTasks   []string               `json:"remove_tasks"`   // task IDs to remove
	AddTasks      []WorkflowTask         `json:"add_tasks"`      // additional tasks
	ModifyFlow    []FlowModification     `json:"modify_flow"`    // flow modifications
}

// TaskOverride represents modifications to a template task
type TaskOverride struct {
	TaskID      string                 `json:"task_id"`
	Name        string                 `json:"name,omitempty"`
	Description string                 `json:"description,omitempty"`
	Parameters  map[string]interface{} `json:"parameters,omitempty"`
	Priority    *int                   `json:"priority,omitempty"`
}

// FlowModification represents changes to workflow dependencies
type FlowModification struct {
	Type       string `json:"type"` // "add_dependency", "remove_dependency", "modify_dependency"
	FromTaskID string `json:"from_task_id"`
	ToTaskID   string `json:"to_task_id"`
	Condition  string `json:"condition,omitempty"`
}

// NewWorkflowTemplateLibrary creates a new template library
func NewWorkflowTemplateLibrary() *WorkflowTemplateLibrary {
	library := &WorkflowTemplateLibrary{
		templates:  make(map[string]*WorkflowTemplate),
		categories: make(map[string][]string),
		tags:       make(map[string][]string),
	}

	// Initialize with comprehensive template collection
	library.initializeTemplateLibrary()

	return library
}

// GetTemplate retrieves a template by ID
func (wtl *WorkflowTemplateLibrary) GetTemplate(templateID string) (*WorkflowTemplate, error) {
	template, exists := wtl.templates[templateID]
	if !exists {
		return nil, fmt.Errorf("template not found: %s", templateID)
	}

	// Return a copy
	templateCopy := *template
	return &templateCopy, nil
}

// SearchTemplates searches for templates based on criteria
func (wtl *WorkflowTemplateLibrary) SearchTemplates(request TemplateSearchRequest) ([]WorkflowTemplate, error) {
	var results []WorkflowTemplate

	for _, template := range wtl.templates {
		if wtl.matchesSearchCriteria(template, request) {
			results = append(results, *template)
		}
	}

	// Sort results
	wtl.sortTemplates(results, request.SortBy)

	// Apply limit
	if request.Limit > 0 && len(results) > request.Limit {
		results = results[:request.Limit]
	}

	return results, nil
}

// GetCategories returns all template categories
func (wtl *WorkflowTemplateLibrary) GetCategories() []TemplateCategory {
	categories := []TemplateCategory{
		{
			ID:          "data-analytics",
			Name:        "Data Analytics",
			Description: "Templates for data analysis, processing, and visualization workflows",
			Icon:        "bar-chart",
			Popular:     true,
		},
		{
			ID:          "content-creation",
			Name:        "Content Creation",
			Description: "Templates for content writing, editing, and publishing workflows",
			Icon:        "edit",
			Popular:     true,
		},
		{
			ID:          "research-validation",
			Name:        "Research & Validation",
			Description: "Templates for research, fact-checking, and validation workflows",
			Icon:        "search",
			Popular:     false,
		},
		{
			ID:          "automation",
			Name:        "Process Automation",
			Description: "Templates for automating business processes and workflows",
			Icon:        "zap",
			Popular:     true,
		},
		{
			ID:          "quality-assurance",
			Name:        "Quality Assurance",
			Description: "Templates for testing, validation, and quality control workflows",
			Icon:        "check-circle",
			Popular:     false,
		},
		{
			ID:          "customer-service",
			Name:        "Customer Service",
			Description: "Templates for customer support and service workflows",
			Icon:        "users",
			Popular:     false,
		},
	}

	// Add template counts
	for i := range categories {
		if templateIDs, exists := wtl.categories[categories[i].ID]; exists {
			categories[i].Templates = templateIDs
		}
	}

	return categories
}

// CustomizeTemplate creates a customized workflow from a template
func (wtl *WorkflowTemplateLibrary) CustomizeTemplate(options TemplateCustomizationOptions) (*MultiAgentWorkflow, error) {
	template, err := wtl.GetTemplate(options.TemplateID)
	if err != nil {
		return nil, err
	}

	// Create base workflow from template
	workflow := &MultiAgentWorkflow{
		ID:          uuid.New().String(),
		Name:        options.Name,
		Description: options.Description,
		Status:      WorkflowStatusDraft,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Metadata: map[string]interface{}{
			"template_id":      template.ID,
			"template_version": "1.0",
			"customized":       true,
		},
	}

	// Copy and customize tasks
	taskIDMap := make(map[string]string) // template task ID -> new task ID
	for _, taskTemplate := range template.TaskTemplates {
		// Check if task should be removed
		if wtl.contains(options.RemoveTasks, taskTemplate.ID) {
			continue
		}

		// Create new task
		newTaskID := uuid.New().String()
		taskIDMap[taskTemplate.ID] = newTaskID

		task := WorkflowTask{
			ID:            newTaskID,
			WorkflowID:    workflow.ID,
			Name:          taskTemplate.Name,
			Description:   taskTemplate.Description,
			Type:          taskTemplate.Type,
			Parameters:    make(map[string]interface{}),
			Status:        "pending",
			Priority:      taskTemplate.Priority,
			EstimatedCost: taskTemplate.EstimatedCost,
			EstimatedTime: taskTemplate.EstimatedTime,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}

		// Copy parameters
		for k, v := range taskTemplate.Parameters {
			task.Parameters[k] = v
		}

		// Apply custom parameters
		for k, v := range options.Parameters {
			task.Parameters[k] = v
		}

		// Apply task overrides
		for _, override := range options.TaskOverrides {
			if override.TaskID == taskTemplate.ID {
				if override.Name != "" {
					task.Name = override.Name
				}
				if override.Description != "" {
					task.Description = override.Description
				}
				if override.Priority != nil {
					task.Priority = *override.Priority
				}
				for k, v := range override.Parameters {
					task.Parameters[k] = v
				}
			}
		}

		// Assign agent if mapping provided
		if agentID, exists := options.AgentMappings[taskTemplate.AgentRole]; exists {
			task.AssignedAgentID = agentID
		}

		workflow.Tasks = append(workflow.Tasks, task)
	}

	// Add additional tasks
	for _, additionalTask := range options.AddTasks {
		additionalTask.ID = uuid.New().String()
		additionalTask.WorkflowID = workflow.ID
		additionalTask.CreatedAt = time.Now()
		additionalTask.UpdatedAt = time.Now()
		workflow.Tasks = append(workflow.Tasks, additionalTask)
	}

	// Copy and modify dependencies
	for _, depTemplate := range template.Dependencies {
		fromTaskID, fromExists := taskIDMap[depTemplate.FromTaskID]
		toTaskID, toExists := taskIDMap[depTemplate.ToTaskID]

		if fromExists && toExists {
			dependency := WorkflowDependency{
				ID:          uuid.New().String(),
				WorkflowID:  workflow.ID,
				FromTaskID:  fromTaskID,
				ToTaskID:    toTaskID,
				Type:        depTemplate.Type,
				Description: depTemplate.Description,
			}
			workflow.Dependencies = append(workflow.Dependencies, dependency)
		}
	}

	// Apply flow modifications
	for _, modification := range options.ModifyFlow {
		switch modification.Type {
		case "add_dependency":
			dependency := WorkflowDependency{
				ID:          uuid.New().String(),
				WorkflowID:  workflow.ID,
				FromTaskID:  modification.FromTaskID,
				ToTaskID:    modification.ToTaskID,
				Type:        "custom",
				Description: "Custom dependency",
			}
			workflow.Dependencies = append(workflow.Dependencies, dependency)
		case "remove_dependency":
			// Remove matching dependency
			for i, dep := range workflow.Dependencies {
				if dep.FromTaskID == modification.FromTaskID && dep.ToTaskID == modification.ToTaskID {
					workflow.Dependencies = append(workflow.Dependencies[:i], workflow.Dependencies[i+1:]...)
					break
				}
			}
		}
	}

	// Calculate estimates
	var totalCost float64
	var maxTime time.Duration
	agentSet := make(map[string]bool)

	for _, task := range workflow.Tasks {
		totalCost += task.EstimatedCost
		if task.EstimatedTime > maxTime {
			maxTime = task.EstimatedTime
		}
		if task.AssignedAgentID != "" {
			agentSet[task.AssignedAgentID] = true
		}
	}

	workflow.EstimatedCost = totalCost
	workflow.EstimatedTime = maxTime
	workflow.Agents = make([]string, 0, len(agentSet))
	for agentID := range agentSet {
		workflow.Agents = append(workflow.Agents, agentID)
	}

	log.Printf("Customized workflow '%s' from template '%s'", workflow.Name, template.Name)
	return workflow, nil
}

// Helper methods

func (wtl *WorkflowTemplateLibrary) initializeTemplateLibrary() {
	templates := []*WorkflowTemplate{
		// Data Analytics Templates
		{
			ID:          "comprehensive-data-analysis",
			Name:        "Comprehensive Data Analysis Pipeline",
			Description: "End-to-end data analysis workflow with collection, cleaning, analysis, and reporting",
			Category:    "data-analytics",
			RequiredAgents: []AgentRequirement{
				{Role: "collector", Type: AgentTypeDataAnalyst, Capabilities: []string{"data_collection"}},
				{Role: "cleaner", Type: AgentTypeDataAnalyst, Capabilities: []string{"data_cleaning"}},
				{Role: "analyst", Type: AgentTypeDataAnalyst, Capabilities: []string{"statistical_analysis"}},
				{Role: "visualizer", Type: AgentTypeDataAnalyst, Capabilities: []string{"data_visualization"}},
				{Role: "reporter", Type: AgentTypeContentWriter, Capabilities: []string{"report_generation"}},
			},
			TaskTemplates: []TaskTemplate{
				{
					ID:            "collect-data",
					Name:          "Collect Data",
					Description:   "Gather data from specified sources",
					AgentRole:     "collector",
					Type:          "data_collection",
					EstimatedCost: 0.50,
					EstimatedTime: 10 * time.Minute,
					Priority:      1,
				},
				{
					ID:            "clean-data",
					Name:          "Clean and Prepare Data",
					Description:   "Clean, validate, and prepare data for analysis",
					AgentRole:     "cleaner",
					Type:          "data_cleaning",
					EstimatedCost: 0.75,
					EstimatedTime: 15 * time.Minute,
					Priority:      2,
				},
				{
					ID:            "analyze-data",
					Name:          "Perform Statistical Analysis",
					Description:   "Conduct comprehensive statistical analysis",
					AgentRole:     "analyst",
					Type:          "statistical_analysis",
					EstimatedCost: 1.25,
					EstimatedTime: 25 * time.Minute,
					Priority:      3,
				},
				{
					ID:            "create-visualizations",
					Name:          "Create Data Visualizations",
					Description:   "Generate charts, graphs, and visual representations",
					AgentRole:     "visualizer",
					Type:          "data_visualization",
					EstimatedCost: 0.80,
					EstimatedTime: 20 * time.Minute,
					Priority:      4,
				},
				{
					ID:            "generate-report",
					Name:          "Generate Analysis Report",
					Description:   "Create comprehensive analysis report with findings",
					AgentRole:     "reporter",
					Type:          "report_generation",
					EstimatedCost: 1.00,
					EstimatedTime: 30 * time.Minute,
					Priority:      5,
				},
			},
			Dependencies: []DependencyTemplate{
				{FromTaskID: "collect-data", ToTaskID: "clean-data", Type: "sequential"},
				{FromTaskID: "clean-data", ToTaskID: "analyze-data", Type: "sequential"},
				{FromTaskID: "analyze-data", ToTaskID: "create-visualizations", Type: "parallel"},
				{FromTaskID: "analyze-data", ToTaskID: "generate-report", Type: "sequential"},
				{FromTaskID: "create-visualizations", ToTaskID: "generate-report", Type: "sequential"},
			},
			EstimatedCost: 4.30,
			EstimatedTime: 60 * time.Minute,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},

		// Content Creation Templates
		{
			ID:          "blog-post-creation",
			Name:        "Blog Post Creation Workflow",
			Description: "Complete blog post creation from research to publication",
			Category:    "content-creation",
			RequiredAgents: []AgentRequirement{
				{Role: "researcher", Type: AgentTypeResearcher, Capabilities: []string{"web_research"}},
				{Role: "writer", Type: AgentTypeContentWriter, Capabilities: []string{"content_writing"}},
				{Role: "editor", Type: AgentTypeValidator, Capabilities: []string{"content_review"}},
				{Role: "seo_optimizer", Type: AgentTypeSpecialist, Capabilities: []string{"seo_optimization"}},
			},
			TaskTemplates: []TaskTemplate{
				{
					ID:            "research-topic",
					Name:          "Research Topic",
					Description:   "Conduct comprehensive research on the blog topic",
					AgentRole:     "researcher",
					Type:          "web_research",
					EstimatedCost: 0.60,
					EstimatedTime: 15 * time.Minute,
					Priority:      1,
				},
				{
					ID:            "write-content",
					Name:          "Write Blog Content",
					Description:   "Create engaging blog post content",
					AgentRole:     "writer",
					Type:          "content_writing",
					EstimatedCost: 1.20,
					EstimatedTime: 45 * time.Minute,
					Priority:      2,
				},
				{
					ID:            "edit-content",
					Name:          "Edit and Review",
					Description:   "Edit content for clarity, grammar, and style",
					AgentRole:     "editor",
					Type:          "content_review",
					EstimatedCost: 0.80,
					EstimatedTime: 20 * time.Minute,
					Priority:      3,
				},
				{
					ID:            "optimize-seo",
					Name:          "SEO Optimization",
					Description:   "Optimize content for search engines",
					AgentRole:     "seo_optimizer",
					Type:          "seo_optimization",
					EstimatedCost: 0.40,
					EstimatedTime: 10 * time.Minute,
					Priority:      4,
				},
			},
			Dependencies: []DependencyTemplate{
				{FromTaskID: "research-topic", ToTaskID: "write-content", Type: "sequential"},
				{FromTaskID: "write-content", ToTaskID: "edit-content", Type: "sequential"},
				{FromTaskID: "edit-content", ToTaskID: "optimize-seo", Type: "sequential"},
			},
			EstimatedCost: 3.00,
			EstimatedTime: 90 * time.Minute,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},

		// Research & Validation Templates
		{
			ID:          "fact-checking-workflow",
			Name:        "Comprehensive Fact-Checking Workflow",
			Description: "Multi-source fact-checking and validation process",
			Category:    "research-validation",
			RequiredAgents: []AgentRequirement{
				{Role: "primary_researcher", Type: AgentTypeResearcher, Capabilities: []string{"web_research"}},
				{Role: "secondary_researcher", Type: AgentTypeResearcher, Capabilities: []string{"academic_research"}},
				{Role: "fact_checker", Type: AgentTypeValidator, Capabilities: []string{"fact_checking"}},
				{Role: "source_validator", Type: AgentTypeValidator, Capabilities: []string{"source_validation"}},
			},
			TaskTemplates: []TaskTemplate{
				{
					ID:            "primary-research",
					Name:          "Primary Source Research",
					Description:   "Research claims using primary sources",
					AgentRole:     "primary_researcher",
					Type:          "web_research",
					EstimatedCost: 0.80,
					EstimatedTime: 20 * time.Minute,
					Priority:      1,
				},
				{
					ID:            "secondary-research",
					Name:          "Academic Source Research",
					Description:   "Research using academic and scholarly sources",
					AgentRole:     "secondary_researcher",
					Type:          "academic_research",
					EstimatedCost: 1.00,
					EstimatedTime: 25 * time.Minute,
					Priority:      1,
				},
				{
					ID:            "cross-reference",
					Name:          "Cross-Reference Facts",
					Description:   "Cross-reference findings from multiple sources",
					AgentRole:     "fact_checker",
					Type:          "fact_checking",
					EstimatedCost: 0.90,
					EstimatedTime: 15 * time.Minute,
					Priority:      2,
				},
				{
					ID:            "validate-sources",
					Name:          "Validate Source Credibility",
					Description:   "Assess credibility and reliability of sources",
					AgentRole:     "source_validator",
					Type:          "source_validation",
					EstimatedCost: 0.70,
					EstimatedTime: 10 * time.Minute,
					Priority:      3,
				},
			},
			Dependencies: []DependencyTemplate{
				{FromTaskID: "primary-research", ToTaskID: "cross-reference", Type: "sequential"},
				{FromTaskID: "secondary-research", ToTaskID: "cross-reference", Type: "sequential"},
				{FromTaskID: "cross-reference", ToTaskID: "validate-sources", Type: "sequential"},
			},
			EstimatedCost: 3.40,
			EstimatedTime: 45 * time.Minute,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
	}

	// Add templates to library
	for _, template := range templates {
		wtl.templates[template.ID] = template

		// Add to category index
		if template.Category != "" {
			wtl.categories[template.Category] = append(wtl.categories[template.Category], template.ID)
		}

		// Add to tag index (using category as tag for now)
		if template.Category != "" {
			wtl.tags[template.Category] = append(wtl.tags[template.Category], template.ID)
		}
	}

	log.Printf("Initialized template library with %d templates", len(templates))
}

func (wtl *WorkflowTemplateLibrary) matchesSearchCriteria(template *WorkflowTemplate, request TemplateSearchRequest) bool {
	// Query matching
	if request.Query != "" {
		query := strings.ToLower(request.Query)
		if !strings.Contains(strings.ToLower(template.Name), query) &&
			!strings.Contains(strings.ToLower(template.Description), query) {
			return false
		}
	}

	// Category filtering
	if len(request.Categories) > 0 {
		found := false
		for _, category := range request.Categories {
			if template.Category == category {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	return true
}

func (wtl *WorkflowTemplateLibrary) sortTemplates(templates []WorkflowTemplate, sortBy string) {
	// Simple sorting implementation
	// In a real system, this would include popularity metrics
}

func (wtl *WorkflowTemplateLibrary) contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
