apiVersion: apps/v1
kind: Deployment
metadata:
  name: multi-agent-orchestrator
  labels:
    app: multi-agent-orchestrator
spec:
  replicas: 1
  selector:
    matchLabels:
      app: multi-agent-orchestrator
  template:
    metadata:
      labels:
        app: multi-agent-orchestrator
    spec:
      containers:
      - name: multi-agent-orchestrator
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-multi-agent-orchestrator:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8083
        env:
        - name: PORT
          value: "8083"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8083
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8083
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: multi-agent-orchestrator-service
  labels:
    app: multi-agent-orchestrator
spec:
  type: ClusterIP
  ports:
  - port: 8083
    targetPort: 8083
    protocol: TCP
    name: http
  selector:
    app: multi-agent-orchestrator
