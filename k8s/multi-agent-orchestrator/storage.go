package main

import (
	"fmt"
	"sync"
)

// MemoryWorkflowStorage provides in-memory storage for workflows and templates
type MemoryWorkflowStorage struct {
	workflows map[string]MultiAgentWorkflow
	templates map[string]WorkflowTemplate
	mu        sync.RWMutex
}

// NewMemoryWorkflowStorage creates a new in-memory workflow storage
func NewMemoryWorkflowStorage() *MemoryWorkflowStorage {
	return &MemoryWorkflowStorage{
		workflows: make(map[string]MultiAgentWorkflow),
		templates: make(map[string]WorkflowTemplate),
	}
}

// SaveWorkflow saves a workflow to memory
func (mws *MemoryWorkflowStorage) SaveWorkflow(workflow MultiAgentWorkflow) error {
	mws.mu.Lock()
	defer mws.mu.Unlock()
	mws.workflows[workflow.ID] = workflow
	return nil
}

// LoadWorkflow loads a workflow from memory
func (mws *MemoryWorkflowStorage) LoadWorkflow(workflowID string) (*MultiAgentWorkflow, error) {
	mws.mu.RLock()
	defer mws.mu.RUnlock()

	if workflow, exists := mws.workflows[workflowID]; exists {
		return &workflow, nil
	}
	return nil, fmt.Errorf("workflow not found: %s", workflowID)
}

// LoadAllWorkflows loads all workflows from memory
func (mws *MemoryWorkflowStorage) LoadAllWorkflows() ([]MultiAgentWorkflow, error) {
	mws.mu.RLock()
	defer mws.mu.RUnlock()

	workflows := make([]MultiAgentWorkflow, 0, len(mws.workflows))
	for _, workflow := range mws.workflows {
		workflows = append(workflows, workflow)
	}
	return workflows, nil
}

// DeleteWorkflow deletes a workflow from memory
func (mws *MemoryWorkflowStorage) DeleteWorkflow(workflowID string) error {
	mws.mu.Lock()
	defer mws.mu.Unlock()
	delete(mws.workflows, workflowID)
	return nil
}

// SaveTemplate saves a template to memory
func (mws *MemoryWorkflowStorage) SaveTemplate(template WorkflowTemplate) error {
	mws.mu.Lock()
	defer mws.mu.Unlock()
	mws.templates[template.ID] = template
	return nil
}

// LoadTemplate loads a template from memory
func (mws *MemoryWorkflowStorage) LoadTemplate(templateID string) (*WorkflowTemplate, error) {
	mws.mu.RLock()
	defer mws.mu.RUnlock()

	if template, exists := mws.templates[templateID]; exists {
		return &template, nil
	}
	return nil, fmt.Errorf("template not found: %s", templateID)
}

// LoadAllTemplates loads all templates from memory
func (mws *MemoryWorkflowStorage) LoadAllTemplates() ([]WorkflowTemplate, error) {
	mws.mu.RLock()
	defer mws.mu.RUnlock()

	templates := make([]WorkflowTemplate, 0, len(mws.templates))
	for _, template := range mws.templates {
		templates = append(templates, template)
	}
	return templates, nil
}
