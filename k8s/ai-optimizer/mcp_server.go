package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/gorilla/websocket"
)

// MCPServer provides MCP server capabilities for the AI Optimizer
type MCPServer struct {
	upgrader websocket.Upgrader
}

// MCPRequest represents an incoming MCP JSON-RPC request
type MCPRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      interface{} `json:"id"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params,omitempty"`
}

// MCPResponse represents an outgoing MCP JSON-RPC response
type MCPResponse struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      interface{} `json:"id"`
	Result  interface{} `json:"result,omitempty"`
	Error   *MCPError   `json:"error,omitempty"`
}

// MCPError represents an MCP error
type MCPError struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// MCPTool represents an MCP tool definition
type MCPTool struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	InputSchema interface{} `json:"inputSchema"`
}

// MCPResource represents an MCP resource definition
type MCPResource struct {
	URI         string `json:"uri"`
	Name        string `json:"name"`
	Description string `json:"description"`
	MimeType    string `json:"mimeType,omitempty"`
}

// NewMCPServer creates a new MCP server instance
func NewMCPServer() *MCPServer {
	return &MCPServer{
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// Allow connections from any origin for now
				// In production, implement proper origin checking
				return true
			},
		},
	}
}

// HandleMCPConnection handles MCP WebSocket connections
func (s *MCPServer) HandleMCPConnection(w http.ResponseWriter, r *http.Request) {
	conn, err := s.upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("MCP Server: Failed to upgrade to WebSocket: %v", err)
		return
	}
	defer conn.Close()

	log.Printf("MCP Server: New client connected")

	// Handle MCP protocol initialization
	if err := s.handleInitialization(conn); err != nil {
		log.Printf("MCP Server: Initialization failed: %v", err)
		return
	}

	// Handle incoming messages
	for {
		var req MCPRequest
		if err := conn.ReadJSON(&req); err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("MCP Server: WebSocket error: %v", err)
			}
			break
		}

		response := s.handleMCPRequest(req)
		if err := conn.WriteJSON(response); err != nil {
			log.Printf("MCP Server: Error sending response: %v", err)
			break
		}
	}

	log.Printf("MCP Server: Client disconnected")
}

// handleInitialization handles the MCP initialization handshake
func (s *MCPServer) handleInitialization(conn *websocket.Conn) error {
	// Send server capabilities
	capabilities := map[string]interface{}{
		"tools": map[string]interface{}{
			"listChanged": true,
		},
		"resources": map[string]interface{}{
			"subscribe":   true,
			"listChanged": true,
		},
	}

	initResponse := MCPResponse{
		JSONRPC: "2.0",
		ID:      "init",
		Result: map[string]interface{}{
			"protocolVersion": "2025-06-18",
			"capabilities":    capabilities,
			"serverInfo": map[string]interface{}{
				"name":    "ai-optimizer-mcp-server",
				"version": "1.0.0",
			},
		},
	}

	return conn.WriteJSON(initResponse)
}

// handleMCPRequest processes incoming MCP requests
func (s *MCPServer) handleMCPRequest(req MCPRequest) MCPResponse {
	switch req.Method {
	case "tools/list":
		return s.handleToolsList(req)
	case "tools/call":
		return s.handleToolCall(req)
	case "resources/list":
		return s.handleResourcesList(req)
	case "resources/read":
		return s.handleResourceRead(req)
	default:
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32601,
				Message: "Method not found",
			},
		}
	}
}

// handleToolsList returns the list of available tools
func (s *MCPServer) handleToolsList(req MCPRequest) MCPResponse {
	tools := []MCPTool{
		{
			Name:        "optimize_llm_routing",
			Description: "Intelligently route LLM requests based on prompt analysis and model capabilities",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"prompt": map[string]interface{}{
						"type":        "string",
						"description": "The prompt to analyze and route",
					},
					"model": map[string]interface{}{
						"type":        "string",
						"description": "Requested model (optional)",
					},
					"user_id": map[string]interface{}{
						"type":        "string",
						"description": "User ID for personalized routing",
					},
					"api_type": map[string]interface{}{
						"type":        "string",
						"description": "API type (chat_completions, embeddings, etc.)",
					},
				},
				"required": []string{"prompt"},
			},
		},
		{
			Name:        "get_cost_analysis",
			Description: "Get real-time cost analysis for different model options",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"prompt": map[string]interface{}{
						"type":        "string",
						"description": "The prompt to analyze for cost estimation",
					},
					"models": map[string]interface{}{
						"type":        "array",
						"items":       map[string]interface{}{"type": "string"},
						"description": "List of models to compare (optional)",
					},
				},
				"required": []string{"prompt"},
			},
		},
		{
			Name:        "analyze_prompt",
			Description: "Perform advanced prompt classification and requirement analysis",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"prompt": map[string]interface{}{
						"type":        "string",
						"description": "The prompt to analyze",
					},
				},
				"required": []string{"prompt"},
			},
		},
		{
			Name:        "get_performance_metrics",
			Description: "Get live performance data for all models",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"model_id": map[string]interface{}{
						"type":        "string",
						"description": "Specific model ID (optional)",
					},
					"time_range": map[string]interface{}{
						"type":        "string",
						"description": "Time range for metrics (1h, 24h, 7d)",
						"default":     "1h",
					},
				},
			},
		},
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      req.ID,
		Result: map[string]interface{}{
			"tools": tools,
		},
	}
}

// handleToolCall executes a tool call
func (s *MCPServer) handleToolCall(req MCPRequest) MCPResponse {
	params, ok := req.Params.(map[string]interface{})
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "Invalid params",
			},
		}
	}

	toolName, ok := params["name"].(string)
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "Tool name required",
			},
		}
	}

	arguments, _ := params["arguments"].(map[string]interface{})

	switch toolName {
	case "optimize_llm_routing":
		return s.executeOptimizeLLMRouting(req.ID, arguments)
	case "get_cost_analysis":
		return s.executeGetCostAnalysis(req.ID, arguments)
	case "analyze_prompt":
		return s.executeAnalyzePrompt(req.ID, arguments)
	case "get_performance_metrics":
		return s.executeGetPerformanceMetrics(req.ID, arguments)
	default:
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32601,
				Message: "Tool not found",
			},
		}
	}
}

// executeOptimizeLLMRouting implements the optimize_llm_routing tool
func (s *MCPServer) executeOptimizeLLMRouting(id interface{}, args map[string]interface{}) MCPResponse {
	prompt, ok := args["prompt"].(string)
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32602,
				Message: "Prompt is required",
			},
		}
	}

	// Use existing routing logic
	ctx := context.Background()
	if err := loadModelProfilesFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load model profiles: %v", err)
	}

	// Create a basic OptimizationRequest for MCP
	optReq := OptimizationRequest{
		Prompt:  prompt,
		Model:   getString(args, "model"),
		APIType: getString(args, "api_type"),
	}

	// Apply intelligent routing using existing functions
	var routeResp AIOptimizerRouteResponse
	if len(routingStrategies) > 0 {
		strategy := routingStrategies[0]
		switch strategy.Strategy {
		case "default":
			routeResp = applyDefaultStrategy(strategy, modelProfiles, optReq)
		case "cascade":
			routeResp = applyCascadeStrategy(strategy, modelProfiles, optReq)
		case "parallel":
			routeResp = applyParallelStrategy(strategy, modelProfiles, optReq)
		default:
			routeResp = applyDefaultStrategy(strategy, modelProfiles, optReq)
		}
	} else {
		routeResp = AIOptimizerRouteResponse{
			Error: "No routing strategies available",
		}
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": fmt.Sprintf("Optimized routing result for prompt (%.50s...):\nSelected Model: %s\nBackend Type: %s\nTask Type: %s\nPolicy Applied: %s",
						prompt, routeResp.SelectedBackendID, routeResp.BackendType, routeResp.TaskType, routeResp.PolicyIDApplied),
				},
			},
			"isError": routeResp.Error != "",
		},
	}
}

// executeGetCostAnalysis implements the get_cost_analysis tool
func (s *MCPServer) executeGetCostAnalysis(id interface{}, args map[string]interface{}) MCPResponse {
	prompt, ok := args["prompt"].(string)
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32602,
				Message: "Prompt is required",
			},
		}
	}

	// Estimate token count (simple approximation)
	estimatedTokens := len(prompt) / 4 // Rough estimate: 4 chars per token

	// Get cost analysis for available models
	costAnalysis := make([]map[string]interface{}, 0)
	for _, profile := range modelProfiles {
		estimatedCost := float64(estimatedTokens) * profile.CostPerInputToken
		costAnalysis = append(costAnalysis, map[string]interface{}{
			"model_id":         profile.ID,
			"model_name":       profile.Name,
			"estimated_cost":   estimatedCost,
			"cost_per_token":   profile.CostPerInputToken,
			"estimated_tokens": estimatedTokens,
			"pricing_tier":     profile.PricingTier,
		})
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": fmt.Sprintf("Cost analysis for %d models:\n%s", len(costAnalysis), formatCostAnalysis(costAnalysis)),
				},
			},
			"isError": false,
		},
	}
}

// executeAnalyzePrompt implements the analyze_prompt tool
func (s *MCPServer) executeAnalyzePrompt(id interface{}, args map[string]interface{}) MCPResponse {
	prompt, ok := args["prompt"].(string)
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32602,
				Message: "Prompt is required",
			},
		}
	}

	// Perform basic prompt analysis
	analysis := map[string]interface{}{
		"length":     len(prompt),
		"word_count": len(prompt) / 5, // Rough estimate
		"complexity": determineComplexity(prompt),
		"task_type":  determineTaskType(prompt),
		"domain":     determineDomain(prompt),
		"timestamp":  time.Now().UTC(),
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": fmt.Sprintf("Prompt Analysis:\nLength: %d characters\nComplexity: %s\nTask Type: %s\nDomain: %s",
						analysis["length"], analysis["complexity"], analysis["task_type"], analysis["domain"]),
				},
			},
			"isError": false,
		},
	}
}

// executeGetPerformanceMetrics implements the get_performance_metrics tool
func (s *MCPServer) executeGetPerformanceMetrics(id interface{}, args map[string]interface{}) MCPResponse {
	modelID := getString(args, "model_id")
	timeRange := getString(args, "time_range")
	if timeRange == "" {
		timeRange = "1h"
	}

	// Get performance metrics
	metrics := make([]map[string]interface{}, 0)
	for _, profile := range modelProfiles {
		if modelID == "" || profile.ID == modelID {
			metrics = append(metrics, map[string]interface{}{
				"model_id":         profile.ID,
				"model_name":       profile.Name,
				"expected_latency": profile.ExpectedLatencyMs,
				"expected_cost":    profile.ExpectedCost,
				"tier":             profile.Tier,
				"backend_type":     profile.BackendType,
				"capabilities":     profile.Capabilities,
				"time_range":       timeRange,
			})
		}
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": fmt.Sprintf("Performance metrics for %d models:\n%s", len(metrics), formatPerformanceMetrics(metrics)),
				},
			},
			"isError": false,
		},
	}
}

// handleResourcesList returns the list of available resources
func (s *MCPServer) handleResourcesList(req MCPRequest) MCPResponse {
	resources := []MCPResource{
		{
			URI:         "ai-optimizer://model-profiles",
			Name:        "Model Profiles",
			Description: "Current model capabilities and pricing information",
			MimeType:    "application/json",
		},
		{
			URI:         "ai-optimizer://routing-policies",
			Name:        "Routing Policies",
			Description: "Active routing rules and preferences",
			MimeType:    "application/json",
		},
		{
			URI:         "ai-optimizer://optimization-history",
			Name:        "Optimization History",
			Description: "Historical routing decisions and outcomes",
			MimeType:    "application/json",
		},
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      req.ID,
		Result: map[string]interface{}{
			"resources": resources,
		},
	}
}

// handleResourceRead reads a specific resource
func (s *MCPServer) handleResourceRead(req MCPRequest) MCPResponse {
	params, ok := req.Params.(map[string]interface{})
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "Invalid params",
			},
		}
	}

	uri, ok := params["uri"].(string)
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "URI is required",
			},
		}
	}

	switch uri {
	case "ai-optimizer://model-profiles":
		return s.readModelProfiles(req.ID)
	case "ai-optimizer://routing-policies":
		return s.readRoutingPolicies(req.ID)
	case "ai-optimizer://optimization-history":
		return s.readOptimizationHistory(req.ID)
	default:
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32601,
				Message: "Resource not found",
			},
		}
	}
}

// Helper functions for resource reading
func (s *MCPServer) readModelProfiles(id interface{}) MCPResponse {
	profilesJSON, _ := json.MarshalIndent(modelProfiles, "", "  ")
	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"contents": []map[string]interface{}{
				{
					"uri":      "ai-optimizer://model-profiles",
					"mimeType": "application/json",
					"text":     string(profilesJSON),
				},
			},
		},
	}
}

func (s *MCPServer) readRoutingPolicies(id interface{}) MCPResponse {
	strategiesJSON, _ := json.MarshalIndent(routingStrategies, "", "  ")
	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"contents": []map[string]interface{}{
				{
					"uri":      "ai-optimizer://routing-policies",
					"mimeType": "application/json",
					"text":     string(strategiesJSON),
				},
			},
		},
	}
}

func (s *MCPServer) readOptimizationHistory(id interface{}) MCPResponse {
	// For now, return a placeholder
	history := map[string]interface{}{
		"message":   "Optimization history tracking not yet implemented",
		"timestamp": time.Now().UTC(),
	}
	historyJSON, _ := json.MarshalIndent(history, "", "  ")
	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"contents": []map[string]interface{}{
				{
					"uri":      "ai-optimizer://optimization-history",
					"mimeType": "application/json",
					"text":     string(historyJSON),
				},
			},
		},
	}
}

// Helper functions
func getString(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}

func formatCostAnalysis(analysis []map[string]interface{}) string {
	result := ""
	for _, item := range analysis {
		result += fmt.Sprintf("- %s: $%.6f (%.6f per token)\n",
			item["model_name"], item["estimated_cost"], item["cost_per_token"])
	}
	return result
}

func formatPerformanceMetrics(metrics []map[string]interface{}) string {
	result := ""
	for _, item := range metrics {
		result += fmt.Sprintf("- %s: %.2fms latency, Tier %v\n",
			item["model_name"], item["expected_latency"], item["tier"])
	}
	return result
}

func determineComplexity(prompt string) string {
	if len(prompt) < 100 {
		return "simple"
	} else if len(prompt) < 500 {
		return "medium"
	}
	return "complex"
}

func determineTaskType(prompt string) string {
	// Simple heuristics - in production, use more sophisticated analysis
	lowerPrompt := strings.ToLower(prompt)
	if lowerPrompt != prompt {
		return "text_generation"
	}
	return "general"
}

func determineDomain(prompt string) string {
	// Simple heuristics - in production, use more sophisticated analysis
	promptLower := strings.ToLower(prompt)

	// Check for technical domains
	if strings.Contains(promptLower, "code") || strings.Contains(promptLower, "programming") ||
		strings.Contains(promptLower, "function") || strings.Contains(promptLower, "algorithm") {
		return "technical"
	}

	// Check for creative domains
	if strings.Contains(promptLower, "story") || strings.Contains(promptLower, "creative") ||
		strings.Contains(promptLower, "write") || strings.Contains(promptLower, "poem") {
		return "creative"
	}

	// Check for analytical domains
	if strings.Contains(promptLower, "analyze") || strings.Contains(promptLower, "data") ||
		strings.Contains(promptLower, "research") || strings.Contains(promptLower, "study") {
		return "analytical"
	}

	return "general"
}

// Global MCP server instance
var mcpServer *MCPServer

// InitializeMCPServer initializes the MCP server
func InitializeMCPServer() {
	mcpServer = NewMCPServer()
	log.Printf("AI Optimizer MCP Server: Initialized successfully")
}
