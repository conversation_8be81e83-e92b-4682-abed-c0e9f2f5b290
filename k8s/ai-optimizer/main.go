package main

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"math"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	_ "github.com/ClickHouse/clickhouse-go/v2"
	"github.com/go-redis/redis/v8"
)

// ModelProfile defines the structure for an LLM model's profile
type ModelProfile struct {
	ID                 string    `json:"id"`
	Name               string    `json:"name"`
	Aliases            []string  `json:"aliases"`
	Capabilities       []string  `json:"capabilities"`
	PricingTier        string    `json:"pricing_tier"`
	DataSensitivity    string    `json:"data_sensitivity"`
	ExpectedLatencyMs  float64   `json:"expected_latency_ms"`
	ExpectedCost       float64   `json:"expected_cost"`
	BackendURL         string    `json:"url"` // Must be "url" for consistency with populate_redis.py
	BackendType        string    `json:"backend_type"`
	CostPerInputToken  float64   `json:"cost_per_input_token"`
	CostPerOutputToken float64   `json:"cost_per_output_token"`
	CPUCostPerHour     float64   `json:"cpu_cost_per_hour"`
	MemoryCostPerHour  float64   `json:"memory_cost_per_hour"`
	APIKey             string    `json:"api_key,omitempty"` // API Key for external models
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
	Tier               int       `json:"tier"` // MODEL_TIER_BASIC, MODEL_TIER_INTERMEDIATE, MODEL_TIER_ADVANCED

	// Enhanced real-time performance metrics
	RealTimeMetrics  *RealTimeMetrics `json:"real_time_metrics,omitempty"`
	QualityScore     float64          `json:"quality_score,omitempty"`     // 0.0-1.0 based on evaluation results
	UserSatisfaction float64          `json:"user_satisfaction,omitempty"` // 0.0-1.0 based on user feedback
	DynamicPricing   *DynamicPricing  `json:"dynamic_pricing,omitempty"`   // Real-time pricing adjustments
}

// RealTimeMetrics tracks live performance data for models
type RealTimeMetrics struct {
	AvgLatencyMs      float64   `json:"avg_latency_ms"`     // Rolling average latency (last 100 requests)
	SuccessRate       float64   `json:"success_rate"`       // Success rate (last 100 requests)
	RequestCount      int64     `json:"request_count"`      // Total requests in last hour
	ErrorRate         float64   `json:"error_rate"`         // Error rate (last 100 requests)
	LastUpdated       time.Time `json:"last_updated"`       // When metrics were last updated
	ThroughputRPM     float64   `json:"throughput_rpm"`     // Requests per minute
	P95LatencyMs      float64   `json:"p95_latency_ms"`     // 95th percentile latency
	AvailabilityScore float64   `json:"availability_score"` // 0.0-1.0 availability score
}

// DynamicPricing represents real-time pricing adjustments
type DynamicPricing struct {
	CurrentMultiplier   float64   `json:"current_multiplier"`    // Price multiplier (1.0 = base price)
	DemandLevel         string    `json:"demand_level"`          // "low", "medium", "high"
	LastPriceUpdate     time.Time `json:"last_price_update"`     // When pricing was last updated
	BaseInputCost       float64   `json:"base_input_cost"`       // Original input token cost
	BaseOutputCost      float64   `json:"base_output_cost"`      // Original output token cost
	EffectiveInputCost  float64   `json:"effective_input_cost"`  // Current effective input cost
	EffectiveOutputCost float64   `json:"effective_output_cost"` // Current effective output cost
}

// UserPreference tracks user-specific routing preferences
type UserPreference struct {
	UserID           string             `json:"user_id"`
	PreferredModels  []string           `json:"preferred_models"` // User's preferred model IDs
	AvoidedModels    []string           `json:"avoided_models"`   // Models user wants to avoid
	PriorityWeights  map[string]float64 `json:"priority_weights"` // "cost", "quality", "speed", "reliability"
	FeedbackHistory  []UserFeedback     `json:"feedback_history"` // Historical feedback
	LastUpdated      time.Time          `json:"last_updated"`
	AdaptiveLearning bool               `json:"adaptive_learning"` // Enable ML-based preference learning
}

// UserFeedback represents user feedback on model performance
type UserFeedback struct {
	RequestID    string    `json:"request_id"`
	ModelID      string    `json:"model_id"`
	TaskType     string    `json:"task_type"`
	Rating       float64   `json:"rating"`        // 1.0-5.0 user rating
	FeedbackType string    `json:"feedback_type"` // "quality", "speed", "cost", "overall"
	Comments     string    `json:"comments,omitempty"`
	Timestamp    time.Time `json:"timestamp"`
	Helpful      bool      `json:"helpful"` // Whether user found response helpful
}

// Constants for routing strategies
const (
	ROUTING_STRATEGY_DEFAULT  = "default"
	ROUTING_STRATEGY_CASCADE  = "cascade"
	ROUTING_STRATEGY_PARALLEL = "parallel"
	ROUTING_STRATEGY_HYBRID   = "hybrid"
)

// RoutingStrategy defines how requests should be routed
type RoutingStrategy struct {
	ID                string                     `json:"id"`
	Name              string                     `json:"name"`
	Strategy          string                     `json:"strategy"` // ROUTING_STRATEGY_* constants
	ModelRequirements ModelCapabilityRequirement `json:"model_requirements,omitempty"`
	ModelPriorities   []string                   `json:"model_priorities,omitempty"`  // For cascade routing
	ParallelModels    []string                   `json:"parallel_models,omitempty"`   // For parallel routing
	ComparisonMethod  string                     `json:"comparison_method,omitempty"` // For parallel routing
	EnableFallback    bool                       `json:"enable_fallback"`             // Whether to use fallback routing
	FallbackModelID   string                     `json:"fallback_model_id,omitempty"` // Specific fallback model ID
	PolicyID          string                     `json:"policy_id,omitempty"`
	Priority          int                        `json:"priority"`
	TaskType          string                     `json:"task_type,omitempty"`
}

// ModelCapabilityRequirement defines what capabilities are needed for a specific task
type ModelCapabilityRequirement struct {
	RequiredCapabilities []string `json:"required_capabilities"`
	MinimumTier          int      `json:"minimum_tier"`
	PreferredProvider    string   `json:"preferred_provider,omitempty"`
}

// RouterDecision captures the routing decision process for logging and analysis
type RouterDecision struct {
	RequestID            string        `json:"request_id"`
	StrategyUsed         string        `json:"strategy_used"`
	AttemptedModels      []string      `json:"attempted_models,omitempty"`
	SelectedModelID      string        `json:"selected_model_id"`
	FallbackUsed         bool          `json:"fallback_used"`
	DecisionTime         time.Time     `json:"decision_time"`
	DecisionRationale    string        `json:"decision_rationale"`
	TaskType             string        `json:"task_type"`
	ExecutionTime        time.Duration `json:"execution_time,omitempty"`
	CircuitBreakerEvents []string      `json:"circuit_breaker_events,omitempty"`
	PolicyApplied        string        `json:"policy_applied,omitempty"`
	ModelTier            int           `json:"model_tier,omitempty"`
}

// PromptAnalysis contains intelligent analysis of the user's prompt (from proxy-gateway)
type PromptAnalysis struct {
	Intent       string            `json:"intent"`        // question, instruction, creative, analysis, etc.
	TaskType     string            `json:"task_type"`     // code_generation, creative_writing, factual_qa, etc.
	Complexity   string            `json:"complexity"`    // low, medium, high
	Domain       string            `json:"domain"`        // programming, creative, business, academic, etc.
	Keywords     []string          `json:"keywords"`      // extracted key terms
	Confidence   float64           `json:"confidence"`    // 0-1 confidence in analysis
	RequiredCaps []string          `json:"required_caps"` // required model capabilities
	Metadata     map[string]string `json:"metadata"`      // additional context
}

// TokenBudget defines limits on token usage for optimization
type TokenBudget struct {
	MaxInputTokens  int `json:"max_input_tokens"`
	MaxOutputTokens int `json:"max_output_tokens"`
	Priority        int `json:"priority"` // 1-10, with 10 being highest priority
}

// OptimizationRequest is the request body received from the proxy-gateway
type OptimizationRequest struct {
	Prompt          string          `json:"prompt"`
	Model           string          `json:"model"` // The model requested by the client (e.g., "gpt-3.5-turbo")
	UserID          string          `json:"user_id,omitempty"`
	UserRoles       []string        `json:"user_roles,omitempty"`
	DataSensitivity string          `json:"data_sensitivity,omitempty"`
	PreferredLLMID  string          `json:"preferred_llm_id,omitempty"` // NEW: For X-Preferred-LLM-ID header
	TaskType        string          `json:"task_type,omitempty"`
	APIType         string          `json:"api_type,omitempty"`     // NEW: API type (chat_completions, embeddings, image_generation, etc.)
	Analysis        *PromptAnalysis `json:"analysis,omitempty"`     // NEW: Intelligent prompt analysis
	TokenBudget     *TokenBudget    `json:"token_budget,omitempty"` // NEW: Token budget constraints for cost optimization
}

// AIOptimizerRouteResponse is the response body sent to the proxy-gateway
type AIOptimizerRouteResponse struct {
	SelectedBackendID  string `json:"selected_backend_id"`
	BackendType        string `json:"backend_type"`
	BackendURL         string `json:"backend_url"`
	APIKey             string `json:"api_key,omitempty"` // API Key for the selected backend
	PolicyIDApplied    string `json:"policy_id_applied"`
	ModelUsed          string `json:"model_used"`
	TaskType           string `json:"task_type"`
	Error              string `json:"error,omitempty"`
	FromCache          bool   `json:"from_from_cache,omitempty"`      // If AI Optimizer served from its cache
	CachedResponseBody string `json:"cached_response_body,omitempty"` // Cached body if FromCache is true
}

var (
	redisClient       *redis.Client
	modelProfiles     map[string]ModelProfile
	routingStrategies []RoutingStrategy

	// Enhanced tracking for intelligent routing
	userPreferences  map[string]*UserPreference  // UserID -> UserPreference
	performanceCache map[string]*RealTimeMetrics // ModelID -> RealTimeMetrics
	preferencesLock  sync.RWMutex
	metricsLock      sync.RWMutex
)

func main() {
	// Initialize Redis client
	redisClient = redis.NewClient(&redis.Options{
		Addr: "redis:6379", // TODO: Make this configurable
		DB:   0,            // Default DB
	})

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := redisClient.Ping(ctx).Result()
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	log.Println("Successfully connected to Redis.")

	// Initialize model profiles
	modelProfiles = make(map[string]ModelProfile)

	// Load initial model profiles from Redis
	if err := loadModelProfilesFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load model profiles from Redis on startup: %v", err)

		// If we're in standard edition and no profiles exist, create default ones
		edition := os.Getenv("EDITION")
		if edition == "standard" && len(modelProfiles) == 0 {
			log.Println("Standard edition detected with no model profiles. Creating default profiles...")
			createStandardEditionModelProfiles(ctx)
		}
	}

	// Ensure we have model profiles for intelligent routing
	if len(modelProfiles) == 0 {
		log.Println("No model profiles available. Creating fallback profiles...")
		createFallbackModelProfiles(ctx)
	}

	// Load routing strategies from Redis
	if err := loadRoutingStrategiesFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load routing strategies from Redis on startup: %v", err)
		routingStrategies = []RoutingStrategy{}
	}

	// Initialize enhanced tracking systems
	userPreferences = make(map[string]*UserPreference)
	performanceCache = make(map[string]*RealTimeMetrics)

	// Start background processes for enhanced intelligence
	go startPerformanceMetricsUpdater()
	go startDynamicPricingUpdater()
	go startUserPreferenceLearning()

	log.Println("Enhanced AI routing intelligence initialized")

	http.HandleFunc("/route", handleRouteRequest)
	http.HandleFunc("/generate", handleGenerateRequest)
	http.HandleFunc("/health", handleHealthCheck)

	// Enhanced intelligence endpoints
	http.HandleFunc("/feedback", handleUserFeedback)
	http.HandleFunc("/preferences", handleUserPreferences)
	http.HandleFunc("/metrics", handleRealTimeMetrics)
	http.HandleFunc("/model-capabilities", handleModelCapabilities)

	// Initialize MCP Server
	InitializeMCPServer()
	http.HandleFunc("/mcp", mcpServer.HandleMCPConnection)

	log.Println("AI Optimizer starting on port 8085 with MCP support...")
	log.Fatal(http.ListenAndServe(":8085", nil))
}

// filterModelProfilesByAPIType filters model profiles based on their API type capabilities
func filterModelProfilesByAPIType(profiles map[string]ModelProfile, apiType string) map[string]ModelProfile {
	filtered := make(map[string]ModelProfile)

	for id, profile := range profiles {
		// Check if the model supports the requested API type
		if supportsAPIType(profile, apiType) {
			filtered[id] = profile
		}
	}

	return filtered
}

// supportsAPIType checks if a model profile supports a specific API type
func supportsAPIType(profile ModelProfile, apiType string) bool {
	// Check capabilities array for API type support
	for _, capability := range profile.Capabilities {
		switch apiType {
		case "chat_completions":
			if capability == "chat" || capability == "text_generation" || capability == "conversation" {
				return true
			}
		case "embeddings":
			if capability == "embeddings" || capability == "text_embedding" {
				return true
			}
		case "image_generation":
			if capability == "image_generation" || capability == "image" || capability == "dalle" {
				return true
			}
		case "audio_speech":
			if capability == "audio_generation" || capability == "tts" || capability == "speech" {
				return true
			}
		case "audio_transcription":
			if capability == "audio_transcription" || capability == "stt" || capability == "whisper" {
				return true
			}
		case "moderation":
			if capability == "moderation" || capability == "content_filtering" {
				return true
			}
		}
	}

	// Default: if no specific API type is specified or no capabilities are defined,
	// assume the model supports basic text generation (chat_completions)
	if apiType == "chat_completions" || apiType == "" {
		return true
	}

	return false
}

func handleRouteRequest(w http.ResponseWriter, r *http.Request) {
	log.Printf("=== AI OPTIMIZER ROUTE REQUEST RECEIVED ===")
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var optReq OptimizationRequest
	if err := json.NewDecoder(r.Body).Decode(&optReq); err != nil {
		log.Printf("Error decoding request: %v", err)
		http.Error(w, "Error decoding request", http.StatusBadRequest)
		return
	}
	log.Printf("Decoded request - Prompt: %.50s..., Model: %s", optReq.Prompt, optReq.Model)

	// Apply intelligent routing with prompt analysis and API type consideration
	var routeResp AIOptimizerRouteResponse

	// Log the API type and analysis for debugging
	if optReq.APIType != "" {
		log.Printf("Routing request for API type: %s", optReq.APIType)
	}
	if optReq.Analysis != nil {
		log.Printf("Prompt analysis - Intent: %s, TaskType: %s, Complexity: %s, Domain: %s, Confidence: %.2f",
			optReq.Analysis.Intent, optReq.Analysis.TaskType, optReq.Analysis.Complexity,
			optReq.Analysis.Domain, optReq.Analysis.Confidence)
	}
	if optReq.TokenBudget != nil {
		log.Printf("Token budget constraints - MaxInput: %d, MaxOutput: %d, Priority: %d",
			optReq.TokenBudget.MaxInputTokens, optReq.TokenBudget.MaxOutputTokens, optReq.TokenBudget.Priority)
	}

	// First, check if the user's requested model exists and is available
	if optReq.Model != "" {
		if profile, exists := modelProfiles[optReq.Model]; exists {
			log.Printf("Using user's requested model: %s", optReq.Model)
			log.Printf("Model profile - BackendType: %s, BackendURL: %s, APIKey: %s",
				profile.BackendType, profile.BackendURL,
				func() string {
					if profile.APIKey != "" {
						return "[REDACTED]"
					} else {
						return "[EMPTY]"
					}
				}())
			routeResp = AIOptimizerRouteResponse{
				SelectedBackendID: profile.ID,
				BackendType:       profile.BackendType,
				BackendURL:        profile.BackendURL,
				APIKey:            profile.APIKey,
				ModelUsed:         profile.ID,
				TaskType:          optReq.TaskType,
			}
		} else {
			log.Printf("User's requested model '%s' not found, proceeding with intelligent routing", optReq.Model)
			log.Printf("Available models: %v", func() []string {
				var models []string
				for id := range modelProfiles {
					models = append(models, id)
				}
				return models
			}())
		}
	}

	// If user's model wasn't available, try intelligent capability-based routing if we have prompt analysis
	if routeResp.SelectedBackendID == "" && optReq.Analysis != nil && optReq.Analysis.Confidence > 0.7 {
		log.Printf("Attempting intelligent capability-based routing")
		if capabilityRouteResp := applyCapabilityBasedRouting(optReq, modelProfiles); capabilityRouteResp.SelectedBackendID != "" {
			routeResp = capabilityRouteResp
			log.Printf("Successfully routed using capability-based routing to model: %s", routeResp.SelectedBackendID)
		}
	}

	// If capability-based routing didn't work, fall back to traditional strategies
	if routeResp.SelectedBackendID == "" {
		if len(routingStrategies) == 0 {
			routeResp = AIOptimizerRouteResponse{
				Error: "No routing strategies found",
			}
		} else {
			log.Printf("Falling back to traditional routing strategies")
			// Find the first matching strategy based on TaskType, APIType, and Model
		strategyLoop:
			for _, strategy := range routingStrategies {
				if strategy.TaskType != "" && strategy.TaskType != optReq.TaskType {
					continue // Skip if TaskType doesn't match
				}

				// Add API type-specific routing logic
				if optReq.APIType != "" {
					// Filter model profiles based on API type capabilities
					filteredProfiles := filterModelProfilesByAPIType(modelProfiles, optReq.APIType)
					if len(filteredProfiles) == 0 {
						log.Printf("No models found supporting API type: %s", optReq.APIType)
						continue
					}
					// Use filtered profiles for this strategy
					modelProfiles = filteredProfiles
				}

				switch strategy.Strategy {
				case ROUTING_STRATEGY_DEFAULT:
					routeResp = applyDefaultStrategy(strategy, modelProfiles, optReq)
					if routeResp.SelectedBackendID != "" || routeResp.Error != "" {
						break strategyLoop // Strategy found or error occurred, exit loop
					}
				case ROUTING_STRATEGY_CASCADE:
					routeResp = applyCascadeStrategy(strategy, modelProfiles, optReq)
					if routeResp.SelectedBackendID != "" || routeResp.Error != "" {
						break strategyLoop // Strategy found or error occurred, exit loop
					}
				case ROUTING_STRATEGY_PARALLEL:
					routeResp = applyParallelStrategy(strategy, modelProfiles, optReq)
					if routeResp.SelectedBackendID != "" || routeResp.Error != "" {
						break strategyLoop // Strategy found or error occurred, exit loop
					}
				default:
					log.Printf("Strategy not supported: %s", strategy.Strategy) // Log unsupported strategies
					continue                                                    // Skip to the next strategy
				}

				if routeResp.SelectedBackendID != "" || routeResp.Error != "" {
					break // Strategy found or error occurred, exit loop
				}
			}
		}

		// If no strategy was applied, return an error
		if routeResp.SelectedBackendID == "" && routeResp.Error == "" {
			routeResp = AIOptimizerRouteResponse{
				Error: "No suitable routing strategy found",
			}
		}
	} // Close the else block for traditional routing strategies

	w.Header().Set("Content-Type", "application/json")

	// Debug: Log the response being sent
	respJSON, _ := json.Marshal(routeResp)
	log.Printf("Sending route response: %s", string(respJSON))

	if err := json.NewEncoder(w).Encode(routeResp); err != nil {
		log.Printf("Error encoding response: %v", err)
		http.Error(w, "Error encoding response", http.StatusInternalServerError)
		return
	}
	log.Printf("Successfully routed request - HTTP 200 response sent")
	log.Printf("=== AI OPTIMIZER ROUTE REQUEST COMPLETED ===")

	// Flush the response to ensure it's sent immediately
	if flusher, ok := w.(http.Flusher); ok {
		flusher.Flush()
	}
}

func applyDefaultStrategy(strategy RoutingStrategy, modelProfiles map[string]ModelProfile, optReq OptimizationRequest) AIOptimizerRouteResponse {
	var routeResp AIOptimizerRouteResponse

	// Determine TaskType from prompt analysis or fallback to strategy TaskType
	taskType := strategy.TaskType
	if optReq.Analysis != nil && optReq.Analysis.TaskType != "" {
		taskType = optReq.Analysis.TaskType
	}

	if len(strategy.ModelPriorities) > 0 {
		selectedModelID := strategy.ModelPriorities[0]
		if profile, ok := modelProfiles[selectedModelID]; ok {
			// Check if the model meets the minimum tier requirement
			if profile.Tier >= strategy.ModelRequirements.MinimumTier {
				log.Printf("Applying default strategy: routing to model %s (Tier: %d)", profile.ID, profile.Tier)
				log.Printf("Model profile BackendURL: %s", profile.BackendURL)
				routeResp = AIOptimizerRouteResponse{
					SelectedBackendID: profile.ID,
					BackendType:       profile.BackendType,
					BackendURL:        profile.BackendURL,
					APIKey:            profile.APIKey,
					PolicyIDApplied:   strategy.PolicyID,
					ModelUsed:         profile.ID,
					TaskType:          taskType,
				}
				log.Printf("Created route response with BackendURL: %s", routeResp.BackendURL)
			} else {
				routeResp.Error = fmt.Sprintf("Model %s does not meet minimum tier requirement (Required: %d, Actual: %d)", selectedModelID, strategy.ModelRequirements.MinimumTier, profile.Tier)
				log.Println(routeResp.Error)
			}
		} else {
			routeResp.Error = fmt.Sprintf("Model profile not found: %s", selectedModelID)
			log.Println(routeResp.Error)
		}
	} else {
		routeResp.Error = "No model priorities defined for default strategy"
		log.Println(routeResp.Error)
	}
	return routeResp
}

func applyCascadeStrategy(strategy RoutingStrategy, modelProfiles map[string]ModelProfile, optReq OptimizationRequest) AIOptimizerRouteResponse {
	var routeResp AIOptimizerRouteResponse

	// Determine TaskType from prompt analysis or fallback to strategy TaskType
	taskType := strategy.TaskType
	if optReq.Analysis != nil && optReq.Analysis.TaskType != "" {
		taskType = optReq.Analysis.TaskType
	}

	if len(strategy.ModelPriorities) > 0 {
		for _, modelID := range strategy.ModelPriorities {
			if profile, ok := modelProfiles[modelID]; ok {
				// Check if the model meets the minimum tier requirement
				if profile.Tier >= strategy.ModelRequirements.MinimumTier {
					log.Printf("Applying cascade strategy: attempting model %s (Tier: %d)", profile.ID, profile.Tier)
					routeResp = AIOptimizerRouteResponse{
						SelectedBackendID: profile.ID,
						BackendType:       profile.BackendType,
						BackendURL:        profile.BackendURL,
						APIKey:            profile.APIKey,
						PolicyIDApplied:   strategy.PolicyID,
						ModelUsed:         profile.ID,
						TaskType:          taskType,
					}
					break
				} else {
					log.Printf("Model %s does not meet minimum tier requirement (Required: %d, Actual: %d)", modelID, strategy.ModelRequirements.MinimumTier, profile.Tier)
					continue
				}
			} else {
				log.Printf("Model profile not found: %s", modelID)
				continue
			}
		}
		if routeResp.SelectedBackendID == "" {
			routeResp.Error = "No suitable model found in cascade strategy"
			log.Println(routeResp.Error)
		}
	} else {
		routeResp.Error = "No model priorities defined for cascade strategy"
		log.Println(routeResp.Error)
	}
	return routeResp
}

func applyParallelStrategy(strategy RoutingStrategy, modelProfiles map[string]ModelProfile, optReq OptimizationRequest) AIOptimizerRouteResponse {
	var routeResp AIOptimizerRouteResponse

	// Determine TaskType from prompt analysis or fallback to strategy TaskType
	taskType := strategy.TaskType
	if optReq.Analysis != nil && optReq.Analysis.TaskType != "" {
		taskType = optReq.Analysis.TaskType
	}

	if len(strategy.ParallelModels) > 0 {
		var wg sync.WaitGroup
		responseChan := make(chan struct {
			ModelID  string
			Response string
			Error    error
		}, len(strategy.ParallelModels))

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		for _, modelID := range strategy.ParallelModels {
			wg.Add(1)
			go func(modelID string) {
				defer wg.Done()
				if profile, ok := modelProfiles[modelID]; ok {
					// Check if the model meets the minimum tier requirement
					if profile.Tier >= strategy.ModelRequirements.MinimumTier {
						log.Printf("Applying parallel strategy: sending request to model %s (Tier: %d)", profile.ID, profile.Tier)
						// TODO: Implement the actual request to the backend
						// For now, simulate a response
						time.Sleep(time.Duration(profile.ExpectedLatencyMs) * time.Millisecond)
						// Simulate an error
						var err error
						if time.Now().Unix()%3 == 0 {
							err = fmt.Errorf("simulated error for model %s", modelID)
						}
						select {
						case responseChan <- struct {
							ModelID  string
							Response string
							Error    error
						}{ModelID: modelID, Response: fmt.Sprintf("Response from %s", modelID), Error: err}:
						case <-ctx.Done():
							log.Printf("Request to %s cancelled due to timeout", modelID)
							return
						}
					} else {
						log.Printf("Model %s does not meet minimum tier requirement (Required: %d, Actual: %d)", modelID, strategy.ModelRequirements.MinimumTier, profile.Tier)
						select {
						case responseChan <- struct {
							ModelID  string
							Response string
							Error    error
						}{ModelID: modelID, Response: "", Error: fmt.Errorf("model %s does not meet minimum tier requirement (Required: %d, Actual: %d)", modelID, strategy.ModelRequirements.MinimumTier, profile.Tier)}:
						case <-ctx.Done():
							log.Printf("Request to %s cancelled due to timeout", modelID)
							return
						}
					}
				} else {
					log.Printf("Model profile not found: %s", modelID)
					select {
					case responseChan <- struct {
						ModelID  string
						Response string
						Error    error
					}{ModelID: modelID, Response: "", Error: fmt.Errorf("model profile not found: %s", modelID)}:
					case <-ctx.Done():
						log.Printf("Request to %s cancelled due to timeout", modelID)
						return
					}
				}
			}(modelID)
		}

		wg.Wait()
		close(responseChan)

		responses := make([]struct {
			ModelID  string
			Response string
			Error    error
		}, 0)
		for resp := range responseChan {
			responses = append(responses, resp)
		}

		bestResponse := compareResponses(responses)
		if bestResponse.Error == nil {
			// Access ModelID from bestResponse, not profile
			if bestResponse.ModelID != "" {
				if profile, ok := modelProfiles[bestResponse.ModelID]; ok {
					log.Printf("Parallel strategy: selecting model %s (Tier: %d)", profile.ID, profile.Tier)
					routeResp = AIOptimizerRouteResponse{
						SelectedBackendID: profile.ID,
						BackendType:       profile.BackendType,
						BackendURL:        profile.BackendURL,
						APIKey:            profile.APIKey,
						PolicyIDApplied:   strategy.PolicyID,
						ModelUsed:         profile.ID,
						TaskType:          taskType,
					}
				} else {
					routeResp.Error = fmt.Sprintf("Model profile not found: %s", bestResponse.ModelID)
					log.Println(routeResp.Error)
				}

			} else {
				routeResp.Error = "bestResponse.ModelID is empty"
				log.Println(routeResp.Error)
			}

		} else {
			routeResp.Error = bestResponse.Error.Error()
			log.Println(routeResp.Error)
		}

	} else {
		routeResp.Error = "No parallel models defined for parallel strategy"
		log.Println(routeResp.Error)
	}
	return routeResp
}

// GetModelProfileByID safely retrieves a model profile from the cache.
func GetModelProfileByID(id string) (ModelProfile, bool) {
	mp, ok := modelProfiles[id]
	return mp, ok
}

// compareResponses compares the responses from different models based on the specified method.
func compareResponses(responses []struct {
	ModelID  string
	Response string
	Error    error
}) struct {
	ModelID  string
	Response string
	Error    error
} {
	// Implement different comparison methods (e.g., latency, cost, quality)
	// For now, select the first successful response
	for _, resp := range responses {
		if resp.Error == nil {
			return resp
		}
	}
	// If all responses have errors, return the first error
	if len(responses) > 0 {
		return responses[0]
	}
	// If no responses, return an error
	return struct {
		ModelID  string
		Response string
		Error    error
	}{Error: fmt.Errorf("no responses received from parallel models")}
}

// applyCapabilityBasedRouting performs intelligent routing based on prompt analysis and model capabilities
func applyCapabilityBasedRouting(optReq OptimizationRequest, modelProfiles map[string]ModelProfile) AIOptimizerRouteResponse {
	var routeResp AIOptimizerRouteResponse

	if optReq.Analysis == nil {
		return routeResp // No analysis available
	}

	analysis := optReq.Analysis
	log.Printf("Capability-based routing for task: %s, complexity: %s, domain: %s",
		analysis.TaskType, analysis.Complexity, analysis.Domain)

	// Score each model based on capability matching
	type ModelScore struct {
		ModelID string
		Score   float64
		Profile ModelProfile
	}

	var modelScores []ModelScore

	for modelID, profile := range modelProfiles {
		score := calculateCapabilityScore(analysis, profile, optReq.APIType)

		// Apply token budget constraints if provided
		if optReq.TokenBudget != nil {
			score = applyTokenBudgetAdjustments(score, profile, optReq.TokenBudget)
		}

		// Apply user preference adjustments if user ID is available
		if optReq.UserID != "" {
			score = applyUserPreferenceAdjustments(score, modelID, optReq.UserID, analysis)
		}

		if score > 0 {
			modelScores = append(modelScores, ModelScore{
				ModelID: modelID,
				Score:   score,
				Profile: profile,
			})
		}
	}

	if len(modelScores) == 0 {
		log.Printf("No models found with suitable capabilities for task: %s", analysis.TaskType)
		return routeResp
	}

	// Sort by score (highest first)
	for i := 0; i < len(modelScores)-1; i++ {
		for j := i + 1; j < len(modelScores); j++ {
			if modelScores[i].Score < modelScores[j].Score {
				modelScores[i], modelScores[j] = modelScores[j], modelScores[i]
			}
		}
	}

	// Select the best model
	bestModel := modelScores[0]
	log.Printf("Selected model %s with capability score: %.3f for task: %s",
		bestModel.ModelID, bestModel.Score, analysis.TaskType)

	routeResp = AIOptimizerRouteResponse{
		SelectedBackendID: bestModel.Profile.ID,
		BackendType:       bestModel.Profile.BackendType,
		BackendURL:        bestModel.Profile.BackendURL,
		APIKey:            bestModel.Profile.APIKey,
		PolicyIDApplied:   "capability-based-routing",
		ModelUsed:         bestModel.Profile.ID,
		TaskType:          analysis.TaskType,
	}

	return routeResp
}

// calculateCapabilityScore calculates how well a model matches the prompt analysis requirements
// Enhanced with real-time metrics, user preferences, quality scoring, and dynamic pricing
func calculateCapabilityScore(analysis *PromptAnalysis, profile ModelProfile, apiType string) float64 {
	var score float64

	// Base score for API type compatibility
	if !supportsAPIType(profile, apiType) {
		return 0 // Model doesn't support the required API type
	}
	score += 0.15 // Base score for API compatibility (reduced to make room for new factors)

	// Task type specific scoring
	taskTypeScore := getTaskTypeScore(analysis.TaskType, profile)
	score += taskTypeScore * 0.25 // 25% weight for task type matching (reduced)

	// Complexity-based scoring
	complexityScore := getComplexityScore(analysis.Complexity, profile)
	score += complexityScore * 0.15 // 15% weight for complexity handling (reduced)

	// Domain-specific scoring
	domainScore := getDomainScore(analysis.Domain, profile)
	score += domainScore * 0.08 // 8% weight for domain expertise (reduced)

	// Enhanced real-time performance scoring
	performanceScore := getRealTimePerformanceScore(profile)
	score += performanceScore * 0.15 // 15% weight for real-time performance

	// Quality scoring based on evaluation results
	qualityScore := getQualityScore(profile)
	score += qualityScore * 0.12 // 12% weight for quality

	// Dynamic cost efficiency with real-time pricing
	costScore := getDynamicCostEfficiencyScore(analysis.Complexity, profile)
	score += costScore * 0.10 // 10% weight for dynamic cost efficiency

	log.Printf("Enhanced Model %s scores - Task: %.2f, Complexity: %.2f, Domain: %.2f, Performance: %.2f, Quality: %.2f, Cost: %.2f, Total: %.3f",
		profile.ID, taskTypeScore, complexityScore, domainScore, performanceScore, qualityScore, costScore, score)

	return score
}

// getTaskTypeScore returns a score based on how well the model handles the specific task type
func getTaskTypeScore(taskType string, profile ModelProfile) float64 {
	// Define model capabilities for different task types
	modelCapabilities := map[string]map[string]float64{
		"gpt-4": {
			"code_generation":  0.95,
			"reasoning":        0.95,
			"creative_writing": 0.90,
			"factual_qa":       0.85,
			"analysis":         0.92,
			"summarization":    0.88,
			"translation":      0.80,
			"mathematical":     0.90,
			"conversational":   0.85,
			"other":            0.80,
		},
		"gpt-3.5-turbo": {
			"code_generation":  0.80,
			"reasoning":        0.75,
			"creative_writing": 0.85,
			"factual_qa":       0.80,
			"analysis":         0.70,
			"summarization":    0.85,
			"translation":      0.75,
			"mathematical":     0.70,
			"conversational":   0.90,
			"other":            0.75,
		},
		"claude-3-sonnet": {
			"code_generation":  0.88,
			"reasoning":        0.90,
			"creative_writing": 0.92,
			"factual_qa":       0.88,
			"analysis":         0.90,
			"summarization":    0.90,
			"translation":      0.85,
			"mathematical":     0.85,
			"conversational":   0.88,
			"other":            0.85,
		},
		"gemini-pro": {
			"code_generation":  0.85,
			"reasoning":        0.88,
			"creative_writing": 0.80,
			"factual_qa":       0.90,
			"analysis":         0.85,
			"summarization":    0.82,
			"translation":      0.88,
			"mathematical":     0.88,
			"conversational":   0.82,
			"multimodal":       0.95,
			"other":            0.80,
		},
		// Cohere models
		"command-r-plus": {
			"code_generation":  0.75,
			"reasoning":        0.85,
			"creative_writing": 0.80,
			"factual_qa":       0.92,
			"analysis":         0.88,
			"summarization":    0.90,
			"translation":      0.90,
			"mathematical":     0.75,
			"conversational":   0.85,
			"rag":              0.95,
			"other":            0.80,
		},
		"command-r": {
			"code_generation":  0.70,
			"reasoning":        0.80,
			"creative_writing": 0.75,
			"factual_qa":       0.88,
			"analysis":         0.82,
			"summarization":    0.85,
			"translation":      0.88,
			"mathematical":     0.70,
			"conversational":   0.80,
			"rag":              0.90,
			"other":            0.75,
		},
		// Hugging Face models
		"meta-llama-3.1-70b-instruct": {
			"code_generation":  0.88,
			"reasoning":        0.85,
			"creative_writing": 0.82,
			"factual_qa":       0.80,
			"analysis":         0.83,
			"summarization":    0.80,
			"translation":      0.75,
			"mathematical":     0.82,
			"conversational":   0.80,
			"other":            0.78,
		},
		"mistralai-mixtral-8x7b-instruct": {
			"code_generation":  0.82,
			"reasoning":        0.80,
			"creative_writing": 0.78,
			"factual_qa":       0.75,
			"analysis":         0.78,
			"summarization":    0.80,
			"translation":      0.85,
			"mathematical":     0.78,
			"conversational":   0.75,
			"other":            0.75,
		},
		// Mistral AI models
		"mistral-large-latest": {
			"code_generation":  0.88,
			"reasoning":        0.90,
			"creative_writing": 0.80,
			"factual_qa":       0.82,
			"analysis":         0.85,
			"summarization":    0.80,
			"translation":      0.82,
			"mathematical":     0.85,
			"conversational":   0.78,
			"function_calling": 0.90,
			"other":            0.80,
		},
		"mistral-medium-latest": {
			"code_generation":  0.82,
			"reasoning":        0.80,
			"creative_writing": 0.75,
			"factual_qa":       0.78,
			"analysis":         0.80,
			"summarization":    0.78,
			"translation":      0.80,
			"mathematical":     0.78,
			"conversational":   0.75,
			"function_calling": 0.82,
			"other":            0.75,
		},
		// GROK (xAI) models
		"grok-1": {
			"code_generation":  0.82,
			"reasoning":        0.88,
			"creative_writing": 0.85,
			"factual_qa":       0.90,
			"analysis":         0.85,
			"summarization":    0.80,
			"translation":      0.75,
			"mathematical":     0.85,
			"conversational":   0.88,
			"real_time_data":   0.95,
			"other":            0.80,
		},
		"grok-1.5-vision": {
			"code_generation":  0.85,
			"reasoning":        0.90,
			"creative_writing": 0.85,
			"factual_qa":       0.92,
			"analysis":         0.88,
			"summarization":    0.82,
			"translation":      0.78,
			"mathematical":     0.88,
			"conversational":   0.90,
			"multimodal":       0.90,
			"real_time_data":   0.95,
			"other":            0.82,
		},
	}

	if capabilities, exists := modelCapabilities[profile.ID]; exists {
		if score, exists := capabilities[taskType]; exists {
			return score
		}
		// Return default score for unknown task types
		if score, exists := capabilities["other"]; exists {
			return score
		}
	}

	// Default moderate score for unknown models
	return 0.6
}

// getComplexityScore returns a score based on how well the model handles the complexity level
func getComplexityScore(complexity string, profile ModelProfile) float64 {
	switch complexity {
	case "low":
		// For low complexity, prefer faster/cheaper models
		if profile.ExpectedLatencyMs < 1000 {
			return 1.0
		} else if profile.ExpectedLatencyMs < 2000 {
			return 0.8
		}
		return 0.6
	case "medium":
		// For medium complexity, balanced approach
		return 0.8
	case "high":
		// For high complexity, prefer more capable models (higher tier)
		if profile.Tier >= 3 { // Advanced models
			return 1.0
		} else if profile.Tier >= 2 { // Intermediate models
			return 0.7
		}
		return 0.4 // Basic models may struggle with high complexity
	default:
		return 0.7 // Default moderate score
	}
}

// getDomainScore returns a score based on domain-specific model strengths
func getDomainScore(domain string, profile ModelProfile) float64 {
	// Define domain preferences for different models
	domainPreferences := map[string]map[string]float64{
		"gpt-4": {
			"programming": 0.95,
			"academic":    0.90,
			"business":    0.85,
			"technical":   0.90,
			"creative":    0.85,
			"science":     0.88,
			"general":     0.80,
		},
		"gpt-3.5-turbo": {
			"programming": 0.80,
			"academic":    0.75,
			"business":    0.85,
			"technical":   0.75,
			"creative":    0.80,
			"science":     0.70,
			"general":     0.85,
		},
		"claude-3-sonnet": {
			"programming": 0.85,
			"academic":    0.95,
			"business":    0.80,
			"technical":   0.85,
			"creative":    0.95,
			"science":     0.85,
			"general":     0.85,
		},
		"gemini-pro": {
			"programming": 0.85,
			"academic":    0.85,
			"business":    0.80,
			"technical":   0.90,
			"creative":    0.75,
			"science":     0.90,
			"general":     0.80,
		},
	}

	if preferences, exists := domainPreferences[profile.ID]; exists {
		if score, exists := preferences[domain]; exists {
			return score
		}
		// Return general score if specific domain not found
		if score, exists := preferences["general"]; exists {
			return score
		}
	}

	// Default moderate score for unknown models/domains
	return 0.7
}

// getRealTimePerformanceScore calculates performance score based on real-time metrics
func getRealTimePerformanceScore(profile ModelProfile) float64 {
	metricsLock.RLock()
	defer metricsLock.RUnlock()

	metrics := profile.RealTimeMetrics
	if metrics == nil {
		// Fallback to cached metrics
		if cachedMetrics, exists := performanceCache[profile.ID]; exists {
			metrics = cachedMetrics
		} else {
			return 0.7 // Default score if no metrics available
		}
	}

	// Calculate composite performance score
	var score float64

	// Success rate (40% weight)
	score += metrics.SuccessRate * 0.4

	// Latency performance (30% weight) - lower latency is better
	latencyScore := 1.0
	if profile.ExpectedLatencyMs > 0 {
		latencyRatio := metrics.AvgLatencyMs / profile.ExpectedLatencyMs
		if latencyRatio <= 1.0 {
			latencyScore = 1.0 // Better than expected
		} else if latencyRatio <= 1.5 {
			latencyScore = 0.8 // Acceptable
		} else if latencyRatio <= 2.0 {
			latencyScore = 0.5 // Poor
		} else {
			latencyScore = 0.2 // Very poor
		}
	}
	score += latencyScore * 0.3

	// Availability score (20% weight)
	score += metrics.AvailabilityScore * 0.2

	// Error rate penalty (10% weight) - lower error rate is better
	errorPenalty := 1.0 - metrics.ErrorRate
	score += errorPenalty * 0.1

	return math.Min(1.0, score)
}

// getQualityScore calculates quality score based on evaluation results and user satisfaction
func getQualityScore(profile ModelProfile) float64 {
	var score float64 = 0.7 // Default score

	// Use quality score from profile if available
	if profile.QualityScore > 0 {
		score = profile.QualityScore * 0.6 // 60% weight for evaluation-based quality
	}

	// Use user satisfaction if available
	if profile.UserSatisfaction > 0 {
		score += profile.UserSatisfaction * 0.4 // 40% weight for user satisfaction
	} else {
		score += 0.7 * 0.4 // Default user satisfaction
	}

	return math.Min(1.0, score)
}

// getDynamicCostEfficiencyScore calculates cost efficiency using dynamic pricing
func getDynamicCostEfficiencyScore(complexity string, profile ModelProfile) float64 {
	// Use dynamic pricing if available, otherwise fall back to base pricing
	inputCost := profile.CostPerInputToken
	outputCost := profile.CostPerOutputToken

	if profile.DynamicPricing != nil {
		inputCost = profile.DynamicPricing.EffectiveInputCost
		outputCost = profile.DynamicPricing.EffectiveOutputCost
	}

	costPerToken := inputCost + outputCost

	switch complexity {
	case "low":
		// For low complexity, heavily favor cheaper models
		if costPerToken < 0.001 {
			return 1.0
		} else if costPerToken < 0.005 {
			return 0.9
		} else if costPerToken < 0.01 {
			return 0.7
		} else if costPerToken < 0.02 {
			return 0.5
		}
		return 0.2
	case "medium":
		// For medium complexity, moderate cost consideration
		if costPerToken < 0.005 {
			return 0.9
		} else if costPerToken < 0.02 {
			return 0.8
		} else if costPerToken < 0.05 {
			return 0.6
		}
		return 0.4
	case "high":
		// For high complexity, cost is less important than capability
		if costPerToken < 0.02 {
			return 0.8
		} else if costPerToken < 0.1 {
			return 0.7
		} else if costPerToken < 0.2 {
			return 0.5
		}
		return 0.3
	default:
		return 0.6 // Default moderate score
	}
}

// applyUserPreferenceAdjustments adjusts model scores based on user preferences
func applyUserPreferenceAdjustments(baseScore float64, modelID, userID string, analysis *PromptAnalysis) float64 {
	preferencesLock.RLock()
	defer preferencesLock.RUnlock()

	userPref, exists := userPreferences[userID]
	if !exists {
		// Create default user preference if not exists
		userPref = &UserPreference{
			UserID: userID,
			PriorityWeights: map[string]float64{
				"cost":        0.25,
				"quality":     0.25,
				"speed":       0.25,
				"reliability": 0.25,
			},
			AdaptiveLearning: true,
			LastUpdated:      time.Now(),
		}
		userPreferences[userID] = userPref
	}

	adjustedScore := baseScore

	// Check if model is in preferred list
	for _, preferredModel := range userPref.PreferredModels {
		if preferredModel == modelID {
			adjustedScore *= 1.2 // 20% boost for preferred models
			log.Printf("Applied preference boost for user %s, model %s", userID, modelID)
			break
		}
	}

	// Check if model is in avoided list
	for _, avoidedModel := range userPref.AvoidedModels {
		if avoidedModel == modelID {
			adjustedScore *= 0.5 // 50% penalty for avoided models
			log.Printf("Applied avoidance penalty for user %s, model %s", userID, modelID)
			break
		}
	}

	// Apply context-aware adjustments based on prompt analysis
	if analysis != nil {
		// Adjust based on task complexity and user preferences
		if analysis.Complexity == "high" && userPref.PriorityWeights["quality"] > 0.4 {
			adjustedScore *= 1.1 // Boost for quality-focused users on complex tasks
		}
		if analysis.Complexity == "low" && userPref.PriorityWeights["cost"] > 0.4 {
			adjustedScore *= 1.1 // Boost for cost-focused users on simple tasks
		}
	}

	// Apply priority weight adjustments based on user preferences
	if profile, exists := modelProfiles[modelID]; exists {
		// Cost preference adjustment
		if costWeight, exists := userPref.PriorityWeights["cost"]; exists && costWeight > 0.3 {
			// User prioritizes cost - boost cheaper models
			costPerToken := profile.CostPerInputToken + profile.CostPerOutputToken
			if costPerToken < 0.01 {
				adjustedScore *= (1.0 + (costWeight-0.25)*0.4) // Up to 10% boost
			}
		}

		// Quality preference adjustment
		if qualityWeight, exists := userPref.PriorityWeights["quality"]; exists && qualityWeight > 0.3 {
			// User prioritizes quality - boost high-quality models
			if profile.QualityScore > 0.8 {
				adjustedScore *= (1.0 + (qualityWeight-0.25)*0.4) // Up to 10% boost
			}
		}

		// Speed preference adjustment
		if speedWeight, exists := userPref.PriorityWeights["speed"]; exists && speedWeight > 0.3 {
			// User prioritizes speed - boost faster models
			if profile.RealTimeMetrics != nil && profile.RealTimeMetrics.AvgLatencyMs < profile.ExpectedLatencyMs {
				adjustedScore *= (1.0 + (speedWeight-0.25)*0.4) // Up to 10% boost
			}
		}

		// Reliability preference adjustment
		if reliabilityWeight, exists := userPref.PriorityWeights["reliability"]; exists && reliabilityWeight > 0.3 {
			// User prioritizes reliability - boost reliable models
			if profile.RealTimeMetrics != nil && profile.RealTimeMetrics.SuccessRate > 0.95 {
				adjustedScore *= (1.0 + (reliabilityWeight-0.25)*0.4) // Up to 10% boost
			}
		}
	}

	return math.Min(1.0, adjustedScore)
}

// applyTokenBudgetAdjustments adjusts model scores based on token budget constraints
func applyTokenBudgetAdjustments(baseScore float64, profile ModelProfile, tokenBudget *TokenBudget) float64 {
	if tokenBudget == nil {
		return baseScore
	}

	adjustedScore := baseScore

	// Calculate estimated cost based on token budget
	estimatedInputCost := float64(tokenBudget.MaxInputTokens) * profile.CostPerInputToken
	estimatedOutputCost := float64(tokenBudget.MaxOutputTokens) * profile.CostPerOutputToken
	totalEstimatedCost := estimatedInputCost + estimatedOutputCost

	// Apply cost efficiency adjustments based on priority
	switch {
	case tokenBudget.Priority >= 8: // High priority - cost is less important
		// Minimal cost penalty for high priority requests
		if totalEstimatedCost > 0.1 { // Very expensive
			adjustedScore *= 0.95
		}
	case tokenBudget.Priority >= 5: // Medium priority - balanced approach
		// Moderate cost consideration
		if totalEstimatedCost > 0.05 {
			adjustedScore *= 0.9
		} else if totalEstimatedCost < 0.01 {
			adjustedScore *= 1.1 // Boost for cost-efficient models
		}
	case tokenBudget.Priority < 5: // Low priority - cost is very important
		// Strong cost optimization
		if totalEstimatedCost > 0.02 {
			adjustedScore *= 0.7 // Heavy penalty for expensive models
		} else if totalEstimatedCost < 0.005 {
			adjustedScore *= 1.2 // Strong boost for cheap models
		}
	}

	// Check if model can handle the token budget (context window limits)
	// This is a simplified check - in practice, you'd want to check actual model limits
	maxTokens := tokenBudget.MaxInputTokens + tokenBudget.MaxOutputTokens

	// Penalize models that might struggle with large token budgets
	if maxTokens > 32000 {
		// Very large requests - favor models with large context windows
		if strings.Contains(strings.ToLower(profile.Name), "claude") {
			adjustedScore *= 1.1 // Claude models handle large contexts well
		} else if strings.Contains(strings.ToLower(profile.Name), "gpt-4") {
			adjustedScore *= 1.05 // GPT-4 models are decent with large contexts
		} else {
			adjustedScore *= 0.8 // Other models might struggle
		}
	} else if maxTokens > 16000 {
		// Large requests
		if strings.Contains(strings.ToLower(profile.Name), "claude") ||
			strings.Contains(strings.ToLower(profile.Name), "gpt-4") {
			adjustedScore *= 1.05
		}
	}

	log.Printf("Token budget adjustment for model %s: base=%.3f, adjusted=%.3f, cost=%.6f, priority=%d",
		profile.ID, baseScore, adjustedScore, totalEstimatedCost, tokenBudget.Priority)

	return math.Min(1.0, math.Max(0.0, adjustedScore))
}

// addUserFeedback adds user feedback to the system for preference learning
func addUserFeedback(userID, requestID, modelID, taskType, feedbackType string, rating float64, helpful bool, comments string) {
	preferencesLock.Lock()
	defer preferencesLock.Unlock()

	userPref, exists := userPreferences[userID]
	if !exists {
		userPref = &UserPreference{
			UserID:           userID,
			AdaptiveLearning: true,
			PriorityWeights: map[string]float64{
				"cost":        0.25,
				"quality":     0.25,
				"speed":       0.25,
				"reliability": 0.25,
			},
			LastUpdated: time.Now(),
		}
		userPreferences[userID] = userPref
	}

	feedback := UserFeedback{
		RequestID:    requestID,
		ModelID:      modelID,
		TaskType:     taskType,
		Rating:       rating,
		FeedbackType: feedbackType,
		Comments:     comments,
		Timestamp:    time.Now(),
		Helpful:      helpful,
	}

	userPref.FeedbackHistory = append(userPref.FeedbackHistory, feedback)

	// Keep only last 50 feedback entries to prevent memory bloat
	if len(userPref.FeedbackHistory) > 50 {
		userPref.FeedbackHistory = userPref.FeedbackHistory[len(userPref.FeedbackHistory)-50:]
	}

	// Update model preferences based on feedback
	if rating >= 4.0 && helpful {
		// Add to preferred models if not already there
		found := false
		for _, preferred := range userPref.PreferredModels {
			if preferred == modelID {
				found = true
				break
			}
		}
		if !found {
			userPref.PreferredModels = append(userPref.PreferredModels, modelID)
		}

		// Remove from avoided models if present
		for i, avoided := range userPref.AvoidedModels {
			if avoided == modelID {
				userPref.AvoidedModels = append(userPref.AvoidedModels[:i], userPref.AvoidedModels[i+1:]...)
				break
			}
		}
	} else if rating <= 2.0 && !helpful {
		// Add to avoided models if not already there
		found := false
		for _, avoided := range userPref.AvoidedModels {
			if avoided == modelID {
				found = true
				break
			}
		}
		if !found {
			userPref.AvoidedModels = append(userPref.AvoidedModels, modelID)
		}

		// Remove from preferred models if present
		for i, preferred := range userPref.PreferredModels {
			if preferred == modelID {
				userPref.PreferredModels = append(userPref.PreferredModels[:i], userPref.PreferredModels[i+1:]...)
				break
			}
		}
	}

	userPref.LastUpdated = time.Now()

	log.Printf("Added feedback for user %s: model %s, rating %.1f, type %s", userID, modelID, rating, feedbackType)
}

// loadModelProfilesFromRedis fetches all model profiles from Redis into the cache.
func loadModelProfilesFromRedis(ctx context.Context) error {
	newModelProfiles := make(map[string]ModelProfile)
	keys, err := redisClient.Keys(ctx, "model_profile:*").Result()
	if err != nil {
		err = fmt.Errorf("failed to get model profile keys from Redis: %w", err)
		log.Println(err)
		return err
	}

	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("Error getting model profile %s from Redis: %v", key, err)
			continue
		}
		var profile ModelProfile
		if err := json.Unmarshal([]byte(val), &profile); err != nil {
			log.Printf("Error unmarshalling model profile %s: %v", key, err)
			continue
		}
		newModelProfiles[profile.ID] = profile
	}
	modelProfiles = newModelProfiles
	log.Printf("Loaded %d model profiles from Redis.", len(modelProfiles))

	// Run a simple test
	runRoutingTest()

	return nil
}

// loadRoutingStrategiesFromRedis fetches all routing strategies from Redis.
func loadRoutingStrategiesFromRedis(ctx context.Context) error {
	newRoutingStrategies := []RoutingStrategy{}
	keys, err := redisClient.Keys(ctx, "routing_strategy:*").Result()
	if err != nil && err != redis.Nil {
		err = fmt.Errorf("failed to get routing strategy keys from Redis: %w", err)
		log.Println(err)
		return err
	}

	if len(keys) == 0 {
		log.Println("No routing strategies found in Redis.")
		return nil
	}

	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("Error getting routing strategy %s from Redis: %v", key, err)
			continue
		}
		var strategy RoutingStrategy
		if err := json.Unmarshal([]byte(val), &strategy); err != nil {
			log.Printf("Error unmarshalling routing strategy %s: %v", key, err)
			continue
		}
		// Basic validation of the loaded strategy
		if strategy.Strategy == "" {
			log.Printf("Routing strategy %s has no strategy defined, skipping", key)
			continue
		}
		newRoutingStrategies = append(newRoutingStrategies, strategy)
	}
	routingStrategies = newRoutingStrategies
	log.Printf("Loaded %d routing strategies from Redis.", len(routingStrategies))
	return nil
}

func runRoutingTest() {
	log.Println("Running routing test...")

	// Simulate a few different scenarios
	testCases := []struct {
		TaskType        string
		ModelPriorities []string
		ExpectedModel   string
		MinimumTier     int
	}{
		{
			TaskType:        "simple_task",
			ModelPriorities: []string{"model1", "model2", "model3"},
			ExpectedModel:   "model1",
			MinimumTier:     1,
		},
		{
			TaskType:        "complex_task",
			ModelPriorities: []string{"model3", "model2", "model1"},
			ExpectedModel:   "model3",
			MinimumTier:     2,
		},
		{
			TaskType:        "unsupported_task",
			ModelPriorities: []string{"model1", "model2", "model3"},
			ExpectedModel:   "",
			MinimumTier:     3,
		},
	}

	// Create dummy routing strategy
	strategy := RoutingStrategy{
		ID:       "test_strategy",
		Name:     "Test Strategy",
		Strategy: ROUTING_STRATEGY_DEFAULT,
		ModelRequirements: ModelCapabilityRequirement{
			MinimumTier: 0,
		},
		ModelPriorities: []string{},
		TaskType:        "",
	}

	// Create dummy model profiles
	modelProfiles["model1"] = ModelProfile{ID: "model1", Name: "Model 1", Tier: 1, BackendType: "test", BackendURL: "test"}
	modelProfiles["model2"] = ModelProfile{ID: "model2", Name: "Model 2", Tier: 2, BackendType: "test", BackendURL: "test"}
	modelProfiles["model3"] = ModelProfile{ID: "model3", Name: "Model 3", Tier: 3, BackendType: "test", BackendURL: "test"}

	for _, tc := range testCases {
		strategy.TaskType = tc.TaskType
		strategy.ModelPriorities = tc.ModelPriorities
		strategy.ModelRequirements.MinimumTier = tc.MinimumTier

		optReq := OptimizationRequest{
			TaskType: tc.TaskType,
		}

		routeResp := applyDefaultStrategy(strategy, modelProfiles, optReq)

		if routeResp.SelectedBackendID != tc.ExpectedModel {
			log.Printf("Test failed for task type %s: expected model %s, got %s", tc.TaskType, tc.ExpectedModel, routeResp.SelectedBackendID)
		} else {
			log.Printf("Test passed for task type %s: selected model %s", tc.TaskType, routeResp.SelectedBackendID)
		}
	}

	log.Println("Routing test completed.")
}

// handleModelCapabilities returns the capability scores for all models
func handleModelCapabilities(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	// Return the hardcoded capability scores
	capabilities := map[string]map[string]float64{
		"gpt-4o-mini": {
			"code_generation":  0.90,
			"reasoning":        0.88,
			"creative_writing": 0.85,
			"factual_qa":       0.90,
			"analysis":         0.88,
			"summarization":    0.85,
			"translation":      0.82,
			"mathematical":     0.88,
			"conversational":   0.85,
			"overall":          0.87,
		},
		"gpt-3.5-turbo": {
			"code_generation":  0.80,
			"reasoning":        0.75,
			"creative_writing": 0.78,
			"factual_qa":       0.82,
			"analysis":         0.75,
			"summarization":    0.80,
			"translation":      0.75,
			"mathematical":     0.70,
			"conversational":   0.85,
			"overall":          0.78,
		},
		"claude-3-haiku": {
			"code_generation":  0.85,
			"reasoning":        0.88,
			"creative_writing": 0.90,
			"factual_qa":       0.85,
			"analysis":         0.88,
			"summarization":    0.85,
			"translation":      0.80,
			"mathematical":     0.82,
			"conversational":   0.88,
			"overall":          0.86,
		},
		"gemini-2.5-flash-preview-05-20": {
			"code_generation":  0.85,
			"reasoning":        0.88,
			"creative_writing": 0.80,
			"factual_qa":       0.90,
			"analysis":         0.85,
			"summarization":    0.82,
			"translation":      0.88,
			"mathematical":     0.88,
			"conversational":   0.82,
			"multimodal":       0.95,
			"overall":          0.86,
		},
		"command-r-plus": {
			"code_generation":  0.75,
			"reasoning":        0.85,
			"creative_writing": 0.80,
			"factual_qa":       0.92,
			"analysis":         0.88,
			"summarization":    0.90,
			"translation":      0.90,
			"mathematical":     0.75,
			"conversational":   0.85,
			"rag":              0.95,
			"overall":          0.85,
		},
		"command-r": {
			"code_generation":  0.70,
			"reasoning":        0.80,
			"creative_writing": 0.75,
			"factual_qa":       0.88,
			"analysis":         0.82,
			"summarization":    0.85,
			"translation":      0.88,
			"mathematical":     0.70,
			"conversational":   0.80,
			"rag":              0.90,
			"overall":          0.81,
		},
		"mistral-large-latest": {
			"code_generation":  0.88,
			"reasoning":        0.90,
			"creative_writing": 0.80,
			"factual_qa":       0.82,
			"analysis":         0.85,
			"summarization":    0.80,
			"translation":      0.82,
			"mathematical":     0.85,
			"conversational":   0.78,
			"function_calling": 0.90,
			"overall":          0.84,
		},
		"mistral-medium-latest": {
			"code_generation":  0.82,
			"reasoning":        0.80,
			"creative_writing": 0.75,
			"factual_qa":       0.78,
			"analysis":         0.80,
			"summarization":    0.78,
			"translation":      0.80,
			"mathematical":     0.78,
			"conversational":   0.75,
			"function_calling": 0.82,
			"overall":          0.79,
		},
		"grok-1": {
			"code_generation":  0.82,
			"reasoning":        0.88,
			"creative_writing": 0.85,
			"factual_qa":       0.90,
			"analysis":         0.85,
			"summarization":    0.80,
			"translation":      0.75,
			"mathematical":     0.85,
			"conversational":   0.88,
			"real_time_data":   0.95,
			"overall":          0.85,
		},
		"grok-1.5-vision": {
			"code_generation":  0.85,
			"reasoning":        0.90,
			"creative_writing": 0.85,
			"factual_qa":       0.92,
			"analysis":         0.88,
			"summarization":    0.82,
			"translation":      0.78,
			"mathematical":     0.88,
			"conversational":   0.90,
			"multimodal":       0.90,
			"real_time_data":   0.95,
			"overall":          0.88,
		},
		"meta-llama-3.1-70b-instruct": {
			"code_generation":  0.88,
			"reasoning":        0.85,
			"creative_writing": 0.82,
			"factual_qa":       0.80,
			"analysis":         0.83,
			"summarization":    0.80,
			"translation":      0.75,
			"mathematical":     0.82,
			"conversational":   0.80,
			"overall":          0.82,
		},
	}

	if err := json.NewEncoder(w).Encode(capabilities); err != nil {
		log.Printf("Error encoding model capabilities response: %v", err)
		http.Error(w, "Error encoding response", http.StatusInternalServerError)
		return
	}
}

// GenerateRequest represents a request for LLM text generation
type GenerateRequest struct {
	Prompt  string                 `json:"prompt"`
	Model   string                 `json:"model"`
	Options map[string]interface{} `json:"options,omitempty"`
}

// GenerateResponse represents the response from LLM text generation
type GenerateResponse struct {
	Response string `json:"response"`
	Model    string `json:"model"`
	Error    string `json:"error,omitempty"`
}

func handleGenerateRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var genReq GenerateRequest
	if err := json.NewDecoder(r.Body).Decode(&genReq); err != nil {
		log.Printf("Error decoding generate request: %v", err)
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	log.Printf("Received generate request for prompt: %s", genReq.Prompt)

	// Call the proxy-gateway for real LLM response
	proxyGatewayURL := os.Getenv("PROXY_GATEWAY_URL")
	if proxyGatewayURL == "" {
		proxyGatewayURL = "https://scale-llm.com"
	}

	// Create chat completion request
	chatReq := map[string]interface{}{
		"model": genReq.Model,
		"messages": []map[string]string{
			{"role": "user", "content": genReq.Prompt},
		},
	}

	// Add options to the request
	if genReq.Options != nil {
		for k, v := range genReq.Options {
			chatReq[k] = v
		}
	}

	reqBody, err := json.Marshal(chatReq)
	if err != nil {
		log.Printf("Failed to marshal chat request: %v", err)
		http.Error(w, "Failed to create request", http.StatusInternalServerError)
		return
	}

	// Make request to proxy-gateway
	client := &http.Client{Timeout: 60 * time.Second}
	req, err := http.NewRequest("POST", proxyGatewayURL+"/v1/chat/completions", bytes.NewBuffer(reqBody))
	if err != nil {
		log.Printf("Failed to create proxy request: %v", err)
		http.Error(w, "Failed to create proxy request", http.StatusInternalServerError)
		return
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to call proxy-gateway: %v", err)
		http.Error(w, "Failed to call LLM service", http.StatusInternalServerError)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Printf("Proxy-gateway returned status %d", resp.StatusCode)
		http.Error(w, fmt.Sprintf("LLM service returned status %d", resp.StatusCode), http.StatusInternalServerError)
		return
	}

	// Read the response body
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Failed to read proxy response: %v", err)
		http.Error(w, "Failed to read LLM response", http.StatusInternalServerError)
		return
	}

	// Check if response is gzip compressed and decompress if needed
	var chatResp map[string]interface{}
	if bytes.HasPrefix(respBody, []byte{0x1f, 0x8b}) {
		// Decompress gzip content
		reader, err := gzip.NewReader(bytes.NewReader(respBody))
		if err != nil {
			log.Printf("Failed to create gzip reader: %v", err)
			http.Error(w, "Failed to decompress response", http.StatusInternalServerError)
			return
		}
		defer reader.Close()

		decompressed, err := io.ReadAll(reader)
		if err != nil {
			log.Printf("Failed to decompress response: %v", err)
			http.Error(w, "Failed to decompress response", http.StatusInternalServerError)
			return
		}

		if err := json.Unmarshal(decompressed, &chatResp); err != nil {
			log.Printf("Failed to parse decompressed response: %v", err)
			http.Error(w, "Failed to parse LLM response", http.StatusInternalServerError)
			return
		}
	} else {
		// Response is not compressed
		if err := json.Unmarshal(respBody, &chatResp); err != nil {
			log.Printf("Failed to parse response: %v", err)
			http.Error(w, "Failed to parse LLM response", http.StatusInternalServerError)
			return
		}
	}

	// Extract the response text from different possible formats
	responseText := ""

	// Try OpenAI format first
	if choices, ok := chatResp["choices"].([]interface{}); ok && len(choices) > 0 {
		if choice, ok := choices[0].(map[string]interface{}); ok {
			if message, ok := choice["message"].(map[string]interface{}); ok {
				if content, ok := message["content"].(string); ok {
					responseText = content
				}
			}
		}
	}

	// Try Gemini format if OpenAI format didn't work
	if responseText == "" {
		if candidates, ok := chatResp["candidates"].([]interface{}); ok && len(candidates) > 0 {
			if candidate, ok := candidates[0].(map[string]interface{}); ok {
				if content, ok := candidate["content"].(map[string]interface{}); ok {
					if parts, ok := content["parts"].([]interface{}); ok && len(parts) > 0 {
						if part, ok := parts[0].(map[string]interface{}); ok {
							if text, ok := part["text"].(string); ok {
								responseText = text
							}
						}
					}
				}
			}
		}
	}

	if responseText == "" {
		log.Printf("No response content found in LLM response")
		http.Error(w, "No response content found", http.StatusInternalServerError)
		return
	}

	// Return successful response in the format expected by the planning service
	response := map[string]interface{}{
		"text":  responseText,
		"model": genReq.Model,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
	log.Printf("Returned real LLM response for generate request")
}

func handleHealthCheck(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
}

// startPerformanceMetricsUpdater runs a background process to update real-time performance metrics
func startPerformanceMetricsUpdater() {
	ticker := time.NewTicker(30 * time.Second) // Update every 30 seconds
	defer ticker.Stop()

	log.Println("Started performance metrics updater")

	for range ticker.C {
		updateRealTimeMetrics()
	}
}

// updateRealTimeMetrics fetches and updates real-time performance data for all models
func updateRealTimeMetrics() {
	ctx := context.Background()

	// Query ClickHouse for recent performance data (last hour)
	clickhouseURL := os.Getenv("CLICKHOUSE_URL")
	if clickhouseURL == "" {
		return // Skip if ClickHouse not configured
	}

	metricsLock.Lock()
	defer metricsLock.Unlock()

	for modelID := range modelProfiles {
		metrics := calculateModelMetrics(ctx, modelID)
		if metrics != nil {
			performanceCache[modelID] = metrics

			// Update model profile with real-time metrics
			if profile, exists := modelProfiles[modelID]; exists {
				profile.RealTimeMetrics = metrics
				modelProfiles[modelID] = profile
			}
		}
	}

	log.Printf("Updated real-time metrics for %d models", len(performanceCache))
}

// calculateModelMetrics calculates real-time metrics for a specific model
func calculateModelMetrics(ctx context.Context, modelID string) *RealTimeMetrics {
	// This would typically query ClickHouse for recent performance data
	// For now, we'll simulate with reasonable defaults and some variation
	// TODO: Use ctx for ClickHouse query timeout and cancellation
	_ = ctx // Suppress unused parameter warning until ClickHouse integration

	baseLatency := 1000.0 // Base latency in ms
	if profile, exists := modelProfiles[modelID]; exists {
		baseLatency = profile.ExpectedLatencyMs
	}

	// Add some realistic variation (±20%)
	variation := (float64(time.Now().Unix()%100) - 50) / 250.0 // -0.2 to +0.2
	currentLatency := baseLatency * (1.0 + variation)

	return &RealTimeMetrics{
		AvgLatencyMs:      currentLatency,
		SuccessRate:       0.95 + (float64(time.Now().Unix()%10) / 100.0), // 0.95-1.0
		RequestCount:      int64(50 + time.Now().Unix()%100),              // 50-150 requests
		ErrorRate:         0.05 - (float64(time.Now().Unix()%10) / 200.0), // 0.0-0.05
		LastUpdated:       time.Now(),
		ThroughputRPM:     60.0 + float64(time.Now().Unix()%40),          // 60-100 RPM
		P95LatencyMs:      currentLatency * 1.5,                          // P95 is typically 1.5x average
		AvailabilityScore: 0.98 + (float64(time.Now().Unix()%5) / 250.0), // 0.98-1.0
	}
}

// startDynamicPricingUpdater runs a background process to update dynamic pricing
func startDynamicPricingUpdater() {
	ticker := time.NewTicker(5 * time.Minute) // Update every 5 minutes
	defer ticker.Stop()

	log.Println("Started dynamic pricing updater")

	for range ticker.C {
		updateDynamicPricing()
	}
}

// updateDynamicPricing updates pricing based on demand and performance
func updateDynamicPricing() {
	metricsLock.RLock()
	defer metricsLock.RUnlock()

	for modelID, profile := range modelProfiles {
		metrics := performanceCache[modelID]
		if metrics == nil {
			continue
		}

		// Calculate demand level based on request count and throughput
		demandLevel := "medium"
		multiplier := 1.0

		if metrics.RequestCount > 100 {
			demandLevel = "high"
			multiplier = 1.2 // 20% price increase for high demand
		} else if metrics.RequestCount < 30 {
			demandLevel = "low"
			multiplier = 0.8 // 20% price decrease for low demand
		}

		// Adjust pricing based on performance
		if metrics.SuccessRate < 0.9 {
			multiplier *= 0.9 // Reduce price for poor performance
		}
		if metrics.AvgLatencyMs > profile.ExpectedLatencyMs*1.5 {
			multiplier *= 0.95 // Reduce price for high latency
		}

		// Update dynamic pricing
		dynamicPricing := &DynamicPricing{
			CurrentMultiplier:   multiplier,
			DemandLevel:         demandLevel,
			LastPriceUpdate:     time.Now(),
			BaseInputCost:       profile.CostPerInputToken,
			BaseOutputCost:      profile.CostPerOutputToken,
			EffectiveInputCost:  profile.CostPerInputToken * multiplier,
			EffectiveOutputCost: profile.CostPerOutputToken * multiplier,
		}

		// Update model profile
		updatedProfile := profile
		updatedProfile.DynamicPricing = dynamicPricing
		modelProfiles[modelID] = updatedProfile
	}

	log.Printf("Updated dynamic pricing for %d models", len(modelProfiles))
}

// createStandardEditionModelProfiles creates essential model profiles for standard edition
func createStandardEditionModelProfiles(ctx context.Context) {
	log.Println("Creating standard edition model profiles...")

	// Get API keys from environment
	openaiKey := os.Getenv("OPENAI_API_KEY")
	googleKey := os.Getenv("GOOGLE_API_KEY")
	anthropicKey := os.Getenv("ANTHROPIC_API_KEY")
	cohereKey := os.Getenv("COHERE_API_KEY")
	mistralKey := os.Getenv("MISTRAL_API_KEY")
	grokKey := os.Getenv("GROK_API_KEY")

	// Log API key availability (without exposing the keys)
	log.Printf("API Key availability - OpenAI: %t, Google: %t, Anthropic: %t, Cohere: %t, Mistral: %t, Grok: %t",
		openaiKey != "", googleKey != "", anthropicKey != "", cohereKey != "", mistralKey != "", grokKey != "")

	standardProfiles := []ModelProfile{
		// OpenAI Models
		{
			ID:                 "gpt-3.5-turbo",
			Name:               "GPT-3.5 Turbo",
			Aliases:            []string{"gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106"},
			Capabilities:       []string{"chat", "completion", "reasoning"},
			PricingTier:        "budget",
			DataSensitivity:    "standard",
			BackendType:        "openai-external",
			BackendURL:         "https://api.openai.com/v1/chat/completions",
			APIKey:             openaiKey,
			ExpectedCost:       0.0015,
			ExpectedLatencyMs:  800,
			CostPerInputToken:  0.0000005,
			CostPerOutputToken: 0.0000015,
			Tier:               1,
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
		},
		{
			ID:                 "gpt-4o-mini",
			Name:               "GPT-4o Mini",
			Aliases:            []string{"gpt-4o-mini-2024-07-18"},
			Capabilities:       []string{"chat", "completion", "reasoning", "code", "analysis"},
			PricingTier:        "premium",
			DataSensitivity:    "standard",
			BackendType:        "openai-external",
			BackendURL:         "https://api.openai.com/v1/chat/completions",
			APIKey:             openaiKey,
			ExpectedCost:       0.0002,
			ExpectedLatencyMs:  600,
			CostPerInputToken:  0.00000015,
			CostPerOutputToken: 0.0000006,
			Tier:               2,
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
		},
		// Google Models
		{
			ID:                 "gemini-2.5-flash-preview-05-20",
			Name:               "Gemini 2.5 Flash",
			Aliases:            []string{"gemini-2.5-flash", "gemini-flash"},
			Capabilities:       []string{"chat", "completion", "reasoning", "multimodal", "analysis"},
			PricingTier:        "premium",
			DataSensitivity:    "standard",
			BackendType:        "google-external",
			BackendURL:         "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent",
			APIKey:             googleKey,
			ExpectedCost:       0.0001,
			ExpectedLatencyMs:  500,
			CostPerInputToken:  0.000000075,
			CostPerOutputToken: 0.0000003,
			Tier:               2,
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
		},
		// Anthropic Models
		{
			ID:                 "claude-3-haiku",
			Name:               "Claude 3 Haiku",
			Aliases:            []string{"claude-3-haiku-20240307"},
			Capabilities:       []string{"chat", "completion", "reasoning", "creative_writing"},
			PricingTier:        "budget",
			DataSensitivity:    "standard",
			BackendType:        "anthropic",
			BackendURL:         "https://api.anthropic.com/v1",
			APIKey:             anthropicKey,
			ExpectedCost:       0.0008,
			ExpectedLatencyMs:  700,
			CostPerInputToken:  0.00000025,
			CostPerOutputToken: 0.00000125,
			Tier:               1,
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
		},
		// Cohere Models
		{
			ID:                 "command-r-plus",
			Name:               "Command R+",
			Aliases:            []string{"command-r-plus-08-2024"},
			Capabilities:       []string{"chat", "completion", "reasoning", "rag", "search"},
			PricingTier:        "premium",
			DataSensitivity:    "standard",
			BackendType:        "cohere",
			BackendURL:         "https://api.cohere.ai/v1",
			APIKey:             cohereKey,
			ExpectedCost:       0.003,
			ExpectedLatencyMs:  900,
			CostPerInputToken:  0.000003,
			CostPerOutputToken: 0.000015,
			Tier:               2,
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
		},
		// Mistral Models
		{
			ID:                 "mistral-large-latest",
			Name:               "Mistral Large",
			Aliases:            []string{"mistral-large-2407"},
			Capabilities:       []string{"chat", "completion", "reasoning", "code", "function_calling"},
			PricingTier:        "premium",
			DataSensitivity:    "standard",
			BackendType:        "mistral",
			BackendURL:         "https://api.mistral.ai/v1",
			APIKey:             mistralKey,
			ExpectedCost:       0.004,
			ExpectedLatencyMs:  1000,
			CostPerInputToken:  0.000004,
			CostPerOutputToken: 0.000012,
			Tier:               2,
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
		},
		// Grok Models
		{
			ID:                 "grok-1.5-vision",
			Name:               "Grok 1.5 Vision",
			Aliases:            []string{"grok-vision"},
			Capabilities:       []string{"chat", "completion", "reasoning", "multimodal", "real_time_data"},
			PricingTier:        "premium",
			DataSensitivity:    "standard",
			BackendType:        "grok",
			BackendURL:         "https://api.x.ai/v1",
			APIKey:             grokKey,
			ExpectedCost:       0.005,
			ExpectedLatencyMs:  1200,
			CostPerInputToken:  0.000005,
			CostPerOutputToken: 0.000015,
			Tier:               3,
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
		},
	}

	// Store profiles in memory and Redis
	for _, profile := range standardProfiles {
		// Only add if API key is available
		if profile.APIKey != "" {
			modelProfiles[profile.ID] = profile

			// Save to Redis for persistence
			profileJSON, err := json.Marshal(profile)
			if err != nil {
				log.Printf("Error marshalling model profile %s: %v", profile.ID, err)
				continue
			}

			key := "model_profile:" + profile.ID
			if err := redisClient.Set(ctx, key, profileJSON, 0).Err(); err != nil {
				log.Printf("Warning: Could not save model profile %s to Redis: %v", profile.ID, err)
				// Continue anyway - we have it in memory
			}

			log.Printf("Created standard edition model profile: %s (%s) with backend URL: %s",
				profile.Name, profile.BackendType, profile.BackendURL)
		} else {
			log.Printf("Skipping model profile %s - no API key provided", profile.ID)
		}
	}

	log.Printf("Created %d standard edition model profiles", len(modelProfiles))
}

// createFallbackModelProfiles creates minimal fallback profiles if nothing else is available
func createFallbackModelProfiles(ctx context.Context) {
	log.Println("Creating fallback model profiles...")
	_ = ctx // Suppress unused parameter warning - reserved for future Redis operations

	// Create minimal profiles for basic functionality
	fallbackProfiles := []ModelProfile{
		{
			ID:                 "gpt-3.5-turbo",
			Name:               "GPT-3.5 Turbo (Fallback)",
			Capabilities:       []string{"chat", "completion"},
			PricingTier:        "budget",
			DataSensitivity:    "standard",
			BackendType:        "openai-external",
			BackendURL:         "https://api.openai.com/v1/chat/completions",
			APIKey:             os.Getenv("OPENAI_API_KEY"),
			ExpectedCost:       0.0015,
			ExpectedLatencyMs:  800,
			CostPerInputToken:  0.0000005,
			CostPerOutputToken: 0.0000015,
			Tier:               1,
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
		},
		{
			ID:                 "gpt-4o-mini",
			Name:               "GPT-4o Mini (Fallback)",
			Capabilities:       []string{"chat", "completion", "reasoning"},
			PricingTier:        "premium",
			DataSensitivity:    "standard",
			BackendType:        "openai-external",
			BackendURL:         "https://api.openai.com/v1/chat/completions",
			APIKey:             os.Getenv("OPENAI_API_KEY"),
			ExpectedCost:       0.0002,
			ExpectedLatencyMs:  600,
			CostPerInputToken:  0.00000015,
			CostPerOutputToken: 0.0000006,
			Tier:               2,
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
		},
	}

	for _, profile := range fallbackProfiles {
		if profile.APIKey != "" {
			modelProfiles[profile.ID] = profile
			log.Printf("Created fallback model profile: %s (%s) with backend URL: %s",
				profile.Name, profile.BackendType, profile.BackendURL)
		} else {
			log.Printf("Skipping fallback model profile %s - no API key available", profile.ID)
		}
	}

	log.Printf("Created %d fallback model profiles", len(modelProfiles))
}

// startUserPreferenceLearning runs a background process for adaptive user preference learning
func startUserPreferenceLearning() {
	ticker := time.NewTicker(10 * time.Minute) // Update every 10 minutes
	defer ticker.Stop()

	log.Println("Started user preference learning")

	for range ticker.C {
		updateUserPreferences()
	}
}

// updateUserPreferences analyzes user feedback and updates preferences
func updateUserPreferences() {
	preferencesLock.Lock()
	defer preferencesLock.Unlock()

	// This would typically analyze user feedback from ClickHouse
	// For now, we'll simulate preference learning

	for _, preference := range userPreferences {
		if !preference.AdaptiveLearning {
			continue
		}

		// Analyze recent feedback to update preferences
		if len(preference.FeedbackHistory) > 0 {
			updatePreferenceWeights(preference)
			preference.LastUpdated = time.Now()
		}
	}

	log.Printf("Updated preferences for %d users", len(userPreferences))
}

// updatePreferenceWeights updates user preference weights based on feedback
func updatePreferenceWeights(preference *UserPreference) {
	// Initialize weights if not set
	if preference.PriorityWeights == nil {
		preference.PriorityWeights = map[string]float64{
			"cost":        0.25,
			"quality":     0.25,
			"speed":       0.25,
			"reliability": 0.25,
		}
	}

	// Analyze recent feedback (last 10 entries)
	recentFeedback := preference.FeedbackHistory
	if len(recentFeedback) > 10 {
		recentFeedback = recentFeedback[len(recentFeedback)-10:]
	}

	// Adjust weights based on feedback patterns
	for _, feedback := range recentFeedback {
		if feedback.Rating >= 4.0 { // Good rating
			// Increase weight for the feedback type
			if weight, exists := preference.PriorityWeights[feedback.FeedbackType]; exists {
				preference.PriorityWeights[feedback.FeedbackType] = math.Min(1.0, weight+0.05)
			}
		} else if feedback.Rating <= 2.0 { // Poor rating
			// Decrease weight for the feedback type
			if weight, exists := preference.PriorityWeights[feedback.FeedbackType]; exists {
				preference.PriorityWeights[feedback.FeedbackType] = math.Max(0.1, weight-0.05)
			}
		}
	}

	// Normalize weights to sum to 1.0
	total := 0.0
	for _, weight := range preference.PriorityWeights {
		total += weight
	}
	if total > 0 {
		for key, weight := range preference.PriorityWeights {
			preference.PriorityWeights[key] = weight / total
		}
	}
}

// handleUserFeedback handles user feedback submission for preference learning
func handleUserFeedback(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var feedbackReq struct {
		UserID       string  `json:"user_id"`
		RequestID    string  `json:"request_id"`
		ModelID      string  `json:"model_id"`
		TaskType     string  `json:"task_type"`
		FeedbackType string  `json:"feedback_type"` // "quality", "speed", "cost", "overall"
		Rating       float64 `json:"rating"`        // 1.0-5.0
		Helpful      bool    `json:"helpful"`
		Comments     string  `json:"comments,omitempty"`
	}

	if err := json.NewDecoder(r.Body).Decode(&feedbackReq); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Validate input
	if feedbackReq.UserID == "" || feedbackReq.ModelID == "" || feedbackReq.Rating < 1.0 || feedbackReq.Rating > 5.0 {
		http.Error(w, "Invalid feedback data", http.StatusBadRequest)
		return
	}

	// Add feedback to the system
	addUserFeedback(
		feedbackReq.UserID,
		feedbackReq.RequestID,
		feedbackReq.ModelID,
		feedbackReq.TaskType,
		feedbackReq.FeedbackType,
		feedbackReq.Rating,
		feedbackReq.Helpful,
		feedbackReq.Comments,
	)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"status":  "success",
		"message": "Feedback recorded successfully",
	})
}

// handleUserPreferences handles user preference management
func handleUserPreferences(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		// Get user preferences
		userID := r.URL.Query().Get("user_id")
		if userID == "" {
			http.Error(w, "user_id parameter required", http.StatusBadRequest)
			return
		}

		preferencesLock.RLock()
		userPref, exists := userPreferences[userID]
		preferencesLock.RUnlock()

		if !exists {
			// Return default preferences
			userPref = &UserPreference{
				UserID: userID,
				PriorityWeights: map[string]float64{
					"cost":        0.25,
					"quality":     0.25,
					"speed":       0.25,
					"reliability": 0.25,
				},
				AdaptiveLearning: true,
				LastUpdated:      time.Now(),
			}
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(userPref)

	case http.MethodPost:
		// Update user preferences
		var prefReq UserPreference
		if err := json.NewDecoder(r.Body).Decode(&prefReq); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
			return
		}

		if prefReq.UserID == "" {
			http.Error(w, "user_id required", http.StatusBadRequest)
			return
		}

		preferencesLock.Lock()
		prefReq.LastUpdated = time.Now()
		userPreferences[prefReq.UserID] = &prefReq
		preferencesLock.Unlock()

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]string{
			"status":  "success",
			"message": "Preferences updated successfully",
		})

	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}

// handleRealTimeMetrics handles real-time metrics queries
func handleRealTimeMetrics(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	modelID := r.URL.Query().Get("model_id")

	metricsLock.RLock()
	defer metricsLock.RUnlock()

	if modelID != "" {
		// Return metrics for specific model
		if metrics, exists := performanceCache[modelID]; exists {
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]interface{}{
				"model_id": modelID,
				"metrics":  metrics,
			})
		} else {
			http.Error(w, "Model not found", http.StatusNotFound)
		}
	} else {
		// Return metrics for all models
		response := make(map[string]*RealTimeMetrics)
		for id, metrics := range performanceCache {
			response[id] = metrics
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}
}
