package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
)

// HuggingFaceChatRequest represents the incoming request body for Hugging Face Inference API.
type HuggingFaceChatRequest struct {
	Model       string               `json:"model"`
	Messages    []HuggingFaceMessage `json:"messages"`
	MaxTokens   int                  `json:"max_tokens,omitempty"`
	Temperature *float64             `json:"temperature,omitempty"`
	Stream      bool                 `json:"stream,omitempty"`
}

type HuggingFaceMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// HuggingFaceChatResponse represents the Hugging Face Inference API response.
type HuggingFaceChatResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

// countWords provides a simple word-based token estimation
func countWords(text string) int {
	words := strings.Fields(text)
	return len(words)
}

// handleHuggingFaceRequest simulates the Hugging Face Inference API endpoint.
func handleHuggingFaceRequest(w http.ResponseWriter, r *http.Request) {
	requestID := r.Header.Get("X-Request-ID")
	if requestID == "" {
		requestID = "UNKNOWN_REQUEST"
	}
	backendID := os.Getenv("BACKEND_ID")
	delayMsStr := os.Getenv("DELAY_MS")
	delayMs, _ := strconv.Atoi(delayMsStr)

	log.Printf("[%s] [%s] Received Hugging Face Inference API request.", backendID, requestID)

	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		log.Printf("[%s] [%s] Method not allowed: %s", backendID, requestID, r.Method)
		return
	}

	// Read request body
	var hfRequest HuggingFaceChatRequest
	if err := json.NewDecoder(r.Body).Decode(&hfRequest); err != nil {
		log.Printf("[%s] [%s] Error decoding request body: %v", backendID, requestID, err)
		http.Error(w, "Invalid request body format for Hugging Face Inference API", http.StatusBadRequest)
		return
	}

	// Extract prompt from messages
	fullPrompt := ""
	for _, msg := range hfRequest.Messages {
		if msg.Role == "user" {
			fullPrompt += msg.Content + "\n"
		}
	}
	fullPrompt = strings.TrimSpace(fullPrompt)

	// Simulate processing time (latency)
	if delayMs > 0 {
		time.Sleep(time.Duration(delayMs) * time.Millisecond)
	}

	// Generate simulated response content based on model
	var simulatedResponseContent string
	if strings.Contains(hfRequest.Model, "llama") {
		simulatedResponseContent = fmt.Sprintf("This is a simulated Llama response from %s via Hugging Face for prompt: '%s'. Llama models excel at code generation and reasoning tasks.", backendID, fullPrompt)
	} else if strings.Contains(hfRequest.Model, "mixtral") {
		simulatedResponseContent = fmt.Sprintf("This is a simulated Mixtral response from %s via Hugging Face for prompt: '%s'. Mixtral is efficient and multilingual.", backendID, fullPrompt)
	} else {
		simulatedResponseContent = fmt.Sprintf("This is a simulated Hugging Face response from %s for model %s and prompt: '%s'. Open source models provide great flexibility.", backendID, hfRequest.Model, fullPrompt)
	}

	// Estimate token counts
	inputTokens := countWords(fullPrompt)
	outputTokens := countWords(simulatedResponseContent)

	// Create response
	response := HuggingFaceChatResponse{
		ID:      fmt.Sprintf("hf-%s", uuid.New().String()),
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   hfRequest.Model,
		Choices: []struct {
			Index   int `json:"index"`
			Message struct {
				Role    string `json:"role"`
				Content string `json:"content"`
			} `json:"message"`
			FinishReason string `json:"finish_reason"`
		}{
			{
				Index: 0,
				Message: struct {
					Role    string `json:"role"`
					Content string `json:"content"`
				}{
					Role:    "assistant",
					Content: simulatedResponseContent,
				},
				FinishReason: "stop",
			},
		},
		Usage: struct {
			PromptTokens     int `json:"prompt_tokens"`
			CompletionTokens int `json:"completion_tokens"`
			TotalTokens      int `json:"total_tokens"`
		}{
			PromptTokens:     inputTokens,
			CompletionTokens: outputTokens,
			TotalTokens:      inputTokens + outputTokens,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)

	log.Printf("[%s] [%s] Sent Hugging Face response. Input Tokens: %d, Output Tokens: %d", backendID, requestID, inputTokens, outputTokens)
}

func main() {
	listenAddr := os.Getenv("LISTEN_ADDR")
	if listenAddr == "" {
		listenAddr = ":5007" // Default port for mock-huggingface
	}

	backendID := os.Getenv("BACKEND_ID")
	if backendID == "" {
		backendID = "mock-huggingface"
	}

	delayMsStr := os.Getenv("DELAY_MS")
	delayMs, err := strconv.Atoi(delayMsStr)
	if err != nil || delayMs < 0 {
		delayMs = 400 // Default delay for Hugging Face mock
	}

	log.Printf("Starting Mock Hugging Face Backend '%s' on %s with simulated initial delay of %d ms", backendID, listenAddr, delayMs)

	// Support both chat completions and model-specific endpoints
	http.HandleFunc("/v1/chat/completions", handleHuggingFaceRequest)
	http.HandleFunc("/models/", handleHuggingFaceRequest) // For model-specific endpoints

	err = http.ListenAndServe(listenAddr, nil)
	if err != nil {
		log.Fatalf("Server failed: %v", err)
	}
}
