#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration from Environment Variables (passed from Cloud Build) ---
export GCP_PROJECT_ID="${GCP_PROJECT_ID}"
export GCP_REGION="${GCP_REGION}"
export ARTIFACT_REGISTRY_REPO="${ARTIFACT_REGISTRY_REPO}"
export BUILD_ID="${BUILD_ID}"
export GKE_CLUSTER_NAME="${GKE_CLUSTER_NAME}"

echo "Injecting API Keys via Kubernetes Secrets. Ensure secrets are created in the cluster."
echo "Correcting Redis connection environment variables for populate_redis.py."

# Construct the full image name
#REDIS_POPULATOR_IMAGE="${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/${ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-redis-populator-job:latest"
REDIS_POPULATOR_IMAGE="us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-redis-populator-job:latest"

# Define a unique job name to avoid conflicts if previous jobs are still around
# If BUILD_ID is empty, generate a unique suffix using timestamp and random string
if [ -z "${BUILD_ID}" ]; then
    TIMESTAMP="$(date +%s)"
    RANDOM_STR="$(openssl rand -hex 4)"
    JOB_NAME="redis-populator-job-${TIMESTAMP}${RANDOM_STR}"
else
    JOB_NAME="redis-populator-job-${BUILD_ID}"
fi

echo "Creating Kubernetes Job to populate Redis..."
echo "Job Name: ${JOB_NAME}"
echo "Redis Populator Image: ${REDIS_POPULATOR_IMAGE}"


# Kubernetes Job YAML
cat <<EOF | kubectl apply -f -
apiVersion: batch/v1
kind: Job
metadata:
  name: ${JOB_NAME}
  namespace: default # Or your target namespace
spec:
  template:
    metadata:
      labels:
        job: ${JOB_NAME}
    spec:
      containers:
      - name: redis-populator
        image: ${REDIS_POPULATOR_IMAGE}
        # Use the full Redis populator script (with mock LLMs removed)
        command: ["python3", "/app/populate_redis.py"]
        env:
        # Redis connection details
        - name: REDIS_HOST
          value: "redis" # Kubernetes service name for Redis
        - name: REDIS_PORT
          value: "6379"
        - name: REDIS_DB
          value: "0" # Default Redis database
        - name: REDIS_PASSWORD
          value: "" # No password for Redis

        # Load all environment variables from deployment-config ConfigMap
        envFrom:
        - configMapRef:
            name: deployment-config
      restartPolicy: OnFailure
  backoffLimit: 3 # Retry up to 3 times on failure
EOF


echo "Waiting for Redis Populator Job ${JOB_NAME} to complete..."
kubectl wait --for=condition=complete job/${JOB_NAME} --timeout=300s || {
    echo "Redis Populator Job ${JOB_NAME} failed or timed out. Checking logs..."
    # Get the pod name for the job
    POD_NAME=$(kubectl get pods --selector=job-name=${JOB_NAME} -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    if [ -n "$POD_NAME" ]; then
        echo "Pod name: $POD_NAME"
        echo "Pod logs:"
        kubectl logs $POD_NAME || echo "Failed to get pod logs"
        echo "Pod description:"
        kubectl describe pod $POD_NAME || echo "Failed to describe pod"
    else
        echo "No pod found for job ${JOB_NAME}"
        kubectl logs job/${JOB_NAME} || echo "Failed to get job logs"
    fi
    exit 1
}
echo "Redis Populator Job ${JOB_NAME} completed successfully."

# Comment out the cleanup lines TEMPORARILY for debugging
# echo "Cleaning up Redis Populator Job ${JOB_NAME}..."
# kubectl delete job/${JOB_NAME} --ignore-not-found=true --grace-period=0 --force
# echo "Redis Populator Job ${JOB_NAME} cleaned up."

