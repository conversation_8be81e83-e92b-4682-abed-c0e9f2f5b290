#!/usr/bin/env python3
"""
Simplified Redis populator for external LLM APIs only.
This script removes all mock backends and uses only real external APIs.
Reads API keys from deployment-config.env file.
"""

import redis
import json
import os
import sys
import uuid
import requests
from datetime import datetime, timezone

# Redis connection
redis_host = os.getenv("REDIS_HOST", "redis-master")
redis_port = int(os.getenv("REDIS_PORT", "6379"))
redis_password = os.getenv("REDIS_PASSWORD", "")

# API Keys from environment variables (populated from deployment-config ConfigMap)
openai_api_key = os.getenv("OPENAI_API_KEY", "")
anthropic_api_key = os.getenv("ANTHROPIC_API_KEY", "")
google_api_key = os.getenv("GOOGLE_API_KEY", "")
cohere_api_key = os.getenv("COHERE_API_KEY", "")
huggingface_api_key = os.getenv("HUGGINGFACE_API_KEY", "")
mistral_api_key = os.getenv("MISTRAL_API_KEY", "")
grok_api_key = os.getenv("GROK_API_KEY", "")

print("📄 Reading API keys from deployment-config.env via environment variables")

# Log which API keys are available
print("🔑 API Key Status:")
print(f"   OpenAI: {'✅ Available' if openai_api_key else '❌ Missing'}")
print(f"   Google: {'✅ Available' if google_api_key else '❌ Missing'}")
print(f"   Anthropic: {'✅ Available' if anthropic_api_key else '❌ Missing'}")
print(f"   Cohere: {'✅ Available' if cohere_api_key else '❌ Missing'}")
print(f"   HuggingFace: {'✅ Available' if huggingface_api_key else '❌ Missing'}")
print(f"   Mistral: {'✅ Available' if mistral_api_key else '❌ Missing'}")
print(f"   Grok: {'✅ Available' if grok_api_key else '❌ Missing'}")

print(f"Connecting to Redis at {redis_host}:{redis_port}")
print(f"Redis password: {'[SET]' if redis_password else '[EMPTY]'}")

try:
    r = redis.Redis(host=redis_host, port=redis_port, password=redis_password, decode_responses=True)
    print("Redis client created, attempting to ping...")
    r.ping()
    print("✅ Connected to Redis successfully")
except Exception as e:
    print(f"❌ Failed to connect to Redis: {e}")
    print(f"Exception type: {type(e).__name__}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# Define the ModelProfile class
class ModelProfile:
    def __init__(self, id, name, backend_type, url, api_key=None,
                 expected_cost=0.0, expected_latency_ms=0,
                 capabilities=None, data_sensitivity_level="low",
                 cost_per_input_token=0.0, cost_per_output_token=0.0,
                 version="", owner="", status="active", documentation_url="", license="",
                 input_context_length=0, output_context_length=0,
                 region="", provider="", description="", tier=1):
        self.id = id
        self.name = name
        self.backend_type = backend_type
        self.url = url
        self.api_key = api_key
        self.expected_cost = expected_cost
        self.expected_latency_ms = expected_latency_ms
        self.capabilities = capabilities or []
        self.data_sensitivity_level = data_sensitivity_level
        self.cost_per_input_token = cost_per_input_token
        self.cost_per_output_token = cost_per_output_token
        self.version = version
        self.owner = owner
        self.status = status
        self.documentation_url = documentation_url
        self.license = license
        self.input_context_length = input_context_length
        self.output_context_length = output_context_length
        self.region = region
        self.provider = provider
        self.description = description
        self.tier = tier

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "backend_type": self.backend_type,
            "url": self.url,
            "api_key": self.api_key,
            "expected_cost": self.expected_cost,
            "expected_latency_ms": self.expected_latency_ms,
            "capabilities": self.capabilities,
            "data_sensitivity_level": self.data_sensitivity_level,
            "cost_per_input_token": self.cost_per_input_token,
            "cost_per_output_token": self.cost_per_output_token,
            "version": self.version,
            "owner": self.owner,
            "status": self.status,
            "documentation_url": self.documentation_url,
            "license": self.license,
            "input_context_length": self.input_context_length,
            "output_context_length": self.output_context_length,
            "region": self.region,
            "provider": self.provider,
            "description": self.description,
            "tier": self.tier
        }

# Define the RoutingStrategy class
class RoutingStrategy:
    def __init__(self, id, name, strategy, task_type="", policy_id="",
                 model_priorities=None, parallel_models=None, comparison_method="",
                 minimum_tier=0, enable_fallback=False, fallback_model_id="", priority=100,
                 required_capabilities=None, preferred_provider=""):
        self.id = id
        self.name = name
        self.strategy = strategy  # "default", "cascade", "parallel", "hybrid"
        self.task_type = task_type
        self.policy_id = policy_id
        self.model_priorities = model_priorities if model_priorities is not None else []
        self.parallel_models = parallel_models if parallel_models is not None else []
        self.comparison_method = comparison_method
        self.enable_fallback = enable_fallback
        self.fallback_model_id = fallback_model_id
        self.priority = priority
        # Fix the model_requirements structure to match Go struct
        self.model_requirements = {
            "minimum_tier": minimum_tier,
            "required_capabilities": required_capabilities if required_capabilities is not None else [],
            "preferred_provider": preferred_provider
        }

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "strategy": self.strategy,
            "task_type": self.task_type,
            "policy_id": self.policy_id,
            "model_priorities": self.model_priorities,
            "parallel_models": self.parallel_models,
            "comparison_method": self.comparison_method,
            "enable_fallback": self.enable_fallback,
            "fallback_model_id": self.fallback_model_id,
            "priority": self.priority,
            "model_requirements": self.model_requirements
        }

    def to_json(self):
        return json.dumps(self.to_dict())

# Define the Policy class
class Policy:
    def __init__(self, name, description, criteria=None, action="", backend_id="", priority=100, rules=None,
                 metadata=None, rate_limit=0, budget=0.0,
                 effect="", subjects=None, resource_type="", resource_ids=None, permissions=None, status="active"):
        self.id = str(uuid.uuid4())
        self.name = name
        self.description = description
        # CRITICAL FIX: Criteria and rules will be serialized to JSON strings.
        # Default criteria to an empty JSON object {} if not provided.
        self.criteria = json.dumps(criteria) if criteria is not None else "{}"
        self.action = action
        self.backend_id = backend_id
        self.priority = priority
        self.rules = json.dumps(rules) if rules is not None else "{}"
        self.created_at = datetime.now(timezone.utc).isoformat()
        self.updated_at = datetime.now(timezone.utc).isoformat()
        self.metadata = json.dumps(metadata) if metadata is not None else "{}"
        self.rate_limit = rate_limit
        self.budget = budget

        # RBAC fields
        self.effect = effect
        self.subjects = subjects if subjects is not None else []
        self.resource_type = resource_type
        self.resource_ids = resource_ids if resource_ids is not None else []
        self.permissions = permissions if permissions is not None else []
        self.status = status

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "criteria": self.criteria,
            "action": self.action,
            "backend_id": self.backend_id,
            "priority": self.priority,
            "rules": self.rules,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "metadata": self.metadata,
            "rate_limit": self.rate_limit,
            "budget": self.budget,
            "effect": self.effect,
            "subjects": self.subjects,
            "resource_type": self.resource_type,
            "resource_ids": self.resource_ids,
            "permissions": self.permissions,
            "status": self.status
        }

    def to_json(self):
        return json.dumps(self.to_dict())

# Define the Prompt class
class Prompt:
    def __init__(self, id, name, version, content, description="", tags=None, owner="", status="active", metadata=None):
        self.id = id
        self.name = name
        self.version = version
        self.content = content
        self.description = description
        self.tags = tags if tags is not None else []
        self.owner = owner
        self.status = status
        self.metadata = json.dumps(metadata) if metadata is not None else "{}"
        self.created_at = datetime.now(timezone.utc).isoformat()
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "version": self.version,
            "content": self.content,
            "description": self.description,
            "tags": self.tags,
            "owner": self.owner,
            "status": self.status,
            "metadata": self.metadata,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    def to_json(self):
        return json.dumps(self.to_dict())

def populate_external_model_profiles():
    """Populate Redis with external LLM API profiles only"""
    
    model_profiles_data = []

    # OpenAI GPT-3.5 Turbo
    model_profiles_data.append(ModelProfile(
        id="gpt-3.5-turbo",
        name="GPT-3.5 Turbo (External)",
        backend_type="openai-external",
        url="https://api.openai.com/v1/chat/completions",
        api_key=openai_api_key,
        expected_cost=0.0000005,
        expected_latency_ms=200,
        cost_per_input_token=0.0000005,
        cost_per_output_token=0.0000015,
        capabilities=["chat", "text-completion", "code-generation"],
        version="0125",
        owner="OpenAI",
        status="active",
        documentation_url="https://platform.openai.com/docs/models/gpt-3-5-turbo",
        license="Proprietary",
        input_context_length=16385,
        output_context_length=4096,
        provider="OpenAI",
        region="global",
        description="OpenAI's GPT-3.5 Turbo model for general-purpose text generation.",
        tier=1
    ))

    # OpenAI GPT-4
    model_profiles_data.append(ModelProfile(
        id="gpt-4",
        name="GPT-4 (External)",
        backend_type="openai-external",
        url="https://api.openai.com/v1/chat/completions",
        api_key=openai_api_key,
        expected_cost=0.00003,
        expected_latency_ms=300,
        cost_per_input_token=0.00003,
        cost_per_output_token=0.00006,
        capabilities=["chat", "text-completion", "code-generation", "reasoning"],
        version="gpt-4-0613",
        owner="OpenAI",
        status="active",
        documentation_url="https://platform.openai.com/docs/models/gpt-4",
        license="Proprietary",
        input_context_length=8192,
        output_context_length=4096,
        provider="OpenAI",
        region="global",
        description="OpenAI's GPT-4 model for advanced reasoning and complex tasks.",
        tier=3
    ))

    # OpenAI GPT-4o Mini
    model_profiles_data.append(ModelProfile(
        id="gpt-4o-mini",
        name="GPT-4o Mini (External)",
        backend_type="openai-external",
        url="https://api.openai.com/v1/chat/completions",
        api_key=openai_api_key,
        expected_cost=0.000003,
        expected_latency_ms=200,
        cost_per_input_token=0.00000015,
        cost_per_output_token=0.0000006,
        capabilities=["chat", "text-completion", "code-generation"],
        version="gpt-4o-mini-2024-07-18",
        owner="OpenAI",
        status="active",
        documentation_url="https://platform.openai.com/docs/models/gpt-4o-mini",
        license="Proprietary",
        input_context_length=128000,
        output_context_length=16384,
        provider="OpenAI",
        region="global",
        description="OpenAI's cost-effective GPT-4o Mini model.",
        tier=2
    ))

    # Anthropic Claude 3 Haiku
    model_profiles_data.append(ModelProfile(
        id="claude-3-haiku",
        name="Claude 3 Haiku (External)",
        backend_type="anthropic-external",
        url="https://api.anthropic.com/v1/messages",
        api_key=anthropic_api_key,
        expected_cost=0.00000025,
        expected_latency_ms=250,
        cost_per_input_token=0.00000025,
        cost_per_output_token=0.00000125,
        capabilities=["chat", "text-completion", "analysis"],
        version="claude-3-haiku-20240307",
        owner="Anthropic",
        status="active",
        documentation_url="https://docs.anthropic.com/claude/docs/models-overview",
        license="Proprietary",
        input_context_length=200000,
        output_context_length=4096,
        provider="Anthropic",
        region="global",
        description="Anthropic's fast and cost-effective Claude 3 Haiku model.",
        tier=1
    ))

    # Anthropic Claude 3 Sonnet
    model_profiles_data.append(ModelProfile(
        id="claude-3-sonnet",
        name="Claude 3 Sonnet (External)",
        backend_type="anthropic-external",
        url="https://api.anthropic.com/v1/messages",
        api_key=anthropic_api_key,
        expected_cost=0.000003,
        expected_latency_ms=400,
        cost_per_input_token=0.000003,
        cost_per_output_token=0.000015,
        capabilities=["chat", "text-completion", "analysis", "reasoning"],
        version="claude-3-sonnet-20240229",
        owner="Anthropic",
        status="active",
        documentation_url="https://docs.anthropic.com/claude/docs/models-overview",
        license="Proprietary",
        input_context_length=200000,
        output_context_length=4096,
        provider="Anthropic",
        region="global",
        description="Anthropic's balanced Claude 3 Sonnet model for complex tasks.",
        tier=2
    ))

    # Google Gemini Pro
    model_profiles_data.append(ModelProfile(
        id="gemini-pro",
        name="Gemini Pro (External)",
        backend_type="google-external",
        url="https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent",
        api_key=google_api_key,
        expected_cost=0.0000005,
        expected_latency_ms=300,
        cost_per_input_token=0.0000005,
        cost_per_output_token=0.0000015,
        capabilities=["chat", "text-completion", "multimodal"],
        version="gemini-pro",
        owner="Google",
        status="active",
        documentation_url="https://ai.google.dev/models/gemini",
        license="Proprietary",
        input_context_length=30720,
        output_context_length=2048,
        provider="Google",
        region="global",
        description="Google's Gemini Pro model for text and multimodal tasks.",
        tier=2
    ))

    # Google Gemini 2.5 Flash Preview 05-20 (Current recommendation)
    model_profiles_data.append(ModelProfile(
        id="gemini-2.5-flash-preview-05-20",
        name="Gemini 2.5 Flash Preview (External)",
        backend_type="google-external",
        url="https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent",
        api_key=google_api_key,
        expected_cost=0.0000003,
        expected_latency_ms=90,
        cost_per_input_token=0.0000003,
        cost_per_output_token=0.0000006,
        capabilities=["chat", "text-completion", "multimodal", "long-context"],
        version="2.5-flash-preview-05-20",
        owner="Google DeepMind",
        status="active",
        documentation_url="https://ai.google.dev/gemini-api/docs/models",
        license="Proprietary",
        input_context_length=1048576,
        output_context_length=65536,
        provider="Google",
        region="global",
        description="Google Gemini 2.5 Flash Preview, suitable for synthetic data generation and general high-performance tasks.",
        tier=2
    ))

    # Cohere Command R+
    if cohere_api_key:
        model_profiles_data.append(ModelProfile(
            id="command-r-plus",
            name="Command R+ (External)",
            backend_type="cohere-external",
            url="https://api.cohere.ai/v1/chat",
            api_key=cohere_api_key,
            expected_cost=0.000003,
            expected_latency_ms=400,
            cost_per_input_token=0.000003,
            cost_per_output_token=0.000015,
            capabilities=["chat", "text-completion", "reasoning", "rag"],
            version="command-r-plus-04-2024",
            owner="Cohere",
            status="active",
            documentation_url="https://docs.cohere.com/docs/command-r-plus",
            license="Proprietary",
            input_context_length=128000,
            output_context_length=4096,
            provider="Cohere",
            region="global",
            description="Cohere's Command R+ model optimized for RAG and complex reasoning.",
            tier=2
        ))

    # Mistral Large
    if mistral_api_key:
        model_profiles_data.append(ModelProfile(
            id="mistral-large-latest",
            name="Mistral Large (External)",
            backend_type="mistral-external",
            url="https://api.mistral.ai/v1/chat/completions",
            api_key=mistral_api_key,
            expected_cost=0.000008,
            expected_latency_ms=500,
            cost_per_input_token=0.000008,
            cost_per_output_token=0.000024,
            capabilities=["chat", "text-completion", "reasoning", "code-generation"],
            version="mistral-large-2407",
            owner="Mistral AI",
            status="active",
            documentation_url="https://docs.mistral.ai/getting-started/models/",
            license="Proprietary",
            input_context_length=128000,
            output_context_length=4096,
            provider="Mistral",
            region="global",
            description="Mistral's flagship large model for complex reasoning tasks.",
            tier=3
        ))

    # Grok Beta
    if grok_api_key:
        model_profiles_data.append(ModelProfile(
            id="grok-beta",
            name="Grok Beta (External)",
            backend_type="grok-external",
            url="https://api.x.ai/v1/chat/completions",
            api_key=grok_api_key,
            expected_cost=0.000005,
            expected_latency_ms=600,
            cost_per_input_token=0.000005,
            cost_per_output_token=0.000015,
            capabilities=["chat", "text-completion", "reasoning", "real-time"],
            version="grok-beta",
            owner="xAI",
            status="active",
            documentation_url="https://docs.x.ai/api",
            license="Proprietary",
            input_context_length=131072,
            output_context_length=4096,
            provider="xAI",
            region="global",
            description="xAI's Grok model with real-time information access.",
            tier=2
        ))

    # HuggingFace Llama 3.1 70B (via Inference API)
    if huggingface_api_key:
        model_profiles_data.append(ModelProfile(
            id="meta-llama-3.1-70b-instruct",
            name="Llama 3.1 70B Instruct (HF)",
            backend_type="huggingface-external",
            url="https://api-inference.huggingface.co/models/meta-llama/Meta-Llama-3.1-70B-Instruct",
            api_key=huggingface_api_key,
            expected_cost=0.000004,
            expected_latency_ms=800,
            cost_per_input_token=0.000004,
            cost_per_output_token=0.000004,
            capabilities=["chat", "text-completion", "reasoning", "code-generation"],
            version="3.1-70b-instruct",
            owner="Meta",
            status="active",
            documentation_url="https://huggingface.co/meta-llama/Meta-Llama-3.1-70B-Instruct",
            license="Llama 3.1 Community License",
            input_context_length=131072,
            output_context_length=4096,
            provider="HuggingFace",
            region="global",
            description="Meta's Llama 3.1 70B model via HuggingFace Inference API.",
            tier=2
        ))

    # Store profiles in Redis
    for profile in model_profiles_data:
        key = f"model_profile:{profile.id}"
        try:
            r.set(key, json.dumps(profile.to_dict()))
            print(f"✅ Stored model profile: {profile.id} ({profile.name})")
        except Exception as e:
            print(f"❌ Failed to store {profile.id}: {e}")

    print(f"\n✅ Successfully populated {len(model_profiles_data)} external model profiles")

def populate_routing_strategies():
    """Create routing strategies that the AI Optimizer can understand."""
    routing_strategies_data = []

    # Strategy 1: Default routing for factual queries to cost-effective models
    routing_strategies_data.append(RoutingStrategy(
        id="factual-query-default",
        name="Factual Query Default Strategy",
        strategy="default",
        task_type="factual_query",
        model_priorities=["gpt-3.5-turbo", "gemini-2.5-flash-preview-05-20"],
        minimum_tier=1
    ))

    # Strategy 2: Creative writing to more capable models
    routing_strategies_data.append(RoutingStrategy(
        id="creative-writing-default",
        name="Creative Writing Default Strategy",
        strategy="default",
        task_type="creative_writing",
        model_priorities=["gpt-4o-mini", "claude-3-haiku", "gemini-2.5-flash-preview-05-20"],
        minimum_tier=2
    ))

    # Strategy 3: Simple chat to cheapest available
    routing_strategies_data.append(RoutingStrategy(
        id="simple-chat-default",
        name="Simple Chat Default Strategy",
        strategy="default",
        task_type="simple_chat",
        model_priorities=["gemini-2.5-flash-preview-05-20", "gpt-3.5-turbo"],
        minimum_tier=1
    ))

    # Strategy 4: Default fallback strategy (no specific task type)
    routing_strategies_data.append(RoutingStrategy(
        id="default-fallback",
        name="Default Fallback Strategy",
        strategy="default",
        task_type="",  # Empty means it applies to any task type
        model_priorities=["gemini-2.5-flash-preview-05-20", "gpt-3.5-turbo"],
        minimum_tier=1
    ))

    # Store routing strategies directly in Redis
    print("🚀 Populating routing strategies in Redis...")
    for strategy_obj in routing_strategies_data:
        try:
            redis_key = f"routing_strategy:{strategy_obj.id}"
            strategy_json = strategy_obj.to_json()
            r.set(redis_key, strategy_json)
            print(f"✅ Stored routing strategy: {strategy_obj.id} ({strategy_obj.name})")
        except Exception as e:
            print(f"❌ Failed to store routing strategy {strategy_obj.id}: {e}")

    print(f"\n✅ Successfully populated {len(routing_strategies_data)} routing strategies")

def populate_policies():
    """Create essential policies for external LLM access"""
    policies_data = []

    # Policy: Allow all authenticated users to access external models
    policies_data.append(Policy(
        name="Allow All Authenticated to External Models",
        description="Allows all authenticated users to access external LLM models.",
        effect="ALLOW",
        subjects=["authenticated"],
        resource_type="model",
        resource_ids=["gpt-3.5-turbo", "gpt-4", "gpt-4o-mini", "claude-3-haiku", "claude-3-sonnet", "gemini-pro", "gemini-2.5-flash-preview-05-20"],
        permissions=["access_llm"],
        priority=10,
        status="active"
    ))

    # Policy: Route requests for 'long-context' model to Google Gemini Flash
    policies_data.append(Policy(
        name="Long Context to Google Gemini Flash",
        description="Routes requests specifically asking for 'long-context' capability to Gemini 2.5 Flash.",
        criteria={"requested_model": "long-context", "capability": "long-context"},
        action="ROUTE",
        backend_id="gemini-2.5-flash-preview-05-20",
        priority=20,
        status="active"
    ))

    # Policy: Cost optimization for general queries
    policies_data.append(Policy(
        name="Cost Optimization for General Queries",
        description="Optimizes routing for general queries based on cost preference.",
        criteria={"task_type": "factual_query"},
        action="OPTIMIZE",
        rules={"optimization_goal": "cost", "preferred_backends": ["gpt-3.5-turbo", "gemini-2.5-flash-preview-05-20"]},
        priority=40,
        status="active"
    ))

    # Use policy-manager API to create policies
    policy_manager_url = "http://policy-manager:8083/api/policies"
    headers = {'Content-type': 'application/json'}

    print("🚀 Populating policies via policy-manager...")
    success_count = 0
    for policy_obj in policies_data:
        try:
            response = requests.post(policy_manager_url, data=policy_obj.to_json(), headers=headers, timeout=30)
            response.raise_for_status()
            print(f"✅ Created policy: {policy_obj.name}")
            success_count += 1
        except requests.exceptions.RequestException as e:
            print(f"❌ Failed to create policy '{policy_obj.name}': {e}")
        except Exception as e:
            print(f"❌ Unexpected error creating policy '{policy_obj.name}': {e}")

    print(f"\n✅ Successfully populated {success_count}/{len(policies_data)} policies")

def populate_prompts():
    """Create essential prompt templates"""
    prompts_data = []

    prompts_data.append(Prompt(
        id="summarization-template",
        name="Summarization Template",
        version="1.0",
        content="Please summarize the following text concisely:",
        description="A basic template for summarizing text.",
        tags=["text-summary", "template"],
        owner="Content Team",
        status="active",
        metadata={"category": "content-creation"}
    ))

    prompts_data.append(Prompt(
        id="code-generation-python",
        name="Python Code Generator",
        version="2.0",
        content="Generate a Python function that does the following: {description}",
        description="Template for generating Python code snippets.",
        tags=["code-gen", "python"],
        owner="Dev Team",
        status="active",
        metadata={"category": "code-generation", "language": "python"}
    ))

    prompts_data.append(Prompt(
        id="chat-assistant",
        name="Chat Assistant Template",
        version="1.0",
        content="You are a helpful AI assistant. Please respond to the user's question: {question}",
        description="Basic template for chat interactions.",
        tags=["chat", "assistant"],
        owner="AI Team",
        status="active",
        metadata={"category": "chat"}
    ))

    # Use policy-manager API to create prompts
    policy_manager_url = "http://policy-manager:8083/api/prompts"
    headers = {'Content-type': 'application/json'}

    print("🚀 Populating prompts via policy-manager...")
    success_count = 0
    for prompt_obj in prompts_data:
        try:
            response = requests.post(policy_manager_url, data=prompt_obj.to_json(), headers=headers, timeout=30)
            response.raise_for_status()
            print(f"✅ Created prompt: {prompt_obj.name}")
            success_count += 1
        except requests.exceptions.RequestException as e:
            print(f"❌ Failed to create prompt '{prompt_obj.name}': {e}")
        except Exception as e:
            print(f"❌ Unexpected error creating prompt '{prompt_obj.name}': {e}")

    print(f"\n✅ Successfully populated {success_count}/{len(prompts_data)} prompts")

if __name__ == "__main__":
    print("🚀 Populating Redis with external LLM API profiles, policies, and prompts...")

    try:
        print("📊 Step 1/4: Populating external model profiles...")
        populate_external_model_profiles()
        print("✅ Step 1/4 completed!")

        print("📋 Step 2/4: Populating policies...")
        populate_policies()
        print("✅ Step 2/4 completed!")

        print("📝 Step 3/4: Populating prompts...")
        populate_prompts()
        print("✅ Step 3/4 completed!")

        print("🔀 Step 4/4: Populating routing strategies...")
        populate_routing_strategies()
        print("✅ Step 4/4 completed!")

        print("✅ Redis population completed successfully!")

    except Exception as e:
        print(f"❌ Redis population failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
