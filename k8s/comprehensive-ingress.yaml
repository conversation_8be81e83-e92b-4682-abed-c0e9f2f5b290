apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: comprehensive-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Authentication service routing
      - path: /auth
        pathType: Prefix
        backend:
          service:
            name: auth-service
            port:
              number: 80

      # Landing page - highest priority, exact root match
      - path: /
        pathType: Exact
        backend:
          service:
            name: landing-page
            port:
              number: 80

      # Standard edition API routing (handled by separate ingress in standard-edition namespace)
      # This is just a fallback - actual routing is in standard-edition-api-ingress.yaml

      # Enterprise edition frontend routing (with rewrite support)
      - path: /enterprise(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: frontend-dashboard
            port:
              number: 80

      # Enterprise API routing is handled by enterprise-api-ingress.yaml

      # Enterprise dashboard API routing (legacy path)
      - path: /dashboard
        pathType: Prefix
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      # Enterprise planning service routing
      - path: /planning
        pathType: Prefix
        backend:
          service:
            name: planning-service
            port:
              number: 8084

      # Enterprise evaluation service routing
      - path: /evaluation
        pathType: Prefix
        backend:
          service:
            name: evaluation-service
            port:
              number: 8085

      # Enterprise integration service routing
      - path: /integration
        pathType: Prefix
        backend:
          service:
            name: integration-service
            port:
              number: 8086

      # Fallback to enterprise frontend for any unmatched paths
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend
            port:
              number: 80
