package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/gorilla/mux"
	"github.com/redis/go-redis/v9"
)

// SentimentAnalysisRequest represents a request for sentiment analysis
type SentimentAnalysisRequest struct {
	Text     string            `json:"text"`
	Source   string            `json:"source"`   // social_media, feedback, support, etc.
	Metadata map[string]string `json:"metadata"` // additional context
}

// SentimentResult represents the result of sentiment analysis
type SentimentResult struct {
	ID          string             `json:"id"`
	Text        string             `json:"text"`
	Source      string             `json:"source"`
	Sentiment   SentimentScores    `json:"sentiment"`
	Emotions    EmotionScores      `json:"emotions"`
	Topics      []string           `json:"topics"`
	Keywords    []string           `json:"keywords"`
	Intent      string             `json:"intent"`
	Urgency     string             `json:"urgency"`
	Confidence  float64            `json:"confidence"`
	Insights    []string           `json:"insights"`
	Timestamp   time.Time          `json:"timestamp"`
	Metadata    map[string]string  `json:"metadata"`
	Suggestions []ActionSuggestion `json:"suggestions"`
}

// SentimentScores represents sentiment analysis scores
type SentimentScores struct {
	Positive float64 `json:"positive"`
	Neutral  float64 `json:"neutral"`
	Negative float64 `json:"negative"`
	Overall  string  `json:"overall"` // positive, neutral, negative
}

// EmotionScores represents emotion detection scores
type EmotionScores struct {
	Joy        float64 `json:"joy"`
	Anger      float64 `json:"anger"`
	Fear       float64 `json:"fear"`
	Sadness    float64 `json:"sadness"`
	Surprise   float64 `json:"surprise"`
	Disgust    float64 `json:"disgust"`
	Trust      float64 `json:"trust"`
	Excitement float64 `json:"excitement"`
}

// ActionSuggestion represents suggested actions based on sentiment
type ActionSuggestion struct {
	Type       string `json:"type"`       // response, escalation, improvement
	Priority   string `json:"priority"`   // high, medium, low
	Action     string `json:"action"`     // specific action to take
	Reason     string `json:"reason"`     // why this action is suggested
	Department string `json:"department"` // which team should handle
}

// SentimentTrend represents sentiment trends over time
type SentimentTrend struct {
	Period    string          `json:"period"`
	Sentiment SentimentScores `json:"sentiment"`
	Volume    int             `json:"volume"`
	Topics    []string        `json:"topics"`
}

// SentimentService handles sentiment analysis operations
type SentimentService struct {
	redisClient *redis.Client
	llmClient   *LLMClient
}

// LLMClient handles communication with external LLMs
type LLMClient struct {
	baseURL string
	apiKey  string
}

func main() {
	// Initialize Redis client
	redisClient := redis.NewClient(&redis.Options{
		Addr:     getEnv("REDIS_ADDR", "redis:6379"),
		Password: getEnv("REDIS_PASSWORD", ""),
		DB:       0,
	})

	// Test Redis connection
	ctx := context.Background()
	_, err := redisClient.Ping(ctx).Result()
	if err != nil {
		log.Printf("Warning: Redis connection failed: %v", err)
	}

	// Initialize LLM client
	llmClient := &LLMClient{
		baseURL: getEnv("LLM_BASE_URL", "http://proxy-gateway:8080"),
		apiKey:  getEnv("LLM_API_KEY", ""),
	}

	// Initialize sentiment service
	sentimentService := &SentimentService{
		redisClient: redisClient,
		llmClient:   llmClient,
	}

	// Set up HTTP routes
	router := mux.NewRouter()

	// Health check
	router.HandleFunc("/health", healthHandler).Methods("GET")

	// Sentiment analysis endpoints
	router.HandleFunc("/analyze", sentimentService.analyzeHandler).Methods("POST")
	router.HandleFunc("/batch-analyze", sentimentService.batchAnalyzeHandler).Methods("POST")
	router.HandleFunc("/trends", sentimentService.trendsHandler).Methods("GET")
	router.HandleFunc("/insights", sentimentService.insightsHandler).Methods("GET")
	router.HandleFunc("/social-monitoring", sentimentService.socialMonitoringHandler).Methods("GET", "POST")

	// Real-time sentiment streaming
	router.HandleFunc("/stream", sentimentService.streamHandler).Methods("GET")

	// Sentiment-driven recommendations
	router.HandleFunc("/recommendations", sentimentService.recommendationsHandler).Methods("GET")

	// Start the HTTP server
	port := getEnv("PORT", "8088")
	fmt.Printf("Sentiment Analysis Service listening on port %s...\n", port)
	log.Fatal(http.ListenAndServe(":"+port, router))
}

func healthHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":    "healthy",
		"service":   "sentiment-service",
		"timestamp": time.Now().UTC(),
		"version":   "1.0.0",
	})
}

// analyzeHandler handles single sentiment analysis requests
func (s *SentimentService) analyzeHandler(w http.ResponseWriter, r *http.Request) {
	var req SentimentAnalysisRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if req.Text == "" {
		http.Error(w, "Text is required", http.StatusBadRequest)
		return
	}

	result, err := s.analyzeSentiment(r.Context(), req)
	if err != nil {
		log.Printf("Sentiment analysis failed: %v", err)
		http.Error(w, "Analysis failed", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(result)
}

// batchAnalyzeHandler handles batch sentiment analysis requests
func (s *SentimentService) batchAnalyzeHandler(w http.ResponseWriter, r *http.Request) {
	var requests []SentimentAnalysisRequest
	if err := json.NewDecoder(r.Body).Decode(&requests); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	results := make([]SentimentResult, 0, len(requests))
	for _, req := range requests {
		if req.Text == "" {
			continue
		}

		result, err := s.analyzeSentiment(r.Context(), req)
		if err != nil {
			log.Printf("Batch analysis failed for text: %v", err)
			continue
		}
		results = append(results, *result)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"results": results,
		"total":   len(results),
	})
}

// trendsHandler provides sentiment trends over time
func (s *SentimentService) trendsHandler(w http.ResponseWriter, r *http.Request) {
	source := r.URL.Query().Get("source")
	period := r.URL.Query().Get("period") // hour, day, week, month
	if period == "" {
		period = "day"
	}

	trends, err := s.getSentimentTrends(r.Context(), source, period)
	if err != nil {
		log.Printf("Failed to get trends: %v", err)
		http.Error(w, "Failed to get trends", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"trends": trends,
		"period": period,
		"source": source,
	})
}

// insightsHandler provides sentiment-driven insights
func (s *SentimentService) insightsHandler(w http.ResponseWriter, r *http.Request) {
	insights, err := s.generateInsights(r.Context())
	if err != nil {
		log.Printf("Failed to generate insights: %v", err)
		http.Error(w, "Failed to generate insights", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(insights)
}

// socialMonitoringHandler handles social media monitoring
func (s *SentimentService) socialMonitoringHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method == "GET" {
		// Get current social monitoring status
		status, err := s.getSocialMonitoringStatus(r.Context())
		if err != nil {
			http.Error(w, "Failed to get monitoring status", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(status)
		return
	}

	// POST: Configure social monitoring
	var config map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&config); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	err := s.configureSocialMonitoring(r.Context(), config)
	if err != nil {
		http.Error(w, "Failed to configure monitoring", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"status": "configured"})
}

// streamHandler provides real-time sentiment streaming
func (s *SentimentService) streamHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("Access-Control-Allow-Origin", "*")

	// Stream sentiment updates
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// Get latest sentiment data
			data, err := s.getLatestSentimentData(r.Context())
			if err != nil {
				continue
			}

			fmt.Fprintf(w, "data: %s\n\n", data)
			if f, ok := w.(http.Flusher); ok {
				f.Flush()
			}
		case <-r.Context().Done():
			return
		}
	}
}

// recommendationsHandler provides sentiment-driven recommendations
func (s *SentimentService) recommendationsHandler(w http.ResponseWriter, r *http.Request) {
	recommendations, err := s.generateRecommendations(r.Context())
	if err != nil {
		log.Printf("Failed to generate recommendations: %v", err)
		http.Error(w, "Failed to generate recommendations", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(recommendations)
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
