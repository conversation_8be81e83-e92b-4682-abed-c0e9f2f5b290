package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"
	"crypto/rand"
	"math/big"
)

// analyzeSentiment performs comprehensive sentiment analysis using LLM
func (s *SentimentService) analyzeSentiment(ctx context.Context, req SentimentAnalysisRequest) (*SentimentResult, error) {
	// Generate unique ID
	id, err := generateID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate ID: %v", err)
	}

	// Build analysis prompt
	prompt := s.buildSentimentPrompt(req.Text, req.Source)

	// Call LLM for analysis
	response, err := s.llmClient.generateResponse(ctx, prompt)
	if err != nil {
		// Fallback to basic analysis
		return s.performBasicSentimentAnalysis(id, req), nil
	}

	// Parse LLM response
	result, err := s.parseSentimentResponse(id, req, response)
	if err != nil {
		// Fallback to basic analysis
		return s.performBasicSentimentAnalysis(id, req), nil
	}

	// Store result in Redis
	if s.redisClient != nil {
		s.storeSentimentResult(ctx, result)
	}

	return result, nil
}

// buildSentimentPrompt creates a comprehensive prompt for sentiment analysis
func (s *SentimentService) buildSentimentPrompt(text, source string) string {
	return fmt.Sprintf(`Analyze the following text for comprehensive sentiment and emotional intelligence:

Text: "%s"
Source: %s

Please provide a detailed analysis including:

1. **Sentiment Scores** (0.0-1.0):
   - Positive sentiment score
   - Neutral sentiment score  
   - Negative sentiment score
   - Overall sentiment classification

2. **Emotion Detection** (0.0-1.0):
   - Joy, Anger, Fear, Sadness, Surprise, Disgust, Trust, Excitement

3. **Content Analysis**:
   - Key topics and themes
   - Important keywords
   - Intent classification (complaint, praise, question, request, etc.)
   - Urgency level (low, medium, high, critical)

4. **Actionable Insights**:
   - Key insights from the text
   - Suggested actions with priorities
   - Recommended department/team to handle
   - Confidence score for the analysis

Format your response as JSON with this exact structure:
{
  "sentiment": {
    "positive": 0.0,
    "neutral": 0.0,
    "negative": 0.0,
    "overall": "positive|neutral|negative"
  },
  "emotions": {
    "joy": 0.0,
    "anger": 0.0,
    "fear": 0.0,
    "sadness": 0.0,
    "surprise": 0.0,
    "disgust": 0.0,
    "trust": 0.0,
    "excitement": 0.0
  },
  "topics": ["topic1", "topic2"],
  "keywords": ["keyword1", "keyword2"],
  "intent": "intent_classification",
  "urgency": "low|medium|high|critical",
  "insights": ["insight1", "insight2"],
  "confidence": 0.0,
  "suggestions": [
    {
      "type": "response|escalation|improvement",
      "priority": "high|medium|low",
      "action": "specific action description",
      "reason": "why this action is suggested",
      "department": "team that should handle this"
    }
  ]
}`, text, source)
}

// generateResponse calls the LLM service for text generation
func (llm *LLMClient) generateResponse(ctx context.Context, prompt string) (string, error) {
	requestBody := map[string]interface{}{
		"model": "gemini-2.0-flash-thinking-exp",
		"messages": []map[string]string{
			{
				"role":    "user",
				"content": prompt,
			},
		},
		"temperature": 0.3,
		"max_tokens":  2000,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %v", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", llm.baseURL+"/v1/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if llm.apiKey != "" {
		req.Header.Set("Authorization", "Bearer "+llm.apiKey)
	}

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	var response map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %v", err)
	}

	choices, ok := response["choices"].([]interface{})
	if !ok || len(choices) == 0 {
		return "", fmt.Errorf("no choices in response")
	}

	choice, ok := choices[0].(map[string]interface{})
	if !ok {
		return "", fmt.Errorf("invalid choice format")
	}

	message, ok := choice["message"].(map[string]interface{})
	if !ok {
		return "", fmt.Errorf("invalid message format")
	}

	content, ok := message["content"].(string)
	if !ok {
		return "", fmt.Errorf("invalid content format")
	}

	return content, nil
}

// parseSentimentResponse parses LLM response into SentimentResult
func (s *SentimentService) parseSentimentResponse(id string, req SentimentAnalysisRequest, response string) (*SentimentResult, error) {
	// Extract JSON from response
	jsonStart := strings.Index(response, "{")
	jsonEnd := strings.LastIndex(response, "}") + 1
	
	if jsonStart == -1 || jsonEnd <= jsonStart {
		return nil, fmt.Errorf("no valid JSON found in response")
	}

	jsonStr := response[jsonStart:jsonEnd]
	
	var parsed map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &parsed); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %v", err)
	}

	result := &SentimentResult{
		ID:        id,
		Text:      req.Text,
		Source:    req.Source,
		Timestamp: time.Now().UTC(),
		Metadata:  req.Metadata,
	}

	// Parse sentiment scores
	if sentiment, ok := parsed["sentiment"].(map[string]interface{}); ok {
		result.Sentiment = SentimentScores{
			Positive: getFloat64(sentiment["positive"]),
			Neutral:  getFloat64(sentiment["neutral"]),
			Negative: getFloat64(sentiment["negative"]),
			Overall:  getString(sentiment["overall"]),
		}
	}

	// Parse emotions
	if emotions, ok := parsed["emotions"].(map[string]interface{}); ok {
		result.Emotions = EmotionScores{
			Joy:        getFloat64(emotions["joy"]),
			Anger:      getFloat64(emotions["anger"]),
			Fear:       getFloat64(emotions["fear"]),
			Sadness:    getFloat64(emotions["sadness"]),
			Surprise:   getFloat64(emotions["surprise"]),
			Disgust:    getFloat64(emotions["disgust"]),
			Trust:      getFloat64(emotions["trust"]),
			Excitement: getFloat64(emotions["excitement"]),
		}
	}

	// Parse other fields
	result.Topics = getStringArray(parsed["topics"])
	result.Keywords = getStringArray(parsed["keywords"])
	result.Intent = getString(parsed["intent"])
	result.Urgency = getString(parsed["urgency"])
	result.Insights = getStringArray(parsed["insights"])
	result.Confidence = getFloat64(parsed["confidence"])

	// Parse suggestions
	if suggestions, ok := parsed["suggestions"].([]interface{}); ok {
		for _, s := range suggestions {
			if suggestion, ok := s.(map[string]interface{}); ok {
				result.Suggestions = append(result.Suggestions, ActionSuggestion{
					Type:       getString(suggestion["type"]),
					Priority:   getString(suggestion["priority"]),
					Action:     getString(suggestion["action"]),
					Reason:     getString(suggestion["reason"]),
					Department: getString(suggestion["department"]),
				})
			}
		}
	}

	return result, nil
}

// performBasicSentimentAnalysis provides fallback analysis
func (s *SentimentService) performBasicSentimentAnalysis(id string, req SentimentAnalysisRequest) *SentimentResult {
	text := strings.ToLower(req.Text)
	
	// Basic sentiment detection
	positiveWords := []string{"good", "great", "excellent", "amazing", "love", "like", "happy", "satisfied", "wonderful"}
	negativeWords := []string{"bad", "terrible", "awful", "hate", "dislike", "angry", "frustrated", "disappointed", "horrible"}
	
	positiveCount := 0
	negativeCount := 0
	
	for _, word := range positiveWords {
		if strings.Contains(text, word) {
			positiveCount++
		}
	}
	
	for _, word := range negativeWords {
		if strings.Contains(text, word) {
			negativeCount++
		}
	}
	
	var sentiment SentimentScores
	if positiveCount > negativeCount {
		sentiment = SentimentScores{Positive: 0.7, Neutral: 0.2, Negative: 0.1, Overall: "positive"}
	} else if negativeCount > positiveCount {
		sentiment = SentimentScores{Positive: 0.1, Neutral: 0.2, Negative: 0.7, Overall: "negative"}
	} else {
		sentiment = SentimentScores{Positive: 0.3, Neutral: 0.4, Negative: 0.3, Overall: "neutral"}
	}

	return &SentimentResult{
		ID:        id,
		Text:      req.Text,
		Source:    req.Source,
		Sentiment: sentiment,
		Emotions:  EmotionScores{Trust: 0.5},
		Topics:    []string{"general"},
		Keywords:  []string{"feedback"},
		Intent:    "general",
		Urgency:   "medium",
		Confidence: 0.6,
		Insights:   []string{"Basic sentiment analysis performed"},
		Timestamp:  time.Now().UTC(),
		Metadata:   req.Metadata,
		Suggestions: []ActionSuggestion{
			{
				Type:       "response",
				Priority:   "medium",
				Action:     "Review and respond to feedback",
				Reason:     "Customer feedback requires attention",
				Department: "customer_service",
			},
		},
	}
}

// Helper functions
func generateID() (string, error) {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, 12)
	for i := range b {
		n, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			return "", err
		}
		b[i] = charset[n.Int64()]
	}
	return string(b), nil
}

func getFloat64(v interface{}) float64 {
	if f, ok := v.(float64); ok {
		return f
	}
	return 0.0
}

func getString(v interface{}) string {
	if s, ok := v.(string); ok {
		return s
	}
	return ""
}

func getStringArray(v interface{}) []string {
	if arr, ok := v.([]interface{}); ok {
		result := make([]string, 0, len(arr))
		for _, item := range arr {
			if s, ok := item.(string); ok {
				result = append(result, s)
			}
		}
		return result
	}
	return []string{}
}
