package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"
)

// storeSentimentResult stores sentiment analysis result in Redis
func (s *SentimentService) storeSentimentResult(ctx context.Context, result *SentimentResult) error {
	if s.redisClient == nil {
		return fmt.Errorf("redis client not available")
	}

	// Store individual result
	key := fmt.Sprintf("sentiment:result:%s", result.ID)
	data, err := json.Marshal(result)
	if err != nil {
		return fmt.Errorf("failed to marshal result: %v", err)
	}

	err = s.redisClient.Set(ctx, key, data, 24*time.Hour).Err()
	if err != nil {
		return fmt.Errorf("failed to store result: %v", err)
	}

	// Add to time-series data for trends
	timeKey := fmt.Sprintf("sentiment:trends:%s:%s", result.Source, time.Now().Format("2006-01-02-15"))
	trendData := map[string]any{
		"timestamp": result.Timestamp,
		"sentiment": result.Sentiment,
		"source":    result.Source,
		"urgency":   result.Urgency,
	}

	trendJSON, _ := json.Marshal(trendData)
	s.redisClient.LPush(ctx, timeKey, trendJSON)
	s.redisClient.Expire(ctx, timeKey, 7*24*time.Hour) // Keep for 7 days

	return nil
}

// getSentimentTrends retrieves sentiment trends over time
func (s *SentimentService) getSentimentTrends(ctx context.Context, source, period string) ([]SentimentTrend, error) {
	if s.redisClient == nil {
		return s.getMockTrends(source, period), nil
	}

	var trends []SentimentTrend
	now := time.Now()

	// Determine time range based on period
	var timeFormat string
	var duration time.Duration
	var intervals int

	switch period {
	case "hour":
		timeFormat = "2006-01-02-15"
		duration = time.Hour
		intervals = 24
	case "day":
		timeFormat = "2006-01-02"
		duration = 24 * time.Hour
		intervals = 7
	case "week":
		timeFormat = "2006-W02"
		duration = 7 * 24 * time.Hour
		intervals = 4
	case "month":
		timeFormat = "2006-01"
		duration = 30 * 24 * time.Hour
		intervals = 12
	default:
		timeFormat = "2006-01-02"
		duration = 24 * time.Hour
		intervals = 7
	}

	for i := 0; i < intervals; i++ {
		periodTime := now.Add(-time.Duration(i) * duration)
		periodKey := periodTime.Format(timeFormat)

		var keyPattern string
		if source != "" {
			keyPattern = fmt.Sprintf("sentiment:trends:%s:%s", source, periodKey)
		} else {
			keyPattern = fmt.Sprintf("sentiment:trends:*:%s", periodKey)
		}

		// Get data for this period
		keys, err := s.redisClient.Keys(ctx, keyPattern).Result()
		if err != nil {
			continue
		}

		var totalPositive, totalNeutral, totalNegative float64
		var volume int
		topicsMap := make(map[string]int)

		for _, key := range keys {
			data, err := s.redisClient.LRange(ctx, key, 0, -1).Result()
			if err != nil {
				continue
			}

			for _, item := range data {
				var trendData map[string]any
				if err := json.Unmarshal([]byte(item), &trendData); err != nil {
					continue
				}

				if sentiment, ok := trendData["sentiment"].(map[string]any); ok {
					totalPositive += getFloat64(sentiment["positive"])
					totalNeutral += getFloat64(sentiment["neutral"])
					totalNegative += getFloat64(sentiment["negative"])
					volume++
				}
			}
		}

		if volume > 0 {
			trend := SentimentTrend{
				Period: periodKey,
				Sentiment: SentimentScores{
					Positive: totalPositive / float64(volume),
					Neutral:  totalNeutral / float64(volume),
					Negative: totalNegative / float64(volume),
				},
				Volume: volume,
				Topics: getTopTopics(topicsMap, 5),
			}

			// Determine overall sentiment
			if trend.Sentiment.Positive > trend.Sentiment.Negative {
				trend.Sentiment.Overall = "positive"
			} else if trend.Sentiment.Negative > trend.Sentiment.Positive {
				trend.Sentiment.Overall = "negative"
			} else {
				trend.Sentiment.Overall = "neutral"
			}

			trends = append(trends, trend)
		}
	}

	return trends, nil
}

// generateInsights creates actionable insights from sentiment data
func (s *SentimentService) generateInsights(ctx context.Context) (map[string]any, error) {
	// Get recent sentiment data
	trends, err := s.getSentimentTrends(ctx, "", "day")
	if err != nil {
		return s.getMockInsights(), nil
	}

	insights := map[string]any{
		"summary":         s.generateSummaryInsights(trends),
		"alerts":          s.generateAlerts(trends),
		"recommendations": s.generateActionableRecommendations(trends),
		"metrics":         s.calculateKeyMetrics(trends),
		"timestamp":       time.Now().UTC(),
	}

	return insights, nil
}

// getSocialMonitoringStatus returns current social monitoring configuration
func (s *SentimentService) getSocialMonitoringStatus(ctx context.Context) (map[string]any, error) {
	if s.redisClient == nil {
		return s.getMockSocialStatus(), nil
	}

	configKey := "sentiment:social:config"
	config, err := s.redisClient.Get(ctx, configKey).Result()
	if err != nil {
		return s.getMockSocialStatus(), nil
	}

	var status map[string]any
	if err := json.Unmarshal([]byte(config), &status); err != nil {
		return s.getMockSocialStatus(), nil
	}

	// Add real-time metrics
	status["last_updated"] = time.Now().UTC()
	status["active_monitors"] = s.getActiveMonitorCount(ctx)
	status["recent_mentions"] = s.getRecentMentionCount(ctx)

	return status, nil
}

// configureSocialMonitoring sets up social media monitoring
func (s *SentimentService) configureSocialMonitoring(ctx context.Context, config map[string]any) error {
	if s.redisClient == nil {
		log.Printf("Social monitoring configured: %+v", config)
		return nil
	}

	configKey := "sentiment:social:config"
	data, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %v", err)
	}

	return s.redisClient.Set(ctx, configKey, data, 0).Err()
}

// getLatestSentimentData returns latest sentiment data for streaming
func (s *SentimentService) getLatestSentimentData(ctx context.Context) (string, error) {
	log.Printf("Retrieving latest sentiment data for streaming")

	// Check context for cancellation
	select {
	case <-ctx.Done():
		return "", ctx.Err()
	default:
	}

	data := map[string]any{
		"timestamp": time.Now().UTC(),
		"sentiment": map[string]float64{
			"positive": 0.65,
			"neutral":  0.25,
			"negative": 0.10,
		},
		"volume":          42,
		"trending_topics": []string{"product_feedback", "customer_service", "feature_requests"},
		"alerts": []map[string]any{
			{
				"type":     "negative_spike",
				"severity": "medium",
				"message":  "Negative sentiment increased by 15% in the last hour",
			},
		},
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", err
	}

	return string(jsonData), nil
}

// generateRecommendations creates sentiment-driven recommendations
func (s *SentimentService) generateRecommendations(ctx context.Context) (map[string]any, error) {
	log.Printf("Generating sentiment-driven recommendations")

	// Check context for cancellation
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	recommendations := map[string]any{
		"immediate_actions": []map[string]any{
			{
				"priority":   "high",
				"action":     "Address negative feedback about response times",
				"department": "customer_service",
				"impact":     "Reduce negative sentiment by 20%",
				"timeline":   "24 hours",
			},
			{
				"priority":   "medium",
				"action":     "Amplify positive feedback about new features",
				"department": "marketing",
				"impact":     "Increase positive sentiment by 15%",
				"timeline":   "1 week",
			},
		},
		"strategic_initiatives": []map[string]any{
			{
				"initiative":      "Proactive Customer Outreach Program",
				"description":     "Implement automated sentiment-based customer outreach",
				"expected_impact": "30% improvement in customer satisfaction",
				"timeline":        "1 month",
			},
			{
				"initiative":      "Social Media Response Automation",
				"description":     "Deploy AI-powered social media response system",
				"expected_impact": "50% faster response times",
				"timeline":        "2 weeks",
			},
		},
		"product_improvements": []map[string]any{
			{
				"area":       "User Interface",
				"feedback":   "Users find navigation confusing",
				"suggestion": "Redesign main navigation based on user feedback",
				"priority":   "high",
			},
			{
				"area":       "Performance",
				"feedback":   "App loading times are slow",
				"suggestion": "Optimize backend performance and caching",
				"priority":   "medium",
			},
		},
		"timestamp": time.Now().UTC(),
	}

	return recommendations, nil
}

// Helper methods for mock data when Redis is unavailable
func (s *SentimentService) getMockTrends(source, period string) []SentimentTrend {
	log.Printf("Generating mock sentiment trends for source: %s, period: %s", source, period)
	return []SentimentTrend{
		{
			Period: "2024-01-15",
			Sentiment: SentimentScores{
				Positive: 0.65,
				Neutral:  0.25,
				Negative: 0.10,
				Overall:  "positive",
			},
			Volume: 150,
			Topics: []string{"product_feedback", "customer_service"},
		},
		{
			Period: "2024-01-14",
			Sentiment: SentimentScores{
				Positive: 0.60,
				Neutral:  0.30,
				Negative: 0.10,
				Overall:  "positive",
			},
			Volume: 120,
			Topics: []string{"feature_requests", "support"},
		},
	}
}

func (s *SentimentService) getMockInsights() map[string]any {
	return map[string]any{
		"summary":         "Overall sentiment is positive with 65% positive feedback",
		"alerts":          []string{"Negative sentiment spike detected in customer service"},
		"recommendations": []string{"Focus on improving response times", "Amplify positive product feedback"},
		"metrics": map[string]float64{
			"satisfaction_score": 7.8,
			"sentiment_trend":    0.15,
			"response_rate":      0.85,
		},
		"timestamp": time.Now().UTC(),
	}
}

func (s *SentimentService) getMockSocialStatus() map[string]any {
	return map[string]any{
		"platforms": map[string]bool{
			"twitter":   true,
			"facebook":  false,
			"instagram": true,
			"linkedin":  false,
		},
		"keywords":        []string{"AI Operations Hub", "scale-llm", "customer feedback"},
		"active_monitors": 3,
		"recent_mentions": 25,
		"last_updated":    time.Now().UTC(),
	}
}

// Additional helper methods
func (s *SentimentService) generateSummaryInsights(trends []SentimentTrend) string {
	if len(trends) == 0 {
		return "No recent sentiment data available"
	}

	avgPositive := 0.0
	for _, trend := range trends {
		avgPositive += trend.Sentiment.Positive
	}
	avgPositive /= float64(len(trends))

	if avgPositive > 0.6 {
		return "Overall sentiment is positive with strong customer satisfaction"
	} else if avgPositive > 0.4 {
		return "Sentiment is neutral with mixed customer feedback"
	} else {
		return "Negative sentiment detected - immediate attention required"
	}
}

func (s *SentimentService) generateAlerts(trends []SentimentTrend) []string {
	alerts := []string{}

	if len(trends) >= 2 {
		recent := trends[0]
		previous := trends[1]

		if recent.Sentiment.Negative > previous.Sentiment.Negative+0.1 {
			alerts = append(alerts, "Negative sentiment spike detected")
		}

		if float64(recent.Volume) < float64(previous.Volume)*0.5 {
			alerts = append(alerts, "Significant drop in feedback volume")
		}
	}

	return alerts
}

func (s *SentimentService) generateActionableRecommendations(trends []SentimentTrend) []string {
	log.Printf("Generating actionable recommendations based on %d sentiment trends", len(trends))
	recommendations := []string{
		"Monitor customer service response times",
		"Amplify positive feedback on social media",
		"Address common complaint themes",
		"Implement proactive customer outreach",
	}

	return recommendations
}

func (s *SentimentService) calculateKeyMetrics(trends []SentimentTrend) map[string]float64 {
	if len(trends) == 0 {
		return map[string]float64{
			"satisfaction_score": 5.0,
			"sentiment_trend":    0.0,
			"response_rate":      0.0,
		}
	}

	avgSentiment := 0.0
	totalVolume := 0

	for _, trend := range trends {
		avgSentiment += trend.Sentiment.Positive
		totalVolume += trend.Volume
	}

	avgSentiment /= float64(len(trends))

	return map[string]float64{
		"satisfaction_score": avgSentiment * 10, // Convert to 1-10 scale
		"sentiment_trend":    0.05,              // Mock trend
		"response_rate":      0.85,              // Mock response rate
	}
}

func (s *SentimentService) getActiveMonitorCount(ctx context.Context) int {
	log.Printf("Retrieving active monitor count")

	// Check context for cancellation
	select {
	case <-ctx.Done():
		return 0
	default:
	}

	return 3 // Mock value
}

func (s *SentimentService) getRecentMentionCount(ctx context.Context) int {
	log.Printf("Retrieving recent mention count")

	// Check context for cancellation
	select {
	case <-ctx.Done():
		return 0
	default:
	}

	return 25 // Mock value
}

func getTopTopics(topicsMap map[string]int, limit int) []string {
	// Simple implementation - in production, sort by frequency
	topics := []string{}
	count := 0
	for topic := range topicsMap {
		if count >= limit {
			break
		}
		topics = append(topics, topic)
		count++
	}
	return topics
}
