package main

import (
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/rs/cors"
)

// ExplanationRequest represents a request for AI explanation
type ExplanationRequest struct {
	ID                string                 `json:"id"`
	Type              string                 `json:"type"` // "agent_selection", "planning_decision", "llm_response"
	ModelID           string                 `json:"model_id"`
	AgentID           string                 `json:"agent_id,omitempty"`
	PlanID            string                 `json:"plan_id,omitempty"`
	Input             map[string]interface{} `json:"input"`
	Output            map[string]interface{} `json:"output"`
	ExplanationMethod string                 `json:"explanation_method"` // "lime", "shap", "integrated_gradients", "attention"
	Configuration     map[string]interface{} `json:"configuration,omitempty"`
	Timestamp         time.Time              `json:"timestamp"`
}

// Explanation represents the explanation result
type Explanation struct {
	ID                string                 `json:"id"`
	RequestID         string                 `json:"request_id"`
	Type              string                 `json:"type"`
	Method            string                 `json:"method"`
	ModelID           string                 `json:"model_id"`
	AgentID           string                 `json:"agent_id,omitempty"`
	PlanID            string                 `json:"plan_id,omitempty"`
	FeatureImportance []FeatureImportance    `json:"feature_importance"`
	LocalExplanation  map[string]interface{} `json:"local_explanation"`
	GlobalExplanation map[string]interface{} `json:"global_explanation,omitempty"`
	ConfidenceScore   float64                `json:"confidence_score"`
	ExplanationText   string                 `json:"explanation_text"`
	Visualizations    []Visualization        `json:"visualizations"`
	Metadata          map[string]interface{} `json:"metadata"`
	CreatedAt         time.Time              `json:"created_at"`
	UpdatedAt         time.Time              `json:"updated_at"`
}

// FeatureImportance represents the importance of individual features
type FeatureImportance struct {
	Feature    string  `json:"feature"`
	Importance float64 `json:"importance"`
	Direction  string  `json:"direction"` // "positive", "negative", "neutral"
	Confidence float64 `json:"confidence"`
}

// Visualization represents explanation visualizations
type Visualization struct {
	Type        string                 `json:"type"` // "bar_chart", "heatmap", "attention_map", "decision_tree"
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Data        map[string]interface{} `json:"data"`
	Config      map[string]interface{} `json:"config"`
}

// ExplainabilityService manages AI explanations
type ExplainabilityService struct {
	explanations map[string]Explanation
	requests     map[string]ExplanationRequest
	mu           sync.RWMutex
}

// NewExplainabilityService creates a new explainability service
func NewExplainabilityService() *ExplainabilityService {
	service := &ExplainabilityService{
		explanations: make(map[string]Explanation),
		requests:     make(map[string]ExplanationRequest),
	}

	// Initialize with sample data
	service.initializeSampleData()

	return service
}

// initializeSampleData creates sample explanations for demonstration
func (es *ExplainabilityService) initializeSampleData() {
	sampleExplanations := []Explanation{
		{
			ID:        "exp-001",
			RequestID: "req-001",
			Type:      "agent_selection",
			Method:    "shap",
			ModelID:   "agent-selector-v1",
			AgentID:   "agent-planning-001",
			FeatureImportance: []FeatureImportance{
				{Feature: "task_complexity", Importance: 0.35, Direction: "positive", Confidence: 0.92},
				{Feature: "agent_availability", Importance: 0.28, Direction: "positive", Confidence: 0.88},
				{Feature: "historical_performance", Importance: 0.22, Direction: "positive", Confidence: 0.85},
				{Feature: "cost_efficiency", Importance: 0.15, Direction: "negative", Confidence: 0.78},
			},
			LocalExplanation: map[string]interface{}{
				"decision_reason": "Agent selected due to high task complexity requiring specialized planning capabilities",
				"key_factors":     []string{"task_complexity", "agent_availability", "historical_performance"},
				"confidence":      0.89,
			},
			ConfidenceScore: 0.89,
			ExplanationText: "The agent was selected primarily because the task complexity score (0.35 importance) indicated a need for specialized planning capabilities. Agent availability (0.28) and historical performance (0.22) were also significant factors.",
			Visualizations: []Visualization{
				{
					Type:        "bar_chart",
					Title:       "Feature Importance for Agent Selection",
					Description: "SHAP values showing feature contributions to agent selection decision",
					Data: map[string]interface{}{
						"features":   []string{"task_complexity", "agent_availability", "historical_performance", "cost_efficiency"},
						"importance": []float64{0.35, 0.28, 0.22, 0.15},
						"directions": []string{"positive", "positive", "positive", "negative"},
					},
				},
			},
			Metadata: map[string]interface{}{
				"shap_version":        "0.41.0",
				"model_version":       "v1.2.3",
				"explanation_time_ms": 245,
			},
			CreatedAt: time.Now().Add(-2 * time.Hour),
			UpdatedAt: time.Now().Add(-2 * time.Hour),
		},
		{
			ID:        "exp-002",
			RequestID: "req-002",
			Type:      "planning_decision",
			Method:    "lime",
			ModelID:   "planning-engine-v2",
			PlanID:    "plan-456",
			FeatureImportance: []FeatureImportance{
				{Feature: "goal_complexity", Importance: 0.42, Direction: "positive", Confidence: 0.94},
				{Feature: "resource_availability", Importance: 0.31, Direction: "positive", Confidence: 0.87},
				{Feature: "time_constraints", Importance: 0.18, Direction: "negative", Confidence: 0.82},
				{Feature: "dependency_count", Importance: 0.09, Direction: "positive", Confidence: 0.75},
			},
			LocalExplanation: map[string]interface{}{
				"decision_reason":        "Multi-step decomposition chosen due to high goal complexity and sufficient resources",
				"decomposition_strategy": "hierarchical",
				"estimated_steps":        5,
				"confidence":             0.91,
			},
			ConfidenceScore: 0.91,
			ExplanationText: "The planning engine chose a hierarchical decomposition strategy because goal complexity (0.42 importance) was high and resource availability (0.31) was sufficient. Time constraints had a negative impact (-0.18) but were manageable.",
			Visualizations: []Visualization{
				{
					Type:        "heatmap",
					Title:       "Planning Decision Factors",
					Description: "LIME explanation showing how input features influenced the planning decision",
					Data: map[string]interface{}{
						"features": []string{"goal_complexity", "resource_availability", "time_constraints", "dependency_count"},
						"values":   []float64{0.42, 0.31, -0.18, 0.09},
					},
				},
			},
			Metadata: map[string]interface{}{
				"lime_version":        "*******",
				"samples_used":        1000,
				"explanation_time_ms": 892,
			},
			CreatedAt: time.Now().Add(-1 * time.Hour),
			UpdatedAt: time.Now().Add(-1 * time.Hour),
		},
	}

	for _, explanation := range sampleExplanations {
		es.explanations[explanation.ID] = explanation
	}
}

// CreateExplanation generates an explanation for a given request
func (es *ExplainabilityService) CreateExplanation(w http.ResponseWriter, r *http.Request) {
	var request ExplanationRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	request.ID = uuid.New().String()
	request.Timestamp = time.Now()

	// Store the request
	es.mu.Lock()
	es.requests[request.ID] = request
	es.mu.Unlock()

	// Generate explanation
	explanation := es.generateExplanation(request)

	es.mu.Lock()
	es.explanations[explanation.ID] = explanation
	es.mu.Unlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(explanation)

	log.Printf("Created explanation %s for %s request %s", explanation.ID, request.Type, request.ID)
}

// generateExplanation simulates explanation generation
func (es *ExplainabilityService) generateExplanation(request ExplanationRequest) Explanation {
	// Simulate different explanation methods
	var featureImportance []FeatureImportance
	var explanationText string
	var visualizations []Visualization
	var confidence float64

	switch request.Type {
	case "agent_selection":
		featureImportance = es.generateAgentSelectionExplanation()
		explanationText = "Agent selection was based on task requirements, availability, and performance history."
		confidence = 0.85 + (0.15 * math.Sin(float64(time.Now().Unix())))
		visualizations = []Visualization{
			{
				Type:        "bar_chart",
				Title:       "Agent Selection Factors",
				Description: fmt.Sprintf("%s explanation for agent selection", request.ExplanationMethod),
				Data: map[string]interface{}{
					"method":     request.ExplanationMethod,
					"features":   extractFeatureNames(featureImportance),
					"importance": extractImportanceValues(featureImportance),
				},
			},
		}

	case "planning_decision":
		featureImportance = es.generatePlanningExplanation()
		explanationText = "Planning strategy was determined by goal complexity, resources, and constraints."
		confidence = 0.88 + (0.12 * math.Cos(float64(time.Now().Unix())))
		visualizations = []Visualization{
			{
				Type:        "heatmap",
				Title:       "Planning Decision Matrix",
				Description: fmt.Sprintf("%s explanation for planning decision", request.ExplanationMethod),
				Data: map[string]interface{}{
					"method":           request.ExplanationMethod,
					"decision_factors": extractFeatureNames(featureImportance),
					"impact_scores":    extractImportanceValues(featureImportance),
				},
			},
		}

	case "llm_response":
		featureImportance = es.generateLLMResponseExplanation()
		explanationText = "LLM response was influenced by prompt structure, context, and model parameters."
		confidence = 0.82 + (0.18 * math.Sin(float64(time.Now().Unix()+100)))
		visualizations = []Visualization{
			{
				Type:        "attention_map",
				Title:       "Attention Weights",
				Description: fmt.Sprintf("%s explanation for LLM response", request.ExplanationMethod),
				Data: map[string]interface{}{
					"method":    request.ExplanationMethod,
					"tokens":    []string{"prompt", "context", "parameters", "history"},
					"attention": extractImportanceValues(featureImportance),
				},
			},
		}
	}

	return Explanation{
		ID:                uuid.New().String(),
		RequestID:         request.ID,
		Type:              request.Type,
		Method:            request.ExplanationMethod,
		ModelID:           request.ModelID,
		AgentID:           request.AgentID,
		PlanID:            request.PlanID,
		FeatureImportance: featureImportance,
		LocalExplanation: map[string]interface{}{
			"method":     request.ExplanationMethod,
			"type":       request.Type,
			"confidence": confidence,
		},
		ConfidenceScore: confidence,
		ExplanationText: explanationText,
		Visualizations:  visualizations,
		Metadata: map[string]interface{}{
			"method":             request.ExplanationMethod,
			"generation_time_ms": 150 + int(50*math.Sin(float64(time.Now().Unix()))),
			"model_version":      "v1.0.0",
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}

// generateAgentSelectionExplanation creates feature importance for agent selection
func (es *ExplainabilityService) generateAgentSelectionExplanation() []FeatureImportance {
	return []FeatureImportance{
		{Feature: "task_complexity", Importance: 0.30 + (0.15 * math.Sin(float64(time.Now().Unix()))), Direction: "positive", Confidence: 0.90},
		{Feature: "agent_availability", Importance: 0.25 + (0.10 * math.Cos(float64(time.Now().Unix()))), Direction: "positive", Confidence: 0.85},
		{Feature: "historical_performance", Importance: 0.20 + (0.08 * math.Sin(float64(time.Now().Unix()+50))), Direction: "positive", Confidence: 0.88},
		{Feature: "cost_efficiency", Importance: 0.15 + (0.05 * math.Cos(float64(time.Now().Unix()+100))), Direction: "negative", Confidence: 0.82},
		{Feature: "specialization_match", Importance: 0.10 + (0.05 * math.Sin(float64(time.Now().Unix()+150))), Direction: "positive", Confidence: 0.79},
	}
}

// generatePlanningExplanation creates feature importance for planning decisions
func (es *ExplainabilityService) generatePlanningExplanation() []FeatureImportance {
	return []FeatureImportance{
		{Feature: "goal_complexity", Importance: 0.40 + (0.10 * math.Sin(float64(time.Now().Unix()))), Direction: "positive", Confidence: 0.93},
		{Feature: "resource_availability", Importance: 0.30 + (0.08 * math.Cos(float64(time.Now().Unix()))), Direction: "positive", Confidence: 0.89},
		{Feature: "time_constraints", Importance: 0.20 + (0.05 * math.Sin(float64(time.Now().Unix()+75))), Direction: "negative", Confidence: 0.86},
		{Feature: "dependency_complexity", Importance: 0.10 + (0.03 * math.Cos(float64(time.Now().Unix()+125))), Direction: "positive", Confidence: 0.81},
	}
}

// generateLLMResponseExplanation creates feature importance for LLM responses
func (es *ExplainabilityService) generateLLMResponseExplanation() []FeatureImportance {
	return []FeatureImportance{
		{Feature: "prompt_structure", Importance: 0.35 + (0.10 * math.Sin(float64(time.Now().Unix()))), Direction: "positive", Confidence: 0.91},
		{Feature: "context_relevance", Importance: 0.28 + (0.08 * math.Cos(float64(time.Now().Unix()))), Direction: "positive", Confidence: 0.87},
		{Feature: "model_parameters", Importance: 0.22 + (0.06 * math.Sin(float64(time.Now().Unix()+60))), Direction: "positive", Confidence: 0.84},
		{Feature: "conversation_history", Importance: 0.15 + (0.05 * math.Cos(float64(time.Now().Unix()+120))), Direction: "positive", Confidence: 0.80},
	}
}

// Helper functions
func extractFeatureNames(features []FeatureImportance) []string {
	names := make([]string, len(features))
	for i, f := range features {
		names[i] = f.Feature
	}
	return names
}

func extractImportanceValues(features []FeatureImportance) []float64 {
	values := make([]float64, len(features))
	for i, f := range features {
		values[i] = f.Importance
	}
	return values
}

// GetExplanation retrieves an explanation by ID
func (es *ExplainabilityService) GetExplanation(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	es.mu.RLock()
	defer es.mu.RUnlock()

	if explanation, exists := es.explanations[id]; exists {
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(explanation)
		return
	}

	http.Error(w, "Explanation not found", http.StatusNotFound)
}

// GetExplanations retrieves all explanations with optional filtering
func (es *ExplainabilityService) GetExplanations(w http.ResponseWriter, r *http.Request) {
	es.mu.RLock()
	defer es.mu.RUnlock()

	explanationType := r.URL.Query().Get("type")
	method := r.URL.Query().Get("method")

	var filteredExplanations []Explanation
	for _, explanation := range es.explanations {
		if explanationType != "" && explanation.Type != explanationType {
			continue
		}
		if method != "" && explanation.Method != method {
			continue
		}
		filteredExplanations = append(filteredExplanations, explanation)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(filteredExplanations)
}

// HealthCheck provides service health status
func (es *ExplainabilityService) HealthCheck(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":             "healthy",
		"service":            "explainability-service",
		"timestamp":          time.Now(),
		"explanations_count": len(es.explanations),
		"supported_methods":  []string{"lime", "shap", "integrated_gradients", "attention"},
	})
}

func main() {
	service := NewExplainabilityService()

	r := mux.NewRouter()

	// API routes
	r.HandleFunc("/health", service.HealthCheck).Methods("GET")
	r.HandleFunc("/explanations", service.CreateExplanation).Methods("POST")
	r.HandleFunc("/explanations", service.GetExplanations).Methods("GET")
	r.HandleFunc("/explanations/{id}", service.GetExplanation).Methods("GET")

	// Enable CORS
	c := cors.New(cors.Options{
		AllowedOrigins: []string{"*"},
		AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders: []string{"*"},
	})

	handler := c.Handler(r)

	port := ":8085"
	fmt.Printf("Explainability Service starting on port %s...\n", port)
	log.Fatal(http.ListenAndServe(port, handler))
}
