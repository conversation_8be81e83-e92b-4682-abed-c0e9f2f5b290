# Build stage for ai-optimizer
FROM golang:1.24.2-alpine AS optimizer-builder
WORKDIR /app
COPY ./ai-optimizer .
RUN go mod tidy

# Build stage for planning-service
FROM golang:1.24.2-alpine AS builder

WORKDIR /app

# Install dependencies
RUN apk add --no-cache git

# Copy modules
COPY --from=optimizer-builder /app /ai-optimizer
COPY ./planning-service .

# Update the replace directive in go.mod
RUN sed -i 's|../ai-optimizer|/ai-optimizer|g' go.mod

# Download dependencies
RUN go mod download

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o planning-service .

# Final stage
FROM alpine:latest

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates

# Set working directory
WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/planning-service .

# Expose port
EXPOSE 8080

# Run the application
CMD ["./planning-service"]