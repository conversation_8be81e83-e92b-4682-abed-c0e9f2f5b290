# Enterprise Frontend Ingress - Separate from API routes to avoid rewrite conflicts
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: enterprise-frontend-ingress
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    # NO rewrite-target for frontend static assets - pass through as-is
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Enterprise Frontend - NO path rewriting for static assets
      - path: /enterprise
        pathType: Prefix
        backend:
          service:
            name: frontend-dashboard
            port:
              number: 80

---
# Enterprise API Ingress - Separate with rewrite rules for API endpoints
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: enterprise-api-ingress-fixed
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Enterprise API Routes (with path rewriting)
      # Responsible AI v1 API routes (highest priority)
      - path: /enterprise/v1/(bias-metrics.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: bias-detection-service
            port:
              number: 8084

      - path: /enterprise/v1/(explanations.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: explainability-service
            port:
              number: 8085

      - path: /enterprise/v1/(robustness-tests.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: robustness-testing-service
            port:
              number: 8086

      - path: /enterprise/v1/(compliance/.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: compliance-service
            port:
              number: 8087

      - path: /enterprise/v1/(factsheets.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: governance-service
            port:
              number: 8080

      # Specific API routes (lower priority)
      - path: /enterprise/(api/prompts.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      - path: /enterprise/(api/policies.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      - path: /enterprise/(api/model-profiles.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      - path: /enterprise/(api/integration.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      - path: /enterprise/(api/planning.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: planning-service
            port:
              number: 8080

      - path: /enterprise/(api/evaluation.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: evaluation-service
            port:
              number: 8087

      - path: /enterprise/(api/multi-agent.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: multi-agent-orchestrator-service
            port:
              number: 8083

      # Enterprise Edition Dashboard API (with path rewriting)
      - path: /enterprise/(dashboard.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      # Enterprise Edition Dashboard API routes
      - path: /enterprise/(api/summary.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      - path: /enterprise/(api/time-series.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      - path: /enterprise/(api/inference-logs.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      - path: /enterprise/(api/backend-latencies.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      - path: /enterprise/(api/optimal-backend.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      # Additional Enterprise Dashboard API routes
      - path: /enterprise/(api/model-capabilities.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      - path: /enterprise/(api/evaluation-results.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      - path: /enterprise/(api/curated-data.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      # Enterprise Planning Service routes (v1 API) - route directly to planning-service
      - path: /enterprise/(api/planning/v1/.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: planning-service
            port:
              number: 8080

      # Enterprise Synthetic Data API routes
      - path: /enterprise/(api/synthetic-data/.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      # Root-level synthetic data API routes (for frontend calls without /enterprise prefix)
      - path: /(api/synthetic-data/.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      # Multi-agent v1 routes moved to separate enterprise-mcp-ingress.yaml

      # Enterprise Sentiment Service routes (rewrite /enterprise/api/sentiment/X to /api/sentiment/X)
      - path: /enterprise/(api/sentiment.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: sentiment-service
            port:
              number: 8088

      # Enterprise Social Integration Service routes (rewrite /enterprise/api/social/X to /api/social/X)
      - path: /enterprise/(api/social.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: social-integration-service
            port:
              number: 8089

      # Enterprise Governance Service routes (rewrite /enterprise/governance/X to /X)
      - path: /enterprise/governance/(policies.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: governance-service
            port:
              number: 8080

      - path: /enterprise/governance/(alerts.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: governance-service
            port:
              number: 8080

      - path: /enterprise/governance/(compliance-status.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: governance-service
            port:
              number: 8080

      - path: /enterprise/governance/(audit-logs.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: governance-service
            port:
              number: 8080

      - path: /enterprise/governance/(metrics.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: governance-service
            port:
              number: 8080

      - path: /enterprise/governance/(factsheets.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: governance-service
            port:
              number: 8080

      - path: /enterprise/governance/(health.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: governance-service
            port:
              number: 8080

      - path: /enterprise/governance/(ws/.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: governance-service
            port:
              number: 8080

      # MCP routes moved to separate enterprise-mcp-ingress.yaml

      # Chat completions and v1 API routes moved to enterprise-mcp-ingress.yaml

      # Enterprise Edition General API (proxy-gateway) - MUST be last
      - path: /enterprise/(api.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: proxy-gateway
            port:
              number: 8080
