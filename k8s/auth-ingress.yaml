apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: auth-service-ingress
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Direct auth routes - specific endpoints only
      - path: /auth/google
        pathType: Exact
        backend:
          service:
            name: auth-service
            port:
              number: 80
      - path: /auth/callback
        pathType: Exact
        backend:
          service:
            name: auth-service
            port:
              number: 80
      - path: /auth/logout
        pathType: Exact
        backend:
          service:
            name: auth-service
            port:
              number: 80
      - path: /auth/me
        pathType: Exact
        backend:
          service:
            name: auth-service
            port:
              number: 80
      - path: /auth/profile
        pathType: Exact
        backend:
          service:
            name: auth-service
            port:
              number: 80
      - path: /auth/validate
        pathType: Exact
        backend:
          service:
            name: auth-service
            port:
              number: 80

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: enterprise-auth-ingress
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /auth/$1
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Enterprise edition auth routes - exclude success path
      - path: /enterprise/auth/(google|callback|logout|me|profile|validate)
        pathType: ImplementationSpecific
        backend:
          service:
            name: auth-service
            port:
              number: 80

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: standard-auth-ingress
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /auth/$1
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Standard edition auth routes - exclude success path
      - path: /standard/auth/(google|callback|logout|me|profile|validate)
        pathType: ImplementationSpecific
        backend:
          service:
            name: auth-service
            port:
              number: 80
