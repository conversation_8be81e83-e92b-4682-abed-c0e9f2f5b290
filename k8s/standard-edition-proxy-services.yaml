# ExternalName services in default namespace to proxy to standard edition services
apiVersion: v1
kind: Service
metadata:
  name: policy-manager-standard-proxy
  namespace: default
  labels:
    app: policy-manager-standard-proxy
    edition: standard
spec:
  type: ExternalName
  externalName: policy-manager.standard-edition.svc.cluster.local
  ports:
  - port: 8083
    targetPort: 8083

---
apiVersion: v1
kind: Service
metadata:
  name: dashboard-api-standard-proxy
  namespace: default
  labels:
    app: dashboard-api-standard-proxy
    edition: standard
spec:
  type: ExternalName
  externalName: dashboard-api-standard.standard-edition.svc.cluster.local
  ports:
  - port: 8081
    targetPort: 8081

---
apiVersion: v1
kind: Service
metadata:
  name: proxy-gateway-standard-proxy
  namespace: default
  labels:
    app: proxy-gateway-standard-proxy
    edition: standard
spec:
  type: ExternalName
  externalName: proxy-gateway-standard.standard-edition.svc.cluster.local
  ports:
  - port: 8080
    targetPort: 8080

---
apiVersion: v1
kind: Service
metadata:
  name: api-fallback-standard-proxy
  namespace: default
  labels:
    app: api-fallback-standard-proxy
    edition: standard
spec:
  type: ExternalName
  externalName: api-fallback-service.standard-edition.svc.cluster.local
  ports:
  - port: 8080
    targetPort: 8080
