apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: unified-ai-operations-hub-ingress
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Landing page - highest priority, exact root match
      - path: /
        pathType: Exact
        backend:
          service:
            name: landing-page
            port:
              number: 80

      # Authentication service routes moved to separate auth-ingress.yaml

      # Standard Edition routes moved to separate standard-api-ingress-fixed.yaml



      # Enterprise Edition routes moved to separate enterprise-frontend-ingress.yaml

      # Root domain API routes (enterprise edition)
      - path: /api/prompts
        pathType: Prefix
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      - path: /api/policies
        pathType: Prefix
        backend:
          service:
            name: policy-manager-standard-proxy-fixed
            port:
              number: 8083

      - path: /api/model-profiles
        pathType: Prefix
        backend:
          service:
            name: policy-manager-standard-proxy-fixed
            port:
              number: 8083

      - path: /api/integration
        pathType: Prefix
        backend:
          service:
            name: dashboard-api-standard-proxy-fixed
            port:
              number: 8081

      - path: /api/planning
        pathType: Prefix
        backend:
          service:
            name: api-fallback-standard-proxy-fixed
            port:
              number: 8080

      - path: /api/evaluation
        pathType: Prefix
        backend:
          service:
            name: api-fallback-standard-proxy-fixed
            port:
              number: 8080

      - path: /api/multi-agent
        pathType: Prefix
        backend:
          service:
            name: api-fallback-standard-proxy-fixed
            port:
              number: 8080

      # Root domain dashboard API
      - path: /dashboard
        pathType: Prefix
        backend:
          service:
            name: dashboard-api-standard-proxy-fixed
            port:
              number: 8081

      # Root domain general API (proxy-gateway) - MUST be after specific routes
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: proxy-gateway-standard-proxy-fixed
            port:
              number: 8080

      # Fallback to landing page for any unmatched paths
      - path: /
        pathType: Prefix
        backend:
          service:
            name: landing-page
            port:
              number: 80
