# Enterprise MCP Ingress - Separate ingress for MCP endpoints with specific rewrite rules
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: enterprise-mcp-ingress
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /mcp/$1
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Enterprise MCP endpoints - rewrite /enterprise/api/mcp/X to /mcp/X
      - path: /enterprise/api/mcp/(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: proxy-gateway
            port:
              number: 8080

---
# Enterprise Service-Specific MCP Ingress - Different rewrite rule for service MCP endpoints
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: enterprise-service-mcp-ingress
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Service-specific MCP endpoints - rewrite /enterprise/api/SERVICE/mcp to /mcp
      - path: /enterprise/api/planning/(mcp.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: planning-service
            port:
              number: 8080

      - path: /enterprise/api/evaluation/(mcp.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: evaluation-service
            port:
              number: 8087

      - path: /enterprise/api/integration/(mcp.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: integration-service
            port:
              number: 8080

---
# Enterprise Chat Completions Ingress - Separate ingress for v1 API endpoints with specific rewrite
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: enterprise-chat-ingress
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Enterprise v1 API endpoints - rewrite /enterprise/api/v1/X to /v1/X
      - path: /enterprise/api/(v1/chat/completions.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: proxy-gateway
            port:
              number: 8080

      - path: /enterprise/api/(v1/completions.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: proxy-gateway
            port:
              number: 8080

      - path: /enterprise/api/(v1/embeddings.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: proxy-gateway
            port:
              number: 8080

      - path: /enterprise/api/(v1/.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: proxy-gateway
            port:
              number: 8080

---
# Enterprise Multi-Agent Ingress - Separate ingress for multi-agent endpoints with specific rewrite
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: enterprise-multi-agent-ingress
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Enterprise Multi-Agent v1 endpoints - rewrite /enterprise/api/multi-agent/v1/X to /v1/X
      - path: /enterprise/api/multi-agent/(v1/.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: multi-agent-orchestrator-service
            port:
              number: 8083
