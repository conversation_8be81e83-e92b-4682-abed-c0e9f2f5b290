FROM golang:1.20-alpine AS builder

WORKDIR /app

# Copy go mod files first for better caching
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o integration-service .

FROM alpine:latest

# Install git, curl, python3, and gcloud CLI for GitOps operations
RUN apk add --no-cache \
    git \
    curl \
    bash \
    ca-certificates \
    python3 \
    py3-pip \
    && ln -sf python3 /usr/bin/python \
    && curl -sSL https://sdk.cloud.google.com | bash \
    && ln -s /root/google-cloud-sdk/bin/gcloud /usr/local/bin/gcloud \
    && ln -s /root/google-cloud-sdk/bin/kubectl /usr/local/bin/kubectl

WORKDIR /app

# Copy the binary from builder stage
COPY --from=builder /app/integration-service .

# Create workspace directory for GitOps operations
RUN mkdir -p /tmp/gitops-workspace

# Set environment variables
ENV PORT=8080
ENV WORKSPACE_DIR=/tmp/gitops-workspace

EXPOSE 8080

CMD ["./integration-service"]
