apiVersion: apps/v1
kind: Deployment
metadata:
  name: integration-service
  labels:
    app: integration-service
spec:
  selector:
    matchLabels:
      app: integration-service
  replicas: 1
  template:
    metadata:
      labels:
        app: integration-service
    spec:
      containers:
        - name: integration-service
          image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-integration-service:latest
          ports:
            - containerPort: 8080
          env:
            - name: PORT
              value: "8080"
            - name: GIT_REPO_URL
              value: "https://github.com/datlaphani/ai-cost-performance-optimizer.git"
            - name: GIT_BRANCH
              value: "main"
            - name: GIT_TOKEN
              valueFrom:
                secretKeyRef:
                  name: git-credentials
                  key: token
                  optional: true
            - name: WEBHOOK_SECRET
              valueFrom:
                secretKeyRef:
                  name: git-credentials
                  key: webhook-secret
                  optional: true
            - name: WORKSPACE_DIR
              value: "/tmp/gitops-workspace"
            - name: CLOUD_BUILD_PROJECT
              value: "silken-zenith-460615-s7"
            - name: CLOUD_BUILD_REGION
              value: "us-central1"
          volumeMounts:
            - name: workspace
              mountPath: /tmp/gitops-workspace
          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
      volumes:
        - name: workspace
          emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: integration-service
  labels:
    app: integration-service
spec:
  selector:
    app: integration-service
  ports:
    - port: 8080
      targetPort: 8080
      protocol: TCP
  type: ClusterIP
