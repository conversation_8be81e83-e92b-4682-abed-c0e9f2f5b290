package main

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/gorilla/mux"
)

// GitOpsConfig holds configuration for GitOps operations
type GitOpsConfig struct {
	GitRepoURL        string
	GitBranch         string
	GitToken          string
	WebhookSecret     string
	WorkspaceDir      string
	CloudBuildProject string
	CloudBuildRegion  string
}

// PromptOpsGitOps handles GitOps operations for PromptOps
type PromptOpsGitOps struct {
	config GitOpsConfig
}

// WebhookPayload represents GitHub/GitLab webhook payload
type WebhookPayload struct {
	Ref        string `json:"ref"`
	Repository struct {
		Name     string `json:"name"`
		FullName string `json:"full_name"`
		CloneURL string `json:"clone_url"`
	} `json:"repository"`
	Commits []struct {
		ID       string   `json:"id"`
		Message  string   `json:"message"`
		Added    []string `json:"added"`
		Modified []string `json:"modified"`
		Removed  []string `json:"removed"`
	} `json:"commits"`
}

// PromptDeploymentRequest represents a request to deploy prompts
type PromptDeploymentRequest struct {
	Environment string            `json:"environment"`
	Prompts     []PromptManifest  `json:"prompts"`
	Metadata    map[string]string `json:"metadata"`
}

// PromptManifest represents a prompt configuration
type PromptManifest struct {
	ID           string            `json:"id"`
	Name         string            `json:"name"`
	Version      string            `json:"version"`
	Content      string            `json:"content"`
	Variables    []PromptVariable  `json:"variables"`
	Tags         []string          `json:"tags"`
	ModelTargets []string          `json:"model_targets"`
	UseCase      string            `json:"use_case"`
	Metadata     map[string]string `json:"metadata"`
}

// PromptVariable represents a prompt variable
type PromptVariable struct {
	Name         string `json:"name"`
	Type         string `json:"type"`
	Description  string `json:"description"`
	Required     bool   `json:"required"`
	DefaultValue string `json:"default_value"`
	Validation   string `json:"validation"`
}

func main() {
	fmt.Println("Enhanced Integration Service with PromptOps GitOps starting...")

	// Initialize GitOps configuration
	config := GitOpsConfig{
		GitRepoURL:        getEnvOrDefault("GIT_REPO_URL", ""),
		GitBranch:         getEnvOrDefault("GIT_BRANCH", "main"),
		GitToken:          getEnvOrDefault("GIT_TOKEN", ""),
		WebhookSecret:     getEnvOrDefault("WEBHOOK_SECRET", ""),
		WorkspaceDir:      getEnvOrDefault("WORKSPACE_DIR", "/tmp/gitops-workspace"),
		CloudBuildProject: getEnvOrDefault("CLOUD_BUILD_PROJECT", ""),
		CloudBuildRegion:  getEnvOrDefault("CLOUD_BUILD_REGION", "us-central1"),
	}

	gitops := &PromptOpsGitOps{config: config}

	// Initialize workspace
	if err := gitops.initializeWorkspace(); err != nil {
		log.Printf("Warning: Failed to initialize workspace: %v", err)
	}

	// Set up HTTP routes
	router := mux.NewRouter()

	// Health check
	router.HandleFunc("/health", healthHandler).Methods("GET")

	// Legacy schema translation endpoint
	router.HandleFunc("/", legacyHandler).Methods("GET")

	// GitOps endpoints
	router.HandleFunc("/gitops/webhook", gitops.webhookHandler).Methods("POST")
	router.HandleFunc("/gitops/deploy", gitops.deployHandler).Methods("POST")
	router.HandleFunc("/gitops/sync", gitops.syncHandler).Methods("POST")
	router.HandleFunc("/gitops/status", gitops.statusHandler).Methods("GET")

	// PromptOps GitOps endpoints
	router.HandleFunc("/promptops/export", gitops.exportPromptsHandler).Methods("POST")
	router.HandleFunc("/promptops/import", gitops.importPromptsHandler).Methods("POST")
	router.HandleFunc("/promptops/validate", gitops.validatePromptsHandler).Methods("POST")
	router.HandleFunc("/promptops/diff", gitops.diffPromptsHandler).Methods("POST")

	// Initialize MCP Server
	InitializeMCPIntegrationServer(gitops)
	router.HandleFunc("/mcp", mcpIntegrationServer.HandleMCPConnection)

	// Start the HTTP server
	port := getEnvOrDefault("PORT", "8080")
	fmt.Printf("Enhanced Integration Service listening on port %s with MCP support...\n", port)
	log.Fatal(http.ListenAndServe(":"+port, router))
}

// Utility functions
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// Health check handler
func healthHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":    "healthy",
		"service":   "integration-service",
		"timestamp": time.Now().UTC(),
		"features":  []string{"gitops", "promptops", "schema-translation"},
	})
}

// Legacy handler for backward compatibility
func legacyHandler(w http.ResponseWriter, r *http.Request) {
	translatedSchema := translateSchema()
	fmt.Fprintf(w, "Integration service is running! Translated schema: %v", translatedSchema)
}

func translateSchema() map[string]interface{} {
	// Implement schema translation logic here
	return map[string]interface{}{
		"provider":   "anthropic",
		"translated": true,
	}
}

// GitOps methods for PromptOpsGitOps

// Initialize workspace for GitOps operations
func (p *PromptOpsGitOps) initializeWorkspace() error {
	// Create workspace directory
	if err := os.MkdirAll(p.config.WorkspaceDir, 0755); err != nil {
		return fmt.Errorf("failed to create workspace directory: %v", err)
	}

	// Clone repository if it doesn't exist
	repoDir := filepath.Join(p.config.WorkspaceDir, "repo")
	if _, err := os.Stat(repoDir); os.IsNotExist(err) {
		if p.config.GitRepoURL != "" {
			return p.cloneRepository()
		}
	}

	return nil
}

// Clone Git repository
func (p *PromptOpsGitOps) cloneRepository() error {
	repoDir := filepath.Join(p.config.WorkspaceDir, "repo")

	var cmd *exec.Cmd
	if p.config.GitToken != "" {
		// Use token authentication
		authURL := strings.Replace(p.config.GitRepoURL, "https://", fmt.Sprintf("https://%s@", p.config.GitToken), 1)
		cmd = exec.Command("git", "clone", "-b", p.config.GitBranch, authURL, repoDir)
	} else {
		cmd = exec.Command("git", "clone", "-b", p.config.GitBranch, p.config.GitRepoURL, repoDir)
	}

	cmd.Env = append(os.Environ(), "GIT_TERMINAL_PROMPT=0")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to clone repository: %v, output: %s", err, output)
	}

	log.Printf("Successfully cloned repository to %s", repoDir)
	return nil
}

// Webhook handler for Git events
func (p *PromptOpsGitOps) webhookHandler(w http.ResponseWriter, r *http.Request) {
	// Verify webhook signature if secret is configured
	if p.config.WebhookSecret != "" {
		if !p.verifyWebhookSignature(r) {
			http.Error(w, "Invalid signature", http.StatusUnauthorized)
			return
		}
	}

	// Parse webhook payload
	var payload WebhookPayload
	if err := json.NewDecoder(r.Body).Decode(&payload); err != nil {
		http.Error(w, "Invalid payload", http.StatusBadRequest)
		return
	}

	// Check if this is a push to the target branch
	targetRef := fmt.Sprintf("refs/heads/%s", p.config.GitBranch)
	if payload.Ref != targetRef {
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{
			"status": "ignored",
			"reason": "not target branch",
		})
		return
	}

	// Check if any prompt-related files were changed
	promptFilesChanged := false
	for _, commit := range payload.Commits {
		for _, file := range append(append(commit.Added, commit.Modified...), commit.Removed...) {
			if strings.Contains(file, "prompts/") || strings.HasSuffix(file, ".prompt.yaml") || strings.HasSuffix(file, ".prompt.json") {
				promptFilesChanged = true
				break
			}
		}
		if promptFilesChanged {
			break
		}
	}

	if !promptFilesChanged {
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{
			"status": "ignored",
			"reason": "no prompt files changed",
		})
		return
	}

	// Trigger deployment
	go func() {
		if err := p.triggerDeployment(payload); err != nil {
			log.Printf("Failed to trigger deployment: %v", err)
		}
	}()

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{
		"status": "triggered",
		"commit": payload.Commits[0].ID,
	})
}

// Verify webhook signature
func (p *PromptOpsGitOps) verifyWebhookSignature(r *http.Request) bool {
	signature := r.Header.Get("X-Hub-Signature-256")
	if signature == "" {
		signature = r.Header.Get("X-GitLab-Token")
		return signature == p.config.WebhookSecret
	}

	// GitHub signature verification
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return false
	}
	r.Body = io.NopCloser(bytes.NewBuffer(body))

	mac := hmac.New(sha256.New, []byte(p.config.WebhookSecret))
	mac.Write(body)
	expectedSignature := "sha256=" + hex.EncodeToString(mac.Sum(nil))

	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// Trigger deployment via Cloud Build
func (p *PromptOpsGitOps) triggerDeployment(payload WebhookPayload) error {
	if p.config.CloudBuildProject == "" {
		return fmt.Errorf("cloud build project not configured")
	}

	// Pull latest changes
	if err := p.pullLatestChanges(); err != nil {
		return fmt.Errorf("failed to pull latest changes: %v", err)
	}

	// Validate prompts
	if err := p.validatePrompts(); err != nil {
		return fmt.Errorf("prompt validation failed: %v", err)
	}

	// Trigger Cloud Build
	cmd := exec.Command("gcloud", "builds", "submit",
		"--project", p.config.CloudBuildProject,
		"--region", p.config.CloudBuildRegion,
		"--config", "cloudbuild-prompts.yaml",
		"--substitutions", fmt.Sprintf("_COMMIT_SHA=%s,_BRANCH_NAME=%s", payload.Commits[0].ID, p.config.GitBranch),
		filepath.Join(p.config.WorkspaceDir, "repo"))

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to trigger cloud build: %v, output: %s", err, output)
	}

	log.Printf("Successfully triggered Cloud Build deployment for commit %s", payload.Commits[0].ID)
	return nil
}

// Pull latest changes from Git repository
func (p *PromptOpsGitOps) pullLatestChanges() error {
	repoDir := filepath.Join(p.config.WorkspaceDir, "repo")

	cmd := exec.Command("git", "pull", "origin", p.config.GitBranch)
	cmd.Dir = repoDir
	cmd.Env = append(os.Environ(), "GIT_TERMINAL_PROMPT=0")

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to pull changes: %v, output: %s", err, output)
	}

	return nil
}

// Deploy handler for manual deployments
func (p *PromptOpsGitOps) deployHandler(w http.ResponseWriter, r *http.Request) {
	var req PromptDeploymentRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate environment
	if req.Environment == "" {
		http.Error(w, "Environment is required", http.StatusBadRequest)
		return
	}

	// Deploy prompts
	deploymentID := fmt.Sprintf("manual-%d", time.Now().Unix())
	go func() {
		if err := p.deployPrompts(req, deploymentID); err != nil {
			log.Printf("Failed to deploy prompts: %v", err)
		}
	}()

	w.WriteHeader(http.StatusAccepted)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":        "accepted",
		"deployment_id": deploymentID,
		"environment":   req.Environment,
		"prompt_count":  len(req.Prompts),
	})
}

// Sync handler to sync with Git repository
func (p *PromptOpsGitOps) syncHandler(w http.ResponseWriter, r *http.Request) {
	if err := p.pullLatestChanges(); err != nil {
		http.Error(w, fmt.Sprintf("Failed to sync: %v", err), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":    "synced",
		"timestamp": time.Now().UTC(),
	})
}

// Status handler to get GitOps status
func (p *PromptOpsGitOps) statusHandler(w http.ResponseWriter, r *http.Request) {
	repoDir := filepath.Join(p.config.WorkspaceDir, "repo")

	// Get current commit
	cmd := exec.Command("git", "rev-parse", "HEAD")
	cmd.Dir = repoDir
	output, err := cmd.Output()
	currentCommit := "unknown"
	if err == nil {
		currentCommit = strings.TrimSpace(string(output))
	}

	// Get branch
	cmd = exec.Command("git", "branch", "--show-current")
	cmd.Dir = repoDir
	output, err = cmd.Output()
	currentBranch := "unknown"
	if err == nil {
		currentBranch = strings.TrimSpace(string(output))
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":         "active",
		"current_commit": currentCommit,
		"current_branch": currentBranch,
		"target_branch":  p.config.GitBranch,
		"workspace_dir":  p.config.WorkspaceDir,
		"timestamp":      time.Now().UTC(),
	})
}

// Export prompts handler
func (p *PromptOpsGitOps) exportPromptsHandler(w http.ResponseWriter, r *http.Request) {
	// Get prompts from PromptOps service
	prompts, err := p.fetchPromptsFromService()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to fetch prompts: %v", err), http.StatusInternalServerError)
		return
	}

	// Convert to Git format and commit
	if err := p.exportPromptsToGit(prompts); err != nil {
		http.Error(w, fmt.Sprintf("Failed to export prompts: %v", err), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":       "exported",
		"prompt_count": len(prompts),
		"timestamp":    time.Now().UTC(),
	})
}

// Import prompts handler
func (p *PromptOpsGitOps) importPromptsHandler(w http.ResponseWriter, r *http.Request) {
	// Pull latest changes
	if err := p.pullLatestChanges(); err != nil {
		http.Error(w, fmt.Sprintf("Failed to sync repository: %v", err), http.StatusInternalServerError)
		return
	}

	// Load prompts from Git
	prompts, err := p.loadPromptsFromGit()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to load prompts from Git: %v", err), http.StatusInternalServerError)
		return
	}

	// Import to PromptOps service
	if err := p.importPromptsToService(prompts); err != nil {
		http.Error(w, fmt.Sprintf("Failed to import prompts: %v", err), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":       "imported",
		"prompt_count": len(prompts),
		"timestamp":    time.Now().UTC(),
	})
}

// Validate prompts handler
func (p *PromptOpsGitOps) validatePromptsHandler(w http.ResponseWriter, r *http.Request) {
	if err := p.validatePrompts(); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "invalid",
			"error":  err.Error(),
		})
		return
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status": "valid",
	})
}

// Diff prompts handler
func (p *PromptOpsGitOps) diffPromptsHandler(w http.ResponseWriter, r *http.Request) {
	// Get current prompts from service
	servicePrompts, err := p.fetchPromptsFromService()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to fetch service prompts: %v", err), http.StatusInternalServerError)
		return
	}

	// Get prompts from Git
	gitPrompts, err := p.loadPromptsFromGit()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to load Git prompts: %v", err), http.StatusInternalServerError)
		return
	}

	// Calculate diff
	diff := p.calculatePromptDiff(servicePrompts, gitPrompts)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(diff)
}

// Helper methods

// Fetch prompts from PromptOps service
func (p *PromptOpsGitOps) fetchPromptsFromService() ([]PromptManifest, error) {
	// This would call your PromptOps service API
	// For now, return mock data
	return []PromptManifest{
		{
			ID:      "example-prompt-1",
			Name:    "Example Prompt",
			Version: "1.0.0",
			Content: "Hello {{name}}, how can I help you today?",
			Variables: []PromptVariable{
				{
					Name:        "name",
					Type:        "string",
					Description: "User's name",
					Required:    true,
				},
			},
			Tags:         []string{"greeting", "customer-service"},
			ModelTargets: []string{"gpt-4", "claude-3"},
			UseCase:      "customer-support",
		},
	}, nil
}

// Export prompts to Git repository
func (p *PromptOpsGitOps) exportPromptsToGit(prompts []PromptManifest) error {
	repoDir := filepath.Join(p.config.WorkspaceDir, "repo")
	promptsDir := filepath.Join(repoDir, "prompts")

	// Create prompts directory
	if err := os.MkdirAll(promptsDir, 0755); err != nil {
		return fmt.Errorf("failed to create prompts directory: %v", err)
	}

	// Write each prompt to a YAML file
	for _, prompt := range prompts {
		filename := fmt.Sprintf("%s.prompt.yaml", prompt.ID)
		filepath := filepath.Join(promptsDir, filename)

		data, err := json.MarshalIndent(prompt, "", "  ")
		if err != nil {
			return fmt.Errorf("failed to marshal prompt %s: %v", prompt.ID, err)
		}

		if err := os.WriteFile(filepath, data, 0644); err != nil {
			return fmt.Errorf("failed to write prompt file %s: %v", filename, err)
		}
	}

	// Commit changes
	return p.commitChanges("Export prompts from PromptOps service")
}

// Load prompts from Git repository
func (p *PromptOpsGitOps) loadPromptsFromGit() ([]PromptManifest, error) {
	repoDir := filepath.Join(p.config.WorkspaceDir, "repo")
	promptsDir := filepath.Join(repoDir, "prompts")

	var prompts []PromptManifest

	// Read all .prompt.yaml files
	files, err := filepath.Glob(filepath.Join(promptsDir, "*.prompt.yaml"))
	if err != nil {
		return nil, fmt.Errorf("failed to glob prompt files: %v", err)
	}

	for _, file := range files {
		data, err := os.ReadFile(file)
		if err != nil {
			return nil, fmt.Errorf("failed to read file %s: %v", file, err)
		}

		var prompt PromptManifest
		if err := json.Unmarshal(data, &prompt); err != nil {
			return nil, fmt.Errorf("failed to unmarshal prompt from %s: %v", file, err)
		}

		prompts = append(prompts, prompt)
	}

	return prompts, nil
}

// Import prompts to PromptOps service
func (p *PromptOpsGitOps) importPromptsToService(prompts []PromptManifest) error {
	// This would call your PromptOps service API to import prompts
	// For now, just log the operation
	log.Printf("Importing %d prompts to PromptOps service", len(prompts))
	for _, prompt := range prompts {
		log.Printf("Importing prompt: %s (version %s)", prompt.Name, prompt.Version)
	}
	return nil
}

// Validate prompts
func (p *PromptOpsGitOps) validatePrompts() error {
	prompts, err := p.loadPromptsFromGit()
	if err != nil {
		return fmt.Errorf("failed to load prompts: %v", err)
	}

	for _, prompt := range prompts {
		if prompt.ID == "" {
			return fmt.Errorf("prompt missing ID")
		}
		if prompt.Name == "" {
			return fmt.Errorf("prompt %s missing name", prompt.ID)
		}
		if prompt.Content == "" {
			return fmt.Errorf("prompt %s missing content", prompt.ID)
		}
		if prompt.Version == "" {
			return fmt.Errorf("prompt %s missing version", prompt.ID)
		}
	}

	return nil
}

// Deploy prompts to environment
func (p *PromptOpsGitOps) deployPrompts(req PromptDeploymentRequest, deploymentID string) error {
	log.Printf("Deploying %d prompts to environment %s (deployment: %s)",
		len(req.Prompts), req.Environment, deploymentID)

	// This would integrate with your deployment system
	// For now, just simulate the deployment
	time.Sleep(2 * time.Second)

	log.Printf("Successfully deployed prompts to %s", req.Environment)
	return nil
}

// Commit changes to Git
func (p *PromptOpsGitOps) commitChanges(message string) error {
	repoDir := filepath.Join(p.config.WorkspaceDir, "repo")

	// Add all changes
	cmd := exec.Command("git", "add", ".")
	cmd.Dir = repoDir
	if output, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to add changes: %v, output: %s", err, output)
	}

	// Check if there are changes to commit
	cmd = exec.Command("git", "diff", "--cached", "--quiet")
	cmd.Dir = repoDir
	if err := cmd.Run(); err == nil {
		// No changes to commit
		return nil
	}

	// Commit changes
	cmd = exec.Command("git", "commit", "-m", message)
	cmd.Dir = repoDir
	cmd.Env = append(os.Environ(),
		"GIT_AUTHOR_NAME=PromptOps GitOps",
		"GIT_AUTHOR_EMAIL=<EMAIL>",
		"GIT_COMMITTER_NAME=PromptOps GitOps",
		"GIT_COMMITTER_EMAIL=<EMAIL>")

	if output, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to commit changes: %v, output: %s", err, output)
	}

	// Push changes
	cmd = exec.Command("git", "push", "origin", p.config.GitBranch)
	cmd.Dir = repoDir
	cmd.Env = append(os.Environ(), "GIT_TERMINAL_PROMPT=0")

	if output, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to push changes: %v, output: %s", err, output)
	}

	return nil
}

// Calculate diff between service and Git prompts
func (p *PromptOpsGitOps) calculatePromptDiff(servicePrompts, gitPrompts []PromptManifest) map[string]interface{} {
	serviceMap := make(map[string]PromptManifest)
	gitMap := make(map[string]PromptManifest)

	for _, prompt := range servicePrompts {
		serviceMap[prompt.ID] = prompt
	}

	for _, prompt := range gitPrompts {
		gitMap[prompt.ID] = prompt
	}

	var added, modified, deleted []string

	// Find added and modified prompts
	for id, servicePrompt := range serviceMap {
		if gitPrompt, exists := gitMap[id]; exists {
			// Check if modified
			if servicePrompt.Version != gitPrompt.Version ||
				servicePrompt.Content != gitPrompt.Content {
				modified = append(modified, id)
			}
		} else {
			added = append(added, id)
		}
	}

	// Find deleted prompts
	for id := range gitMap {
		if _, exists := serviceMap[id]; !exists {
			deleted = append(deleted, id)
		}
	}

	return map[string]interface{}{
		"added":    added,
		"modified": modified,
		"deleted":  deleted,
		"summary": map[string]int{
			"added":    len(added),
			"modified": len(modified),
			"deleted":  len(deleted),
		},
	}
}
