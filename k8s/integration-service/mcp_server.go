package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gorilla/websocket"
)

// MCPIntegrationServer provides MCP server capabilities for the Integration Service
type MCPIntegrationServer struct {
	upgrader websocket.Upgrader
	gitops   *PromptOpsGitOps
}

// MCPRequest represents an incoming MCP JSON-RPC request
type MCPRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      interface{} `json:"id"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params,omitempty"`
}

// MCPResponse represents an outgoing MCP JSON-RPC response
type MCPResponse struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      interface{} `json:"id"`
	Result  interface{} `json:"result,omitempty"`
	Error   *MCPError   `json:"error,omitempty"`
}

// MCPError represents an MCP error
type MCPError struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// MCPTool represents an MCP tool definition
type MCPTool struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	InputSchema interface{} `json:"inputSchema"`
}

// MCPResource represents an MCP resource definition
type MCPResource struct {
	URI         string `json:"uri"`
	Name        string `json:"name"`
	Description string `json:"description"`
	MimeType    string `json:"mimeType,omitempty"`
}

// NewMCPIntegrationServer creates a new MCP server instance for Integration Service
func NewMCPIntegrationServer(gitops *PromptOpsGitOps) *MCPIntegrationServer {
	return &MCPIntegrationServer{
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// Allow connections from any origin for now
				// In production, implement proper origin checking
				return true
			},
		},
		gitops: gitops,
	}
}

// HandleMCPConnection handles MCP WebSocket connections
func (s *MCPIntegrationServer) HandleMCPConnection(w http.ResponseWriter, r *http.Request) {
	conn, err := s.upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("Integration MCP Server: Failed to upgrade to WebSocket: %v", err)
		return
	}
	defer conn.Close()

	log.Printf("Integration MCP Server: New client connected")

	// Handle MCP protocol initialization
	if err := s.handleInitialization(conn); err != nil {
		log.Printf("Integration MCP Server: Initialization failed: %v", err)
		return
	}

	// Handle incoming messages
	for {
		var req MCPRequest
		if err := conn.ReadJSON(&req); err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("Integration MCP Server: WebSocket error: %v", err)
			}
			break
		}

		response := s.handleMCPRequest(req)
		if err := conn.WriteJSON(response); err != nil {
			log.Printf("Integration MCP Server: Error sending response: %v", err)
			break
		}
	}

	log.Printf("Integration MCP Server: Client disconnected")
}

// handleInitialization handles the MCP initialization handshake
func (s *MCPIntegrationServer) handleInitialization(conn *websocket.Conn) error {
	// Send server capabilities
	capabilities := map[string]interface{}{
		"tools": map[string]interface{}{
			"listChanged": true,
		},
		"resources": map[string]interface{}{
			"subscribe":   true,
			"listChanged": true,
		},
	}

	initResponse := MCPResponse{
		JSONRPC: "2.0",
		ID:      "init",
		Result: map[string]interface{}{
			"protocolVersion": "2025-06-18",
			"capabilities":    capabilities,
			"serverInfo": map[string]interface{}{
				"name":    "integration-service-mcp-server",
				"version": "1.0.0",
			},
		},
	}

	return conn.WriteJSON(initResponse)
}

// handleMCPRequest processes incoming MCP requests
func (s *MCPIntegrationServer) handleMCPRequest(req MCPRequest) MCPResponse {
	switch req.Method {
	case "tools/list":
		return s.handleToolsList(req)
	case "tools/call":
		return s.handleToolCall(req)
	case "resources/list":
		return s.handleResourcesList(req)
	case "resources/read":
		return s.handleResourceRead(req)
	default:
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32601,
				Message: "Method not found",
			},
		}
	}
}

// handleToolsList returns the list of available tools
func (s *MCPIntegrationServer) handleToolsList(req MCPRequest) MCPResponse {
	tools := []MCPTool{
		{
			Name:        "deploy_prompts",
			Description: "Deploy prompts via GitOps workflow to specified environment",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"environment": map[string]interface{}{
						"type":        "string",
						"description": "Target environment (dev, staging, production)",
					},
					"prompts": map[string]interface{}{
						"type":        "array",
						"description": "Array of prompt configurations to deploy",
						"items": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"id":      map[string]interface{}{"type": "string"},
								"name":    map[string]interface{}{"type": "string"},
								"content": map[string]interface{}{"type": "string"},
								"version": map[string]interface{}{"type": "string"},
							},
						},
					},
					"commit_message": map[string]interface{}{
						"type":        "string",
						"description": "Commit message for the deployment",
					},
				},
				"required": []string{"environment", "prompts"},
			},
		},
		{
			Name:        "sync_repository",
			Description: "Synchronize with Git repository to get latest changes",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"force": map[string]interface{}{
						"type":        "boolean",
						"description": "Force sync even if there are local changes",
						"default":     false,
					},
				},
			},
		},
		{
			Name:        "validate_prompts",
			Description: "Validate prompt configurations against schema and best practices",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"prompts": map[string]interface{}{
						"type":        "array",
						"description": "Array of prompt configurations to validate",
						"items": map[string]interface{}{
							"type": "object",
						},
					},
					"strict": map[string]interface{}{
						"type":        "boolean",
						"description": "Enable strict validation mode",
						"default":     false,
					},
				},
				"required": []string{"prompts"},
			},
		},
		{
			Name:        "export_configurations",
			Description: "Export current system configurations to Git repository",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"include_prompts": map[string]interface{}{
						"type":        "boolean",
						"description": "Include prompt configurations in export",
						"default":     true,
					},
					"include_policies": map[string]interface{}{
						"type":        "boolean",
						"description": "Include routing policies in export",
						"default":     true,
					},
					"branch": map[string]interface{}{
						"type":        "string",
						"description": "Target branch for export",
						"default":     "main",
					},
				},
			},
		},
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      req.ID,
		Result: map[string]interface{}{
			"tools": tools,
		},
	}
}

// handleToolCall executes a tool call
func (s *MCPIntegrationServer) handleToolCall(req MCPRequest) MCPResponse {
	params, ok := req.Params.(map[string]interface{})
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "Invalid params",
			},
		}
	}

	toolName, ok := params["name"].(string)
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "Tool name required",
			},
		}
	}

	arguments, _ := params["arguments"].(map[string]interface{})

	switch toolName {
	case "deploy_prompts":
		return s.executeDeployPrompts(req.ID, arguments)
	case "sync_repository":
		return s.executeSyncRepository(req.ID, arguments)
	case "validate_prompts":
		return s.executeValidatePrompts(req.ID, arguments)
	case "export_configurations":
		return s.executeExportConfigurations(req.ID, arguments)
	default:
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32601,
				Message: "Tool not found",
			},
		}
	}
}

// Helper function to get string from map
func getString(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}

// Helper function to get bool from map
func getBool(m map[string]interface{}, key string) bool {
	if val, ok := m[key].(bool); ok {
		return val
	}
	return false
}

// executeDeployPrompts implements the deploy_prompts tool
func (s *MCPIntegrationServer) executeDeployPrompts(id interface{}, args map[string]interface{}) MCPResponse {
	environment := getString(args, "environment")
	if environment == "" {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32602,
				Message: "Environment is required",
			},
		}
	}

	promptsData, ok := args["prompts"].([]interface{})
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32602,
				Message: "Prompts array is required",
			},
		}
	}

	// Convert prompts data
	var prompts []PromptManifest
	for _, p := range promptsData {
		if promptMap, ok := p.(map[string]interface{}); ok {
			prompt := PromptManifest{
				ID:      getString(promptMap, "id"),
				Name:    getString(promptMap, "name"),
				Content: getString(promptMap, "content"),
				Version: getString(promptMap, "version"),
			}
			prompts = append(prompts, prompt)
		}
	}

	// Create deployment request
	deployReq := PromptDeploymentRequest{
		Environment: environment,
		Prompts:     prompts,
		Metadata: map[string]string{
			"source":         "mcp",
			"commit_message": getString(args, "commit_message"),
		},
	}

	// Execute deployment
	deploymentID := fmt.Sprintf("mcp-%d", time.Now().Unix())
	go func() {
		if err := s.gitops.deployPrompts(deployReq, deploymentID); err != nil {
			log.Printf("Failed to deploy prompts via MCP: %v", err)
		}
	}()

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": fmt.Sprintf("Deployment initiated successfully!\nEnvironment: %s\nPrompts: %d\nDeployment ID: %s\nStatus: In Progress",
						environment, len(prompts), deploymentID),
				},
			},
			"isError": false,
		},
	}
}

// executeSyncRepository implements the sync_repository tool
func (s *MCPIntegrationServer) executeSyncRepository(id interface{}, args map[string]interface{}) MCPResponse {
	force := getBool(args, "force")

	// Sync with repository
	if err := s.gitops.pullLatestChanges(); err != nil {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32603,
				Message: fmt.Sprintf("Failed to sync repository: %v", err),
			},
		}
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": fmt.Sprintf("Repository synchronized successfully!\nForce sync: %t\nTimestamp: %s",
						force, time.Now().Format(time.RFC3339)),
				},
			},
			"isError": false,
		},
	}
}

// executeValidatePrompts implements the validate_prompts tool
func (s *MCPIntegrationServer) executeValidatePrompts(id interface{}, args map[string]interface{}) MCPResponse {
	promptsData, ok := args["prompts"].([]interface{})
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32602,
				Message: "Prompts array is required",
			},
		}
	}

	strict := getBool(args, "strict")

	// Validate prompts using existing logic
	if err := s.gitops.validatePrompts(); err != nil {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Result: map[string]interface{}{
				"content": []map[string]interface{}{
					{
						"type": "text",
						"text": fmt.Sprintf("Validation failed!\nStrict mode: %t\nError: %v\nPrompts checked: %d",
							strict, err, len(promptsData)),
					},
				},
				"isError": true,
			},
		}
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": fmt.Sprintf("Validation successful!\nStrict mode: %t\nPrompts validated: %d\nAll prompts passed validation checks",
						strict, len(promptsData)),
				},
			},
			"isError": false,
		},
	}
}

// executeExportConfigurations implements the export_configurations tool
func (s *MCPIntegrationServer) executeExportConfigurations(id interface{}, args map[string]interface{}) MCPResponse {
	includePrompts := getBool(args, "include_prompts")
	includePolicies := getBool(args, "include_policies")
	branch := getString(args, "branch")

	if branch == "" {
		branch = "main"
	}

	// Export configurations using existing logic
	if includePrompts {
		prompts, err := s.gitops.fetchPromptsFromService()
		if err != nil {
			return MCPResponse{
				JSONRPC: "2.0",
				ID:      id,
				Error: &MCPError{
					Code:    -32603,
					Message: fmt.Sprintf("Failed to fetch prompts: %v", err),
				},
			}
		}

		if err := s.gitops.exportPromptsToGit(prompts); err != nil {
			return MCPResponse{
				JSONRPC: "2.0",
				ID:      id,
				Error: &MCPError{
					Code:    -32603,
					Message: fmt.Sprintf("Failed to export prompts: %v", err),
				},
			}
		}
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": fmt.Sprintf("Export completed successfully!\nBranch: %s\nPrompts included: %t\nPolicies included: %t\nTimestamp: %s",
						branch, includePrompts, includePolicies, time.Now().Format(time.RFC3339)),
				},
			},
			"isError": false,
		},
	}
}

// handleResourcesList returns the list of available resources
func (s *MCPIntegrationServer) handleResourcesList(req MCPRequest) MCPResponse {
	resources := []MCPResource{
		{
			URI:         "integration://repositories",
			Name:        "Git Repositories",
			Description: "Connected Git repositories and their status",
			MimeType:    "application/json",
		},
		{
			URI:         "integration://deployments",
			Name:        "Deployment History",
			Description: "GitOps deployment history and status",
			MimeType:    "application/json",
		},
		{
			URI:         "integration://templates",
			Name:        "Prompt Templates",
			Description: "Available prompt templates and configurations",
			MimeType:    "application/json",
		},
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      req.ID,
		Result: map[string]interface{}{
			"resources": resources,
		},
	}
}

// handleResourceRead reads a specific resource
func (s *MCPIntegrationServer) handleResourceRead(req MCPRequest) MCPResponse {
	params, ok := req.Params.(map[string]interface{})
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "Invalid params",
			},
		}
	}

	uri, ok := params["uri"].(string)
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "URI is required",
			},
		}
	}

	switch uri {
	case "integration://repositories":
		return s.readRepositories(req.ID)
	case "integration://deployments":
		return s.readDeploymentHistory(req.ID)
	case "integration://templates":
		return s.readPromptTemplates(req.ID)
	default:
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32601,
				Message: "Resource not found",
			},
		}
	}
}

// Resource reading functions
func (s *MCPIntegrationServer) readRepositories(id interface{}) MCPResponse {
	repositories := map[string]interface{}{
		"configured_repository": map[string]interface{}{
			"url":    s.gitops.config.GitRepoURL,
			"branch": s.gitops.config.GitBranch,
			"status": "connected",
		},
		"workspace": map[string]interface{}{
			"directory": s.gitops.config.WorkspaceDir,
			"status":    "initialized",
		},
		"cloud_build": map[string]interface{}{
			"project": s.gitops.config.CloudBuildProject,
			"region":  s.gitops.config.CloudBuildRegion,
			"enabled": s.gitops.config.CloudBuildProject != "",
		},
		"updated": time.Now().UTC(),
	}

	repositoriesJSON, _ := json.MarshalIndent(repositories, "", "  ")
	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"contents": []map[string]interface{}{
				{
					"uri":      "integration://repositories",
					"mimeType": "application/json",
					"text":     string(repositoriesJSON),
				},
			},
		},
	}
}

func (s *MCPIntegrationServer) readDeploymentHistory(id interface{}) MCPResponse {
	history := map[string]interface{}{
		"message":   "Deployment history tracking not yet implemented",
		"timestamp": time.Now().UTC(),
		"note":      "This will contain GitOps deployment history, status, and rollback information",
		"available_endpoints": []string{
			"/gitops/status",
			"/gitops/deploy",
			"/gitops/sync",
		},
	}

	historyJSON, _ := json.MarshalIndent(history, "", "  ")
	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"contents": []map[string]interface{}{
				{
					"uri":      "integration://deployments",
					"mimeType": "application/json",
					"text":     string(historyJSON),
				},
			},
		},
	}
}

func (s *MCPIntegrationServer) readPromptTemplates(id interface{}) MCPResponse {
	templates := map[string]interface{}{
		"prompt_templates": map[string]interface{}{
			"basic_chat": map[string]interface{}{
				"name":        "Basic Chat Template",
				"description": "Standard chat completion template",
				"variables":   []string{"user_input", "context"},
			},
			"summarization": map[string]interface{}{
				"name":        "Summarization Template",
				"description": "Template for text summarization tasks",
				"variables":   []string{"text", "length", "style"},
			},
			"code_generation": map[string]interface{}{
				"name":        "Code Generation Template",
				"description": "Template for code generation tasks",
				"variables":   []string{"language", "requirements", "style"},
			},
		},
		"deployment_templates": map[string]interface{}{
			"environments":     []string{"dev", "staging", "production"},
			"validation_rules": []string{"schema_check", "content_safety", "performance_test"},
		},
		"updated": time.Now().UTC(),
	}

	templatesJSON, _ := json.MarshalIndent(templates, "", "  ")
	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"contents": []map[string]interface{}{
				{
					"uri":      "integration://templates",
					"mimeType": "application/json",
					"text":     string(templatesJSON),
				},
			},
		},
	}
}

// Global MCP server instance
var mcpIntegrationServer *MCPIntegrationServer

// InitializeMCPIntegrationServer initializes the MCP server for Integration Service
func InitializeMCPIntegrationServer(gitops *PromptOpsGitOps) {
	mcpIntegrationServer = NewMCPIntegrationServer(gitops)
	log.Printf("Integration Service MCP Server: Initialized successfully")
}
