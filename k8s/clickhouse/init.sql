-- This script is executed by <PERSON><PERSON><PERSON><PERSON> when the container starts,
-- thanks to the volume mount in docker-compose.yaml or Kubernetes YAML.
-- It creates the database and tables for storing inference logs, evaluation results,
-- and curated data if they do not already exist.

-- Create the database if it doesn't exist
CREATE DATABASE IF NOT EXISTS default;

-- Create the inference_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS default.inference_logs (
    -- Unique identifier for each request
    request_id String,

    -- Timestamp when the request was received by the proxy (nanosecond precision)
    timestamp DateTime64(9),

    -- HTTP method of the request (e.g., 'POST')
    method String,

    -- Path of the request URL (e.g., '/predict')
    path String,

    -- IP address of the client making the request
    client_ip String,

    -- User agent string from the client
    user_agent String,

    -- ID of the backend AI model selected for this request
    selected_backend_id String,

    -- URL of the selected backend
    backend_url String,

    -- Type of the backend (e.g., 'openai', 'google', 'anthropic')
    backend_type String,

    -- Timestamp when the response was received by the proxy (nanosecond precision)
    response_timestamp DateTime64(9),

    -- Latency of the request (time taken by the backend) in milliseconds
    latency_ms Float64,

    -- HTTP status code of the response from the backend
    status_code Int32,

    -- Error message if the request failed
    error String,

    -- ID of the policy applied to this request
    policy_id_applied String,

    -- Model name requested by the user
    model_requested String,

    -- Actual model ID used for the inference
    model_used String,

    -- Whether the response was streamed (0 for false, 1 for true)
    stream UInt8,

    -- CPU usage rate during inference
    cpu_usage_rate Float64,

    -- Memory usage in bytes during inference
    memory_usage_bytes Float64,

    -- Total calculated cost of the inference request
    total_cost Float64,

    -- Number of input tokens for the inference
    input_tokens Int64,

    -- Number of output tokens for the inference
    output_tokens Int64,

    -- Type of task (e.g., 'text-generation', 'summarization')
    task_type String,

    -- Identifier for the conversation thread
    conversation_id String,

    -- Identifier for the user making the request
    user_id String,

    -- Roles associated with the user (stored as a JSON string or array of strings)
    user_roles Array(String),

    -- Raw request headers (stored as JSON string)
    request_headers String,

    -- Snippet of the request body
    request_body_snippet String,

    -- Snippet of the response body
    response_body_snippet String,

    -- Raw response headers (stored as JSON string)
    response_headers String

) ENGINE = MergeTree()
ORDER BY (timestamp, request_id); -- Define the primary key for sorting data

-- Create the llm_evaluation_results table if it doesn't exist
CREATE TABLE IF NOT EXISTS default.llm_evaluation_results (
    id String,
    request_id String,
    prompt String,
    llm_response String,
    model_id String,
    prompt_id String,
    task_type String,
    evaluation_type String,
    score Float64,
    passed UInt8,
    feedback String,
    evaluated_at DateTime64(9),
    expected_response String,
    raw_metrics String,
    metadata String
) ENGINE = MergeTree()
ORDER BY (evaluated_at, id);

-- Create the curated_data table if it doesn't exist
CREATE TABLE IF NOT EXISTS default.curated_data (
    id String,
    request_id String,
    prompt String,
    llm_response String,
    model_id String,
    task_type String,
    generated_at DateTime64(9),
    evaluation_score Float64,
    evaluation_passed UInt8,
    evaluation_type String,
    feedback String,
    metadata String,
    source_log_id String,
    curated_by String
) ENGINE = MergeTree()
ORDER BY (generated_at, id);
