# Regular services with endpoints in default namespace to proxy to standard edition services
apiVersion: v1
kind: Service
metadata:
  name: policy-manager-standard-proxy-fixed
  namespace: default
  labels:
    app: policy-manager-standard-proxy-fixed
    edition: standard
spec:
  ports:
  - port: 8083
    targetPort: 8083
    protocol: TCP
  type: ClusterIP

---
apiVersion: v1
kind: Endpoints
metadata:
  name: policy-manager-standard-proxy-fixed
  namespace: default
subsets:
- addresses:
  - ip: **************
  ports:
  - port: 8083
    protocol: TCP

---
apiVersion: v1
kind: Service
metadata:
  name: dashboard-api-standard-proxy-fixed
  namespace: default
  labels:
    app: dashboard-api-standard-proxy-fixed
    edition: standard
spec:
  ports:
  - port: 8081
    targetPort: 8081
    protocol: TCP
  type: ClusterIP

---
apiVersion: v1
kind: Endpoints
metadata:
  name: dashboard-api-standard-proxy-fixed
  namespace: default
subsets:
- addresses:
  - ip: **************
  ports:
  - port: 8081
    protocol: TCP

---
apiVersion: v1
kind: Service
metadata:
  name: proxy-gateway-standard-proxy-fixed
  namespace: default
  labels:
    app: proxy-gateway-standard-proxy-fixed
    edition: standard
spec:
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP

---
apiVersion: v1
kind: Endpoints
metadata:
  name: proxy-gateway-standard-proxy-fixed
  namespace: default
subsets:
- addresses:
  - ip: *************
  ports:
  - port: 8080
    protocol: TCP

---
apiVersion: v1
kind: Service
metadata:
  name: api-fallback-standard-proxy-fixed
  namespace: default
  labels:
    app: api-fallback-standard-proxy-fixed
    edition: standard
spec:
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP

---
apiVersion: v1
kind: Endpoints
metadata:
  name: api-fallback-standard-proxy-fixed
  namespace: default
subsets:
- addresses:
  - ip: **************
  ports:
  - port: 8080
    protocol: TCP
