apiVersion: apps/v1
kind: Deployment
metadata:
  name: compliance-service
  labels:
    app: compliance-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: compliance-service
  template:
    metadata:
      labels:
        app: compliance-service
    spec:
      containers:
      - name: compliance-service
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-compliance-service:latest
        ports:
        - containerPort: 8087
        env:
        - name: PORT
          value: "8087"
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8087
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /health
            port: 8087
          initialDelaySeconds: 5
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: compliance-service
  labels:
    app: compliance-service
spec:
  selector:
    app: compliance-service
  ports:
    - protocol: TCP
      port: 8087
      targetPort: 8087
  type: ClusterIP
