package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/rs/cors"
)

// ComplianceFramework represents a regulatory framework
type ComplianceFramework struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Version     string `json:"version"`
	Description string `json:"description"`
	Region      string `json:"region"`
	Type        string `json:"type"` // "regulation", "standard", "guideline"
	URL         string `json:"url"`
}

// ComplianceRequirement represents a specific requirement within a framework
type ComplianceRequirement struct {
	ID            string   `json:"id"`
	FrameworkID   string   `json:"framework_id"`
	Section       string   `json:"section"`
	Title         string   `json:"title"`
	Description   string   `json:"description"`
	Mandatory     bool     `json:"mandatory"`
	RiskLevel     string   `json:"risk_level"`    // "low", "medium", "high", "critical"
	Categories    []string `json:"categories"`    // e.g., ["bias", "transparency", "safety"]
	Applicability string   `json:"applicability"` // "all", "high_risk", "limited_risk"
}

// ComplianceAssessment represents an assessment of compliance for a model/agent
type ComplianceAssessment struct {
	ID                string                       `json:"id"`
	ModelID           string                       `json:"model_id"`
	AgentID           string                       `json:"agent_id,omitempty"`
	FrameworkID       string                       `json:"framework_id"`
	AssessmentDate    time.Time                    `json:"assessment_date"`
	OverallStatus     string                       `json:"overall_status"`   // "compliant", "partial", "non_compliant", "not_assessed"
	ComplianceScore   float64                      `json:"compliance_score"` // 0-1 scale
	RequirementStatus map[string]RequirementStatus `json:"requirement_status"`
	Gaps              []ComplianceGap              `json:"gaps"`
	Recommendations   []ComplianceRecommendation   `json:"recommendations"`
	Evidence          []Evidence                   `json:"evidence"`
	NextReviewDate    time.Time                    `json:"next_review_date"`
	AssessedBy        string                       `json:"assessed_by"`
	CreatedAt         time.Time                    `json:"created_at"`
	UpdatedAt         time.Time                    `json:"updated_at"`
}

// RequirementStatus represents the status of a specific requirement
type RequirementStatus struct {
	RequirementID string    `json:"requirement_id"`
	Status        string    `json:"status"` // "compliant", "partial", "non_compliant", "not_applicable"
	Score         float64   `json:"score"`  // 0-1 scale
	Evidence      []string  `json:"evidence"`
	Notes         string    `json:"notes"`
	LastChecked   time.Time `json:"last_checked"`
}

// ComplianceGap represents a compliance gap
type ComplianceGap struct {
	RequirementID string `json:"requirement_id"`
	GapType       string `json:"gap_type"` // "documentation", "technical", "process", "governance"
	Severity      string `json:"severity"` // "low", "medium", "high", "critical"
	Description   string `json:"description"`
	Impact        string `json:"impact"`
	Timeline      string `json:"timeline"` // "immediate", "short_term", "long_term"
}

// ComplianceRecommendation represents a recommendation to address gaps
type ComplianceRecommendation struct {
	ID          string   `json:"id"`
	GapIDs      []string `json:"gap_ids"`
	Type        string   `json:"type"`     // "technical", "process", "documentation", "training"
	Priority    string   `json:"priority"` // "low", "medium", "high", "critical"
	Description string   `json:"description"`
	Actions     []string `json:"actions"`
	Effort      string   `json:"effort"` // "low", "medium", "high"
	Timeline    string   `json:"timeline"`
	Owner       string   `json:"owner"`
}

// Evidence represents evidence supporting compliance
type Evidence struct {
	ID           string     `json:"id"`
	Type         string     `json:"type"` // "document", "test_result", "audit_report", "certification"
	Title        string     `json:"title"`
	Description  string     `json:"description"`
	URL          string     `json:"url,omitempty"`
	Hash         string     `json:"hash,omitempty"`
	CreatedDate  time.Time  `json:"created_date"`
	ValidUntil   *time.Time `json:"valid_until,omitempty"`
	Requirements []string   `json:"requirements"` // Requirement IDs this evidence supports
}

// ComplianceReport represents a comprehensive compliance report
type ComplianceReport struct {
	ID               string                       `json:"id"`
	Title            string                       `json:"title"`
	ReportType       string                       `json:"report_type"` // "summary", "detailed", "gap_analysis", "trend"
	Scope            map[string]interface{}       `json:"scope"`       // Models, agents, frameworks included
	GeneratedAt      time.Time                    `json:"generated_at"`
	ReportPeriod     map[string]time.Time         `json:"report_period"` // "start", "end"
	OverallSummary   ComplianceSummary            `json:"overall_summary"`
	FrameworkSummary map[string]ComplianceSummary `json:"framework_summary"`
	TrendAnalysis    *TrendAnalysis               `json:"trend_analysis,omitempty"`
	Recommendations  []ComplianceRecommendation   `json:"recommendations"`
	GeneratedBy      string                       `json:"generated_by"`
}

// ComplianceSummary provides summary statistics
type ComplianceSummary struct {
	TotalRequirements    int     `json:"total_requirements"`
	CompliantCount       int     `json:"compliant_count"`
	PartialCount         int     `json:"partial_count"`
	NonCompliantCount    int     `json:"non_compliant_count"`
	NotAssessedCount     int     `json:"not_assessed_count"`
	CompliancePercentage float64 `json:"compliance_percentage"`
	AverageScore         float64 `json:"average_score"`
}

// TrendAnalysis provides trend analysis over time
type TrendAnalysis struct {
	Period           string                `json:"period"` // "monthly", "quarterly", "yearly"
	ComplianceTrend  []ComplianceDataPoint `json:"compliance_trend"`
	GapTrend         []GapDataPoint        `json:"gap_trend"`
	ImprovementAreas []string              `json:"improvement_areas"`
	RiskAreas        []string              `json:"risk_areas"`
}

// ComplianceDataPoint represents a point in compliance trend
type ComplianceDataPoint struct {
	Date             time.Time `json:"date"`
	ComplianceScore  float64   `json:"compliance_score"`
	CompliantCount   int       `json:"compliant_count"`
	TotalAssessments int       `json:"total_assessments"`
}

// GapDataPoint represents a point in gap trend
type GapDataPoint struct {
	Date         time.Time `json:"date"`
	TotalGaps    int       `json:"total_gaps"`
	CriticalGaps int       `json:"critical_gaps"`
	HighGaps     int       `json:"high_gaps"`
	ResolvedGaps int       `json:"resolved_gaps"`
}

// ComplianceService manages compliance assessments and reporting
type ComplianceService struct {
	frameworks   map[string]ComplianceFramework
	requirements map[string]ComplianceRequirement
	assessments  map[string]ComplianceAssessment
	reports      map[string]ComplianceReport
	mu           sync.RWMutex
}

// NewComplianceService creates a new compliance service
func NewComplianceService() *ComplianceService {
	service := &ComplianceService{
		frameworks:   make(map[string]ComplianceFramework),
		requirements: make(map[string]ComplianceRequirement),
		assessments:  make(map[string]ComplianceAssessment),
		reports:      make(map[string]ComplianceReport),
	}

	// Initialize with sample data
	service.initializeSampleData()

	return service
}

// initializeSampleData creates sample compliance data
func (cs *ComplianceService) initializeSampleData() {
	// Sample frameworks
	frameworks := []ComplianceFramework{
		{
			ID:          "eu-ai-act",
			Name:        "EU AI Act",
			Version:     "2024",
			Description: "European Union Artificial Intelligence Act",
			Region:      "EU",
			Type:        "regulation",
			URL:         "https://eur-lex.europa.eu/legal-content/EN/TXT/?uri=CELEX:32024R1689",
		},
		{
			ID:          "nist-ai-rmf",
			Name:        "NIST AI Risk Management Framework",
			Version:     "1.0",
			Description: "NIST Artificial Intelligence Risk Management Framework",
			Region:      "US",
			Type:        "standard",
			URL:         "https://www.nist.gov/itl/ai-risk-management-framework",
		},
		{
			ID:          "iso-23053",
			Name:        "ISO/IEC 23053",
			Version:     "2022",
			Description: "Framework for AI systems using ML",
			Region:      "International",
			Type:        "standard",
			URL:         "https://www.iso.org/standard/74438.html",
		},
	}

	for _, framework := range frameworks {
		cs.frameworks[framework.ID] = framework
	}

	// Sample requirements
	requirements := []ComplianceRequirement{
		{
			ID:            "eu-ai-act-art-9",
			FrameworkID:   "eu-ai-act",
			Section:       "Article 9",
			Title:         "Risk Management System",
			Description:   "Establish and maintain a risk management system",
			Mandatory:     true,
			RiskLevel:     "high",
			Categories:    []string{"risk_management", "governance"},
			Applicability: "high_risk",
		},
		{
			ID:            "eu-ai-act-art-10",
			FrameworkID:   "eu-ai-act",
			Section:       "Article 10",
			Title:         "Data and Data Governance",
			Description:   "Training, validation and testing data sets shall meet quality criteria",
			Mandatory:     true,
			RiskLevel:     "high",
			Categories:    []string{"data_quality", "governance"},
			Applicability: "high_risk",
		},
		{
			ID:            "nist-ai-rmf-govern-1",
			FrameworkID:   "nist-ai-rmf",
			Section:       "GOVERN-1",
			Title:         "Policies and Procedures",
			Description:   "Policies and procedures are in place to address AI risks",
			Mandatory:     false,
			RiskLevel:     "medium",
			Categories:    []string{"governance", "policy"},
			Applicability: "all",
		},
		{
			ID:            "nist-ai-rmf-map-1",
			FrameworkID:   "nist-ai-rmf",
			Section:       "MAP-1",
			Title:         "AI System Context",
			Description:   "Context of use is documented and understood",
			Mandatory:     false,
			RiskLevel:     "medium",
			Categories:    []string{"documentation", "context"},
			Applicability: "all",
		},
	}

	for _, req := range requirements {
		cs.requirements[req.ID] = req
	}

	// Sample assessments
	assessments := []ComplianceAssessment{
		{
			ID:              "assessment-001",
			ModelID:         "gpt-4",
			FrameworkID:     "eu-ai-act",
			AssessmentDate:  time.Now().Add(-7 * 24 * time.Hour),
			OverallStatus:   "partial",
			ComplianceScore: 0.75,
			RequirementStatus: map[string]RequirementStatus{
				"eu-ai-act-art-9": {
					RequirementID: "eu-ai-act-art-9",
					Status:        "compliant",
					Score:         0.9,
					Evidence:      []string{"risk-mgmt-doc-001", "audit-report-2024-q1"},
					Notes:         "Risk management system implemented and documented",
					LastChecked:   time.Now().Add(-7 * 24 * time.Hour),
				},
				"eu-ai-act-art-10": {
					RequirementID: "eu-ai-act-art-10",
					Status:        "partial",
					Score:         0.6,
					Evidence:      []string{"data-quality-report-001"},
					Notes:         "Data governance partially implemented, needs improvement in validation procedures",
					LastChecked:   time.Now().Add(-7 * 24 * time.Hour),
				},
			},
			Gaps: []ComplianceGap{
				{
					RequirementID: "eu-ai-act-art-10",
					GapType:       "process",
					Severity:      "medium",
					Description:   "Data validation procedures not fully documented",
					Impact:        "May affect compliance with data governance requirements",
					Timeline:      "short_term",
				},
			},
			Recommendations: []ComplianceRecommendation{
				{
					ID:          "rec-001",
					GapIDs:      []string{"gap-001"},
					Type:        "process",
					Priority:    "medium",
					Description: "Implement comprehensive data validation procedures",
					Actions:     []string{"Document validation procedures", "Train staff", "Implement automated checks"},
					Effort:      "medium",
					Timeline:    "short_term",
					Owner:       "Data Team",
				},
			},
			Evidence: []Evidence{
				{
					ID:           "risk-mgmt-doc-001",
					Type:         "document",
					Title:        "Risk Management System Documentation",
					Description:  "Comprehensive documentation of AI risk management system",
					CreatedDate:  time.Now().Add(-30 * 24 * time.Hour),
					Requirements: []string{"eu-ai-act-art-9"},
				},
			},
			NextReviewDate: time.Now().Add(90 * 24 * time.Hour),
			AssessedBy:     "Compliance Team",
			CreatedAt:      time.Now().Add(-7 * 24 * time.Hour),
			UpdatedAt:      time.Now().Add(-1 * 24 * time.Hour),
		},
	}

	for _, assessment := range assessments {
		cs.assessments[assessment.ID] = assessment
	}
}

// GetComplianceFrameworks retrieves all compliance frameworks
func (cs *ComplianceService) GetComplianceFrameworks(w http.ResponseWriter, r *http.Request) {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	var frameworks []ComplianceFramework
	for _, framework := range cs.frameworks {
		frameworks = append(frameworks, framework)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(frameworks)
}

// GetComplianceAssessments retrieves compliance assessments with optional filtering
func (cs *ComplianceService) GetComplianceAssessments(w http.ResponseWriter, r *http.Request) {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	modelID := r.URL.Query().Get("model_id")
	frameworkID := r.URL.Query().Get("framework_id")

	var assessments []ComplianceAssessment
	for _, assessment := range cs.assessments {
		if modelID != "" && assessment.ModelID != modelID {
			continue
		}
		if frameworkID != "" && assessment.FrameworkID != frameworkID {
			continue
		}
		assessments = append(assessments, assessment)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(assessments)
}

// GenerateComplianceReport generates a compliance report
func (cs *ComplianceService) GenerateComplianceReport(w http.ResponseWriter, r *http.Request) {
	var request struct {
		Title      string                 `json:"title"`
		ReportType string                 `json:"report_type"`
		Scope      map[string]interface{} `json:"scope"`
		Period     map[string]time.Time   `json:"period"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	report := cs.generateReport(request.Title, request.ReportType, request.Scope, request.Period)

	cs.mu.Lock()
	cs.reports[report.ID] = report
	cs.mu.Unlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(report)
}

// generateReport creates a compliance report
func (cs *ComplianceService) generateReport(title, reportType string, scope map[string]interface{}, period map[string]time.Time) ComplianceReport {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	// Calculate overall summary
	totalReqs := len(cs.requirements)
	compliantCount := 0
	partialCount := 0
	nonCompliantCount := 0
	totalScore := 0.0
	assessmentCount := 0

	frameworkSummary := make(map[string]ComplianceSummary)

	for _, assessment := range cs.assessments {
		assessmentCount++
		switch assessment.OverallStatus {
		case "compliant":
			compliantCount++
		case "partial":
			partialCount++
		case "non_compliant":
			nonCompliantCount++
		}
		totalScore += assessment.ComplianceScore

		// Framework-specific summary
		if _, exists := frameworkSummary[assessment.FrameworkID]; !exists {
			frameworkSummary[assessment.FrameworkID] = ComplianceSummary{}
		}
	}

	var avgScore float64
	if assessmentCount > 0 {
		avgScore = totalScore / float64(assessmentCount)
	}

	compliancePercentage := float64(compliantCount) / float64(assessmentCount) * 100

	overallSummary := ComplianceSummary{
		TotalRequirements:    totalReqs,
		CompliantCount:       compliantCount,
		PartialCount:         partialCount,
		NonCompliantCount:    nonCompliantCount,
		NotAssessedCount:     totalReqs - compliantCount - partialCount - nonCompliantCount,
		CompliancePercentage: compliancePercentage,
		AverageScore:         avgScore,
	}

	return ComplianceReport{
		ID:               uuid.New().String(),
		Title:            title,
		ReportType:       reportType,
		Scope:            scope,
		GeneratedAt:      time.Now(),
		ReportPeriod:     period,
		OverallSummary:   overallSummary,
		FrameworkSummary: frameworkSummary,
		Recommendations:  cs.generateReportRecommendations(),
		GeneratedBy:      "Compliance Service",
	}
}

// generateReportRecommendations generates recommendations for the report
func (cs *ComplianceService) generateReportRecommendations() []ComplianceRecommendation {
	var recommendations []ComplianceRecommendation

	// Analyze gaps across all assessments and generate recommendations
	gapCounts := make(map[string]int)
	for _, assessment := range cs.assessments {
		for _, gap := range assessment.Gaps {
			gapCounts[gap.GapType]++
		}
	}

	if gapCounts["process"] > 0 {
		recommendations = append(recommendations, ComplianceRecommendation{
			ID:          uuid.New().String(),
			Type:        "process",
			Priority:    "high",
			Description: "Standardize and document compliance processes across all models",
			Actions:     []string{"Create process templates", "Implement regular reviews", "Train teams"},
			Effort:      "medium",
			Timeline:    "short_term",
			Owner:       "Compliance Team",
		})
	}

	if gapCounts["documentation"] > 0 {
		recommendations = append(recommendations, ComplianceRecommendation{
			ID:          uuid.New().String(),
			Type:        "documentation",
			Priority:    "medium",
			Description: "Improve documentation quality and completeness",
			Actions:     []string{"Update documentation standards", "Implement automated checks", "Regular audits"},
			Effort:      "low",
			Timeline:    "immediate",
			Owner:       "Documentation Team",
		})
	}

	return recommendations
}

// HealthCheck provides service health status
func (cs *ComplianceService) HealthCheck(w http.ResponseWriter, r *http.Request) {
	cs.mu.RLock()
	frameworkCount := len(cs.frameworks)
	assessmentCount := len(cs.assessments)
	cs.mu.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":               "healthy",
		"service":              "compliance-service",
		"timestamp":            time.Now(),
		"frameworks_count":     frameworkCount,
		"assessments_count":    assessmentCount,
		"supported_frameworks": []string{"EU AI Act", "NIST AI RMF", "ISO/IEC 23053"},
	})
}

func main() {
	service := NewComplianceService()

	r := mux.NewRouter()

	// API routes
	r.HandleFunc("/health", service.HealthCheck).Methods("GET")
	r.HandleFunc("/compliance/frameworks", service.GetComplianceFrameworks).Methods("GET")
	r.HandleFunc("/compliance/assessments", service.GetComplianceAssessments).Methods("GET")
	r.HandleFunc("/compliance/reports", service.GenerateComplianceReport).Methods("POST")

	// Enable CORS
	c := cors.New(cors.Options{
		AllowedOrigins: []string{"*"},
		AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders: []string{"*"},
	})

	handler := c.Handler(r)

	port := ":8087"
	fmt.Printf("Compliance Service starting on port %s...\n", port)
	log.Fatal(http.ListenAndServe(port, handler))
}
