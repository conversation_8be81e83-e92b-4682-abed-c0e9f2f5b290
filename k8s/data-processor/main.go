package main

// Run the following command to update dependencies:
// go mod tidy

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net/http"
	"os"
	"os/signal" // For graceful shutdown
	"sync"
	"syscall" // For graceful shutdown
	"time"

	_ "github.com/ClickHouse/clickhouse-go/v2" // ClickHouse driver
	"github.com/go-redis/redis/v8"             // Redis client
	"github.com/google/uuid"                   // For generating UUIDs for evaluation results and fixing bad ones
	"github.com/segmentio/kafka-go"            // Kafka client
)

// --- Constants ---
const (
	kafkaBroker                     = "kafka:9092"
	kafkaTopic                      = "inference-logs"
	kafkaAgentTopic                 = "agent-performance"
	kafkaWorkflowTopic              = "workflow-execution"
	kafkaPlanningTopic              = "planning-execution"
	kafkaTrustQualityTopic          = "agent-trust-quality" // New topic for trust & quality metrics
	kafkaGroupID                    = "data-processor-group"
	clickhouseHost                  = "clickhouse"
	clickhousePort                  = 9000
	clickhouseDB                    = "default"
	redisAddr                       = "redis:6379"
	prometheusURL                   = "http://prometheus:9090"
	REDIS_MODEL_PROFILES_KEY_PREFIX = "model_profile:"
	MODEL_PROFILE_UPDATES_CHANNEL   = "model_profile_updates"
	REDIS_PROMPT_KEY_PREFIX         = "prompt:"
	PROMPT_UPDATES_CHANNEL          = "prompt_updates"

	// ClickHouse table names
	CLICKHOUSE_INFERENCE_LOGS_TABLE      = "inference_logs"
	CLICKHOUSE_EVALUATION_RESULTS_TABLE  = "llm_evaluation_results"
	CLICKHOUSE_CURATED_DATA_TABLE        = "curated_data"
	CLICKHOUSE_AGENT_PERFORMANCE_TABLE   = "agent_performance"
	CLICKHOUSE_WORKFLOW_EXECUTION_TABLE  = "workflow_execution"
	CLICKHOUSE_PLANNING_LOGS_TABLE       = "planning_logs"
	CLICKHOUSE_AGENT_TRUST_QUALITY_TABLE = "agent_trust_quality_metrics" // New table for metrics

	SYNTHETIC_DATA_GENERATION_MODEL_ID = "gemini-2.5-flash-preview-05-20"
	FEEDBACK_LOOP_INTERVAL             = 3600 * time.Second
)

// --- Global Variables (Protected by Mutexes) ---
var (
	clickhouseConn *sql.DB
	redisClient    *redis.Client
	kafkaReader    *kafka.Reader

	// Multi-Agent Kafka readers
	kafkaAgentReader        *kafka.Reader
	kafkaWorkflowReader     *kafka.Reader
	kafkaPlanningReader     *kafka.Reader
	kafkaTrustQualityReader *kafka.Reader // New reader

	// Caches for Redis-backed data, protected by mutexes
	modelProfiles  map[string]ModelProfile
	modelProfileMu sync.RWMutex
	prompts        map[string]Prompt
	promptsMu      sync.RWMutex

	rng *rand.Rand // Global random number generator instance

	appCtx    context.Context
	appCancel context.CancelFunc
)

// --- Structs ---

type InferenceLog struct {
	RequestID           string          `json:"request_id"`
	Timestamp           time.Time       `json:"timestamp"`
	Method              string          `json:"method"`
	Path                string          `json:"path"`
	ClientIP            string          `json:"client_ip"`
	UserAgent           string          `json:"user_agent"`
	SelectedBackendID   string          `json:"selected_backend_id"`
	BackendURL          string          `json:"backend_url"`
	BackendType         string          `json:"backend_type,omitempty"`
	ResponseTimestamp   time.Time       `json:"response_timestamp"`
	LatencyMs           float64         `json:"latency_ms"`
	StatusCode          int32           `json:"status_code"`
	Error               string          `json:"error,omitempty"`
	PolicyIDApplied     string          `json:"policy_id_applied,omitempty"`
	ModelRequested      string          `json:"model_requested,omitempty"`
	ModelUsed           string          `json:"model_used,omitempty"`
	Stream              bool            `json:"stream,omitempty"`
	CPUUsage            float64         `json:"cpu_usage_rate"`
	MemoryUsage         float64         `json:"memory_usage_bytes"`
	TotalCost           float64         `json:"total_cost"`
	InputTokens         int64           `json:"input_tokens,omitempty"`
	OutputTokens        int64           `json:"output_tokens,omitempty"`
	TaskType            string          `json:"task_type,omitempty"`
	ConversationID      string          `json:"conversation_id,omitempty"`
	UserID              string          `json:"user_id,omitempty"`
	UserRoles           []string        `json:"user_roles,omitempty"`
	RequestHeaders      json.RawMessage `json:"request_headers,omitempty"`
	RequestBodySnippet  string          `json:"request_body_snippet,omitempty"`
	ResponseBodySnippet string          `json:"response_body_snippet,omitempty"`
	ResponseHeaders     json.RawMessage `json:"response_headers,omitempty"`

	// Enhanced fields for intelligent routing analytics
	PromptIntent       string  `json:"prompt_intent,omitempty"`       // question, instruction, creative, etc.
	PromptComplexity   string  `json:"prompt_complexity,omitempty"`   // low, medium, high
	PromptDomain       string  `json:"prompt_domain,omitempty"`       // programming, creative, business, etc.
	AnalysisConfidence float64 `json:"analysis_confidence,omitempty"` // 0-1 confidence in prompt analysis
	RoutingStrategy    string  `json:"routing_strategy,omitempty"`    // capability-based, cost-based, etc.
	CapabilityScore    float64 `json:"capability_score,omitempty"`    // How well the selected model matches the task
}

type ModelProfile struct {
	ID                  string          `json:"id"`
	Name                string          `json:"name"`
	Aliases             []string        `json:"aliases"`
	Capabilities        []string        `json:"capabilities"`
	PricingTier         string          `json:"pricing_tier"`
	DataSensitivity     string          `json:"data_sensitivity"`
	ExpectedLatencyMs   float64         `json:"expected_latency_ms"`
	ExpectedCost        float64         `json:"expected_cost"`
	BackendURL          string          `json:"url"`
	BackendType         string          `json:"backend_type"`
	CostPerInputToken   float64         `json:"cost_per_input_token"`
	CostPerOutputToken  float64         `json:"cost_per_output_token"`
	CPUCostPerHour      float64         `json:"cpu_cost_per_hour"`
	MemoryCostPerHour   float64         `json:"memory_cost_per_hour"`
	APIKey              string          `json:"api_key,omitempty"`
	CreatedAt           time.Time       `json:"created_at"`
	UpdatedAt           time.Time       `json:"updated_at"`
	Version             string          `json:"version,omitempty"`
	Owner               string          `json:"owner,omitempty"`
	Status              string          `json:"status,omitempty"`
	DocumentationURL    string          `json:"documentation_url,omitempty"`
	License             string          `json:"license,omitempty"`
	FineTuningDetails   string          `json:"fine_tuning_details,omitempty"`
	InputContextLength  int             `json:"input_context_length,omitempty"`
	OutputContextLength int             `json:"output_context_length,omitempty"`
	TrainingDataInfo    string          `json:"training_data_info,omitempty"`
	LastEvaluatedAt     *time.Time      `json:"last_evaluated_at,omitempty"`
	EvaluationMetrics   json.RawMessage `json:"evaluation_metrics,omitempty"`
	ComplianceTags      []string        `json:"compliance_tags,omitempty"`
	Region              string          `json:"region,omitempty"`
	Provider            string          `json:"provider,omitempty"`
	Description         string          `json:"description,omitempty"`
}
type Prompt struct {
	ID          string          `json:"id"`
	Name        string          `json:"name"`
	Version     string          `json:"version"`
	Content     string          `json:"content"`
	Description string          `json:"description,omitempty"`
	Tags        []string        `json:"tags,omitempty"`
	Owner       string          `json:"owner,omitempty"`
	Status      string          `json:"status,omitempty"`
	Metadata    json.RawMessage `json:"metadata,omitempty"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
}

type Policy struct {
	ID           string          `json:"id"`
	Name         string          `json:"name"`
	Description  string          `json:"description"`
	Criteria     json.RawMessage `json:"criteria"`
	Action       string          `json:"action"`
	BackendID    string          `json:"backend_id"`
	Priority     int             `json:"priority"`
	Rules        json.RawMessage `json:"rules"`
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`
	Metadata     json.RawMessage `json:"metadata"`
	RateLimit    int             `json:"rate_limit"`
	Budget       float64         `json:"budget"`
	Effect       string          `json:"effect"`
	Subjects     []string        `json:"subjects"`
	ResourceType string          `json:"resource_type"`
	ResourceIDs  []string        `json:"resource_ids"`
	Permissions  []string        `json:"permissions"`
	Status       string          `json:"status"`
}

type EvaluationRequest struct {
	RequestID        string          `json:"request_id"`
	Prompt           string          `json:"prompt"`
	LLMResponse      string          `json:"llm_response"`
	ModelID          string          `json:"model_id"`
	PromptID         string          `json:"prompt_id,omitempty"` // NEW: ID of the prompt used for performance tracking
	TaskType         string          `json:"task_type,omitempty"`
	ExpectedResponse string          `json:"expected_response,omitempty"`
	EvaluationType   string          `json:"evaluation_type,omitempty"`
	RawMetrics       json.RawMessage `json:"raw_metrics,omitempty"`
	Metadata         json.RawMessage `json:"metadata,omitempty"`
}

type LLMEvaluationResult struct {
	ID               string          `json:"id"`
	RequestID        string          `json:"request_id"`
	Prompt           string          `json:"prompt"`
	LLMResponse      string          `json:"llm_response"`
	ModelID          string          `json:"model_id"`
	PromptID         string          `json:"prompt_id,omitempty"` // NEW: ID of the prompt used for performance tracking
	TaskType         string          `json:"task_type,omitempty"`
	EvaluationType   string          `json:"evaluation_type"`
	Score            float64         `json:"score"`
	Passed           bool            `json:"passed"`
	Feedback         string          `json:"feedback,omitempty"`
	EvaluatedAt      time.Time       `json:"evaluated_at"`
	ExpectedResponse string          `json:"expected_response,omitempty"`
	RawMetrics       json.RawMessage `json:"raw_metrics,omitempty"`
	Metadata         json.RawMessage `json:"metadata,omitempty"`
}

type EvaluationResult struct {
	ID               string    `json:"id"`
	RequestID        string    `json:"request_id"`
	Prompt           string    `json:"prompt"`
	LLMResponse      string    `json:"llm_response"`
	ModelID          string    `json:"model_id"`
	PromptID         string    `json:"prompt_id,omitempty"` // NEW: ID of the prompt used for performance tracking
	TaskType         string    `json:"task_type,omitempty"`
	EvaluationType   string    `json:"evaluation_type"`
	Score            float64   `json:"score"`
	Passed           bool      `json:"passed"`
	Feedback         string    `json:"feedback,omitempty"`
	EvaluatedAt      time.Time `json:"evaluated_at"`
	ExpectedResponse string    `json:"expected_response,omitempty"`
	RawMetrics       string    `json:"raw_metrics"`
	Metadata         string    `json:"metadata"`
}

type CuratedData struct {
	ID               string    `json:"id"`
	RequestID        string    `json:"request_id"`
	Prompt           string    `json:"prompt"`
	LLMResponse      string    `json:"llm_response"`
	ModelID          string    `json:"model_id"`
	TaskType         string    `json:"task_type,omitempty"`
	GeneratedAt      time.Time `json:"generated_at"`
	EvaluationScore  float64   `json:"evaluation_score"`
	EvaluationPassed bool      `json:"evaluation_passed"`
	EvaluationType   string    `json:"evaluation_type"`
	Feedback         string    `json:"feedback,omitempty"`
	Metadata         string    `json:"metadata"`
	SourceLogID      string    `json:"source_log_id,omitempty"`
	CuratedBy        string    `json:"curated_by,omitempty"`
}

type OptimizationStatus struct {
	OptimalBackendID string    `json:"optimal_backend_id"`
	AverageLatency   float64   `json:"average_latency_ms"`
	Timestamp        time.Time `json:"timestamp"`
	Optimization     string    `json:"optimization"`
	Message          string    `json:"message"`
}

type OpenAIChatCompletionRequest struct {
	Model    string `json:"model"`
	Messages []struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	} `json:"messages"`
	Stream bool `json:"stream,omitempty"`
}

type OpenAICompletionResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int64 `json:"prompt_tokens"`
		CompletionTokens int64 `json:"completion_tokens"`
		TotalTokens      int64 `json:"total_tokens"`
	} `json:"usage"`
}

type GeminiGenerateContentRequest struct {
	Contents []struct {
		Role  string `json:"role"`
		Parts []struct {
			Text string `json:"text"`
		} `json:"parts"`
	} `json:"contents"`
	GenerationConfig map[string]any `json:"generationConfig,omitempty"`
}

type GeminiGenerateContentResponse struct {
	Candidates []struct {
		Content struct {
			Parts []struct {
				Text string `json:"text"`
			} `json:"parts"`
			Role string `json:"role"`
		} `json:"content"`
	} `json:"candidates"`
	UsageMetadata struct {
		PromptTokenCount     int64 `json:"promptTokenCount"`
		CandidatesTokenCount int64 `json:"candidatesTokenCount"`
		TotalTokenCount      int64 `json:"totalTokenCount"`
	} `json:"usageMetadata"`
}

type AnthropicMessagesRequest struct {
	Model    string `json:"model"`
	Messages []struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	} `json:"messages"`
	MaxTokens int `json:"max_tokens"`
}

type AnthropicMessagesResponse struct {
	ID      string `json:"id"`
	Type    string `json:"type"`
	Role    string `json:"role"`
	Content []struct {
		Type string `json:"type"`
		Text string `json:"text"`
	} `json:"content"`
	StopReason string `json:"stop_reason"`
	Model      string `json:"model"`
	Usage      struct {
		InputTokens  int64 `json:"input_tokens"`
		OutputTokens int64 `json:"output_tokens"`
	} `json:"usage"`
}

type AgentPerformanceLog struct {
	AgentID         string     `json:"agent_id"`
	AgentName       string     `json:"agent_name"`
	AgentType       string     `json:"agent_type"`
	TaskID          string     `json:"task_id"`
	TaskType        string     `json:"task_type"`
	Status          string     `json:"status"`
	StartedAt       time.Time  `json:"started_at"`
	CompletedAt     *time.Time `json:"completed_at,omitempty"`
	Duration        *int64     `json:"duration_ms,omitempty"`
	Success         bool       `json:"success"`
	ErrorMessage    string     `json:"error_message,omitempty"`
	InputTokens     int64      `json:"input_tokens"`
	OutputTokens    int64      `json:"output_tokens"`
	Cost            float64    `json:"cost"`
	HealthStatus    string     `json:"health_status"`
	Capabilities    []string   `json:"capabilities"`
	CurrentWorkload int        `json:"current_workload"`
	MaxConcurrency  int        `json:"max_concurrency"`
	LastActiveAt    time.Time  `json:"last_active_at"`
	Metadata        string     `json:"metadata"`
}

type WorkflowExecutionLog struct {
	WorkflowID      string     `json:"workflow_id"`
	WorkflowName    string     `json:"workflow_name"`
	ExecutionID     string     `json:"execution_id"`
	Status          string     `json:"status"`
	StartedAt       time.Time  `json:"started_at"`
	CompletedAt     *time.Time `json:"completed_at,omitempty"`
	Duration        *int64     `json:"duration_ms,omitempty"`
	TasksTotal      int        `json:"tasks_total"`
	TasksCompleted  int        `json:"tasks_completed"`
	TasksFailed     int        `json:"tasks_failed"`
	AgentsInvolved  []string   `json:"agents_involved"`
	TotalCost       float64    `json:"total_cost"`
	CreatedBy       string     `json:"created_by"`
	ErrorMessage    string     `json:"error_message,omitempty"`
	ProgressPercent float64    `json:"progress_percent"`
	Metadata        string     `json:"metadata"`
}

type PlanningExecutionLog struct {
	GoalID          string     `json:"goal_id"`
	GoalDescription string     `json:"goal_description"`
	ExecutionID     string     `json:"execution_id"`
	Status          string     `json:"status"`
	StartedAt       time.Time  `json:"started_at"`
	CompletedAt     *time.Time `json:"completed_at,omitempty"`
	Duration        *int64     `json:"duration_ms,omitempty"`
	TasksTotal      int        `json:"tasks_total"`
	TasksCompleted  int        `json:"tasks_completed"`
	TasksFailed     int        `json:"tasks_failed"`
	TotalCost       float64    `json:"total_cost"`
	CreatedBy       string     `json:"created_by"`
	ErrorMessage    string     `json:"error_message,omitempty"`
	ProgressPercent float64    `json:"progress_percent"`
	Metadata        string     `json:"metadata"`
}

type AgentTrustAndQualityLog struct {
	AgentID                    string    `json:"agent_id"`
	Timestamp                  time.Time `json:"timestamp"`
	Accuracy                   float64   `json:"accuracy"`
	Precision                  float64   `json:"precision"`
	Recall                     float64   `json:"recall"`
	F1Score                    float64   `json:"f1_score"`
	AdversarialRobustnessScore float64   `json:"adversarial_robustness_score"`
	DriftScore                 float64   `json:"drift_score"`
	FairnessScore              float64   `json:"fairness_score"`
	ExplainabilityScore        float64   `json:"explainability_score"`
	PrivacyScore               float64   `json:"privacy_score"`
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func init() {
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	source := rand.NewSource(time.Now().UnixNano())
	rng = rand.New(source)

	chHost := os.Getenv("CLICKHOUSE_HOST")
	chPort := os.Getenv("CLICKHOUSE_PORT")
	chDB := os.Getenv("CLICKHOUSE_DB")
	chUser := os.Getenv("CLICKHOUSE_USER")
	chPassword := os.Getenv("CLICKHOUSE_PASSWORD")

	if chHost == "" || chPort == "" || chDB == "" || chUser == "" || chPassword == "" {
		log.Fatalf("Missing one or more ClickHouse environment variables (CLICKHOUSE_HOST, CLICKHOUSE_PORT, CLICKHOUSE_DB, CLICKHOUSE_USER, CLICKHOUSE_PASSWORD)")
	}

	clickhouseDSN := fmt.Sprintf("tcp://%s:%s/%s?username=%s&password=%s",
		chHost, chPort, chDB, chUser, chPassword)

	var err error
	clickhouseConn, err = sql.Open("clickhouse", clickhouseDSN)
	if err != nil {
		log.Fatalf("Failed to open ClickHouse connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err = clickhouseConn.PingContext(ctx); err != nil {
		log.Fatalf("Failed to ping ClickHouse with DSN '%s': %v", clickhouseDSN, err)
	}
	log.Println("Successfully connected to ClickHouse.")

	redisClient = redis.NewClient(&redis.Options{
		Addr: redisAddr,
		DB:   0, // Default DB
	})
	ctxRedis, cancelRedis := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancelRedis()
	if _, err = redisClient.Ping(ctxRedis).Result(); err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	log.Println("Successfully connected to Redis.")

	maxRetries := 10
	retryDelay := 5 * time.Second
	for i := range maxRetries {
		log.Printf("Attempt %d/%d: Loading model profiles and prompts from Redis...", i+1, maxRetries)

		loadErr := loadModelProfilesFromRedis(context.Background())
		if loadErr != nil {
			log.Printf("Attempt %d/%d: Error loading model profiles from Redis: %v.", i+1, maxRetries, loadErr)
			time.Sleep(retryDelay)
			continue
		}

		loadErr = loadPromptsFromRedis(context.Background())
		if loadErr != nil {
			log.Printf("Attempt %d/%d: Error loading prompts from Redis: %v.", i+1, maxRetries, loadErr)
			time.Sleep(retryDelay)
			continue
		}

		if len(modelProfiles) > 0 && len(prompts) > 0 {
			log.Printf("Successfully loaded %d model profiles and %d prompts from Redis after %d attempts.", len(modelProfiles), len(prompts), i+1)
			break
		} else {
			log.Printf("Attempt %d/%d: Loaded 0 model profiles (%d) or 0 prompts (%d). Retrying in %v...", i+1, maxRetries, len(modelProfiles), len(prompts), retryDelay)
			time.Sleep(retryDelay)
		}

		if i == maxRetries-1 {
			log.Println("Failed to load model profiles and prompts from Redis after all attempts. Dashboard data may be incomplete.")
			os.Exit(1)
		}
	}

	kafkaReader = kafka.NewReader(kafka.ReaderConfig{
		Brokers:  []string{kafkaBroker},
		Topic:    kafkaTopic,
		GroupID:  kafkaGroupID,
		MaxBytes: 10e6,
	})
	log.Println("Kafka inference logs reader initialized.")

	kafkaAgentReader = kafka.NewReader(kafka.ReaderConfig{
		Brokers:  []string{kafkaBroker},
		Topic:    kafkaAgentTopic,
		GroupID:  kafkaGroupID + "-agent",
		MaxBytes: 10e6,
	})
	log.Println("Kafka agent performance reader initialized.")

	kafkaWorkflowReader = kafka.NewReader(kafka.ReaderConfig{
		Brokers:  []string{kafkaBroker},
		Topic:    kafkaWorkflowTopic,
		GroupID:  kafkaGroupID + "-workflow",
		MaxBytes: 10e6,
	})
	log.Println("Kafka workflow execution reader initialized.")

	kafkaPlanningReader = kafka.NewReader(kafka.ReaderConfig{
		Brokers:  []string{kafkaBroker},
		Topic:    kafkaPlanningTopic,
		GroupID:  kafkaGroupID + "-planning",
		MaxBytes: 10e6,
	})
	log.Println("Kafka planning execution reader initialized.")

	kafkaTrustQualityReader = kafka.NewReader(kafka.ReaderConfig{
		Brokers:  []string{kafkaBroker},
		Topic:    kafkaTrustQualityTopic,
		GroupID:  kafkaGroupID + "-trust-quality",
		MaxBytes: 10e6,
	})
	log.Println("Kafka agent trust & quality reader initialized.")

	appCtx, appCancel = context.WithCancel(context.Background())

	go setupRedisPubSub()
}

func setupRedisPubSub() {
	pubsub := redisClient.Subscribe(context.Background(), MODEL_PROFILE_UPDATES_CHANNEL, PROMPT_UPDATES_CHANNEL)
	defer pubsub.Close()
	receiveCtx, receiveCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer receiveCancel()
	var err error
	if _, err = pubsub.Receive(receiveCtx); err != nil && err != context.DeadlineExceeded {
		log.Printf("Failed to subscribe to Redis Pub/Sub or timed out during initial receive: %v", err)
		return
	} else if err == context.DeadlineExceeded {
		log.Println("Timed out waiting for Redis Pub/Sub subscription confirmation, continuing...")
	}

	ch := pubsub.Channel()
	log.Printf("Subscribed to Redis Pub/Sub channels '%s' and '%s'.", MODEL_PROFILE_UPDATES_CHANNEL, PROMPT_UPDATES_CHANNEL)

	for {
		select {
		case msg := <-ch:
			log.Printf("Received Redis Pub/Sub message: Channel=%s, Payload=%s", msg.Channel, msg.Payload)
			switch msg.Channel {
			case MODEL_PROFILE_UPDATES_CHANNEL:
				modelProfileMu.Lock()
				if err = loadModelProfilesFromRedis(context.Background()); err != nil {
					log.Printf("Error refreshing model profiles cache: %v", err)
				} else {
					log.Println("Model profiles cache refreshed due to update.")
				}
				modelProfileMu.Unlock()
			case PROMPT_UPDATES_CHANNEL:
				promptsMu.Lock()
				if err = loadPromptsFromRedis(context.Background()); err != nil {
					log.Printf("Error refreshing prompts cache: %v", err)
				} else {
					log.Println("Prompts cache refreshed due to update.")
				}
				promptsMu.Unlock()
			}
		case <-appCtx.Done(): // Listen for application shutdown signal
			log.Println("Redis Pub/Sub listener shutting down.")
			return
		}
	}
}

func loadModelProfilesFromRedis(ctx context.Context) error {
	newModelProfiles := make(map[string]ModelProfile)
	keys, err := redisClient.Keys(ctx, REDIS_MODEL_PROFILES_KEY_PREFIX+"*").Result()
	if err != nil && err != redis.Nil {
		return fmt.Errorf("failed to get model profile keys from Redis: %w", err)
	}
	if err == redis.Nil {
		modelProfiles = newModelProfiles
		return nil
	}
	for _, key := range keys {
		val, getErr := redisClient.Get(ctx, key).Result()
		if getErr != nil {
			log.Printf("Error getting model profile %s from Redis: %v", key, getErr)
			continue
		}
		var profile ModelProfile
		unmarshalErr := json.Unmarshal([]byte(val), &profile)
		if unmarshalErr != nil {
			log.Printf("Error unmarshalling model profile %s: %v", key, unmarshalErr)
			continue
		}
		newModelProfiles[profile.ID] = profile
	}
	modelProfiles = newModelProfiles
	return nil
}

func loadPromptsFromRedis(ctx context.Context) error {
	newPrompts := make(map[string]Prompt)
	keys, err := redisClient.Keys(ctx, REDIS_PROMPT_KEY_PREFIX+"*").Result()
	if err != nil && err != redis.Nil {
		return fmt.Errorf("failed to get prompt keys from Redis: %w", err)
	}
	if err == redis.Nil {
		prompts = newPrompts
		return nil
	}
	for _, key := range keys {
		val, getErr := redisClient.Get(ctx, key).Result()
		if getErr != nil {
			log.Printf("Error getting prompt %s from Redis: %v", key, getErr)
			continue
		}
		var prompt Prompt
		unmarshalErr := json.Unmarshal([]byte(val), &prompt)
		if unmarshalErr != nil {
			log.Printf("Error unmarshalling prompt %s: %v", key, unmarshalErr)
			continue
		}
		newPrompts[fmt.Sprintf("%s:%s", prompt.ID, prompt.Version)] = prompt
	}
	prompts = newPrompts
	return nil
}

func startKafkaConsumer(ctx context.Context) {
	log.Printf("Starting Kafka consumer for topic: %s, group: %s", kafkaTopic, kafkaGroupID)
	for {
		select {
		case <-ctx.Done():
			log.Println("Kafka consumer shutting down.")
			return
		default:
			m, err := kafkaReader.FetchMessage(ctx)
			if err != nil {
				if errors.Is(err, context.Canceled) {
					return
				}
				log.Printf("Error fetching Kafka message: %v", err)
				time.Sleep(1 * time.Second)
				continue
			}

			var logEntry InferenceLog
			if err := json.Unmarshal(m.Value, &logEntry); err != nil {
				log.Printf("Error unmarshalling Kafka message into InferenceLog: %v, message: %s", err, string(m.Value))
				if commitErr := kafkaReader.CommitMessages(ctx, m); commitErr != nil {
					log.Printf("Error committing Kafka message after unmarshalling failure: %v", commitErr)
				}
				continue
			}
			parsed, err := uuid.Parse(logEntry.RequestID)
			if err == nil {
				logEntry.RequestID = parsed.String()
			} else {
				log.Printf("Warning: Malformed RequestID '%s' from Kafka. Will use a generated new UUID for ClickHouse. Error: %v", logEntry.RequestID, err)
				logEntry.RequestID = uuid.New().String()
			}

			if logEntry.TaskType == "" {
				logEntry.TaskType = "unknown"
			}
			if logEntry.PolicyIDApplied == "" {
				logEntry.PolicyIDApplied = "none"
			}
			if logEntry.ModelUsed == "" {
				logEntry.ModelUsed = "unknown"
			}
			if logEntry.UserRoles == nil {
				logEntry.UserRoles = []string{}
			}

			if err := insertInferenceLog(ctx, logEntry); err != nil {
				log.Printf("Error inserting inference log into ClickHouse: %v, logEntry: %+v", err, logEntry)
			} else {
				log.Printf("Successfully processed and inserted log for request_id: %s", logEntry.RequestID)

				if err := sendEvaluationRequest(ctx, logEntry); err != nil {
					log.Printf("Error sending evaluation request: %v", err)
				}
			}

			if commitErr := kafkaReader.CommitMessages(ctx, m); commitErr != nil {
				log.Printf("Error committing Kafka message: %v", commitErr)
			}
		}
	}
}

func insertInferenceLog(ctx context.Context, logEntry InferenceLog) error {
	tx, err := clickhouseConn.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	stmt, err := tx.PrepareContext(ctx, fmt.Sprintf(`
		INSERT INTO %s (
			request_id, timestamp, method, path, client_ip, user_agent, selected_backend_id, backend_url, backend_type,
			response_timestamp, latency_ms, status_code, error, policy_id_applied, model_requested, model_used, stream,
			cpu_usage_rate, memory_usage_bytes, total_cost, input_tokens, output_tokens, task_type, conversation_id, user_id,
			user_roles, request_headers, request_body_snippet, response_body_snippet, response_headers
		) VALUES (
			?, ?, ?, ?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?
		)
	`, CLICKHOUSE_INFERENCE_LOGS_TABLE))
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement for inference logs: %w", err)
	}
	defer stmt.Close()

	streamValue := int32(0)
	if logEntry.Stream {
		streamValue = 1
	}

	requestHeadersStr := string(logEntry.RequestHeaders)
	if requestHeadersStr == "" {
		requestHeadersStr = "{}"
	}
	responseHeadersStr := string(logEntry.ResponseHeaders)
	if responseHeadersStr == "" {
		responseHeadersStr = "{}"
	}

	_, err = stmt.ExecContext(
		ctx,
		logEntry.RequestID,
		logEntry.Timestamp,
		logEntry.Method,
		logEntry.Path,
		logEntry.ClientIP,
		logEntry.UserAgent,
		logEntry.SelectedBackendID,
		logEntry.BackendURL,
		logEntry.BackendType,
		logEntry.ResponseTimestamp,
		logEntry.LatencyMs,
		logEntry.StatusCode,
		logEntry.Error,
		logEntry.PolicyIDApplied,
		logEntry.ModelRequested,
		logEntry.ModelUsed,
		streamValue,
		logEntry.CPUUsage,
		logEntry.MemoryUsage,
		logEntry.TotalCost,
		logEntry.InputTokens,
		logEntry.OutputTokens,
		logEntry.TaskType,
		logEntry.ConversationID,
		logEntry.UserID,
		logEntry.UserRoles,
		requestHeadersStr,
		logEntry.RequestBodySnippet,
		logEntry.ResponseBodySnippet,
		responseHeadersStr,
	)
	if err != nil {
		return fmt.Errorf("failed to execute insert statement for inference log: %w", err)
	}

	return tx.Commit()
}

// extractPromptID attempts to determine the prompt ID from the request body
func extractPromptID(requestBody string) string {
	// Try to parse the request body to extract prompt information
	var requestData map[string]any
	if err := json.Unmarshal([]byte(requestBody), &requestData); err != nil {
		return "" // Can't parse, no prompt ID available
	}

	// Check if there's a prompt_id field in the request
	if promptID, ok := requestData["prompt_id"].(string); ok && promptID != "" {
		return promptID
	}

	// Try to extract the actual prompt content and match it with known prompts
	var promptContent string

	// Handle different request formats
	if messages, ok := requestData["messages"].([]any); ok && len(messages) > 0 {
		// Chat completion format
		if lastMessage, ok := messages[len(messages)-1].(map[string]any); ok {
			if content, ok := lastMessage["content"].(string); ok {
				promptContent = content
			}
		}
	} else if prompt, ok := requestData["prompt"].(string); ok {
		// Direct prompt format
		promptContent = prompt
	}

	// If we have prompt content, try to match it with known prompts
	if promptContent != "" {
		promptsMu.RLock()
		defer promptsMu.RUnlock()

		for _, prompt := range prompts {
			// Simple content matching - in production, you might want more sophisticated matching
			if prompt.Content == promptContent {
				return prompt.ID
			}
		}
	}

	return "" // No prompt ID found
}

// sendPromptPerformanceUpdate sends performance data to policy manager for prompt optimization
func sendPromptPerformanceUpdate(promptID string, score float64, latency float64) {
	if promptID == "" {
		return
	}

	policyManagerURL := os.Getenv("POLICY_MANAGER_URL")
	if policyManagerURL == "" {
		policyManagerURL = "http://policy-manager:8080" // Default internal service URL
	}

	performanceData := map[string]any{
		"prompt_id": promptID,
		"score":     score,
		"latency":   latency,
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	jsonData, err := json.Marshal(performanceData)
	if err != nil {
		log.Printf("Error marshaling prompt performance data: %v", err)
		return
	}

	resp, err := http.Post(policyManagerURL+"/prompts/performance", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		log.Printf("Error sending prompt performance update: %v", err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		log.Printf("Successfully sent prompt performance update for prompt %s (score: %.2f, latency: %.2fms)", promptID, score, latency)
	} else {
		log.Printf("Policy manager returned non-success status for prompt performance update: %d", resp.StatusCode)
	}
}

func sendEvaluationRequest(ctx context.Context, logEntry InferenceLog) error {
	evaluationServiceURL := os.Getenv("EVALUATION_SERVICE_URL")
	if evaluationServiceURL == "" {
		log.Println("EVALUATION_SERVICE_URL not set, skipping evaluation")
		return nil
	}

	// Try to determine the prompt ID by matching the prompt content
	promptID := extractPromptID(logEntry.RequestBodySnippet)

	evaluationRequest := EvaluationRequest{
		RequestID:   logEntry.RequestID,
		Prompt:      logEntry.RequestBodySnippet,
		LLMResponse: logEntry.ResponseBodySnippet,
		ModelID:     logEntry.ModelUsed,
		PromptID:    promptID,
		TaskType:    logEntry.TaskType,
	}

	jsonValue, err := json.Marshal(evaluationRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal evaluation request: %w", err)
	}

	log.Printf("Sending evaluation request to: %s/evaluate, RequestID: %s", evaluationServiceURL, logEntry.RequestID)
	resp, err := http.Post(evaluationServiceURL+"/evaluate", "application/json", bytes.NewBuffer(jsonValue))
	if err != nil {
		return fmt.Errorf("failed to send evaluation request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("failed to read evaluation response body: %w", err)
		}

		var evalResult LLMEvaluationResult
		if err := json.Unmarshal(body, &evalResult); err != nil {
			return fmt.Errorf("failed to unmarshal evaluation response: %w", err)
		}

		evaluationResult := EvaluationResult{
			ID:               evalResult.ID,
			RequestID:        evalResult.RequestID,
			Prompt:           evalResult.Prompt,
			LLMResponse:      evalResult.LLMResponse,
			ModelID:          evalResult.ModelID,
			PromptID:         evalResult.PromptID,
			TaskType:         evalResult.TaskType,
			EvaluationType:   evalResult.EvaluationType,
			Score:            evalResult.Score,
			Passed:           evalResult.Passed,
			Feedback:         evalResult.Feedback,
			EvaluatedAt:      evalResult.EvaluatedAt,
			ExpectedResponse: evalResult.ExpectedResponse,
			RawMetrics:       string(evalResult.RawMetrics),
			Metadata:         string(evalResult.Metadata),
		}

		if err := insertEvaluationResult(ctx, evaluationResult); err != nil {
			return fmt.Errorf("failed to insert evaluation result: %w", err)
		}

		// Send performance data to policy manager for prompt optimization
		if evalResult.PromptID != "" {
			go sendPromptPerformanceUpdate(evalResult.PromptID, evalResult.Score, logEntry.LatencyMs)
		}

		log.Printf("Successfully sent evaluation request for RequestID: %s, Evaluation Score: %.2f", logEntry.RequestID, evalResult.Score)
		return nil
	} else {
		return fmt.Errorf("evaluation service returned non-success status code: %d", resp.StatusCode)
	}
}

// NOTE: Synthetic data generation has been moved to PromptOps for better business value
// This function is deprecated and will be removed in future versions
func startSyntheticDataGeneration(_ context.Context) {
	log.Println("=== SYNTHETIC DATA GENERATION MOVED TO PROMPTOPS ===")
	log.Println("Synthetic data generation has been moved to PromptOps service for enhanced business value")
	log.Println("This functionality is now part of the comprehensive prompt evaluation framework")
	log.Println("The data-processor will continue to handle real inference log processing")
	log.Println("========================================================")
	// No-op - functionality moved to PromptOps for better business value
}

// NOTE: Functions getActiveModelProfile, getRandomPrompt, callLLM, and createSyntheticLogEntry
// have been removed as they were only used for synthetic data generation
// which has been moved to PromptOps for better business value

// NOTE: callLLM and createSyntheticLogEntry functions removed
// These were only used for synthetic data generation which has been moved to PromptOps

func insertEvaluationResult(ctx context.Context, evalResult EvaluationResult) error {
	tx, err := clickhouseConn.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction for evaluation result: %w", err)
	}
	defer tx.Rollback()

	stmt, err := tx.PrepareContext(ctx, fmt.Sprintf(`
		INSERT INTO %s (
			id, request_id, prompt, llm_response, model_id, prompt_id, task_type,
			evaluation_type, score, passed, feedback, evaluated_at,
			expected_response, raw_metrics, metadata
		) VALUES (
			?, ?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?,
			?, ?, ?
		)
	`, CLICKHOUSE_EVALUATION_RESULTS_TABLE))
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement for evaluation results: %w", err)
	}
	defer stmt.Close()
	passedValue := int32(0)
	if evalResult.Passed {
		passedValue = 1
	}

	rawMetricsStr := evalResult.RawMetrics
	if rawMetricsStr == "" {
		rawMetricsStr = "{}"
	}
	metadataStr := evalResult.Metadata
	if metadataStr == "" {
		metadataStr = "{}"
	}

	_, err = stmt.ExecContext(
		ctx,
		evalResult.ID,
		evalResult.RequestID,
		evalResult.Prompt,
		evalResult.LLMResponse,
		evalResult.ModelID,
		evalResult.PromptID,
		evalResult.TaskType,
		evalResult.EvaluationType,
		evalResult.Score,
		passedValue,
		evalResult.Feedback,
		evalResult.EvaluatedAt,
		evalResult.ExpectedResponse,
		rawMetricsStr,
		metadataStr,
	)
	if err != nil {
		return fmt.Errorf("failed to execute insert statement for evaluation result: %w", err)
	}

	return tx.Commit()
}

func insertCuratedData(ctx context.Context, curatedData CuratedData) error {
	tx, err := clickhouseConn.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction for curated data: %w", err)
	}
	defer tx.Rollback()

	stmt, err := tx.PrepareContext(ctx, fmt.Sprintf(`
		INSERT INTO %s (
			id, request_id, prompt, llm_response, model_id, task_type, generated_at,
			evaluation_score, evaluation_passed, evaluation_type, feedback, metadata, source_log_id, curated_by
		) VALUES (
			?, ?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?, ?, ?
		)
	`, CLICKHOUSE_CURATED_DATA_TABLE))
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement for curated data: %w", err)
	}
	defer stmt.Close()
	evaluationPassedValue := int32(0)
	if curatedData.EvaluationPassed {
		evaluationPassedValue = 1
	}

	metadataStr := curatedData.Metadata
	if metadataStr == "" {
		metadataStr = "{}"
	}

	_, err = stmt.ExecContext(
		ctx,
		curatedData.ID,
		curatedData.RequestID,
		curatedData.Prompt,
		curatedData.LLMResponse,
		curatedData.ModelID,
		curatedData.TaskType,
		curatedData.GeneratedAt,
		curatedData.EvaluationScore,
		evaluationPassedValue,
		curatedData.EvaluationType,
		curatedData.Feedback,
		metadataStr,
		curatedData.SourceLogID,
		curatedData.CuratedBy,
	)
	if err != nil {
		return fmt.Errorf("failed to execute insert statement for curated data: %w", err)
	}

	return tx.Commit()
}

func insertAgentPerformanceLog(ctx context.Context, agentLog AgentPerformanceLog) error {
	tx, err := clickhouseConn.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction for agent performance log: %w", err)
	}
	defer tx.Rollback()

	stmt, err := tx.PrepareContext(ctx, fmt.Sprintf(`
		INSERT INTO %s (
			agent_id, agent_name, agent_type, task_id, task_type, status, started_at,
			completed_at, duration, success, error_message, input_tokens, output_tokens,
			cost, health_status, capabilities, current_workload, max_concurrency, last_active_at, metadata
		) VALUES (
			?, ?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?, ?, ?
		)
	`, CLICKHOUSE_AGENT_PERFORMANCE_TABLE))
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement for agent performance log: %w", err)
	}
	defer stmt.Close()

	successValue := int32(0)
	if agentLog.Success {
		successValue = 1
	}

	var completedAt any
	var duration any
	if agentLog.CompletedAt != nil {
		completedAt = *agentLog.CompletedAt
	}
	if agentLog.Duration != nil {
		duration = *agentLog.Duration
	}

	metadataStr := agentLog.Metadata
	if metadataStr == "" {
		metadataStr = "{}"
	}

	_, err = stmt.ExecContext(
		ctx,
		agentLog.AgentID,
		agentLog.AgentName,
		agentLog.AgentType,
		agentLog.TaskID,
		agentLog.TaskType,
		agentLog.Status,
		agentLog.StartedAt,
		completedAt,
		duration,
		successValue,
		agentLog.ErrorMessage,
		agentLog.InputTokens,
		agentLog.OutputTokens,
		agentLog.Cost,
		agentLog.HealthStatus,
		agentLog.Capabilities,
		agentLog.CurrentWorkload,
		agentLog.MaxConcurrency,
		agentLog.LastActiveAt,
		metadataStr,
	)
	if err != nil {
		return fmt.Errorf("failed to execute insert statement for agent performance log: %w", err)
	}

	return tx.Commit()
}

func insertWorkflowExecutionLog(ctx context.Context, workflowLog WorkflowExecutionLog) error {
	tx, err := clickhouseConn.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction for workflow execution log: %w", err)
	}
	defer tx.Rollback()

	stmt, err := tx.PrepareContext(ctx, fmt.Sprintf(`
		INSERT INTO %s (
			workflow_id, workflow_name, execution_id, status, started_at, completed_at,
			duration, tasks_total, tasks_completed, tasks_failed, agents_involved,
			total_cost, created_by, error_message, progress_percent, metadata
		) VALUES (
			?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?,
			?, ?, ?, ?, ?
		)
	`, CLICKHOUSE_WORKFLOW_EXECUTION_TABLE))
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement for workflow execution log: %w", err)
	}
	defer stmt.Close()

	var completedAt any
	var duration any
	if workflowLog.CompletedAt != nil {
		completedAt = *workflowLog.CompletedAt
	}
	if workflowLog.Duration != nil {
		duration = *workflowLog.Duration
	}

	metadataStr := workflowLog.Metadata
	if metadataStr == "" {
		metadataStr = "{}"
	}

	_, err = stmt.ExecContext(
		ctx,
		workflowLog.WorkflowID,
		workflowLog.WorkflowName,
		workflowLog.ExecutionID,
		workflowLog.Status,
		workflowLog.StartedAt,
		completedAt,
		duration,
		workflowLog.TasksTotal,
		workflowLog.TasksCompleted,
		workflowLog.TasksFailed,
		workflowLog.AgentsInvolved,
		workflowLog.TotalCost,
		workflowLog.CreatedBy,
		workflowLog.ErrorMessage,
		workflowLog.ProgressPercent,
		metadataStr,
	)
	if err != nil {
		return fmt.Errorf("failed to execute insert statement for workflow execution log: %w", err)
	}

	return tx.Commit()
}

func insertPlanningExecutionLog(ctx context.Context, planningLog PlanningExecutionLog) error {
	tx, err := clickhouseConn.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction for planning execution log: %w", err)
	}
	defer tx.Rollback()

	stmt, err := tx.PrepareContext(ctx, fmt.Sprintf(`
		INSERT INTO %s (
			goal_id, goal_description, execution_id, status, started_at, completed_at,
			duration, tasks_total, tasks_completed, tasks_failed, total_cost,
			created_by, error_message, progress_percent, metadata
		) VALUES (
			?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?,
			?, ?, ?, ?
		)
	`, CLICKHOUSE_PLANNING_LOGS_TABLE))
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement for planning execution log: %w", err)
	}
	defer stmt.Close()

	var completedAt any
	var duration any
	if planningLog.CompletedAt != nil {
		completedAt = *planningLog.CompletedAt
	}
	if planningLog.Duration != nil {
		duration = *planningLog.Duration
	}

	metadataStr := planningLog.Metadata
	if metadataStr == "" {
		metadataStr = "{}"
	}

	_, err = stmt.ExecContext(
		ctx,
		planningLog.GoalID,
		planningLog.GoalDescription,
		planningLog.ExecutionID,
		planningLog.Status,
		planningLog.StartedAt,
		completedAt,
		duration,
		planningLog.TasksTotal,
		planningLog.TasksCompleted,
		planningLog.TasksFailed,
		planningLog.TotalCost,
		planningLog.CreatedBy,
		planningLog.ErrorMessage,
		planningLog.ProgressPercent,
		metadataStr,
	)
	if err != nil {
		return fmt.Errorf("failed to execute insert statement for planning execution log: %w", err)
	}

	return tx.Commit()
}
func insertTrustAndQualityLog(ctx context.Context, logEntry AgentTrustAndQualityLog) error {
	tx, err := clickhouseConn.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction for trust/quality log: %w", err)
	}
	defer tx.Rollback()

	stmt, err := tx.PrepareContext(ctx, fmt.Sprintf(`
		INSERT INTO %s (
			agent_id, timestamp, accuracy, precision, recall, f1_score,
			adversarial_robustness_score, drift_score, fairness_score,
			explainability_score, privacy_score
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`, CLICKHOUSE_AGENT_TRUST_QUALITY_TABLE))
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement for trust/quality log: %w", err)
	}
	defer stmt.Close()

	_, err = stmt.ExecContext(
		ctx,
		logEntry.AgentID,
		logEntry.Timestamp,
		logEntry.Accuracy,
		logEntry.Precision,
		logEntry.Recall,
		logEntry.F1Score,
		logEntry.AdversarialRobustnessScore,
		logEntry.DriftScore,
		logEntry.FairnessScore,
		logEntry.ExplainabilityScore,
		logEntry.PrivacyScore,
	)
	if err != nil {
		return fmt.Errorf("failed to execute insert statement for trust/quality log: %w", err)
	}

	return tx.Commit()
}

func truncateString(s string, maxLen int) string {
	if len(s) > maxLen {
		return s[:min(len(s), maxLen)] + "..."
	}
	return s
}

// --- Multi-Agent Kafka Consumers ---

func startAgentPerformanceConsumer(ctx context.Context) {
	log.Printf("Starting Kafka agent performance consumer for topic: %s", kafkaAgentTopic)
	for {
		select {
		case <-ctx.Done():
			log.Println("Agent performance consumer shutting down.")
			return
		default:
			m, err := kafkaAgentReader.FetchMessage(ctx)
			if err != nil {
				if errors.Is(err, context.Canceled) {
					return
				}
				log.Printf("Error fetching agent performance Kafka message: %v", err)
				time.Sleep(1 * time.Second)
				continue
			}

			var agentLog AgentPerformanceLog
			if err := json.Unmarshal(m.Value, &agentLog); err != nil {
				log.Printf("Error unmarshalling agent performance message: %v, message: %s", err, string(m.Value))
				if commitErr := kafkaAgentReader.CommitMessages(ctx, m); commitErr != nil {
					log.Printf("Error committing agent performance message after unmarshalling failure: %v", commitErr)
				}
				continue
			}

			if err := insertAgentPerformanceLog(ctx, agentLog); err != nil {
				log.Printf("Error inserting agent performance log: %v", err)
			} else {
				log.Printf("Successfully processed agent performance log for agent: %s", agentLog.AgentID)
			}

			if commitErr := kafkaAgentReader.CommitMessages(ctx, m); commitErr != nil {
				log.Printf("Error committing agent performance message: %v", commitErr)
			}
		}
	}
}

func startWorkflowExecutionConsumer(ctx context.Context) {
	log.Printf("Starting Kafka workflow execution consumer for topic: %s", kafkaWorkflowTopic)
	for {
		select {
		case <-ctx.Done():
			log.Println("Workflow execution consumer shutting down.")
			return
		default:
			m, err := kafkaWorkflowReader.FetchMessage(ctx)
			if err != nil {
				if errors.Is(err, context.Canceled) {
					return
				}
				log.Printf("Error fetching workflow execution Kafka message: %v", err)
				time.Sleep(1 * time.Second)
				continue
			}

			var workflowLog WorkflowExecutionLog
			if err := json.Unmarshal(m.Value, &workflowLog); err != nil {
				log.Printf("Error unmarshalling workflow execution message: %v, message: %s", err, string(m.Value))
				if commitErr := kafkaWorkflowReader.CommitMessages(ctx, m); commitErr != nil {
					log.Printf("Error committing workflow execution message after unmarshalling failure: %v", commitErr)
				}
				continue
			}

			if err := insertWorkflowExecutionLog(ctx, workflowLog); err != nil {
				log.Printf("Error inserting workflow execution log: %v", err)
			} else {
				log.Printf("Successfully processed workflow execution log for workflow: %s", workflowLog.WorkflowID)
			}

			if commitErr := kafkaWorkflowReader.CommitMessages(ctx, m); commitErr != nil {
				log.Printf("Error committing workflow execution message: %v", commitErr)
			}
		}
	}
}

func startPlanningExecutionConsumer(ctx context.Context) {
	log.Printf("Starting Kafka planning execution consumer for topic: %s", kafkaPlanningTopic)
	for {
		select {
		case <-ctx.Done():
			log.Println("Planning execution consumer shutting down.")
			return
		default:
			m, err := kafkaPlanningReader.FetchMessage(ctx)
			if err != nil {
				if errors.Is(err, context.Canceled) {
					return
				}
				log.Printf("Error fetching planning execution Kafka message: %v", err)
				time.Sleep(1 * time.Second)
				continue
			}

			var planningLog PlanningExecutionLog
			if err := json.Unmarshal(m.Value, &planningLog); err != nil {
				log.Printf("Error unmarshalling planning execution message: %v, message: %s", err, string(m.Value))
				if commitErr := kafkaPlanningReader.CommitMessages(ctx, m); commitErr != nil {
					log.Printf("Error committing planning execution message after unmarshalling failure: %v", commitErr)
				}
				continue
			}

			if err := insertPlanningExecutionLog(ctx, planningLog); err != nil {
				log.Printf("Error inserting planning execution log: %v", err)
			} else {
				log.Printf("Successfully processed planning execution log for goal: %s", planningLog.GoalID)
			}

			if commitErr := kafkaPlanningReader.CommitMessages(ctx, m); commitErr != nil {
				log.Printf("Error committing planning execution message: %v", commitErr)
			}
		}
	}
}

func startTrustQualityConsumer(ctx context.Context) {
	log.Printf("Starting Kafka consumer for topic: %s", kafkaTrustQualityTopic)
	for {
		select {
		case <-ctx.Done():
			log.Println("Trust & quality consumer shutting down.")
			return
		default:
			m, err := kafkaTrustQualityReader.FetchMessage(ctx)
			if err != nil {
				if errors.Is(err, context.Canceled) {
					return
				}
				log.Printf("Error fetching trust/quality Kafka message: %v", err)
				time.Sleep(1 * time.Second)
				continue
			}

			var logEntry AgentTrustAndQualityLog
			if err := json.Unmarshal(m.Value, &logEntry); err != nil {
				log.Printf("Error unmarshalling trust/quality message: %v, message: %s", err, string(m.Value))
				if commitErr := kafkaTrustQualityReader.CommitMessages(ctx, m); commitErr != nil {
					log.Printf("Error committing trust/quality message after unmarshalling failure: %v", commitErr)
				}
				continue
			}

			if err := insertTrustAndQualityLog(ctx, logEntry); err != nil {
				log.Printf("Error inserting trust/quality log: %v", err)
			} else {
				log.Printf("Successfully processed trust/quality log for agent: %s", logEntry.AgentID)
			}

			if commitErr := kafkaTrustQualityReader.CommitMessages(ctx, m); commitErr != nil {
				log.Printf("Error committing trust/quality message: %v", commitErr)
			}
		}
	}
}

// --- Main Function ---

func main() {
	// Setup signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start Kafka consumers in goroutines
	go startKafkaConsumer(appCtx)
	go startAgentPerformanceConsumer(appCtx)
	go startWorkflowExecutionConsumer(appCtx)
	go startPlanningExecutionConsumer(appCtx)
	go startTrustQualityConsumer(appCtx) // Start the new consumer

	// Start synthetic data generation loop in a goroutine
	go startSyntheticDataGeneration(appCtx)

	// Wait for shutdown signal
	sig := <-sigChan
	log.Printf("Received signal: %v. Initiating graceful shutdown...", sig)
	appCancel()

	// Allow some time for goroutines to finish
	time.Sleep(2 * time.Second)

	// Cleanup resources
	if err := kafkaReader.Close(); err != nil {
		log.Printf("Error closing Kafka inference reader: %v", err)
	}
	if err := kafkaAgentReader.Close(); err != nil {
		log.Printf("Error closing Kafka agent reader: %v", err)
	}
	if err := kafkaWorkflowReader.Close(); err != nil {
		log.Printf("Error closing Kafka workflow reader: %v", err)
	}
	if err := kafkaPlanningReader.Close(); err != nil {
		log.Printf("Error closing Kafka planning reader: %v", err)
	}
	if err := kafkaTrustQualityReader.Close(); err != nil {
		log.Printf("Error closing Kafka trust/quality reader: %v", err)
	}
	if err := clickhouseConn.Close(); err != nil {
		log.Printf("Error closing ClickHouse connection: %v", err)
	}
	if err := redisClient.Close(); err != nil {
		log.Printf("Error closing Redis client: %v", err)
	}
	log.Println("Shutdown complete.")
}
