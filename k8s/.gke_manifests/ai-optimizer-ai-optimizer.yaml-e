---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ai-optimizer-sa
  namespace: default # CHANGE THIS if your app is in a different namespace
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: ai-optimizer-pod-executor
  namespace: default # CHANGE THIS if your app is in a different namespace
rules:
- apiGroups: [""] # "" indicates the core API group
  resources: ["pods"]
  verbs: ["get", "list"] # Needed for the initContainer to find the ClickHouse pod
- apiGroups: [""]
  resources: ["pods/exec"]
  verbs: ["create"] # Needed for the initContainer to run 'kubectl exec' into pods
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: ai-optimizer-exec-binding
  namespace: default # CHANGE THIS if your app is in a different namespace
subjects:
- kind: ServiceAccount
  name: ai-optimizer-sa
  namespace: default # Must be the same namespace as the Role and ServiceAccount
roleRef:
  kind: Role
  name: ai-optimizer-pod-executor # The Role to bind
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-optimizer
  labels:
    app: ai-optimizer
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-optimizer
  template:
    metadata:
      labels:
        app: ai-optimizer
    spec:
      serviceAccountName: ai-optimizer-sa # Link to the ServiceAccount defined above
      initContainers:
        # Existing init container: Wait for ClickHouse and Redis service reachability
        - name: wait-for-clickhouse-and-redis
          image: busybox:1.36 # Using the version you provided
          command: ["sh", "-c", "echo \"Waiting for ClickHouse and Redis...\"; until nc -z clickhouse 9000 && nc -z redis 6379; do echo \"ClickHouse or Redis not ready, waiting...\"; sleep 2; done; echo \"ClickHouse and Redis are ready.\""]
          resources:
            limits:
              cpu: 20m
              memory: 20Mi
            requests:
              cpu: 10m
              memory: 10Mi

        # NEW INIT CONTAINER: Wait for the 'inference_logs' table to be created in ClickHouse
        # This runs after the services are reachable, but before the main app starts.
        - name: wait-for-inference-logs-table
          # Using kubectl image as it includes bash and the kubectl client.
          # This allows us to exec commands into other pods.
          image: gcr.io/cloud-builders/kubectl:latest # Using a robust image for kubectl exec
          command: ["/bin/bash", "-c"]
          args:
            - |
              echo "Starting wait-for-inference-logs-table init container..."
              CLICKHOUSE_POD=""
              MAX_POD_RETRIES=20
              POD_RETRY_COUNT=0

              # First, find the ClickHouse pod
              # Use '|| true' to prevent script from exiting if kubectl get fails initially
              while [ -z "$CLICKHOUSE_POD" ] && [ "$POD_RETRY_COUNT" -lt "$MAX_POD_RETRIES" ]; do
                echo "Attempting to find ClickHouse pod (attempt $((POD_RETRY_COUNT + 1))/${MAX_POD_RETRIES})..."
                CLICKHOUSE_POD=$(kubectl get pod -l app=clickhouse -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || true)
                if [ -z "$CLICKHOUSE_POD" ]; then
                  sleep 10 # Wait longer if pod isn't found
                fi
                POD_RETRY_COUNT=$((POD_RETRY_COUNT+1))
              done

              if [ -z "$CLICKHOUSE_POD" ]; then
                echo "Error: ClickHouse pod not found after multiple retries. Exiting init container."
                exit 1 # Fail init container if ClickHouse pod is not found
              fi
              echo "Found ClickHouse pod: $CLICKHOUSE_POD"

              # Now, wait for the 'inference_logs' table inside the ClickHouse pod
              MAX_TABLE_RETRIES=60 # Total wait time: 60 * 5s = 5 minutes
              TABLE_RETRY_COUNT=0
              # Use 'EXISTS TABLE' and check for '1' in output to confirm table existence
              until kubectl exec "$CLICKHOUSE_POD" -- clickhouse-client -q "EXISTS TABLE inference_logs;" 2>/dev/null | grep -q "1"; do
                echo "Table 'inference_logs' not found in ClickHouse pod $CLICKHOUSE_POD (attempt $((TABLE_RETRY_COUNT + 1))/${MAX_TABLE_RETRIES}). Retrying in 5 seconds..."
                sleep 5
                TABLE_RETRY_COUNT=$((TABLE_RETRY_COUNT+1))
                if [ "$TABLE_RETRY_COUNT" -ge "$MAX_TABLE_RETRIES" ]; then
                  echo "Timeout: ClickHouse table 'inference_logs' did not appear within the expected time."
                  exit 1 # Fail init container if table doesn't appear
                fi
              done

              echo "ClickHouse table 'inference_logs' is ready for ai-optimizer."
          resources:
            limits:
              cpu: "200m"
              memory: "200Mi"
            requests:
              cpu: "100m"
              memory: "100Mi"

      containers:
        - name: ai-optimizer
          # IMPORTANT: Use the full Artifact Registry path for the image
          image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-ai-optimizer:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8085 # This is the classifier port that proxy-gateway expects
          env:
            # Kafka connection details
            - name: KAFKA_BROKERS
              value: "kafka:9092" # Assumes your Kafka service is named 'kafka' and listens on 9092
            - name: KAFKA_TOPIC
              value: "inference-logs" # The topic where proxy gateway sends logs

            # Proxy Gateway connection for LLM requests
            - name: PROXY_GATEWAY_URL
              value: "http://proxy-gateway:8080" # Internal cluster service URL

            # ClickHouse connection details
              value: clickhouse # Assuming your ClickHouse service is named 'clickhouse'
            - name: CLICKHOUSE_PORT
              value: "9000" # Default ClickHouse client port
            - name: CLICKHOUSE_DB
              value: "default"
            - name: CLICKHOUSE_USER
              valueFrom:
                secretKeyRef:
                  name: clickhouse-credentials
                  key: user
            - name: CLICKHOUSE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: clickhouse-credentials
                  key: password

            # Redis connection details
            - name: REDIS_ADDR
              value: "redis:6379" # The Redis service address and port
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: redis-credentials
                  key: password
                  optional: true # Allow Redis to run without a password if secret/key isn't found
            - name: REDIS_DB
              value: "0" # Default Redis DB, can be configured via ConfigMap/Secret if needed

            # Prometheus connection details (from your Go code)
            - name: PROMETHEUS_HOST
              value: prometheus # Assuming your Prometheus service is named 'prometheus'
            - name: PROMETHEUS_PORT
              value: "9090" # Default Prometheus port

            # GKE Project ID (from your Go code)
            - name: GKE_PROJECT_ID
              value: ${GCP_PROJECT_ID} # This will be substituted by your deployment script

            # Reference API keys from Kubernetes Secrets
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: openai-api-key
                  key: OPENAI_API_KEY
            - name: GOOGLE_API_KEY # Name of the environment variable in the container
              valueFrom:
                secretKeyRef:
                  name: google-api-key  # Name of the Kubernetes Secret you created
                  key: GOOGLE_API_KEY  # Key within that Secret that holds the API key value
            - name: ANTHROPIC_API_KEY
              valueFrom:
                secretKeyRef:
                  name: anthropic-api-key
                  key: ANTHROPIC_API_KEY
            - name: COHERE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: cohere-api-key
                  key: COHERE_API_KEY
                  optional: true
            - name: MISTRAL_API_KEY
              valueFrom:
                secretKeyRef:
                  name: mistral-api-key
                  key: MISTRAL_API_KEY
                  optional: true
            - name: GROK_API_KEY
              valueFrom:
                secretKeyRef:
                  name: grok-api-key
                  key: GROK_API_KEY
                  optional: true
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: jwt-secret
                  key: secret
          resources:
            limits:
              cpu: 200m
              memory: 256Mi
            requests:
              cpu: 100m
              memory: 128Mi
---
apiVersion: v1
kind: Service
metadata:
  name: ai-optimizer # This is the service name that proxy-gateway will try to resolve
spec:
  selector:
    app: ai-optimizer # This must match the 'app' label on your ai-optimizer deployment/pod
  ports:
    - name: classifier-http # Name for the port
      protocol: TCP
      port: 8085 # The port other services will use to connect to this service
      targetPort: 8085 # The port on the ai-optimizer pod that the service will forward to
  type: ClusterIP # Use ClusterIP as it's an internal service, not exposed externally
