# k8s/evaluation-service.yaml
# Combined Deployment and Service definition for the Evaluation Service

apiVersion: apps/v1
kind: Deployment
metadata:
  name: evaluation-service
  labels:
    app: evaluation-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: evaluation-service
  template:
    metadata:
      labels:
        app: evaluation-service
    spec:
      containers:
      - name: evaluation-service
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-evaluation-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8087
        env:
        - name: CLICKHOUSE_HOST
          value: "clickhouse"
        - name: CLICKHOUSE_PORT
          value: "9000"
        - name: CLICKHOUSE_DB
          value: "default"
        - name: CLICKHOUSE_USER
          value: "test"
        - name: CLICKHOUSE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: clickhouse-credentials
              key: password
              optional: false
        - name: EVALUATION_CONFIG_FILE
          value: "/app/evaluation_config.json"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        - name: ENHANCED_EVALUATION_ENABLED
          value: "true"
---
apiVersion: v1
kind: Service
metadata:
  name: evaluation-service
  labels:
    app: evaluation-service
spec:
  selector:
    app: evaluation-service
  ports:
    - protocol: TCP
      port: 8087
      targetPort: 8087
  type: NodePort # Changed from ClusterIP to NodePort for external testing

