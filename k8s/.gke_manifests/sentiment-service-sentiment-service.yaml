apiVersion: apps/v1
kind: Deployment
metadata:
  name: sentiment-service
  labels:
    app: sentiment-service
    component: sentiment-analysis
    tier: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: sentiment-service
  template:
    metadata:
      labels:
        app: sentiment-service
        component: sentiment-analysis
        tier: backend
    spec:
      containers:
      - name: sentiment-service
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-sentiment-service:latest
        ports:
        - containerPort: 8088
        env:
        - name: PORT
          value: "8088"
        - name: REDIS_ADDR
          value: "redis:6379"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: password
              optional: true
        - name: LLM_BASE_URL
          value: "http://proxy-gateway:8080"
        - name: LLM_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-secrets
              key: api-key
              optional: true
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8088
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8088
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: sentiment-config
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: sentiment-config
        configMap:
          name: sentiment-config
          optional: true
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: sentiment-service
  labels:
    app: sentiment-service
    component: sentiment-analysis
spec:
  selector:
    app: sentiment-service
  ports:
  - name: http
    port: 8088
    targetPort: 8088
    protocol: TCP
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: sentiment-config
  labels:
    app: sentiment-service
data:
  config.yaml: |
    sentiment:
      analysis:
        confidence_threshold: 0.7
        batch_size: 100
        cache_ttl: 3600
      social_monitoring:
        platforms:
          - twitter
          - facebook
          - instagram
          - linkedin
        keywords:
          - "AI Operations Hub"
          - "scale-llm"
          - "customer feedback"
          - "product review"
        update_interval: 300
      alerts:
        negative_threshold: 0.3
        volume_drop_threshold: 0.5
        notification_channels:
          - email
          - slack
      trends:
        retention_days: 30
        aggregation_intervals:
          - hour
          - day
          - week
          - month
---
apiVersion: v1
kind: Secret
metadata:
  name: sentiment-secrets
  labels:
    app: sentiment-service
type: Opaque
data:
  # Base64 encoded values - replace with actual secrets
  twitter-api-key: ""
  facebook-access-token: ""
  instagram-access-token: ""
  linkedin-access-token: ""
  slack-webhook-url: ""
  email-smtp-password: ""
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: sentiment-service-network-policy
  labels:
    app: sentiment-service
spec:
  podSelector:
    matchLabels:
      app: sentiment-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: dashboard-api
    - podSelector:
        matchLabels:
          app: frontend
    - podSelector:
        matchLabels:
          app: proxy-gateway
    ports:
    - protocol: TCP
      port: 8088
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to:
    - podSelector:
        matchLabels:
          app: proxy-gateway
    ports:
    - protocol: TCP
      port: 8080
  - to: []
    ports:
    - protocol: TCP
      port: 443  # HTTPS for external APIs
    - protocol: TCP
      port: 80   # HTTP for external APIs
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: sentiment-service-hpa
  labels:
    app: sentiment-service
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: sentiment-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: sentiment-service-monitor
  labels:
    app: sentiment-service
    monitoring: prometheus
spec:
  selector:
    matchLabels:
      app: sentiment-service
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
