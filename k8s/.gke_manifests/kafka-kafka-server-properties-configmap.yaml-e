# kafka-server-properties-configmap.yaml
# Kubernetes ConfigMap for Kafka server.properties

apiVersion: v1
kind: ConfigMap
metadata:
  name: kafka-server-properties # Name for the ConfigMap
data:
  server.properties: |
    # Basic broker settings
    broker.id=1
    zookeeper.connect=zookeeper:2181

    # Explicitly disable KRaft mode with all required settings
    process.roles=
    kraft.enabled=false
    node.id=1

    # Log directories
    log.dirs=/var/lib/kafka/data

    # Listeners and Advertised Listeners configuration
    # Define internal listener only, accessible via the 'kafka' service name within the cluster
    listeners=INTERNAL://:9092
    advertised.listeners=INTERNAL://kafka:9092
    listener.security.protocol.map=INTERNAL:PLAINTEXT
    inter.broker.listener.name=INTERNAL

    # Replication factor for internal topics (like offsets topic)
    offsets.topic.replication.factor=1

    # Disable auto-create topics (topic creation is handled by the kafka-topic-creator job)
    auto.create.topics.enable=false

    # Log configuration (optional, can be adjusted)
    log4j.properties=file:/mnt/kafka/log4j.properties # Updated path to /mnt/kafka/log4j.properties

    # Other common settings (can be added as needed)
    # num.network.threads=3
    # num.io.threads=8
    # socket.send.buffer.bytes=102400
    # socket.receive.buffer.bytes=102400
    # socket.request.max.bytes=104857600
    # num.partitions=1 # Default number of partitions for new topics if auto-create is enabled (it's disabled here)
    # num.recovery.threads.per.data.dir=1
    # log.retention.hours=168
    # log.segment.bytes=1073741824
    # log.retention.check.interval.ms=300000
    # zookeeper.connection.timeout.ms=180000
