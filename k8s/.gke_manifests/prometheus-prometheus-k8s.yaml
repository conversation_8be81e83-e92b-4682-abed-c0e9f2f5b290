# prometheus-k8s.yaml
# Complete Kubernetes YAML definitions for Prometheus

apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config # Name for the ConfigMap
  labels:
    app: prometheus # Add a label to the ConfigMap
data:
  # Embed the content of your prometheus.yml here
  prometheus.yml: |
    # prometheus.yml
    # Global configuration
    global:
      scrape_interval: 15s # Set the scrape interval to every 15 seconds. Default is every 1 minute.
      evaluation_interval: 15s # Evaluate rules every 15 seconds. Default is every 1 minute.
      # scrape_timeout is set to the global default (10s).

    # Alertmanager configuration (optional)
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              # alertmanager:9093 # If you deploy Alertmanager

    # Load rules once and periodically evaluate them in the background.
    rule_files:
      # - "first_rules.yml"
      # - "second_rules.yml"

    # A list of scrape configurations.
    scrape_configs:
      # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.

      # Scrape Prometheus itself
      - job_name: 'prometheus'
        # metrics_path defaults to '/metrics'
        # scheme defaults to 'http'.
        static_configs:
          - targets: ['localhost:9090'] # Scrape the Prometheus container itself

      # NEW JOB: Scrape Kubelet for container metrics (equivalent to cAdvisor in Kubernetes)
      # This job discovers nodes directly and targets the Kubelet's cAdvisor endpoint.
      - job_name: 'kubernetes-cadvisor' # RENAMED JOB: Changed from 'kubernetes-kubelet'
        kubernetes_sd_configs:
          - role: node # Changed role to node for direct node discovery
        scheme: https # Kubelet typically exposes metrics via HTTPS
        tls_config:
          insecure_skip_verify: true # WARNING: Insecure, skip certificate verification for simplicity in Minikube
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token # Use service account token for auth
        relabel_configs:
          # This relabeling rule transforms the discovered node address (e.g., ************:10250)
          # to target the Kubelet's secure metrics port, which is often 10250 or 10255.
          # We'll explicitly set it to 10250 as it's a common default for cAdvisor.
          - source_labels: [__address__]
            regex: '(.*):.*' # Capture the node IP
            target_label: __address__
            replacement: '${1}:10250' # Replace with the node IP and the Kubelet's cAdvisor port

          # Set the metrics path to cAdvisor endpoint
          - source_labels: [__metrics_path__]
            regex: '(.+)' # Capture existing path (if any)
            target_label: __metrics_path__
            replacement: '/metrics/cadvisor' # Explicitly set to cAdvisor path

          # Add node name as a label
          - source_labels: [__meta_kubernetes_node_name]
            target_label: node
          # Add pod name, namespace, and container name labels from discovery metadata
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod_name
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_container_name]
            target_label: container_name # This will be the container's actual name (e.g., mock-backend-gpu1)

        # --- ADDED/MODIFIED: Metric relabeling to modify labels on the scraped metrics ---
        metric_relabel_configs:
          # Derive 'com_docker_compose_service' from the 'pod' label
          # The 'pod' label is consistently present on cAdvisor metrics.
          # Example: pod="mock-backend-gpu1-956d7fb57-n25kt" -> com_docker_compose_service="mock-backend-gpu1"
          - source_labels: [pod] # CHANGED: Source label to 'pod'
            regex: '(mock-backend-gpu[12]).*' # Capture 'mock-backend-gpu1' or 'mock-backend-gpu2'
            target_label: com_docker_compose_service
            action: replace
          # Derive 'container' label from the 'pod' label
          # This should match the backend ID (e.g., mock-backend-gpu1)
          - source_labels: [pod] # CHANGED: Source label to 'pod'
            regex: '(mock-backend-gpu[12]).*' # Capture 'mock-backend-gpu1' or 'mock-backend-gpu2'
            target_label: container # Set the 'container' label on the metric
            action: replace

      # Scrape application endpoints using Kubernetes service discovery
      # This job discovers endpoints and filters based on labels/annotations
      - job_name: 'kubernetes-pods' # Renamed from 'application-metrics' for clarity
        kubernetes_sd_configs:
          - role: endpoints # Discover service endpoints
        scheme: http # Changed to HTTP, as your Go apps likely serve HTTP metrics
        # IMPORTANT: Use bearer token for authentication if applications require it, or if using HTTPS
        # bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token # Only if apps require auth
        # tls_config:
        #   insecure_skip_verify: true # WARNING: Insecure, skip certificate verification for simplicity in Minikube
        relabel_configs:
          # Filter to scrape endpoints of specific services by name
          # Only include services that expose their own /metrics endpoint
          - source_labels: [__meta_kubernetes_service_name]
            regex: 'proxy-gateway|data-processor|dashboard-api' # Removed mock-backend-gpu1/2
            action: keep

          # Example: Filter to scrape specific ports by number
          # If your services expose metrics on specific ports (e.g., 8080, 8081)
          - source_labels: [__meta_kubernetes_endpoint_port_number]
            regex: '8080|8081' # Add the ports your apps expose metrics on
            action: keep

          # Add useful labels from Kubernetes metadata
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod_name
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_service_name]
            target_label: service_name
          - source_labels: [__meta_kubernetes_container_name]
            target_label: container_name

          # Add the Docker Compose service label if available (less common in pure K8s)
          # This might require extracting labels from the pod metadata
          - source_labels: [__meta_kubernetes_pod_label_com_docker_compose_service]
            regex: '(.+)' # Match any non-empty value
            target_label: com_docker_compose_service # Use the label name expected by data processor
            action: replace # Replace the target label if a match is found

      # The 'kubernetes-pods-cadvisor-mock-backends' scrape job has been removed
      # as cAdvisor metrics are collected at the node level by 'kubernetes-kubelet' job.

---
# Define a Service Account for Prometheus
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prometheus-service-account
  namespace: default # Ensure this is the same namespace where Prometheus is deployed

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus-kubelet-access
rules:
- apiGroups: [""]
  resources:
  - nodes
  - nodes/proxy
  - nodes/metrics # For cAdvisor metrics on Kubelet
  - nodes/stats
  - pods # For discovering pods and their labels/annotations
  - services # Required for service discovery
  - endpoints # Required for endpoint discovery
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: prometheus-kubelet-access-binding
subjects:
- kind: ServiceAccount
  name: prometheus-service-account
  namespace: default # Must match the namespace of the ServiceAccount
roleRef:
  kind: ClusterRole
  name: prometheus-kubelet-access
  apiGroup: rbac.authorization.k8s.io # CORRECTED: Changed from rbac.authorization.io to rbac.authorization.k8s.io

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  labels:
    app: prometheus
spec:
  replicas: 1 # Running a single replica for Prometheus in this MVP
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      serviceAccountName: prometheus-service-account # Use the newly defined Service Account
      containers:
      - name: prometheus
        image: prom/prometheus:latest
        imagePullPolicy: IfNotPresent # Use local image if present
        ports:
        - containerPort: 9090 # Prometheus web UI port
        volumeMounts:
        - name: config-volume
          mountPath: /etc/prometheus/ # Mount the directory containing prometheus.yml
        # Optional: Mount a volume for persistent data storage
        # - name: prometheus-data
        #   mountPath: /prometheus
        command:
        - "/bin/prometheus"
        - "--config.file=/etc/prometheus/prometheus.yml" # Point to the mounted config file
        - "--storage.tsdb.path=/prometheus" # Path for data storage
        # Add flags for remote write receiver if needed by your data processor
        # The 'remote-write-receiver' flag might be for a newer Prometheus version or different context.
        # Removing it to avoid the "Unknown option" warning, as it's not critical for scraping.
        # - "--web.enable-remote-write-receiver"
        # - "--enable-feature=remote-write-receiver"
        resources:
          requests:
            memory: "512Mi"
            cpu: "300m"
          limits:
            memory: "1Gi"
            cpu: "700m"
      volumes:
      - name: config-volume
        configMap:
          name: prometheus-config # Reference the ConfigMap created above
      # Optional: Define persistent volume for data
      # - name: prometheus-data
      #   persistentVolumeClaim:
      #     claimName: prometheus-data-pvc # Assuming you create a PVC named prometheus-data-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus # Service name for in-cluster communication and scraping
spec:
  selector:
    app: prometheus
  ports:
  - protocol: TCP
    port: 9090 # Service port
    targetPort: 9090 # Container port
  type: LoadBalancer # Use LoadBalancer to expose Prometheus UI externally
  # In some environments, you might need to use NodePort or Ingress instead of LoadBalancer

