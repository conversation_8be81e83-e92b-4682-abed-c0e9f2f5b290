apiVersion: apps/v1
kind: Deployment
metadata:
  name: planning-service
  labels:
    app: planning-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: planning-service
  template:
    metadata:
      labels:
        app: planning-service
    spec:
      containers:
      - name: planning-service
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-planning-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: PORT
          value: "8080"
        - name: PROXY_GATEWAY_URL
          value: "http://proxy-gateway:8080"
        - name: CLICKHOUSE_HOST
          value: "clickhouse"
        - name: CLICKHOUSE_PORT
          value: "9000"
        - name: CL<PERSON>KHOUSE_DB
          value: "default"
        - name: CLICKHOUSE_USER
          value: "test"
        - name: CLICKHOUSE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: clickhouse-credentials
              key: password
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: planning-service
  labels:
    app: planning-service
spec:
  selector:
    app: planning-service
  ports:
  - protocol: TCP
    port: 8080
    targetPort: 8080
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: planning-service-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /v1/
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID"
spec:
  rules:
  - host: planning.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: planning-service
            port:
              number: 8080
