apiVersion: apps/v1
kind: Deployment
metadata:
  name: multi-agent-orchestrator
  labels:
    app: multi-agent-orchestrator
spec:
  replicas: 1
  selector:
    matchLabels:
      app: multi-agent-orchestrator
  template:
    metadata:
      labels:
        app: multi-agent-orchestrator
    spec:
      containers:
      - name: multi-agent-orchestrator
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-multi-agent-orchestrator:latest
        ports:
        - containerPort: 8083
        env:
        - name: PORT
          value: "8083"
        - name: REDIS_HOST
          value: "redis-service"
        - name: REDIS_PORT
          value: "6379"
        - name: GOVERNANCE_SERVICE_URL
          value: "http://governance-service:8080"
        - name: CLICKHOUSE_HOST
          value: "clickhouse-service"
        - name: CLICKHOUSE_PORT
          value: "8123"
        - name: CLICKHOUSE_USER
          valueFrom:
            secretKeyRef:
              name: clickhouse-credentials
              key: user
        - name: CLICKHOUSE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: clickhouse-credentials
              key: password
        - name: CLICKHOUSE_DB
          valueFrom:
            secretKeyRef:
              name: clickhouse-credentials
              key: clickhouse_db
        - name: HEALTH_CHECK_INTERVAL
          value: "30s"
        - name: MAX_CONCURRENT_WORKFLOWS
          value: "10"
        - name: AGENT_TIMEOUT
          value: "30s"
        - name: SESSION_TIMEOUT
          value: "24h"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8083
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8083
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
---
apiVersion: v1
kind: Service
metadata:
  name: multi-agent-orchestrator-service
  labels:
    app: multi-agent-orchestrator
spec:
  type: ClusterIP
  ports:
  - port: 8083
    targetPort: 8083
    protocol: TCP
    name: http
  selector:
    app: multi-agent-orchestrator
---
apiVersion: v1
kind: Service
metadata:
  name: multi-agent-orchestrator-loadbalancer
  labels:
    app: multi-agent-orchestrator
spec:
  type: LoadBalancer
  ports:
  - port: 8083
    targetPort: 8083
    protocol: TCP
    name: http
  selector:
    app: multi-agent-orchestrator
