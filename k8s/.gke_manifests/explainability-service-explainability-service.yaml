apiVersion: apps/v1
kind: Deployment
metadata:
  name: explainability-service
  labels:
    app: explainability-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: explainability-service
  template:
    metadata:
      labels:
        app: explainability-service
    spec:
      containers:
      - name: explainability-service
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-explainability-service:latest
        ports:
        - containerPort: 8085
        env:
        - name: PORT
          value: "8085"
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8085
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /health
            port: 8085
          initialDelaySeconds: 5
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: explainability-service
  labels:
    app: explainability-service
spec:
  selector:
    app: explainability-service
  ports:
    - protocol: TCP
      port: 8085
      targetPort: 8085
  type: ClusterIP
