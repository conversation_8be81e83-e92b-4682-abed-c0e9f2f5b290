# kafka-topic-creator-job.yaml
# Kubernetes YAML definition for a Job to create the Kafka topic

apiVersion: batch/v1
kind: Job
metadata:
  name: kafka-topic-creator # Job name
spec:
  template:
    spec:
      restartPolicy: OnFailure # Restart the pod if it fails
      containers:
      - name: topic-creator
        # Use the custom image built by your script
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-kafka-topic-creator:latest
        # If using a local image in Minikube, you might need imagePullPolicy: Never or IfNotPresent
        imagePullPolicy: IfNotPresent # Use IfNotPresent to avoid pulling if local image exists
        env:
        - name: KAFKA_BROKERS
          value: "kafka:9092" # Use the Kafka service name and port
        - name: TOPIC_NAME
          value: "inference-logs" # The topic name to create
        # Explicitly set Kafka environment variables required by Confluent tools
        # These are crucial for Kafka command-line tools to function correctly
        - name: KAFKA_ZOOKEEPER_CONNECT
          value: "zookeeper:2181" # Connect to the Zookeeper service
        - name: <PERSON><PERSON><PERSON>_ADVERTISED_LISTENERS
          value: "PLAINTEXT://kafka:9092" # Reflect the Kafka broker's advertised listener
        # Add other environment variables needed by your create_topic_and_wait.sh script
        # - name: BROKER_WAIT_TIMEOUT
        #   value: "240"
        # - name: TOPIC_WAIT_TIMEOUT
        #   value: "60"
        # - name: PRODUCER_WAIT_TIMEOUT
        #   value: "60"
        # - name: WAIT_INTERVAL
        #   value: "2"
        command: ["/app/create_topic_and_wait.sh"] # Ensure this command is not commented out
      # Add init containers if you need to wait for Kafka/Zookeeper before running the job container
      initContainers:
      - name: wait-for-kafka
        image: confluentinc/cp-kafka:latest # Use Confluent Kafka image for its tools
        imagePullPolicy: IfNotPresent # Set image pull policy to IfNotPresent
        # Use kafka-topics --list to check if Kafka is fully ready for topic operations
        command: ['sh', '-c', 'echo "Waiting for Kafka broker to be fully ready for topic operations..."; until /usr/bin/kafka-topics --bootstrap-server kafka:9092 --list > /dev/null 2>&1; do echo "Kafka not fully ready for topic operations, waiting..."; sleep 5; done; echo "Kafka broker is fully ready for topic operations."']
        resources:
          requests:
            memory: "100Mi"
            cpu: "50m"
          limits:
            memory: "200Mi"
            cpu: "100m"
      # This Job should ideally depend on the Kafka deployment being ready.
      # Kubernetes Jobs don't have a direct 'dependsOn' like Docker Compose.
      # The initContainer helps, but a more robust solution might involve
      # using a Kubernetes operator or a custom script that waits for the
      # Kafka deployment status before creating the Job resource.

