apiVersion: apps/v1
kind: Deployment
metadata:
  name: landing-page
  labels:
    app: landing-page
    edition: landing
spec:
  replicas: 2
  selector:
    matchLabels:
      app: landing-page
  template:
    metadata:
      labels:
        app: landing-page
        edition: landing
    spec:
      containers:
      - name: landing-page
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-landing-page:latest
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "32Mi"
            cpu: "25m"
          limits:
            memory: "64Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: landing-page
  labels:
    app: landing-page
    edition: landing
spec:
  selector:
    app: landing-page
  ports:
  - name: http
    port: 80
    targetPort: 80
  type: ClusterIP

# Ingress configuration removed - handled by existing ai-operations-hub-ingress in default namespace
