# data-processor.yaml
# Kubernetes YAML definitions for the Data Processor

apiVersion: v1
kind: ConfigMap
metadata:
  name: data-processor-config # Name for the ConfigMap
data:
  # Configuration for the Data Processor (e.g., cost rates)
  cpu_cost_per_hour: "0.05"
  memory_cost_per_gb_hour: "0.005"
  gpu_cost_per_hour: "0.50"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: data-processor
  labels:
    app: data-processor
spec:
  replicas: 1
  selector:
    matchLabels:
      app: data-processor
  template:
    metadata:
      labels:
        app: data-processor
    spec:
      initContainers:
      - name: wait-for-kafka-and-clickhouse
        image: busybox:1.36
        imagePullPolicy: IfNotPresent
        command: ['sh', '-c', 'echo "Waiting for Kafka and ClickHouse..."; until nc -z kafka 9092 && nc -z clickhouse 9000; do echo "Kafka or ClickHouse not ready, waiting..."; sleep 2; done; echo "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> are ready."']
        resources:
          requests:
            memory: "10Mi"
            cpu: "10m"
          limits:
            memory: "20Mi"
            cpu: "20m"
      - name: wait-for-redis
        image: busybox:1.36
        imagePullPolicy: IfNotPresent
        command: ['sh', '-c', 'echo "Waiting for Redis..."; until nc -z redis 6379; do echo "Redis not ready, waiting..."; sleep 2; done; echo "Redis is ready."']
        resources:
          requests:
            memory: "10Mi"
            cpu: "10m"
          limits:
            memory: "20Mi"
            cpu: "20m"
      - name: wait-for-prometheus
        image: busybox:1.36
        imagePullPolicy: IfNotPresent
        command: ['sh', '-c', 'echo "Waiting for Prometheus..."; until nc -z prometheus 9090; do echo "Prometheus not ready, waiting..."; sleep 2; done; echo "Prometheus is ready."']
        resources:
          requests:
            memory: "10Mi"
            cpu: "10m"
          limits:
            memory: "20Mi"
            cpu: "20m"
      containers:
      - name: data-processor
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-data-processor:latest
        imagePullPolicy: Always
        env:
        - name: KAFKA_BROKERS
          value: "kafka:9092"
        - name: KAFKA_TOPIC
          value: "inference-logs"
        - name: CLICKHOUSE_HOST
          value: "clickhouse"
        - name: CLICKHOUSE_PORT
          value: "9000"
        - name: CLICKHOUSE_DB
          value: "default"
        - name: CLICKHOUSE_USER
          value: "test"
        - name: CLICKHOUSE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: clickhouse-credentials
              key: password
        - name: PROMETHEUS_URL
          value: "http://prometheus:9090"
        - name: CPU_COST_PER_HOUR
          valueFrom:
            configMapKeyRef:
              name: data-processor-config
              key: cpu_cost_per_hour
        - name: MEMORY_COST_PER_GB_HOUR
          valueFrom:
            configMapKeyRef:
              name: data-processor-config
              key: memory_cost_per_gb_hour
        - name: GPU_COST_PER_HOUR
          valueFrom:
            configMapKeyRef:
              name: data-processor-config
              key: gpu_cost_per_hour
        - name: REDIS_ADDR
          value: "redis:6379"
        - name: REDIS_PASSWORD
          value: "" # Adjust if you secure Redis
        - name: REDIS_DB
          value: "0"
        - name: EVALUATION_SERVICE_URL
          value: "http://evaluation-service:8087" # Replace with your service URL
        ports:
        - containerPort: 8086 # Data Processor's HTTP server port
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: data-processor
  labels:
    app: data-processor
spec:
  selector:
    app: data-processor
  ports:
    - protocol: TCP
      port: 8086 # Service port
      targetPort: 8086 # Container port
  type: NodePort # Or LoadBalancer for cloud environments

