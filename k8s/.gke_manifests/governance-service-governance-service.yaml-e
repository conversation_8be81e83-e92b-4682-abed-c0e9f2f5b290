apiVersion: apps/v1
kind: Deployment
metadata:
  name: governance-service
  labels:
    app: governance-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: governance-service
  template:
    metadata:
      labels:
        app: governance-service
    spec:
      containers:
      - name: governance-service
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-governance-service:latest 
        ports:
        - containerPort: 8080
        env:
        - name: PORT
          value: "8080"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "250m"
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: governance-service
  labels:
    app: governance-service
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: governance-service
