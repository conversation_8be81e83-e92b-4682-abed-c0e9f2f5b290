# clickhouse-k8s.yaml
# Kubernetes YAML definitions for ClickHouse

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: clickhouse-data-pvc # Name for the PersistentVolumeClaim
spec:
  accessModes:
    - ReadWriteOnce # Can be mounted as read-write by a single node
  resources:
    requests:
      storage: 5Gi # Request 5GB of storage (adjust as needed)
  # storageClassName: standard # Uncomment and specify if you have a specific storage class (Minikube usually has a default)
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: clickhouse
  labels:
    app: clickhouse
spec:
  replicas: 1 # Running a single replica for the database in this MVP
  selector:
    matchLabels:
      app: clickhouse
  strategy:
    type: Recreate # Recreate the pod on updates (simpler for single-replica stateful apps)
  template:
    metadata:
      labels:
        app: clickhouse
    spec:
      containers:
      - name: clickhouse
        image: clickhouse/clickhouse-server:latest
        ports:
        - containerPort: 9000 # Client port
        - containerPort: 8123 # HTTP port
        env:
        - name: CLICKHOUSE_DB
          value: "default"
        - name: CLICKHOUSE_USER
          value: "test"
        - name: CLICKHOUSE_PASSWORD
          value: "test"
        volumeMounts:
        - name: clickhouse-persistent-storage
          mountPath: /var/lib/clickhouse/ # Mount the persistent volume to the data directory
        - name: clickhouse-init-script
          mountPath: /docker-entrypoint-initdb.d/ # Mount the init script directory, where ClickHouse looks for init scripts
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi" # ClickHouse can be memory intensive
            cpu: "1000m"
      volumes:
      - name: clickhouse-persistent-storage
        persistentVolumeClaim:
          claimName: clickhouse-data-pvc # Reference the PVC created above
      - name: clickhouse-init-script
        configMap:
          name: clickhouse-init-sql # Reference the ConfigMap for the init script
---
apiVersion: v1
kind: Service
metadata:
  name: clickhouse # Service name for in-cluster communication
spec:
  selector:
    app: clickhouse
  ports:
  - name: client
    protocol: TCP
    port: 9000
    targetPort: 9000
  - name: http
    protocol: TCP
    port: 8123
    targetPort: 8123
  # Optional: Expose the HTTP port externally for accessing the ClickHouse UI or client
  # type: NodePort # Uncomment to expose externally via NodePort
---
# ConfigMap for the ClickHouse initialization script
apiVersion: v1
kind: ConfigMap
metadata:
  name: clickhouse-init-sql # Name for the ConfigMap
data:
  init.sql: |
    -- This script is executed by ClickHouse when the container starts,
    -- thanks to the volume mount in docker-compose.yaml or Kubernetes YAML.
    -- It creates the database and tables for storing inference logs, evaluation results,
    -- and curated data if they do not already exist.

    -- Create the database if it doesn't exist
    CREATE DATABASE IF NOT EXISTS default;

    -- Create the inference_logs table if it doesn't exist
    CREATE TABLE IF NOT EXISTS default.inference_logs (
        -- Unique identifier for each request
        request_id String,

        -- Timestamp when the request was received by the proxy (nanosecond precision)
        timestamp DateTime64(9),

        -- HTTP method of the request (e.g., 'POST')
        method String,

        -- Path of the request URL (e.g., '/predict')
        path String,

        -- IP address of the client making the request
        client_ip String,

        -- User agent string from the client
        user_agent String,

        -- ID of the backend AI model selected for this request
        selected_backend_id String,

        -- URL of the selected backend
        backend_url String,

        -- Type of the backend (e.g., 'openai', 'google', 'anthropic')
        backend_type String,

        -- Timestamp when the response was received by the proxy (nanosecond precision)
        response_timestamp DateTime64(9),

        -- Latency of the request (time taken by the backend) in milliseconds
        latency_ms Float64,

        -- HTTP status code of the response from the backend
        status_code Int32,

        -- Error message if the request failed
        error String,

        -- ID of the policy applied to this request
        policy_id_applied String,

        -- Model name requested by the user
        model_requested String,

        -- Actual model ID used for the inference
        model_used String,

        -- Whether the response was streamed (0 for false, 1 for true)
        stream UInt8,

        -- CPU usage rate during inference
        cpu_usage_rate Float64,

        -- Memory usage in bytes during inference
        memory_usage_bytes Float64,

        -- Total calculated cost of the inference request
        total_cost Float64,

        -- Number of input tokens for the inference
        input_tokens Int64,

        -- Number of output tokens for the inference
    output_tokens Int64,

        -- Type of task (e.g., 'text-generation', 'summarization')
        task_type String,

        -- Identifier for the conversation thread
        conversation_id String,

        -- Identifier for the user making the request
        user_id String,

        -- Roles associated with the user (stored as a JSON string or array of strings)
        user_roles Array(String),

        -- Raw request headers (stored as JSON string)
        request_headers String,

        -- Snippet of the request body
        request_body_snippet String,

        -- Snippet of the response body
        response_body_snippet String,

        -- Raw response headers (stored as JSON string)
        response_headers String

    ) ENGINE = MergeTree()
    ORDER BY (timestamp, request_id); -- Define the primary key for sorting data

    -- Create the llm_evaluation_results table if it doesn't exist
    CREATE TABLE IF NOT EXISTS default.llm_evaluation_results (
        id String,
        request_id String,
        prompt String,
        llm_response String,
        model_id String,
        prompt_id String,
        task_type String,
        evaluation_type String,
        score Float64,
        passed UInt8,
        feedback String,
        evaluated_at DateTime64(9),
        expected_response String,
        raw_metrics String,
        metadata String
    ) ENGINE = MergeTree()
    ORDER BY (evaluated_at, id);

    -- Create the curated_data table if it doesn't exist
    CREATE TABLE IF NOT EXISTS default.curated_data (
        id String,
        request_id String,
        prompt String,
        llm_response String,
        model_id String,
        task_type String,
        generated_at DateTime64(9),
        evaluation_score Float64,
        evaluation_passed UInt8,
        evaluation_type String,
        feedback String,
        metadata String,
        source_log_id String,
        curated_by String
    ) ENGINE = MergeTree()
    ORDER BY (generated_at, id);
