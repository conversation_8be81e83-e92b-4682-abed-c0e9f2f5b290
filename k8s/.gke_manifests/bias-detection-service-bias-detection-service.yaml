apiVersion: apps/v1
kind: Deployment
metadata:
  name: bias-detection-service
  labels:
    app: bias-detection-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bias-detection-service
  template:
    metadata:
      labels:
        app: bias-detection-service
    spec:
      containers:
      - name: bias-detection-service
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-bias-detection-service:latest
        ports:
        - containerPort: 8084
        env:
        - name: PORT
          value: "8084"
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8084
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /health
            port: 8084
          initialDelaySeconds: 5
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: bias-detection-service
  labels:
    app: bias-detection-service
spec:
  selector:
    app: bias-detection-service
  ports:
    - protocol: TCP
      port: 8084
      targetPort: 8084
  type: ClusterIP
