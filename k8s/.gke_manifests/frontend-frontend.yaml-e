# k8s/frontend/frontend.yaml
# Combined Kubernetes YAML definitions for the Frontend Dashboard
# Includes Service, ConfigMap, and Deployment

apiVersion: v1
kind: Service
metadata:
  name: frontend-dashboard # Service name for in-cluster communication
spec:
  selector:
    app: frontend-dashboard
  ports:
  - name: http
    protocol: TCP
    port: 80 # Frontend listens on 80
    targetPort: 80
  type: ClusterIP # Use LoadBalancer to expose the dashboard externally

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-nginx-config
  labels:
    app: frontend-dashboard
data:
  # The key here will be the filename inside the pod
  default.conf: |
    # Main Nginx configuration file for frontend-dashboard with proxying to internal services.

    user nginx;
    worker_processes auto;

    error_log /var/log/nginx/error.log debug; # Increased log level for troubleshooting
    pid /run/nginx.pid;

    events {
        worker_connections 1024;
    }

    http {
        include       mime.types;
        default_type  application/octet-stream;

        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                         '$status $body_bytes_sent "$http_referer" '
                         '"$http_user_agent" "$http_x_forwarded_for" '
                         'rt=$request_time uct=$upstream_connect_time uht=$upstream_header_time urt=$upstream_response_time';

        access_log /var/log/nginx/access.log main;

        sendfile         on;
        keepalive_timeout  120; # Increased from 65
        client_body_timeout 120s;
        client_header_timeout 120s;
        send_timeout 120s;

        # Resolver for dynamic DNS resolution within Kubernetes
        resolver kube-dns.kube-system.svc.cluster.local valid=5s ipv6=off;

        # Define upstream servers with improved connection handling
        upstream dashboard_api_upstream {
            server dashboard-api.default.svc.cluster.local:8081;
        }

        upstream proxy_gateway_upstream {
            server proxy-gateway.default.svc.cluster.local:8080;
        }

        upstream policy_manager_upstream {
            server policy-manager.default.svc.cluster.local:8083;
            keepalive 32;
        }

        upstream evaluation_service_upstream {
            server evaluation-service.default.svc.cluster.local:8087 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }

        upstream planning_service_upstream {
            server planning-service.default.svc.cluster.local:8080 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }

        # Responsible AI Services Upstreams
        upstream bias_detection_upstream {
            server bias-detection-service.default.svc.cluster.local:8084 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }

        upstream explainability_upstream {
            server explainability-service.default.svc.cluster.local:8085 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }

        upstream robustness_testing_upstream {
            server robustness-testing-service.default.svc.cluster.local:8086 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }

        upstream compliance_upstream {
            server compliance-service.default.svc.cluster.local:8087 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }

        upstream governance_upstream {
            server governance-service.default.svc.cluster.local:8080 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }

        upstream multi_agent_orchestrator_upstream {
            server multi-agent-orchestrator-service.default.svc.cluster.local:8083 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }

        upstream integration_service_upstream {
            server integration-service.default.svc.cluster.local:8080 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }

        # AI Optimizer upstream for MCP
        upstream ai_optimizer_upstream {
            server ai-optimizer.default.svc.cluster.local:8085 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }

        # Auth service upstream
        upstream auth_service_upstream {
            server auth-service.default.svc.cluster.local:80 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }

        server {
            listen 80;
            server_name localhost;

            # Increase timeouts for all locations
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            
            # Enable keepalive connections to upstream
            proxy_http_version 1.1;
            proxy_set_header Connection "";

            # 1. Planning service proxy - MUST come before general /v1/ proxy
            location /v1/goals {
                proxy_pass http://planning_service_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;

                # Disable caching for dynamic content
                proxy_no_cache 1;
                proxy_cache_bypass 1;

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "planning_service_upstream$request_uri";
                add_header X-Debug-Info "Planning service access";
            }

            location /v1/templates {
                proxy_pass http://planning_service_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;

                # Disable caching for dynamic content
                proxy_no_cache 1;
                proxy_cache_bypass 1;

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "planning_service_upstream$request_uri";
                add_header X-Debug-Info "Planning service access";
            }

            # Planning service API routes - MUST come before general /api/ proxy
            location /api/planning/ {
                # Rewrite /api/planning/v1/* to /v1/*
                rewrite ^/api/planning/(.*) /$1 break;
                proxy_pass http://planning_service_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;

                # Disable caching for dynamic content
                proxy_no_cache 1;
                proxy_cache_bypass 1;

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "planning_service_upstream$request_uri";
                add_header X-Debug-Info "Planning service API access";
            }

            # Multi-Agent Orchestrator proxy
            location /v1/agents {
                proxy_pass http://multi_agent_orchestrator_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;

                # Disable caching for dynamic content
                proxy_no_cache 1;
                proxy_cache_bypass 1;

                # CORS headers for frontend compatibility
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;
                add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;

                # Handle preflight requests
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '*';
                    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID';
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "multi_agent_orchestrator_upstream$request_uri";
                add_header X-Debug-Info "Multi-Agent Orchestrator access";
            }

            location /v1/workflows {
                proxy_pass http://multi_agent_orchestrator_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;

                # Disable caching for dynamic content
                proxy_no_cache 1;
                proxy_cache_bypass 1;

                # CORS headers for frontend compatibility
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;
                add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;

                # Handle preflight requests
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '*';
                    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID';
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "multi_agent_orchestrator_upstream$request_uri";
                add_header X-Debug-Info "Multi-Agent Orchestrator access";
            }

            location /v1/analytics {
                proxy_pass http://multi_agent_orchestrator_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;

                # Disable caching for dynamic content
                proxy_no_cache 1;
                proxy_cache_bypass 1;

                # CORS headers for frontend compatibility
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;
                add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;

                # Handle preflight requests
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '*';
                    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID';
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "multi_agent_orchestrator_upstream$request_uri";
                add_header X-Debug-Info "Multi-Agent Orchestrator access";
            }

            # 2. LLM inference proxy (for chat completions)
            location /v1/chat/completions {
                proxy_pass http://proxy_gateway_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_buffering off;
                proxy_cache off;
                chunked_transfer_encoding off;

                # Disable compression for streaming responses
                gzip off;
                proxy_set_header Accept-Encoding "";

                # Enable CORS for chat interface
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-Preferred-LLM-ID' always;

                # Handle preflight requests
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '*';
                    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
                    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-Preferred-LLM-ID';
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }
            }

            location /v1/chat/ {
                proxy_pass http://proxy_gateway_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_buffering off;
                proxy_cache off;
                chunked_transfer_encoding off;

                # Disable compression for streaming responses
                gzip off;
                proxy_set_header Accept-Encoding "";
            }

            # Authentication service routes
            location /auth/ {
                proxy_pass http://auth_service_upstream/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # Disable caching for authentication
                proxy_no_cache 1;
                proxy_cache_bypass 1;
                proxy_buffering off;
                proxy_redirect off;

                # CORS headers for frontend compatibility
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;
                add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;

                # Handle preflight requests
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '*';
                    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID';
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "auth_service_upstream$request_uri";
                add_header X-Debug-Info "Auth service access";
            }

            # Additional Multi-Agent endpoints
            location /v1/marketplace {
                proxy_pass http://multi_agent_orchestrator_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;

                # Disable caching for dynamic content
                proxy_no_cache 1;
                proxy_cache_bypass 1;

                # CORS headers for frontend compatibility
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;
                add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;

                # Handle preflight requests
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '*';
                    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID';
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "multi_agent_orchestrator_upstream$request_uri";
                add_header X-Debug-Info "Multi-Agent Orchestrator marketplace";
            }


            # Health check endpoint for multi-agent orchestrator
            location /health {
                proxy_pass http://multi_agent_orchestrator_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # No caching for health checks
                proxy_no_cache 1;
                proxy_cache_bypass 1;

                # CORS headers
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range' always;

                # Handle preflight requests
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '*';
                    add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS';
                    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }
            }

            # 2.1. Responsible AI Services Routing (must come before general /v1/ rule)
            location /v1/bias-audits {
                # Handle CORS preflight requests
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '*';
                    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID';
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }

                rewrite ^/v1/(.*) /$1 break;
                proxy_pass http://bias_detection_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # Add CORS headers for actual requests
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;
            }

            location /v1/bias-metrics {
                # Handle CORS preflight requests
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '*';
                    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID';
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }

                rewrite ^/v1/(.*) /$1 break;
                proxy_pass http://bias_detection_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # Add CORS headers for actual requests
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;
            }

            location /v1/explanations {
                # Handle CORS preflight requests
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '*';
                    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID';
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }

                rewrite ^/v1/(.*) /$1 break;
                proxy_pass http://explainability_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # Add CORS headers for actual requests
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;
            }

            location /v1/robustness-tests {
                # Handle CORS preflight requests
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '*';
                    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID';
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }

                rewrite ^/v1/(.*) /$1 break;
                proxy_pass http://robustness_testing_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # Add CORS headers for actual requests
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;
            }

            location /v1/compliance {
                # Handle CORS preflight requests
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '*';
                    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID';
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }

                rewrite ^/v1/(.*) /$1 break;
                proxy_pass http://compliance_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # Add CORS headers for actual requests
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;
            }

            location /v1/factsheets {
                rewrite ^/v1/(.*) /$1 break;
                proxy_pass http://governance_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # Governance service routes
            location /governance/ {
                proxy_pass http://governance_upstream/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # Governance WebSocket support
            location /ws/governance {
                proxy_pass http://governance_upstream/ws/governance;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_cache_bypass $http_upgrade;
                proxy_read_timeout 86400;
            }

            # MCP WebSocket connections - Main MCP Host
            location /mcp/connect {
                proxy_pass http://proxy_gateway_upstream/mcp/connect;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;
                proxy_cache_bypass $http_upgrade;
                proxy_read_timeout 86400;
                proxy_send_timeout 86400;

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "proxy_gateway_upstream/mcp/connect";
                add_header X-Debug-Info "MCP Host WebSocket connection";
            }

            # MCP WebSocket connections - Individual service MCP servers
            location /ai-optimizer/mcp {
                proxy_pass http://ai_optimizer_upstream/mcp;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;
                proxy_cache_bypass $http_upgrade;
                proxy_read_timeout 86400;
                proxy_send_timeout 86400;

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "ai_optimizer_upstream/mcp";
                add_header X-Debug-Info "AI Optimizer MCP WebSocket connection";
            }

            location /planning-service/mcp {
                proxy_pass http://planning_service_upstream/mcp;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;
                proxy_cache_bypass $http_upgrade;
                proxy_read_timeout 86400;
                proxy_send_timeout 86400;

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "planning_service_upstream/mcp";
                add_header X-Debug-Info "Planning Service MCP WebSocket connection";
            }

            location /evaluation-service/mcp {
                proxy_pass http://evaluation_service_upstream/mcp;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;
                proxy_cache_bypass $http_upgrade;
                proxy_read_timeout 86400;
                proxy_send_timeout 86400;

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "evaluation_service_upstream/mcp";
                add_header X-Debug-Info "Evaluation Service MCP WebSocket connection";
            }

            location /integration-service/mcp {
                proxy_pass http://integration_service_upstream/mcp;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;
                proxy_cache_bypass $http_upgrade;
                proxy_read_timeout 86400;
                proxy_send_timeout 86400;

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "integration_service_upstream/mcp";
                add_header X-Debug-Info "Integration Service MCP WebSocket connection";
            }

            # 2.2. General /v1/ proxy (fallback for other v1 endpoints)
            location /v1/ {
                proxy_pass http://proxy_gateway_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_buffering off;
                proxy_cache off;
                chunked_transfer_encoding off;
            }

            # IMPORTANT: Order matters in nginx location blocks!
            # More specific patterns should come before more general ones

            # 3. Policies API - direct pass to policy-manager
            # Keep the existing /policy-api/ endpoint for backward compatibility
            location /policy-api/ {
                # Use variable to ensure DNS resolution happens for each request
                set $policy_manager_backend "policy-manager.default.svc.cluster.local:8083";
                # Remove the /policy-api/ prefix when proxying to policy-manager
                rewrite ^/policy-api/(.*) /$1 break;
                proxy_pass http://$policy_manager_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # Disable caching
                proxy_no_cache 1;
                proxy_cache_bypass 1;
                
                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "$policy_manager_backend/$1";
                add_header X-Debug-Info "Direct policy-manager access";
                
                # Add error handling
                proxy_intercept_errors on;
                error_page 404 502 503 504 = @policy_manager_fallback;
            }
            
            # 3.1 New consistent endpoint for policies - direct pass to policy-manager
            location /api/policies {
                proxy_pass http://policy_manager_upstream/policies;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # Disable caching
                proxy_no_cache 1;
                proxy_cache_bypass 1;

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "policy_manager_upstream/policies";
                add_header X-Debug-Info "Direct policy-manager access via upstream";

                # Add error handling
                proxy_intercept_errors on;
                error_page 404 502 503 504 = @policy_manager_fallback;
            }

            location /api/prompts {
                proxy_pass http://policy_manager_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # Disable caching
                proxy_no_cache 1;
                proxy_cache_bypass 1;

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "policy_manager_upstream";
                add_header X-Debug-Info "Direct policy-manager access via upstream";

                # Add error handling - temporarily disabled for debugging
                # proxy_intercept_errors on;
                # error_page 404 502 503 504 = @policy_manager_fallback;
            }
            
            # 4. Model profiles API - direct pass to policy-manager
            # Support both underscore and hyphen versions for compatibility
            location ~ ^/api/model[-_]profiles {
                # Use variable to ensure DNS resolution happens for each request
                set $policy_manager_backend "policy-manager.default.svc.cluster.local:8083";

                # Rewrite to ensure consistent backend URL regardless of hyphen or underscore
                rewrite ^/api/model-profiles(.*) /model_profiles$1 break;
                rewrite ^/api/model_profiles(.*) /model_profiles$1 break;
                proxy_pass http://$policy_manager_backend/api/model-profiles;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # Disable caching
                proxy_no_cache 1;
                proxy_cache_bypass 1;
                
                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "$policy_manager_backend/api/model-profiles";
                add_header X-Debug-Info "Direct policy-manager access";
                
                # Add error handling
                proxy_intercept_errors on;
                error_page 404 502 503 504 = @policy_manager_fallback;
            }

            # MCP HTTP API endpoints
            location /mcp/status {
                proxy_pass http://proxy_gateway_upstream/mcp/status;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;

                # Disable caching for dynamic content
                proxy_no_cache 1;
                proxy_cache_bypass 1;

                # CORS headers for frontend compatibility
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;

                # Handle preflight requests
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '*';
                    add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS';
                    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID';
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "proxy_gateway_upstream/mcp/status";
                add_header X-Debug-Info "MCP Status API access";
            }

            # MCP API endpoints routing
            location /api/mcp/connect {
                proxy_pass http://proxy_gateway_upstream/mcp/connect;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;
                proxy_cache_bypass $http_upgrade;
                proxy_read_timeout 86400;
                proxy_send_timeout 86400;

                # Add CORS headers
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;
            }

            location /api/mcp/status {
                proxy_pass http://proxy_gateway_upstream/mcp/status;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;

                # Add CORS headers
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;
            }

            # 5. Dashboard API endpoints - IMPORTANT: NO REWRITE for these specific paths, directly proxy
            # Updated to include MCP endpoints
            location ~ ^/api/(summary|time-series|inference-logs|backend-latencies|optimal-backend|evaluation-results|curated-data|set-preference|mcp/metrics) {
                proxy_pass http://dashboard_api_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_intercept_errors on;
                error_log /var/log/nginx/dashboard_api_error.log debug;

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "dashboard_api_upstream$request_uri";
            }

            # 6. General /api/ proxy (catch-all for any other /api paths not specifically handled above)
            # This block is less specific than the regex above, so it will only catch paths
            # that don't match the regex. It seems like a general fallback.
            # If you intend for *all* /api/ calls to go to dashboard_api_upstream
            # unless specifically routed to policy-manager, then this is fine.
            # 6. Integration service API - GitOps and PromptOps integration
            location /api/integration/ {
                rewrite ^/api/integration/(.*) /$1 break;
                proxy_pass http://integration_service_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;

                # Disable caching for dynamic content
                proxy_no_cache 1;
                proxy_cache_bypass 1;

                # CORS headers for frontend compatibility
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;
                add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;

                # Handle preflight requests
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '*';
                    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID';
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }

                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "integration_service_upstream$request_uri";
                add_header X-Debug-Info "Integration service access";
            }

            # If this is ONLY for dashboard-api, consider making it more specific
            # or removing it if the regex above covers all dashboard-api paths.
            # For now, keeping it as is, assuming it catches other unlisted /api/ paths.
            # Note: Given the more specific regex above for common dashboard API paths,
            # this block might not be hit often for dashboard data.
            location /api/ {
                proxy_pass http://dashboard_api_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            location /api/planning/v1/ {
                rewrite ^/api/planning/v1/(.*) /v1/$1 break;
                proxy_pass http://planning_service_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            location /api/multi-agent/auth/ {
                rewrite ^/api/multi-agent/(.*) /$1 break;
                proxy_pass http://multi_agent_orchestrator_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;

                # Disable caching for authentication
                proxy_no_cache 1;
                proxy_cache_bypass 1;

                # Add CORS headers
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;
            }

            location /api/multi-agent/v1/ {
                rewrite ^/api/multi-agent/v1/(.*) /v1/$1 break;
                proxy_pass http://multi_agent_orchestrator_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-User-ID $http_x_user_id;

                # Add CORS headers
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;
            }

            location /api/evaluation/ {
                # Handle CORS preflight requests
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '*';
                    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID';
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }

                rewrite ^/api/evaluation/(.*) /$1 break;
                proxy_pass http://evaluation_service_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # Add CORS headers for actual requests
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-User-ID' always;
            }

            # Fallback for policy-manager endpoints
            location @policy_manager_fallback {
                add_header Content-Type application/json;
                add_header X-Debug-Info "Policy manager service unavailable";
                return 503 '{"error": "Policy manager service temporarily unavailable", "status": 503}';
            }

            # 7. Enterprise frontend routing - serve the SPA for /enterprise
            location /enterprise {
                root /usr/share/nginx/html;
                index index.html;
                try_files $uri $uri/ /index.html;

                # Add enterprise edition headers
                add_header X-Edition "enterprise" always;
                add_header X-Features "full" always;
            }

            # 8. Enterprise-specific static assets - JavaScript files
            location ~ ^/enterprise/assets/(.+\.js)$ {
                root /usr/share/nginx/html;
                try_files /assets/$1 =404;
                add_header Content-Type application/javascript;
                expires 1y;
                add_header Cache-Control "public, immutable";
            }

            # 8b. Enterprise-specific static assets - CSS files
            location ~ ^/enterprise/assets/(.+\.css)$ {
                root /usr/share/nginx/html;
                try_files /assets/$1 =404;
                add_header Content-Type text/css;
                expires 1y;
                add_header Cache-Control "public, immutable";
            }

            # 8c. Enterprise-specific static assets - Other files
            location ~ ^/enterprise/assets/(.*)$ {
                root /usr/share/nginx/html;
                try_files /assets/$1 =404;
                expires 1y;
                add_header Cache-Control "public, immutable";
            }

            location /enterprise/vite.svg {
                root /usr/share/nginx/html;
                try_files /vite.svg =404;
                expires 1y;
                add_header Cache-Control "public, immutable";
            }

            # 9. Static assets - serve with correct MIME types
            location ~* \.js$ {
                root /usr/share/nginx/html;
                add_header Content-Type application/javascript;
                expires 1y;
                add_header Cache-Control "public, immutable";
                try_files $uri =404;
            }

            # 10. Enterprise JavaScript files - specific handling to ensure correct MIME type
            location ~ ^/enterprise/.*\.js$ {
                root /usr/share/nginx/html;
                add_header Content-Type application/javascript;
                expires 1y;
                add_header Cache-Control "public, immutable";
                try_files $uri =404;
            }

            location ~* \.css$ {
                root /usr/share/nginx/html;
                add_header Content-Type text/css;
                expires 1y;
                add_header Cache-Control "public, immutable";
                try_files $uri =404;
            }

            location /assets/ {
                root /usr/share/nginx/html;
                expires 1y;
                add_header Cache-Control "public, immutable";
                try_files $uri =404;
            }

            # 11. SPA fallback for all other routes
            location / {
                root /usr/share/nginx/html;
                index index.html index.htm;
                try_files $uri $uri/ /index.html;
            }

            error_page 500 502 503 504 /50x.html;
            location = /50x.html {
                root /usr/share/nginx/html;
            }
        }
    }

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-dashboard
  labels:
    app: frontend-dashboard
spec:
  replicas: 1
  selector:
    matchLabels:
      app: frontend-dashboard
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: frontend-dashboard
    spec:
      containers:
      - name: frontend-dashboard
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-frontend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 80
          name: http
        command: ["nginx", "-g", "daemon off;"]
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "300m"
        volumeMounts:
        - name: nginx-config-volume
          mountPath: /etc/nginx/nginx.conf
          subPath: default.conf
      volumes:
      - name: nginx-config-volume
        configMap:
          name: frontend-nginx-config
          items:
          - key: default.conf
            path: default.conf
