# redis-k8s.yaml
# Kubernetes YAML definitions for Redis

apiVersion: v1
kind: Service
metadata:
  name: redis # Service name for in-cluster communication
spec:
  selector:
    app: redis
  ports:
  - name: tcp-port
    protocol: TCP
    port: 6379 # Default Redis port
    targetPort: 6379
  type: ClusterIP # Use ClusterIP for in-cluster access

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis # Deployment name
  labels:
    app: redis
spec:
  replicas: 1 # Single replica for simplicity in MVP
  selector:
    matchLabels:
      app: redis
  strategy:
    type: Recreate # Simple strategy for single replica
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:latest # Use the official Redis image
        imagePullPolicy: IfNotPresent # Set image pull policy to IfNotPresent
        ports:
        - containerPort: 6379
          name: tcp-port
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        # Optional: Add volume mount for persistent data if needed for Redis
        # volumeMounts:
        # - name: redis-storage
        #   mountPath: /data
        # Add a readiness probe to check if Redis is ready
        readinessProbe:
          tcpSocket:
            port: 6379
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          tcpSocket:
            port: 6379
          initialDelaySeconds: 15
          periodSeconds: 20
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
      # Optional: Define persistent volume for data
      # volumes:
      # - name: redis-storage
      #   persistentVolumeClaim:
      #     claimName: redis-data-pvc # Assuming you create a PVC

