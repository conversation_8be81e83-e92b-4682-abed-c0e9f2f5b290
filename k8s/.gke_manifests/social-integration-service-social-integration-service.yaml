apiVersion: apps/v1
kind: Deployment
metadata:
  name: social-integration-service
  labels:
    app: social-integration-service
    component: social-integration
    tier: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: social-integration-service
  template:
    metadata:
      labels:
        app: social-integration-service
        component: social-integration
        tier: backend
    spec:
      containers:
      - name: social-integration-service
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-social-integration-service:latest
        ports:
        - containerPort: 8089
        env:
        - name: PORT
          value: "8089"
        - name: REDIS_ADDR
          value: "redis:6379"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: password
              optional: true
        # Social Media API Keys
        - name: TWITTER_API_KEY
          valueFrom:
            secretKeyRef:
              name: social-secrets
              key: twitter-api-key
              optional: true
        - name: TWITTER_API_SECRET
          valueFrom:
            secretKeyRef:
              name: social-secrets
              key: twitter-api-secret
              optional: true
        - name: FACEBOOK_ACCESS_TOKEN
          valueFrom:
            secretKeyRef:
              name: social-secrets
              key: facebook-access-token
              optional: true
        - name: INSTAGRAM_ACCESS_TOKEN
          valueFrom:
            secretKeyRef:
              name: social-secrets
              key: instagram-access-token
              optional: true
        - name: LINKEDIN_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: social-secrets
              key: linkedin-client-id
              optional: true
        - name: LINKEDIN_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: social-secrets
              key: linkedin-client-secret
              optional: true
        # Business Integration API Keys
        - name: SALESFORCE_API_KEY
          valueFrom:
            secretKeyRef:
              name: integration-secrets
              key: salesforce-api-key
              optional: true
        - name: HUBSPOT_API_KEY
          valueFrom:
            secretKeyRef:
              name: integration-secrets
              key: hubspot-api-key
              optional: true
        - name: ZENDESK_API_TOKEN
          valueFrom:
            secretKeyRef:
              name: integration-secrets
              key: zendesk-api-token
              optional: true
        - name: GOOGLE_ANALYTICS_KEY
          valueFrom:
            secretKeyRef:
              name: integration-secrets
              key: google-analytics-key
              optional: true
        - name: MAILCHIMP_API_KEY
          valueFrom:
            secretKeyRef:
              name: integration-secrets
              key: mailchimp-api-key
              optional: true
        - name: SLACK_BOT_TOKEN
          valueFrom:
            secretKeyRef:
              name: integration-secrets
              key: slack-bot-token
              optional: true
        - name: NOTION_API_KEY
          valueFrom:
            secretKeyRef:
              name: integration-secrets
              key: notion-api-key
              optional: true
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8089
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8089
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: social-config
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: social-config
        configMap:
          name: social-integration-config
          optional: true
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: social-integration-service
  labels:
    app: social-integration-service
    component: social-integration
spec:
  selector:
    app: social-integration-service
  ports:
  - name: http
    port: 8089
    targetPort: 8089
    protocol: TCP
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: social-integration-config
  labels:
    app: social-integration-service
data:
  config.yaml: |
    social_platforms:
      twitter:
        api_version: "v2"
        rate_limit: 300
        sync_interval: 300
      facebook:
        api_version: "v18.0"
        rate_limit: 200
        sync_interval: 600
      instagram:
        api_version: "v18.0"
        rate_limit: 200
        sync_interval: 600
      linkedin:
        api_version: "v2"
        rate_limit: 100
        sync_interval: 900
      tiktok:
        api_version: "v1"
        rate_limit: 100
        sync_interval: 900
    
    business_integrations:
      crm:
        sync_interval: 300
        batch_size: 100
      helpdesk:
        sync_interval: 180
        batch_size: 50
      analytics:
        sync_interval: 600
        batch_size: 1000
      marketing:
        sync_interval: 900
        batch_size: 500
      communication:
        sync_interval: 60
        batch_size: 200
      productivity:
        sync_interval: 300
        batch_size: 100
    
    monitoring:
      health_check_interval: 30
      error_threshold: 5
      retry_attempts: 3
      timeout_seconds: 30
    
    data_retention:
      social_posts_days: 30
      analytics_days: 90
      mentions_days: 60
      trends_days: 7
---
apiVersion: v1
kind: Secret
metadata:
  name: social-secrets
  labels:
    app: social-integration-service
type: Opaque
data:
  # Base64 encoded values - replace with actual API keys
  twitter-api-key: ""
  twitter-api-secret: ""
  facebook-access-token: ""
  instagram-access-token: ""
  linkedin-client-id: ""
  linkedin-client-secret: ""
  tiktok-app-id: ""
  tiktok-app-secret: ""
---
apiVersion: v1
kind: Secret
metadata:
  name: integration-secrets
  labels:
    app: social-integration-service
type: Opaque
data:
  # Base64 encoded values - replace with actual API keys
  salesforce-api-key: ""
  hubspot-api-key: ""
  zendesk-api-token: ""
  intercom-access-token: ""
  google-analytics-key: ""
  mailchimp-api-key: ""
  slack-bot-token: ""
  notion-api-key: ""
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: social-integration-service-network-policy
  labels:
    app: social-integration-service
spec:
  podSelector:
    matchLabels:
      app: social-integration-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: dashboard-api
    - podSelector:
        matchLabels:
          app: frontend
    - podSelector:
        matchLabels:
          app: sentiment-service
    ports:
    - protocol: TCP
      port: 8089
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 443  # HTTPS for external APIs
    - protocol: TCP
      port: 80   # HTTP for external APIs
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: social-integration-service-hpa
  labels:
    app: social-integration-service
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: social-integration-service
  minReplicas: 2
  maxReplicas: 8
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
