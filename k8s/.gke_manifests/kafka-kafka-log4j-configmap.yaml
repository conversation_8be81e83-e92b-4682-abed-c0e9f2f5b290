apiVersion: v1
kind: ConfigMap
metadata:
  name: kafka-log4j-config
  namespace: default
data:
  log4j.properties: |
    # Log4j 1.x properties format for backward compatibility
    log4j.rootLogger=INFO, stdout, kafkaAppender

    # Stdout appender
    log4j.appender.stdout=org.apache.log4j.ConsoleAppender
    log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
    log4j.appender.stdout.layout.ConversionPattern=[%d] %p %m (%c)%n

    # Kafka appender
    log4j.appender.kafkaAppender=org.apache.log4j.RollingFileAppender
    log4j.appender.kafkaAppender.File=${kafka.logs.dir}/server.log
    log4j.appender.kafkaAppender.layout=org.apache.log4j.PatternLayout
    log4j.appender.kafkaAppender.layout.ConversionPattern=[%d] %p %m (%c)%n
    log4j.appender.kafkaAppender.MaxFileSize=50MB
    log4j.appender.kafkaAppender.MaxBackupIndex=10

    # State change logger
    log4j.logger.kafka.controller=INFO, controllerAppender
    log4j.additivity.kafka.controller=false
    log4j.appender.controllerAppender=org.apache.log4j.RollingFileAppender
    log4j.appender.controllerAppender.File=${kafka.logs.dir}/controller.log
    log4j.appender.controllerAppender.layout=org.apache.log4j.PatternLayout
    log4j.appender.controllerAppender.layout.ConversionPattern=[%d] %p %m (%c)%n
    log4j.appender.controllerAppender.MaxFileSize=50MB
    log4j.appender.controllerAppender.MaxBackupIndex=10

    # Request logger
    log4j.logger.kafka.request.logger=WARN, requestAppender
    log4j.additivity.kafka.request.logger=false
    log4j.appender.requestAppender=org.apache.log4j.RollingFileAppender
    log4j.appender.requestAppender.File=${kafka.logs.dir}/kafka-request.log
    log4j.appender.requestAppender.layout=org.apache.log4j.PatternLayout
    log4j.appender.requestAppender.layout.ConversionPattern=[%d] %p %m (%c)%n
    log4j.appender.requestAppender.MaxFileSize=50MB
    log4j.appender.requestAppender.MaxBackupIndex=10

    # Kafka logs
    log4j.logger.kafka=INFO
    log4j.logger.org.apache.kafka=INFO

