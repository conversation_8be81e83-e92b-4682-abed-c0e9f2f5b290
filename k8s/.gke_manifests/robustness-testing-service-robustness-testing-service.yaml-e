apiVersion: apps/v1
kind: Deployment
metadata:
  name: robustness-testing-service
  labels:
    app: robustness-testing-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: robustness-testing-service
  template:
    metadata:
      labels:
        app: robustness-testing-service
    spec:
      containers:
      - name: robustness-testing-service
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-robustness-testing-service:latest
        ports:
        - containerPort: 8086
        env:
        - name: PORT
          value: "8086"
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8086
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /health
            port: 8086
          initialDelaySeconds: 5
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: robustness-testing-service
  labels:
    app: robustness-testing-service
spec:
  selector:
    app: robustness-testing-service
  ports:
    - protocol: TCP
      port: 8086
      targetPort: 8086
  type: ClusterIP
