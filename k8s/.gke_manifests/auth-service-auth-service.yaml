apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  labels:
    app: auth-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
      - name: auth-service
        image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-auth-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: PORT
          value: "8080"
        - name: CLICKHOUSE_HOST
          value: "clickhouse"
        - name: CLICKHOUSE_PORT
          value: "9000"
        - name: CLICKHOUSE_DATABASE
          value: "default"
        - name: CLICKHOUSE_USERNAME
          value: "test"
        - name: CLICKHOUSE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: clickhouse-secret
              key: password
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: google-oauth-secret
              key: client-id
        - name: GOO<PERSON>LE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: google-oauth-secret
              key: client-secret
        - name: GOOGLE_REDIRECT_URL
          value: "https://scale-llm.com/auth/callback"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        - name: ADMIN_EMAILS
          valueFrom:
            configMapKeyRef:
              name: auth-config
              key: admin-emails
        - name: FRONTEND_URL
          value: "https://scale-llm.com"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: auth-service
  labels:
    app: auth-service
spec:
  selector:
    app: auth-service
  ports:
  - name: http
    port: 80
    targetPort: 8080
    protocol: TCP
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: auth-config
data:
  admin-emails: "<EMAIL>,<EMAIL>"
# Note: Secrets (google-oauth-secret, jwt-secret, clickhouse-secret) are created by the deployment script
# to ensure they contain the correct values and are not overwritten by empty YAML definitions
