{"evaluation_test_cases": {"goal_classification": [{"id": "gc_001", "description": "Analyze customer feedback data using advanced LLM analysis techniques", "expected_category": "data_analysis", "confidence": 0.95, "tags": ["data", "analysis", "feedback"]}, {"id": "gc_002", "description": "Make API calls to external services for data integration", "expected_category": "automation", "confidence": 0.9, "tags": ["api", "integration", "automation"]}, {"id": "gc_003", "description": "Generate comprehensive market research report", "expected_category": "research", "confidence": 0.85, "tags": ["research", "market", "report"]}, {"id": "gc_004", "description": "Create automated customer support chatbot", "expected_category": "customer_service", "confidence": 0.9, "tags": ["customer", "support", "chatbot"]}, {"id": "gc_005", "description": "Optimize database query performance for faster response times", "expected_category": "optimization", "confidence": 0.85, "tags": ["optimization", "database", "performance"]}, {"id": "gc_006", "description": "Monitor system health and alert on anomalies", "expected_category": "monitoring", "confidence": 0.9, "tags": ["monitoring", "health", "alerts"]}, {"id": "gc_007", "description": "Generate creative marketing content for social media campaigns", "expected_category": "content_creation", "confidence": 0.85, "tags": ["content", "marketing", "creative"]}, {"id": "gc_008", "description": "Provide data-driven recommendations for business strategy decisions", "expected_category": "decision_support", "confidence": 0.8, "tags": ["decision", "strategy", "recommendations"]}], "constraint_extraction": [{"id": "ce_001", "description": "Analyze customer feedback data with 95% accuracy within 2 hours", "expected_constraints": [{"type": "quality", "description": "Analysis accuracy requirement", "limit": ">=0.95", "operator": ">=", "severity": "hard"}, {"type": "time", "description": "Processing time constraint", "limit": "<=2 hours", "operator": "<=", "severity": "medium"}], "confidence": 0.9}, {"id": "ce_002", "description": "Process API calls under $0.10 per request with HTTPS security", "expected_constraints": [{"type": "cost", "description": "Cost per request limit", "limit": "<=0.10", "operator": "<=", "severity": "medium"}, {"type": "compliance", "description": "Security requirement", "limit": "HTTPS required", "operator": "==", "severity": "hard"}], "confidence": 0.95}, {"id": "ce_003", "description": "Generate reports with 99.9% uptime and GDPR compliance", "expected_constraints": [{"type": "resource", "description": "System uptime requirement", "limit": ">=99.9%", "operator": ">=", "severity": "hard"}, {"type": "compliance", "description": "Data privacy compliance", "limit": "GDPR compliant", "operator": "==", "severity": "hard"}], "confidence": 0.85}, {"id": "ce_004", "description": "Optimize performance while maintaining quality above 85% and cost below $1000", "expected_constraints": [{"type": "quality", "description": "Quality threshold", "limit": ">=0.85", "operator": ">=", "severity": "medium"}, {"type": "cost", "description": "Budget constraint", "limit": "<=1000", "operator": "<=", "severity": "hard"}], "confidence": 0.8}]}, "evaluation_thresholds": {"goal_classification": {"pass_threshold": 0.8, "excellent_threshold": 0.95, "confidence_threshold": 0.85}, "constraint_extraction": {"pass_threshold": 0.7, "excellent_threshold": 0.9, "confidence_threshold": 0.8, "min_constraints": 1, "max_constraints": 6}}, "evaluation_models": ["gemini-2.5-flash-preview-05-20", "gpt-4", "gpt-3.5-turbo", "claude-3-haiku"], "test_execution_config": {"timeout_seconds": 30, "retry_attempts": 2, "parallel_execution": true, "max_concurrent_tests": 5}, "reporting_config": {"generate_detailed_report": true, "save_to_file": true, "export_to_git": true, "notify_on_failure": true}}