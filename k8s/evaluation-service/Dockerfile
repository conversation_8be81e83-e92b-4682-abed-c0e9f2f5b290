# k8s/evaluation-service/Dockerfile
FROM golang:1.23-alpine AS builder

WORKDIR /app

COPY go.mod ./
COPY go.sum ./
RUN go mod download

COPY . .

RUN go build -o /app/evaluation-service .

FROM alpine:latest

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates

WORKDIR /app

# Copy the binary
COPY --from=builder /app/evaluation-service .

# Copy the evaluation configuration file
COPY evaluation_config.json .

# Copy test suite files (optional, for container-based testing)
COPY evaluation_test_suite.py .
COPY simple_evaluation_test.py .
COPY evaluation_comparison_demo.py .

EXPOSE 8087

CMD ["./evaluation-service"]
