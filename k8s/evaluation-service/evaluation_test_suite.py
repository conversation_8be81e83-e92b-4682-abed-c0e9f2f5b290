#!/usr/bin/env python3
"""
Enhanced Evaluation Test Suite
Leverages existing AI Operations Hub services for comprehensive LLM evaluation testing.
"""

import json
import requests
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import uuid

class EvaluationTestSuite:
    """
    Comprehensive evaluation test suite that leverages existing services:
    - PromptOps for test case management
    - Planning Service for real test data
    - Integration Service for GitOps test management
    - Policy Manager for test configuration
    """
    
    def __init__(self, base_url: str = "https://scale-llm.com"):
        self.base_url = base_url
        self.test_results = []
        self.test_session_id = str(uuid.uuid4())
        
    def run_comprehensive_evaluation_tests(self):
        """Run all evaluation tests leveraging existing services"""
        print("🚀 Starting Comprehensive Evaluation Test Suite")
        print(f"Session ID: {self.test_session_id}")
        print("=" * 80)
        
        # 1. Test using PromptOps managed test cases
        self.test_promptops_managed_evaluations()
        
        # 2. Test using Planning Service real data
        self.test_planning_service_evaluations()
        
        # 3. Test using Policy Manager configurations
        self.test_policy_driven_evaluations()
        
        # 4. Test GitOps integration for test management
        self.test_gitops_evaluation_management()
        
        # 5. Generate comprehensive test report
        self.generate_test_report()
        
    def test_promptops_managed_evaluations(self):
        """Test evaluation using PromptOps managed test prompts"""
        print("\n📝 Testing PromptOps Managed Evaluations")
        print("-" * 50)
        
        # Create evaluation test prompts in PromptOps
        test_prompts = self.create_evaluation_test_prompts()
        
        for prompt_data in test_prompts:
            try:
                # Execute prompt through PromptOps
                result = self.execute_promptops_evaluation(prompt_data)
                self.test_results.append({
                    "test_type": "promptops_managed",
                    "prompt_id": prompt_data["id"],
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                })
                print(f"✅ {prompt_data['name']}: Score {result.get('score', 0):.3f}")
            except Exception as e:
                print(f"❌ {prompt_data['name']}: {str(e)}")
                
    def test_planning_service_evaluations(self):
        """Test evaluation using real Planning Service data"""
        print("\n🎯 Testing Planning Service Real Data Evaluations")
        print("-" * 50)
        
        # Generate real planning tasks for evaluation
        planning_tests = [
            {
                "description": "Analyze customer satisfaction data from Q4 2024",
                "expected_category": "data_analysis",
                "test_constraints": True
            },
            {
                "description": "Create automated reporting system for sales metrics",
                "expected_category": "automation", 
                "test_constraints": True
            },
            {
                "description": "Research market trends in AI technology adoption",
                "expected_category": "research",
                "test_constraints": False
            }
        ]
        
        for test_case in planning_tests:
            try:
                # Create goal through Planning Service
                goal_result = self.create_planning_goal(test_case)
                
                # Evaluate the LLM responses from planning
                eval_result = self.evaluate_planning_responses(goal_result, test_case)
                
                self.test_results.append({
                    "test_type": "planning_service",
                    "goal_id": goal_result.get("id"),
                    "result": eval_result,
                    "timestamp": datetime.now().isoformat()
                })
                print(f"✅ Planning Goal: Score {eval_result.get('score', 0):.3f}")
            except Exception as e:
                print(f"❌ Planning Goal: {str(e)}")
                
    def test_policy_driven_evaluations(self):
        """Test evaluation using Policy Manager configurations"""
        print("\n⚖️ Testing Policy-Driven Evaluations")
        print("-" * 50)
        
        # Create evaluation policies
        eval_policies = self.create_evaluation_policies()
        
        for policy in eval_policies:
            try:
                # Test evaluation with policy constraints
                result = self.test_policy_evaluation(policy)
                self.test_results.append({
                    "test_type": "policy_driven",
                    "policy_id": policy["id"],
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                })
                print(f"✅ Policy {policy['name']}: {result.get('status', 'unknown')}")
            except Exception as e:
                print(f"❌ Policy {policy['name']}: {str(e)}")
                
    def test_gitops_evaluation_management(self):
        """Test GitOps integration for evaluation test management"""
        print("\n🔄 Testing GitOps Evaluation Management")
        print("-" * 50)
        
        try:
            # Export evaluation test cases to Git
            export_result = self.export_evaluation_tests_to_git()
            print(f"✅ Export to Git: {export_result.get('status', 'unknown')}")
            
            # Import evaluation test cases from Git
            import_result = self.import_evaluation_tests_from_git()
            print(f"✅ Import from Git: {import_result.get('status', 'unknown')}")
            
            # Sync evaluation test repository
            sync_result = self.sync_evaluation_git_repo()
            print(f"✅ Git Sync: {sync_result.get('status', 'unknown')}")
            
        except Exception as e:
            print(f"❌ GitOps Integration: {str(e)}")
            
    def create_evaluation_test_prompts(self) -> List[Dict]:
        """Create test prompts for evaluation (simplified for current deployment)"""
        test_prompts = [
            {
                "id": f"eval-goal-classification-{self.test_session_id}",
                "name": "Goal Classification Test",
                "content": """Classify the following goal into one of these categories:
- data_analysis: Goals involving data processing, analysis, or insights
- content_creation: Goals involving generating text, images, or other content
- automation: Goals involving automating processes or workflows
- research: Goals involving information gathering or research
- decision_support: Goals involving analysis to support decision making
- customer_service: Goals involving customer interaction or support
- monitoring: Goals involving tracking or monitoring systems/metrics
- optimization: Goals involving improving efficiency or performance

Goal: {{goal_description}}

Return only the category name, no additional text.""",
                "variables": [{"name": "goal_description", "type": "string", "required": True}],
                "expected_response": "data_analysis",
                "test_data": {"goal_description": "Analyze customer feedback data using advanced LLM analysis techniques"}
            },
            {
                "id": f"eval-constraint-extraction-{self.test_session_id}",
                "name": "Constraint Extraction Test", 
                "content": """Analyze the following goal description and extract any constraints or limitations.
Return the constraints as a JSON array of objects with the following structure:
{
  "type": "Type of constraint ('cost', 'time', 'quality', 'resource', 'compliance')",
  "description": "Clear description of the constraint",
  "limit": "The limit value (number, string, or boolean)",
  "operator": "Comparison operator ('<=', '>=', '==', '!=', 'contains')",
  "severity": "Constraint severity ('hard', 'soft', 'preference')"
}

Goal Description: {{goal_description}}

Return only the JSON array, no additional text.""",
                "variables": [{"name": "goal_description", "type": "string", "required": True}],
                "expected_response": '[{"type":"quality","description":"Analysis accuracy","limit":">=0.85","operator":">=","severity":"hard"}]',
                "test_data": {"goal_description": "Analyze customer feedback data using advanced LLM analysis techniques"}
            }
        ]
        
        # Create prompts in PromptOps
        for prompt in test_prompts:
            try:
                response = requests.post(
                    f"{self.base_url}/api/prompts",
                    json=prompt,
                    headers={"Content-Type": "application/json"},
                    timeout=30
                )
                if response.status_code in [200, 201]:
                    print(f"✅ Created test prompt: {prompt['name']}")
                else:
                    print(f"⚠️ Failed to create prompt {prompt['name']}: {response.status_code}")
            except Exception as e:
                print(f"⚠️ Error creating prompt {prompt['name']}: {str(e)}")
                
        return test_prompts
        
    def execute_promptops_evaluation(self, prompt_data: Dict) -> Dict:
        """Execute a prompt through PromptOps and evaluate the response"""
        # Execute prompt with test data
        execution_payload = {
            "prompt_id": prompt_data["id"],
            "variables": prompt_data["test_data"],
            "model_id": "gemini-2.5-flash-preview-05-20"
        }
        
        response = requests.post(
            f"{self.base_url}/api/prompts/execute",
            json=execution_payload,
            timeout=30
        )
        
        if response.status_code != 200:
            raise Exception(f"Prompt execution failed: {response.status_code}")
            
        execution_result = response.json()
        
        # Send for evaluation
        eval_payload = {
            "request_id": str(uuid.uuid4()),
            "prompt": prompt_data["content"].replace("{{goal_description}}", prompt_data["test_data"]["goal_description"]),
            "llm_response": execution_result.get("response", ""),
            "model_id": execution_payload["model_id"],
            "expected_response": prompt_data.get("expected_response", ""),
            "task_type": "goal_classification" if "classification" in prompt_data["name"].lower() else "constraint_extraction"
        }
        
        eval_response = requests.post(
            f"{self.base_url}/api/evaluation/evaluate",
            json=eval_payload,
            timeout=30
        )
        
        if eval_response.status_code not in [200, 201]:
            raise Exception(f"Evaluation failed: {eval_response.status_code}")
            
        return eval_response.json()

    def create_planning_goal(self, test_case: Dict) -> Dict:
        """Create a goal through Planning Service for evaluation testing"""
        goal_payload = {
            "description": test_case["description"],
            "priority": 7,
            "success_criteria": [{
                "description": "Successful goal processing",
                "metric": "completion_rate",
                "target": 1.0,
                "operator": ">=",
                "weight": 1.0,
                "required": True
            }]
        }

        response = requests.post(
            f"{self.base_url}/v1/goals",
            json=goal_payload,
            headers={"Content-Type": "application/json", "X-User-ID": f"eval-test-{self.test_session_id}"},
            timeout=30
        )

        if response.status_code not in [200, 201]:
            raise Exception(f"Goal creation failed: {response.status_code}")

        return response.json()

    def evaluate_planning_responses(self, goal_result: Dict, test_case: Dict) -> Dict:
        """Evaluate LLM responses from planning service operations"""
        goal_id = goal_result.get("id")

        # Wait a moment for processing
        time.sleep(2)

        # Get goal details to see LLM interactions
        response = requests.get(
            f"{self.base_url}/v1/goals/{goal_id}",
            headers={"X-User-ID": f"eval-test-{self.test_session_id}"},
            timeout=30
        )

        if response.status_code != 200:
            raise Exception(f"Goal retrieval failed: {response.status_code}")

        goal_details = response.json()

        # Extract classification from metadata
        classification = goal_details.get("metadata", {}).get("classification", "")

        # Evaluate classification accuracy
        expected_category = test_case["expected_category"]
        score = 0.95 if classification == expected_category else 0.30

        return {
            "score": score,
            "expected": expected_category,
            "actual": classification,
            "passed": score >= 0.70,
            "goal_id": goal_id
        }

    def create_evaluation_policies(self) -> List[Dict]:
        """Create evaluation policies in Policy Manager"""
        policies = [
            {
                "id": f"eval-accuracy-policy-{self.test_session_id}",
                "name": "Evaluation Accuracy Policy",
                "description": "Minimum accuracy requirements for LLM evaluations",
                "criteria": json.dumps({
                    "evaluation_type": "goal_classification",
                    "min_score": 0.80
                }),
                "action": "EVALUATE",
                "metadata": {"test_session": self.test_session_id}
            },
            {
                "id": f"eval-quality-policy-{self.test_session_id}",
                "name": "Evaluation Quality Policy",
                "description": "Quality thresholds for constraint extraction",
                "criteria": json.dumps({
                    "evaluation_type": "constraint_extraction",
                    "min_score": 0.70,
                    "required_fields": ["type", "description", "limit"]
                }),
                "action": "EVALUATE",
                "metadata": {"test_session": self.test_session_id}
            }
        ]

        # Create policies
        for policy in policies:
            try:
                response = requests.post(
                    f"{self.base_url}/api/policies",
                    json=policy,
                    headers={"Content-Type": "application/json"},
                    timeout=30
                )
                if response.status_code in [200, 201]:
                    print(f"✅ Created evaluation policy: {policy['name']}")
                else:
                    print(f"⚠️ Failed to create policy {policy['name']}: {response.status_code}")
            except Exception as e:
                print(f"⚠️ Error creating policy {policy['name']}: {str(e)}")

        return policies

    def test_policy_evaluation(self, policy: Dict) -> Dict:
        """Test evaluation against a specific policy"""
        # Get policy criteria
        criteria = json.loads(policy["criteria"])

        # Create test evaluation request
        test_payload = {
            "request_id": str(uuid.uuid4()),
            "prompt": "Test prompt for policy evaluation",
            "llm_response": "data_analysis",
            "model_id": "gemini-2.5-flash-preview-05-20",
            "evaluation_type": criteria.get("evaluation_type", "comprehensive"),
            "expected_response": "data_analysis"
        }

        # Send for evaluation
        response = requests.post(
            f"{self.base_url}/api/evaluation/evaluate",
            json=test_payload,
            timeout=30
        )

        if response.status_code not in [200, 201]:
            return {"status": "failed", "error": f"Evaluation failed: {response.status_code}"}

        eval_result = response.json()
        score = eval_result.get("score", 0)
        min_score = criteria.get("min_score", 0.70)

        return {
            "status": "passed" if score >= min_score else "failed",
            "score": score,
            "min_score": min_score,
            "policy_id": policy["id"]
        }

    def export_evaluation_tests_to_git(self) -> Dict:
        """Export evaluation test cases to Git repository"""
        export_payload = {
            "source": "evaluation_tests",
            "test_session_id": self.test_session_id,
            "tests": [
                {
                    "name": "goal_classification_tests",
                    "type": "evaluation",
                    "test_cases": self.get_goal_classification_test_cases()
                },
                {
                    "name": "constraint_extraction_tests",
                    "type": "evaluation",
                    "test_cases": self.get_constraint_extraction_test_cases()
                }
            ]
        }

        response = requests.post(
            f"{self.base_url}/api/integration/promptops/export",
            json=export_payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )

        if response.status_code in [200, 202]:
            return {"status": "exported", "session_id": self.test_session_id}
        else:
            return {"status": "failed", "error": f"Export failed: {response.status_code}"}

    def import_evaluation_tests_from_git(self) -> Dict:
        """Import evaluation test cases from Git repository"""
        response = requests.post(
            f"{self.base_url}/api/integration/promptops/import",
            headers={"Content-Type": "application/json"},
            timeout=30
        )

        if response.status_code in [200, 202]:
            return {"status": "imported"}
        else:
            return {"status": "failed", "error": f"Import failed: {response.status_code}"}

    def sync_evaluation_git_repo(self) -> Dict:
        """Sync evaluation test repository with Git"""
        response = requests.post(
            f"{self.base_url}/api/integration/gitops/sync",
            timeout=30
        )

        if response.status_code in [200, 202]:
            return {"status": "synced"}
        else:
            return {"status": "failed", "error": f"Sync failed: {response.status_code}"}

    def get_goal_classification_test_cases(self) -> List[Dict]:
        """Get goal classification test cases for GitOps export"""
        return [
            {
                "description": "Analyze customer feedback data using advanced LLM analysis techniques",
                "expected_category": "data_analysis",
                "test_id": "gc_001"
            },
            {
                "description": "Make API calls to external services for data integration",
                "expected_category": "automation",
                "test_id": "gc_002"
            },
            {
                "description": "Generate comprehensive market research report",
                "expected_category": "research",
                "test_id": "gc_003"
            },
            {
                "description": "Create automated customer support chatbot",
                "expected_category": "customer_service",
                "test_id": "gc_004"
            }
        ]

    def get_constraint_extraction_test_cases(self) -> List[Dict]:
        """Get constraint extraction test cases for GitOps export"""
        return [
            {
                "description": "Analyze customer feedback data with 95% accuracy within 2 hours",
                "expected_constraints": [
                    {"type": "quality", "limit": ">=0.95", "severity": "hard"},
                    {"type": "time", "limit": "<=2 hours", "severity": "medium"}
                ],
                "test_id": "ce_001"
            },
            {
                "description": "Process API calls under $0.10 per request with HTTPS security",
                "expected_constraints": [
                    {"type": "cost", "limit": "<=0.10", "severity": "medium"},
                    {"type": "compliance", "limit": "HTTPS required", "severity": "hard"}
                ],
                "test_id": "ce_002"
            }
        ]

    def generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n📊 Comprehensive Evaluation Test Report")
        print("=" * 80)

        # Calculate summary statistics
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results
                          if result.get("result", {}).get("passed", False) or
                             result.get("result", {}).get("status") == "passed")

        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        print(f"Session ID: {self.test_session_id}")
        print(f"Total Tests: {total_tests}")
        print(f"Passed Tests: {passed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"Test Timestamp: {datetime.now().isoformat()}")

        # Group results by test type
        test_types = {}
        for result in self.test_results:
            test_type = result["test_type"]
            if test_type not in test_types:
                test_types[test_type] = []
            test_types[test_type].append(result)

        # Print detailed results by type
        for test_type, results in test_types.items():
            print(f"\n{test_type.replace('_', ' ').title()} Results:")
            print("-" * 40)

            for result in results:
                test_result = result.get("result", {})
                score = test_result.get("score", 0)
                status = test_result.get("status", "unknown")
                passed = test_result.get("passed", False)

                status_icon = "✅" if (passed or status == "passed") else "❌"
                print(f"{status_icon} Score: {score:.3f} | Status: {status}")

        # Save detailed report to file
        report_data = {
            "session_id": self.test_session_id,
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "success_rate": success_rate
            },
            "detailed_results": self.test_results
        }

        try:
            with open(f"evaluation_test_report_{self.test_session_id}.json", "w") as f:
                json.dump(report_data, f, indent=2)
            print(f"\n📄 Detailed report saved: evaluation_test_report_{self.test_session_id}.json")
        except Exception as e:
            print(f"⚠️ Failed to save report: {str(e)}")


if __name__ == "__main__":
    # Run the comprehensive evaluation test suite
    test_suite = EvaluationTestSuite()
    test_suite.run_comprehensive_evaluation_tests()
