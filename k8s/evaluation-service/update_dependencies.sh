#!/bin/bash
# Update Go dependencies for enhanced evaluation service

echo "🔧 Updating Go dependencies for enhanced evaluation service..."

# Clean module cache
go clean -modcache

# Download dependencies
go mod download

# Tidy up dependencies
go mod tidy

# Verify dependencies
go mod verify

echo "✅ Dependencies updated successfully!"

# Show current dependencies
echo "📦 Current dependencies:"
go list -m all

echo ""
echo "🚀 Ready for deployment!"
echo "Run: docker build -t evaluation-service ."
