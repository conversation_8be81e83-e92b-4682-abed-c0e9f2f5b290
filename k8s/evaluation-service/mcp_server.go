package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gorilla/websocket"
)

// MCPEvaluationServer provides MCP server capabilities for the Evaluation Service
type MCPEvaluationServer struct {
	upgrader websocket.Upgrader
}

// MCPRequest represents an incoming MCP JSON-RPC request
type MCPRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      interface{} `json:"id"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params,omitempty"`
}

// MCPResponse represents an outgoing MCP JSON-RPC response
type MCPResponse struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      interface{} `json:"id"`
	Result  interface{} `json:"result,omitempty"`
	Error   *MCPError   `json:"error,omitempty"`
}

// MCPError represents an MCP error
type MCPError struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// MCPTool represents an MCP tool definition
type MCPTool struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	InputSchema interface{} `json:"inputSchema"`
}

// MCPResource represents an MCP resource definition
type MCPResource struct {
	URI         string `json:"uri"`
	Name        string `json:"name"`
	Description string `json:"description"`
	MimeType    string `json:"mimeType,omitempty"`
}

// NewMCPEvaluationServer creates a new MCP server instance for Evaluation Service
func NewMCPEvaluationServer() *MCPEvaluationServer {
	return &MCPEvaluationServer{
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// Allow connections from any origin for now
				// In production, implement proper origin checking
				return true
			},
		},
	}
}

// HandleMCPConnection handles MCP WebSocket connections
func (s *MCPEvaluationServer) HandleMCPConnection(w http.ResponseWriter, r *http.Request) {
	conn, err := s.upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("Evaluation MCP Server: Failed to upgrade to WebSocket: %v", err)
		return
	}
	defer conn.Close()

	log.Printf("Evaluation MCP Server: New client connected")

	// Handle MCP protocol initialization
	if err := s.handleInitialization(conn); err != nil {
		log.Printf("Evaluation MCP Server: Initialization failed: %v", err)
		return
	}

	// Handle incoming messages
	for {
		var req MCPRequest
		if err := conn.ReadJSON(&req); err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("Evaluation MCP Server: WebSocket error: %v", err)
			}
			break
		}

		response := s.handleMCPRequest(req)
		if err := conn.WriteJSON(response); err != nil {
			log.Printf("Evaluation MCP Server: Error sending response: %v", err)
			break
		}
	}

	log.Printf("Evaluation MCP Server: Client disconnected")
}

// handleInitialization handles the MCP initialization handshake
func (s *MCPEvaluationServer) handleInitialization(conn *websocket.Conn) error {
	// Send server capabilities
	capabilities := map[string]interface{}{
		"tools": map[string]interface{}{
			"listChanged": true,
		},
		"resources": map[string]interface{}{
			"subscribe":   true,
			"listChanged": true,
		},
	}

	initResponse := MCPResponse{
		JSONRPC: "2.0",
		ID:      "init",
		Result: map[string]interface{}{
			"protocolVersion": "2025-06-18",
			"capabilities":    capabilities,
			"serverInfo": map[string]interface{}{
				"name":    "evaluation-service-mcp-server",
				"version": "1.0.0",
			},
		},
	}

	return conn.WriteJSON(initResponse)
}

// handleMCPRequest processes incoming MCP requests
func (s *MCPEvaluationServer) handleMCPRequest(req MCPRequest) MCPResponse {
	switch req.Method {
	case "tools/list":
		return s.handleToolsList(req)
	case "tools/call":
		return s.handleToolCall(req)
	case "resources/list":
		return s.handleResourcesList(req)
	case "resources/read":
		return s.handleResourceRead(req)
	default:
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32601,
				Message: "Method not found",
			},
		}
	}
}

// handleToolsList returns the list of available tools
func (s *MCPEvaluationServer) handleToolsList(req MCPRequest) MCPResponse {
	tools := []MCPTool{
		{
			Name:        "evaluate_llm_response",
			Description: "Perform comprehensive LLM response evaluation with multiple quality dimensions",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"prompt": map[string]interface{}{
						"type":        "string",
						"description": "The original prompt sent to the LLM",
					},
					"response": map[string]interface{}{
						"type":        "string",
						"description": "The LLM's response to evaluate",
					},
					"model_id": map[string]interface{}{
						"type":        "string",
						"description": "ID of the model that generated the response",
					},
					"task_type": map[string]interface{}{
						"type":        "string",
						"description": "Type of task (factual_query, summarization, code_generation, etc.)",
					},
					"api_type": map[string]interface{}{
						"type":        "string",
						"description": "API type used (chat_completions, embeddings, etc.)",
					},
				},
				"required": []string{"prompt", "response"},
			},
		},
		{
			Name:        "run_quality_assessment",
			Description: "Run multi-dimensional quality analysis including coherence, relevance, and completeness",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"prompt": map[string]interface{}{
						"type":        "string",
						"description": "The original prompt",
					},
					"response": map[string]interface{}{
						"type":        "string",
						"description": "The response to assess",
					},
					"task_type": map[string]interface{}{
						"type":        "string",
						"description": "Task type for context-specific assessment",
					},
				},
				"required": []string{"prompt", "response"},
			},
		},
		{
			Name:        "perform_bias_detection",
			Description: "Automated bias detection in LLM responses across multiple dimensions",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"prompt": map[string]interface{}{
						"type":        "string",
						"description": "The original prompt",
					},
					"response": map[string]interface{}{
						"type":        "string",
						"description": "The response to analyze for bias",
					},
				},
				"required": []string{"prompt", "response"},
			},
		},
		{
			Name:        "generate_evaluation_report",
			Description: "Generate detailed evaluation report with recommendations and insights",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"evaluation_id": map[string]interface{}{
						"type":        "string",
						"description": "ID of a previous evaluation to generate report for",
					},
					"model_id": map[string]interface{}{
						"type":        "string",
						"description": "Model ID to generate report for (optional)",
					},
					"time_range": map[string]interface{}{
						"type":        "string",
						"description": "Time range for report (1h, 24h, 7d)",
						"default":     "24h",
					},
				},
			},
		},
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      req.ID,
		Result: map[string]interface{}{
			"tools": tools,
		},
	}
}

// handleToolCall executes a tool call
func (s *MCPEvaluationServer) handleToolCall(req MCPRequest) MCPResponse {
	params, ok := req.Params.(map[string]interface{})
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "Invalid params",
			},
		}
	}

	toolName, ok := params["name"].(string)
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "Tool name required",
			},
		}
	}

	arguments, _ := params["arguments"].(map[string]interface{})

	switch toolName {
	case "evaluate_llm_response":
		return s.executeEvaluateLLMResponse(req.ID, arguments)
	case "run_quality_assessment":
		return s.executeRunQualityAssessment(req.ID, arguments)
	case "perform_bias_detection":
		return s.executePerformBiasDetection(req.ID, arguments)
	case "generate_evaluation_report":
		return s.executeGenerateEvaluationReport(req.ID, arguments)
	default:
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32601,
				Message: "Tool not found",
			},
		}
	}
}

// Helper function to get string from map
func getString(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}

// executeEvaluateLLMResponse implements the evaluate_llm_response tool
func (s *MCPEvaluationServer) executeEvaluateLLMResponse(id interface{}, args map[string]interface{}) MCPResponse {
	prompt := getString(args, "prompt")
	response := getString(args, "response")

	if prompt == "" || response == "" {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32602,
				Message: "Both prompt and response are required",
			},
		}
	}

	// Create evaluation request
	evalReq := EvaluationRequest{
		RequestID:   fmt.Sprintf("mcp-%d", time.Now().Unix()),
		Prompt:      prompt,
		LLMResponse: response,
		ModelID:     getString(args, "model_id"),
		TaskType:    getString(args, "task_type"),
		APIType:     getString(args, "api_type"),
	}

	// Set defaults
	if evalReq.ModelID == "" {
		evalReq.ModelID = "unknown"
	}
	if evalReq.TaskType == "" {
		evalReq.TaskType = "general"
	}

	// Perform evaluation using existing logic
	result := performEvaluation(evalReq)

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": fmt.Sprintf("Evaluation Results:\nScore: %.2f/1.0\nPassed: %t\nType: %s\nFeedback: %s\nConfidence: %.2f",
						result.Score, result.Passed, result.EvaluationType, result.Feedback, result.Confidence),
				},
			},
			"isError": false,
		},
	}
}

// executeRunQualityAssessment implements the run_quality_assessment tool
func (s *MCPEvaluationServer) executeRunQualityAssessment(id interface{}, args map[string]interface{}) MCPResponse {
	prompt := getString(args, "prompt")
	response := getString(args, "response")
	taskType := getString(args, "task_type")

	if prompt == "" || response == "" {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32602,
				Message: "Both prompt and response are required",
			},
		}
	}

	// Perform quality evaluation using existing logic
	qualityResult := performQualityEvaluation(prompt, response, taskType)

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": fmt.Sprintf("Quality Assessment:\nOverall Quality: %.2f\nCoherence: %.2f\nRelevance: %.2f\nCompleteness: %.2f\nAccuracy: %.2f\nFluency: %.2f\nImprovement Areas: %v",
						qualityResult.OverallQuality, qualityResult.Coherence, qualityResult.Relevance,
						qualityResult.Completeness, qualityResult.Accuracy, qualityResult.Fluency,
						qualityResult.ImprovementAreas),
				},
			},
			"isError": false,
		},
	}
}

// executePerformBiasDetection implements the perform_bias_detection tool
func (s *MCPEvaluationServer) executePerformBiasDetection(id interface{}, args map[string]interface{}) MCPResponse {
	prompt := getString(args, "prompt")
	response := getString(args, "response")

	if prompt == "" || response == "" {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      id,
			Error: &MCPError{
				Code:    -32602,
				Message: "Both prompt and response are required",
			},
		}
	}

	// Perform bias detection using existing logic
	biasResult := performBiasDetection(prompt, response)

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": fmt.Sprintf("Bias Detection Results:\nOverall Bias Score: %.2f\nDetected Biases: %v\nBias Categories: %v\nConfidence: %.2f\nMitigation Suggestions: %v",
						biasResult.OverallBiasScore, biasResult.DetectedBiases, biasResult.BiasCategories,
						biasResult.Confidence, biasResult.MitigationSuggestions),
				},
			},
			"isError": false,
		},
	}
}

// executeGenerateEvaluationReport implements the generate_evaluation_report tool
func (s *MCPEvaluationServer) executeGenerateEvaluationReport(id interface{}, args map[string]interface{}) MCPResponse {
	evaluationID := getString(args, "evaluation_id")
	modelID := getString(args, "model_id")
	timeRange := getString(args, "time_range")

	if timeRange == "" {
		timeRange = "24h"
	}

	// Generate a comprehensive report
	report := fmt.Sprintf(`# Evaluation Report

## Report Details
- Generated: %s
- Time Range: %s
- Model ID: %s
- Evaluation ID: %s

## Summary
This report provides a comprehensive analysis of LLM evaluation results.

## Key Metrics
- Total Evaluations: Available in analytics
- Average Quality Score: Available in analytics
- Bias Detection Rate: Available in analytics
- Safety Compliance: Available in analytics

## Recommendations
1. Monitor bias detection results regularly
2. Implement quality improvement measures
3. Review safety compliance metrics
4. Consider model fine-tuning based on evaluation feedback

## Next Steps
- Use the evaluation tools to assess specific responses
- Monitor trends using the analytics endpoints
- Implement feedback loops for continuous improvement
`, time.Now().Format(time.RFC3339), timeRange, modelID, evaluationID)

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": report,
				},
			},
			"isError": false,
		},
	}
}

// handleResourcesList returns the list of available resources
func (s *MCPEvaluationServer) handleResourcesList(req MCPRequest) MCPResponse {
	resources := []MCPResource{
		{
			URI:         "evaluation://metrics",
			Name:        "Evaluation Metrics",
			Description: "Available evaluation criteria and scoring methods",
			MimeType:    "application/json",
		},
		{
			URI:         "evaluation://benchmarks",
			Name:        "Quality Benchmarks",
			Description: "Performance benchmarks for different models",
			MimeType:    "application/json",
		},
		{
			URI:         "evaluation://history",
			Name:        "Evaluation History",
			Description: "Historical evaluation results and trends",
			MimeType:    "application/json",
		},
	}

	return MCPResponse{
		JSONRPC: "2.0",
		ID:      req.ID,
		Result: map[string]interface{}{
			"resources": resources,
		},
	}
}

// handleResourceRead reads a specific resource
func (s *MCPEvaluationServer) handleResourceRead(req MCPRequest) MCPResponse {
	params, ok := req.Params.(map[string]interface{})
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "Invalid params",
			},
		}
	}

	uri, ok := params["uri"].(string)
	if !ok {
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32602,
				Message: "URI is required",
			},
		}
	}

	switch uri {
	case "evaluation://metrics":
		return s.readEvaluationMetrics(req.ID)
	case "evaluation://benchmarks":
		return s.readQualityBenchmarks(req.ID)
	case "evaluation://history":
		return s.readEvaluationHistory(req.ID)
	default:
		return MCPResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &MCPError{
				Code:    -32601,
				Message: "Resource not found",
			},
		}
	}
}

// Resource reading functions
func (s *MCPEvaluationServer) readEvaluationMetrics(id interface{}) MCPResponse {
	metrics := map[string]interface{}{
		"quality_dimensions": []string{
			"coherence", "relevance", "completeness", "accuracy", "fluency",
		},
		"bias_dimensions": []string{
			"gender", "racial", "age", "religious", "socioeconomic",
		},
		"safety_dimensions": []string{
			"toxicity", "hate_speech", "violence", "self_harm",
		},
		"evaluation_types": []string{
			"comprehensive", "accuracy", "conciseness_accuracy", "code_quality",
			"goal_classification", "constraint_extraction",
		},
		"api_types": []string{
			"chat_completions", "embeddings", "image_generation", "audio_speech",
		},
		"scoring": map[string]interface{}{
			"range":            "0.0 to 1.0",
			"threshold":        0.7,
			"confidence_range": "0.0 to 1.0",
		},
	}

	metricsJSON, _ := json.MarshalIndent(metrics, "", "  ")
	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"contents": []map[string]interface{}{
				{
					"uri":      "evaluation://metrics",
					"mimeType": "application/json",
					"text":     string(metricsJSON),
				},
			},
		},
	}
}

func (s *MCPEvaluationServer) readQualityBenchmarks(id interface{}) MCPResponse {
	benchmarks := map[string]interface{}{
		"model_benchmarks": map[string]interface{}{
			"gpt-4": map[string]interface{}{
				"average_quality": 0.85,
				"bias_score":      0.15,
				"safety_score":    0.92,
			},
			"claude-3": map[string]interface{}{
				"average_quality": 0.83,
				"bias_score":      0.12,
				"safety_score":    0.94,
			},
			"gemini-pro": map[string]interface{}{
				"average_quality": 0.81,
				"bias_score":      0.18,
				"safety_score":    0.89,
			},
		},
		"task_benchmarks": map[string]interface{}{
			"factual_query": map[string]interface{}{
				"expected_accuracy":  0.9,
				"expected_relevance": 0.85,
			},
			"summarization": map[string]interface{}{
				"expected_conciseness":  0.8,
				"expected_completeness": 0.75,
			},
			"code_generation": map[string]interface{}{
				"expected_functionality": 0.85,
				"expected_quality":       0.8,
			},
		},
		"updated": time.Now().UTC(),
	}

	benchmarksJSON, _ := json.MarshalIndent(benchmarks, "", "  ")
	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"contents": []map[string]interface{}{
				{
					"uri":      "evaluation://benchmarks",
					"mimeType": "application/json",
					"text":     string(benchmarksJSON),
				},
			},
		},
	}
}

func (s *MCPEvaluationServer) readEvaluationHistory(id interface{}) MCPResponse {
	history := map[string]interface{}{
		"message":   "Evaluation history tracking not yet implemented",
		"timestamp": time.Now().UTC(),
		"note":      "This will contain historical evaluation results, trends, and analytics",
		"available_endpoints": []string{
			"/metrics",
			"/analytics/models",
			"/analytics/trends",
		},
	}

	historyJSON, _ := json.MarshalIndent(history, "", "  ")
	return MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result: map[string]interface{}{
			"contents": []map[string]interface{}{
				{
					"uri":      "evaluation://history",
					"mimeType": "application/json",
					"text":     string(historyJSON),
				},
			},
		},
	}
}

// Global MCP server instance
var mcpEvaluationServer *MCPEvaluationServer

// InitializeMCPEvaluationServer initializes the MCP server for Evaluation Service
func InitializeMCPEvaluationServer() {
	mcpEvaluationServer = NewMCPEvaluationServer()
	log.Printf("Evaluation Service MCP Server: Initialized successfully")
}
