#!/usr/bin/env python3
"""
Simple Evaluation Test Suite
Tests the current evaluation system with direct API calls.
"""

import json
import requests
import time
from datetime import datetime
import uuid

class SimpleEvaluationTest:
    """Simple evaluation test that works with current deployment"""
    
    def __init__(self, base_url: str = "https://scale-llm.com"):
        self.base_url = base_url
        self.test_results = []
        self.test_session_id = str(uuid.uuid4())
        
    def run_evaluation_tests(self):
        """Run evaluation tests with current system"""
        print("🚀 Simple Evaluation Test Suite")
        print(f"Session ID: {self.test_session_id}")
        print("=" * 60)
        
        # Test goal classification evaluation
        self.test_goal_classification()
        
        # Test constraint extraction evaluation  
        self.test_constraint_extraction()
        
        # Test planning service integration
        self.test_planning_integration()
        
        # Generate report
        self.generate_report()
        
    def test_goal_classification(self):
        """Test goal classification evaluation"""
        print("\n📝 Testing Goal Classification Evaluation")
        print("-" * 40)
        
        test_cases = [
            {
                "description": "Analyze customer feedback data using advanced LLM analysis techniques",
                "expected": "data_analysis",
                "response": "data_analysis"
            },
            {
                "description": "Make API calls to external services for data integration", 
                "expected": "automation",
                "response": "automation"
            },
            {
                "description": "Generate creative marketing content for social media",
                "expected": "content_creation", 
                "response": "content_creation"
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            try:
                result = self.evaluate_goal_classification(test_case)
                self.test_results.append({
                    "test_type": "goal_classification",
                    "test_id": f"gc_{i+1:03d}",
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                })
                
                score = result.get("score", 0)
                passed = result.get("passed", False)
                status = "✅" if passed else "❌"
                print(f"{status} Test {i+1}: Score {score:.3f} - {test_case['description'][:50]}...")
                
            except Exception as e:
                print(f"❌ Test {i+1}: Error - {str(e)}")
                
    def test_constraint_extraction(self):
        """Test constraint extraction evaluation"""
        print("\n🎯 Testing Constraint Extraction Evaluation")
        print("-" * 40)
        
        test_cases = [
            {
                "description": "Analyze customer feedback data with 95% accuracy within 2 hours",
                "response": '[{"type":"quality","description":"Analysis accuracy","limit":">=0.95","operator":">=","severity":"hard"},{"type":"time","description":"Processing time","limit":"<=2 hours","operator":"<=","severity":"medium"}]'
            },
            {
                "description": "Process API calls under $0.10 per request with HTTPS security",
                "response": '[{"type":"cost","description":"Cost limit","limit":"<=0.10","operator":"<=","severity":"medium"},{"type":"compliance","description":"Security requirement","limit":"HTTPS required","operator":"==","severity":"hard"}]'
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            try:
                result = self.evaluate_constraint_extraction(test_case)
                self.test_results.append({
                    "test_type": "constraint_extraction",
                    "test_id": f"ce_{i+1:03d}",
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                })
                
                score = result.get("score", 0)
                passed = result.get("passed", False)
                status = "✅" if passed else "❌"
                print(f"{status} Test {i+1}: Score {score:.3f} - {test_case['description'][:50]}...")
                
            except Exception as e:
                print(f"❌ Test {i+1}: Error - {str(e)}")
                
    def test_planning_integration(self):
        """Test planning service integration"""
        print("\n🔄 Testing Planning Service Integration")
        print("-" * 40)
        
        test_goals = [
            "Analyze sales performance data from Q4 2024",
            "Create automated reporting dashboard for metrics",
            "Research emerging AI technology trends"
        ]
        
        for i, goal_desc in enumerate(test_goals):
            try:
                # Create goal
                goal_result = self.create_planning_goal(goal_desc)
                goal_id = goal_result.get("id", "unknown")
                
                # Wait for processing
                time.sleep(1)
                
                # Check if goal was processed
                goal_details = self.get_goal_details(goal_id)
                classification = goal_details.get("metadata", {}).get("classification", "")
                
                result = {
                    "goal_id": goal_id,
                    "description": goal_desc,
                    "classification": classification,
                    "processed": bool(classification),
                    "score": 0.8 if classification else 0.2
                }
                
                self.test_results.append({
                    "test_type": "planning_integration",
                    "test_id": f"pi_{i+1:03d}",
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                })
                
                status = "✅" if result["processed"] else "❌"
                print(f"{status} Goal {i+1}: {classification or 'No classification'} - {goal_desc[:40]}...")
                
            except Exception as e:
                print(f"❌ Goal {i+1}: Error - {str(e)}")
                
    def evaluate_goal_classification(self, test_case):
        """Evaluate a goal classification test case"""
        prompt = f"""Classify the following goal into one of these categories:
- data_analysis: Goals involving data processing, analysis, or insights
- content_creation: Goals involving generating text, images, or other content
- automation: Goals involving automating processes or workflows
- research: Goals involving information gathering or research
- decision_support: Goals involving analysis to support decision making
- customer_service: Goals involving customer interaction or support
- monitoring: Goals involving tracking or monitoring systems/metrics
- optimization: Goals involving improving efficiency or performance

Goal: {test_case['description']}

Return only the category name, no additional text."""

        eval_payload = {
            "request_id": str(uuid.uuid4()),
            "prompt": prompt,
            "llm_response": test_case["response"],
            "model_id": "gemini-2.5-flash-preview-05-20",
            "expected_response": test_case["expected"],
            "task_type": "goal_classification"
        }
        
        response = requests.post(
            f"{self.base_url}/api/evaluation/evaluate",
            json=eval_payload,
            timeout=30
        )
        
        if response.status_code not in [200, 201]:
            raise Exception(f"Evaluation failed: {response.status_code} - {response.text}")
            
        return response.json()
        
    def evaluate_constraint_extraction(self, test_case):
        """Evaluate a constraint extraction test case"""
        prompt = f"""Analyze the following goal description and extract any constraints or limitations.
Return the constraints as a JSON array of objects with the following structure:
{{
  "type": "Type of constraint ('cost', 'time', 'quality', 'resource', 'compliance')",
  "description": "Clear description of the constraint",
  "limit": "The limit value (number, string, or boolean)",
  "operator": "Comparison operator ('<=', '>=', '==', '!=', 'contains')",
  "severity": "Constraint severity ('hard', 'soft', 'preference')"
}}

Goal Description: {test_case['description']}

Return only the JSON array, no additional text."""

        eval_payload = {
            "request_id": str(uuid.uuid4()),
            "prompt": prompt,
            "llm_response": test_case["response"],
            "model_id": "gemini-2.5-flash-preview-05-20",
            "task_type": "constraint_extraction"
        }
        
        response = requests.post(
            f"{self.base_url}/api/evaluation/evaluate",
            json=eval_payload,
            timeout=30
        )
        
        if response.status_code not in [200, 201]:
            raise Exception(f"Evaluation failed: {response.status_code} - {response.text}")
            
        return response.json()
        
    def create_planning_goal(self, description):
        """Create a planning goal"""
        goal_payload = {
            "description": description,
            "priority": 5
        }
        
        response = requests.post(
            f"{self.base_url}/v1/goals",
            json=goal_payload,
            headers={"X-User-ID": f"eval-test-{self.test_session_id}"},
            timeout=30
        )
        
        if response.status_code not in [200, 201]:
            raise Exception(f"Goal creation failed: {response.status_code}")
            
        return response.json()
        
    def get_goal_details(self, goal_id):
        """Get goal details"""
        response = requests.get(
            f"{self.base_url}/v1/goals/{goal_id}",
            headers={"X-User-ID": f"eval-test-{self.test_session_id}"},
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {}
            
    def generate_report(self):
        """Generate test report"""
        print("\n📊 Test Report")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results 
                          if r.get("result", {}).get("passed", False) or 
                             r.get("result", {}).get("score", 0) >= 0.7)
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"Session ID: {self.test_session_id}")
        print(f"Total Tests: {total_tests}")
        print(f"Passed Tests: {passed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        # Group by test type
        by_type = {}
        for result in self.test_results:
            test_type = result["test_type"]
            if test_type not in by_type:
                by_type[test_type] = []
            by_type[test_type].append(result)
            
        for test_type, results in by_type.items():
            print(f"\n{test_type.replace('_', ' ').title()}:")
            for result in results:
                test_result = result.get("result", {})
                score = test_result.get("score", 0)
                passed = test_result.get("passed", False) or score >= 0.7
                status = "✅" if passed else "❌"
                print(f"  {status} {result['test_id']}: Score {score:.3f}")


if __name__ == "__main__":
    test_suite = SimpleEvaluationTest()
    test_suite.run_evaluation_tests()
