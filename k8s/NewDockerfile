# Build stage for ai-optimizer
FROM golang:1.24.2-alpine AS optimizer-builder
WORKDIR /app
COPY ./ai-optimizer .
RUN go mod tidy

# Build stage for planning-service
FROM golang:1.24.2-alpine AS builder

WORKDIR /app

# Install dependencies
RUN apk add --no-cache git

# Copy modules
COPY --from=optimizer-builder /app /ai-optimizer
COPY ./planning-service .

# Download dependencies
RUN go mod download

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o planning-service .

# Final stage for planning-service
FROM alpine:latest AS planning-service
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/planning-service .
EXPOSE 8080
CMD ["./planning-service"]

# Build stage for governance-service
FROM golang:1.24.2-alpine AS governance-builder
WORKDIR /app
COPY ./governance-service .
RUN go mod tidy
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o governance-service .

# Final stage for governance-service
FROM alpine:latest AS governance-service
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=governance-builder /app/governance-service .
EXPOSE 8080
CMD ["./governance-service"]