apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: enterprise-api-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/rewrite-target: /api/$1$2
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Enterprise dashboard API endpoints - rewrite /enterprise/api/* to /api/*
      - path: /enterprise/api/(summary|time-series|policies|model-profiles|inference-logs|backend-latencies|optimal-backend|evaluation-results|curated-data|prompts|mcp|planning|multi-agent)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: enterprise-direct-api-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    # No rewrite needed for direct API routes
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Root API endpoints for enterprise edition (without /enterprise prefix)
      - path: /api/prompts
        pathType: Prefix
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

      # Enterprise API endpoints with /enterprise prefix for consistency
      - path: /enterprise/api/prompts
        pathType: Prefix
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081
      - path: /enterprise/api/synthetic-data
        pathType: Prefix
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081
      - path: /enterprise/api/multi-agent
        pathType: Prefix
        backend:
          service:
            name: dashboard-api
            port:
              number: 8081

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: enterprise-integration-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Enterprise integration service endpoints - rewrite /enterprise/api/integration/* to /*
      - path: /enterprise/api/integration/(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: integration-service
            port:
              number: 8080

      # Regular API integration endpoints (for PromptOps GitOps)
      - path: /api/integration/(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: integration-service
            port:
              number: 8080

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: enterprise-planning-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Enterprise planning service endpoints - rewrite /enterprise/api/planning/* to /*
      - path: /enterprise/api/planning/(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: planning-service
            port:
              number: 8084

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: enterprise-proxy-gateway-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      # Enterprise proxy-gateway endpoints - rewrite /enterprise/api/* to /*
      - path: /enterprise/api(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: proxy-gateway
            port:
              number: 8080
