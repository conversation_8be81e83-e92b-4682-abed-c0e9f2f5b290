apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dashboard-api-specific-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /api/$1$2
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com
    http:
      paths:
      - path: /standard/api/(model-profiles)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /standard/api/(model-capabilities)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /standard/api/(policies)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /standard/api/(summary)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /standard/api/(time-series)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /standard/api/(inference-logs)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /standard/api/(backend-latencies)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /standard/api/(optimal-backend)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /standard/api/(evaluation-results)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /standard/api/(evaluation-analytics)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      # Legacy API routes (without /standard prefix) for backward compatibility
      - path: /api/(model-profiles)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /api/(model-capabilities)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /api/(policies)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081

      - path: /api/(summary)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /api/(time-series)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /api/(inference-logs)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /api/(backend-latencies)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /api/(optimal-backend)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /api/(evaluation-results)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
      - path: /api/(evaluation-analytics)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: dashboard-api-standard-external
            port:
              number: 8081
