#!/bin/bash

echo "🔍 Verifying AI Operations Hub Enhanced Deployment Files..."
echo "============================================================"

# Check if we're in the right directory
if [ ! -f "../cloudbuild.yaml" ]; then
    echo "❌ Error: cloudbuild.yaml not found. Please run this script from the k8s directory."
    exit 1
fi

echo "✅ Found cloudbuild.yaml"

# Check main deployment scripts
echo ""
echo "📋 Checking main deployment scripts..."
if [ -f "New_Autostart.sh" ]; then
    echo "✅ Found New_Autostart.sh"
else
    echo "❌ Missing New_Autostart.sh"
fi

# Check new services
echo ""
echo "🆕 Checking new enhanced services..."

# Sentiment Service
if [ -d "sentiment-service" ]; then
    echo "✅ Found sentiment-service directory"
    if [ -f "sentiment-service/main.go" ]; then
        echo "  ✅ Found main.go"
    else
        echo "  ❌ Missing main.go"
    fi
    if [ -f "sentiment-service/sentiment-service.yaml" ]; then
        echo "  ✅ Found sentiment-service.yaml"
    else
        echo "  ❌ Missing sentiment-service.yaml"
    fi
    if [ -f "sentiment-service/Dockerfile" ]; then
        echo "  ✅ Found Dockerfile"
    else
        echo "  ❌ Missing Dockerfile"
    fi
    if [ -f "sentiment-service/go.mod" ]; then
        echo "  ✅ Found go.mod"
    else
        echo "  ❌ Missing go.mod"
    fi
else
    echo "❌ Missing sentiment-service directory"
fi

# Social Integration Service
if [ -d "social-integration-service" ]; then
    echo "✅ Found social-integration-service directory"
    if [ -f "social-integration-service/main.go" ]; then
        echo "  ✅ Found main.go"
    else
        echo "  ❌ Missing main.go"
    fi
    if [ -f "social-integration-service/social-integration-service.yaml" ]; then
        echo "  ✅ Found social-integration-service.yaml"
    else
        echo "  ❌ Missing social-integration-service.yaml"
    fi
    if [ -f "social-integration-service/Dockerfile" ]; then
        echo "  ✅ Found Dockerfile"
    else
        echo "  ❌ Missing Dockerfile"
    fi
    if [ -f "social-integration-service/go.mod" ]; then
        echo "  ✅ Found go.mod"
    else
        echo "  ❌ Missing go.mod"
    fi
else
    echo "❌ Missing social-integration-service directory"
fi

echo ""
echo "🎯 Summary of AI Operations Hub Enhancements:"
echo "=============================================="
echo "✨ Sentiment-Driven Intelligence Enhancement"
echo "🚀 No-Code Workflow Acceleration" 
echo "🌐 Social Media & Integration Platform"
echo "🛡️ Simplified Governance & Compliance"
echo "📊 Product-Market Fit Analytics"
echo ""
echo "✅ Verification complete! Your AI Operations Hub is ready for enhanced deployment."
