#!/bin/bash
# Check ZooKeeper status
echo "Checking ZooKeeper pod status..."
kubectl get pods -l app=zookeeper

echo "Checking ZooKeeper service..."
kubectl get svc zookeeper

echo "Checking ZooKeeper logs..."
ZOOKEEPER_POD=$(kubectl get pods -l app=zookeeper -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
if [ -n "$ZOOKEEPER_POD" ]; then
  kubectl logs $ZOOKEEPER_POD
else
  echo "ZooKeeper pod not found."
fi