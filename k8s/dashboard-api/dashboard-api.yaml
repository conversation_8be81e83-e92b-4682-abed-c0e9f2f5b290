# dashboard-api.yaml
# Kubernetes YAML definitions for the Dashboard API

apiVersion: v1
kind: Service
metadata:
  name: dashboard-api # Service name for in-cluster communication
spec:
  selector:
    app: dashboard-api
  ports:
    - name: http # Port for the API
      protocol: TCP
      port: 8081  # Changed from 8082 to 8081
      targetPort: 8081  # This should match what the dashboard-api app is listening on
  type: ClusterIP # Use ClusterIP for in-cluster access (Frontend will access it)

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dashboard-api # Deployment name
  labels:
    app: dashboard-api
spec:
  replicas: 1 # Running a single replica for simplicity
  selector:
    matchLabels:
      app: dashboard-api
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: dashboard-api
    spec:
      # ADD THIS LINE: Specify the Kubernetes Service Account
      serviceAccountName: dashboard-api-ksa # This must match DASHBOARD_API_KSA_NAME from your script
      initContainers:
        - name: wait-for-clickhouse
          image: busybox:latest
          imagePullPolicy: IfNotPresent
          command: ['sh', '-c', 'echo "Waiting for ClickHouse..."; until nc -z clickhouse 9000; do echo "ClickHouse not ready, waiting..."; sleep 2; done; echo "ClickHouse is ready."']
          resources:
            requests:
              memory: "10Mi"
              cpu: "10m"
            limits:
              memory: "20Mi"
              cpu: "20m"
      initContainers:
        - name: wait-for-policy-manager
          image: busybox:latest
          imagePullPolicy: IfNotPresent
          command: ['sh', '-c', 'echo "Waiting for Policy Manager..."; until nc -z policy-manager 8083; do echo "Policy Manager not ready, waiting..."; sleep 2; done; echo "Policy Manager is ready."']
          resources:
            requests:
              memory: "10Mi"
              cpu: "10m"
            limits:
              memory: "20Mi"
              cpu: "20m"
      containers:
        - name: dashboard-api
          image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-dashboard-api:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8081
              name: http
          env:
            - name: LISTEN_ADDR
              value: ":8081"
            - name: CLICKHOUSE_HOST
              value: "clickhouse"
            - name: CLICKHOUSE_PORT
              value: "9000"
            - name: CLICKHOUSE_DB
              value: "default"
            - name: CLICKHOUSE_USER
              value: "test"
            - name: CLICKHOUSE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: clickhouse-credentials
                  key: password
            - name: GCP_PROJECT_ID
              value: silken-zenith-460615-s7
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: jwt-secret
                  key: secret
          resources:
            requests:
              memory: "64Mi"
              cpu: "50m"
            limits:
              memory: "128Mi"
              cpu: "100m"
          # readinessProbe:
          #   httpGet:
          #     path: /healthz
          #     port: 8081
          #   initialDelaySeconds: 10
          #   periodSeconds: 5
          # livenessProbe:
          #   httpGet:
          #     path: /healthz
          #     port: 8081
          #   initialDelaySeconds: 30
          #   periodSeconds: 10

---
# Optional: Expose the Dashboard API externally if needed (e.g., for direct testing)
# apiVersion: v1
# kind: Service
# metadata:
#   name: dashboard-api-external
# spec:
#   selector:
#     app: dashboard-api
#   ports:
#     - protocol: TCP
#       port: 8081
#       targetPort: 8081
#   type: LoadBalancer
