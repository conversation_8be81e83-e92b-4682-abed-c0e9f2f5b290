package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// User represents a user from the auth service
type User struct {
	ID          string                 `json:"id"`
	Email       string                 `json:"email"`
	Name        string                 `json:"name"`
	Picture     string                 `json:"picture"`
	GoogleID    string                 `json:"google_id"`
	Roles       []string               `json:"roles"`
	Status      string                 `json:"status"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	LastLoginAt *time.Time             `json:"last_login_at,omitempty"`
}

// Claims represents JWT claims
type Claims struct {
	UserID string   `json:"user_id"`
	Email  string   `json:"email"`
	Roles  []string `json:"roles"`
	jwt.RegisteredClaims
}

// AuthMiddleware handles authentication for the proxy gateway
func authMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Skip auth for health checks and public endpoints
		if isPublicEndpoint(r.URL.Path) {
			next(w, r)
			return
		}

		// Extract token from Authorization header
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			http.Error(w, "Authorization header required", http.StatusUnauthorized)
			return
		}

		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			http.Error(w, "Invalid authorization header format", http.StatusUnauthorized)
			return
		}

		token := parts[1]
		user, err := validateJWTToken(token)
		if err != nil {
			log.Printf("Token validation failed: %v", err)
			http.Error(w, "Invalid or expired token", http.StatusUnauthorized)
			return
		}

		// Check if user is active
		if user.Status != "active" {
			http.Error(w, "User account is not active", http.StatusForbidden)
			return
		}

		// Add user context to request headers
		r.Header.Set("X-User-ID", user.ID)
		r.Header.Set("X-User-Email", user.Email)
		r.Header.Set("X-User-Name", user.Name)
		r.Header.Set("X-User-Roles", strings.Join(user.Roles, ","))

		// Continue to next handler
		next(w, r)
	}
}

// validateJWTToken validates a JWT token and returns user info
func validateJWTToken(tokenString string) (*User, error) {
	jwtSecret := []byte(getEnv("JWT_SECRET", ""))
	if len(jwtSecret) == 0 {
		return nil, fmt.Errorf("JWT secret not configured")
	}

	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return jwtSecret, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		// Get user details from auth service
		user, err := getUserFromAuthService(claims.UserID)
		if err != nil {
			return nil, fmt.Errorf("failed to get user details: %w", err)
		}
		return user, nil
	}

	return nil, fmt.Errorf("invalid token claims")
}

// getUserFromAuthService fetches user details from the auth service
func getUserFromAuthService(userID string) (*User, error) {
	// For now, we'll construct a minimal user from the JWT claims
	// In a production system, you might cache user data or call the auth service
	user := &User{
		ID:     userID,
		Status: "active", // Assume active if token is valid
	}

	return user, nil
}

// isPublicEndpoint checks if an endpoint should be publicly accessible
func isPublicEndpoint(path string) bool {
	publicPaths := []string{
		"/health",
		"/metrics",
		"/auth/",
		"/favicon.ico",
	}

	for _, publicPath := range publicPaths {
		if strings.HasPrefix(path, publicPath) {
			return true
		}
	}

	return false
}

// optionalAuthMiddleware provides optional authentication (doesn't block if no auth)
func optionalAuthMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Try to extract and validate token, but don't block if missing
		authHeader := r.Header.Get("Authorization")
		if authHeader != "" {
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) == 2 && parts[0] == "Bearer" {
				token := parts[1]
				user, err := validateJWTToken(token)
				if err == nil && user.Status == "active" {
					// Add user context to request headers
					r.Header.Set("X-User-ID", user.ID)
					r.Header.Set("X-User-Email", user.Email)
					r.Header.Set("X-User-Name", user.Name)
					r.Header.Set("X-User-Roles", strings.Join(user.Roles, ","))
				}
			}
		}

		// Continue regardless of auth status
		next(w, r)
	}
}

// adminMiddleware requires admin role
func adminMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return authMiddleware(func(w http.ResponseWriter, r *http.Request) {
		userRoles := r.Header.Get("X-User-Roles")
		roles := strings.Split(userRoles, ",")

		hasAdmin := false
		for _, role := range roles {
			if strings.TrimSpace(role) == "admin" {
				hasAdmin = true
				break
			}
		}

		if !hasAdmin {
			http.Error(w, "Admin access required", http.StatusForbidden)
			return
		}

		next(w, r)
	})
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
