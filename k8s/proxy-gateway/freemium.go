package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
)

// FreemiumTier represents the user's subscription tier
type FreemiumTier string

const (
	TierFree    FreemiumTier = "free"
	TierStarter FreemiumTier = "starter"
	TierGrowth  FreemiumTier = "growth"
)

// FreemiumLimits defines the usage limits for each tier
type FreemiumLimits struct {
	MonthlyRequests int `json:"monthly_requests"`
	DailyRequests   int `json:"daily_requests"`
	RateLimitRPM    int `json:"rate_limit_rpm"` // Requests per minute
}

// UserUsage tracks a user's current usage
type UserUsage struct {
	UserID          string    `json:"user_id"`
	Tier            string    `json:"tier"`
	MonthlyUsage    int       `json:"monthly_usage"`
	DailyUsage      int       `json:"daily_usage"`
	LastRequestTime time.Time `json:"last_request_time"`
	CurrentMonth    string    `json:"current_month"`
	CurrentDay      string    `json:"current_day"`
}

// FreemiumConfig holds the configuration for freemium features
type FreemiumConfig struct {
	Enabled bool                            `json:"enabled"`
	Limits  map[FreemiumTier]FreemiumLimits `json:"limits"`
}

var (
	freemiumEnabled = false
	freemiumLimits  = map[FreemiumTier]FreemiumLimits{
		TierFree: {
			MonthlyRequests: 1000,
			DailyRequests:   50,
			RateLimitRPM:    10,
		},
		TierStarter: {
			MonthlyRequests: 10000,
			DailyRequests:   500,
			RateLimitRPM:    50,
		},
		TierGrowth: {
			MonthlyRequests: 50000,
			DailyRequests:   2000,
			RateLimitRPM:    200,
		},
	}
)

// InitializeFreemium sets up freemium configuration based on environment variables
func InitializeFreemium() {
	enableFreemium := os.Getenv("ENABLE_FREEMIUM")
	if enableFreemium == "true" {
		freemiumEnabled = true
		log.Println("Freemium mode enabled")

		// Override limits from environment variables if provided
		if freeTierLimit := os.Getenv("FREE_TIER_LIMIT"); freeTierLimit != "" {
			if limit, err := strconv.Atoi(freeTierLimit); err == nil {
				freemiumLimits[TierFree] = FreemiumLimits{
					MonthlyRequests: limit,
					DailyRequests:   limit / 30, // Rough daily estimate
					RateLimitRPM:    10,
				}
			}
		}

		if starterTierLimit := os.Getenv("STARTER_TIER_LIMIT"); starterTierLimit != "" {
			if limit, err := strconv.Atoi(starterTierLimit); err == nil {
				freemiumLimits[TierStarter] = FreemiumLimits{
					MonthlyRequests: limit,
					DailyRequests:   limit / 30,
					RateLimitRPM:    50,
				}
			}
		}

		if growthTierLimit := os.Getenv("GROWTH_TIER_LIMIT"); growthTierLimit != "" {
			if limit, err := strconv.Atoi(growthTierLimit); err == nil {
				freemiumLimits[TierGrowth] = FreemiumLimits{
					MonthlyRequests: limit,
					DailyRequests:   limit / 30,
					RateLimitRPM:    200,
				}
			}
		}
	}
}

// CheckFreemiumLimits validates if a user can make a request based on their tier and usage
func CheckFreemiumLimits(ctx context.Context, userID string, userTier string) error {
	if !freemiumEnabled {
		return nil // Freemium not enabled, allow all requests
	}

	if userID == "" {
		// For startup edition, require user identification
		return fmt.Errorf("user identification required for startup edition")
	}

	tier := FreemiumTier(userTier)
	if tier == "" {
		tier = TierFree // Default to free tier
	}

	limits, exists := freemiumLimits[tier]
	if !exists {
		return fmt.Errorf("unknown tier: %s", tier)
	}

	// Get current usage from Redis
	usage, err := getUserUsage(ctx, userID)
	if err != nil {
		log.Printf("Error getting user usage for %s: %v", userID, err)
		// Allow request but log the error
		return nil
	}

	currentMonth := time.Now().Format("2006-01")
	currentDay := time.Now().Format("2006-01-02")

	// Reset counters if month/day changed
	if usage.CurrentMonth != currentMonth {
		usage.MonthlyUsage = 0
		usage.CurrentMonth = currentMonth
	}
	if usage.CurrentDay != currentDay {
		usage.DailyUsage = 0
		usage.CurrentDay = currentDay
	}

	// Check monthly limit
	if usage.MonthlyUsage >= limits.MonthlyRequests {
		return fmt.Errorf("monthly limit exceeded (%d/%d requests). Upgrade your plan to continue",
			usage.MonthlyUsage, limits.MonthlyRequests)
	}

	// Check daily limit
	if usage.DailyUsage >= limits.DailyRequests {
		return fmt.Errorf("daily limit exceeded (%d/%d requests). Try again tomorrow or upgrade your plan",
			usage.DailyUsage, limits.DailyRequests)
	}

	// Check rate limit (requests per minute)
	if !usage.LastRequestTime.IsZero() {
		timeSinceLastRequest := time.Since(usage.LastRequestTime)
		if timeSinceLastRequest < time.Minute/time.Duration(limits.RateLimitRPM) {
			return fmt.Errorf("rate limit exceeded. Please wait %v before making another request",
				time.Minute/time.Duration(limits.RateLimitRPM)-timeSinceLastRequest)
		}
	}

	return nil
}

// IncrementUserUsage increments the user's usage counters
func IncrementUserUsage(ctx context.Context, userID string, userTier string) error {
	if !freemiumEnabled || userID == "" {
		return nil
	}

	usage, err := getUserUsage(ctx, userID)
	if err != nil {
		log.Printf("Error getting user usage for increment %s: %v", userID, err)
		return err
	}

	currentMonth := time.Now().Format("2006-01")
	currentDay := time.Now().Format("2006-01-02")

	// Reset counters if month/day changed
	if usage.CurrentMonth != currentMonth {
		usage.MonthlyUsage = 0
		usage.CurrentMonth = currentMonth
	}
	if usage.CurrentDay != currentDay {
		usage.DailyUsage = 0
		usage.CurrentDay = currentDay
	}

	// Increment counters
	usage.MonthlyUsage++
	usage.DailyUsage++
	usage.LastRequestTime = time.Now()
	usage.Tier = userTier

	// Save back to Redis
	return saveUserUsage(ctx, userID, usage)
}

// getUserUsage retrieves user usage from Redis
func getUserUsage(ctx context.Context, userID string) (*UserUsage, error) {
	key := fmt.Sprintf("user_usage:%s", userID)
	data, err := redisClient.Get(ctx, key).Result()
	if err == redis.Nil {
		// User doesn't exist, create new usage record
		return &UserUsage{
			UserID:          userID,
			Tier:            string(TierFree),
			MonthlyUsage:    0,
			DailyUsage:      0,
			LastRequestTime: time.Time{},
			CurrentMonth:    time.Now().Format("2006-01"),
			CurrentDay:      time.Now().Format("2006-01-02"),
		}, nil
	} else if err != nil {
		return nil, err
	}

	var usage UserUsage
	if err := json.Unmarshal([]byte(data), &usage); err != nil {
		return nil, err
	}

	return &usage, nil
}

// saveUserUsage saves user usage to Redis
func saveUserUsage(ctx context.Context, userID string, usage *UserUsage) error {
	key := fmt.Sprintf("user_usage:%s", userID)
	data, err := json.Marshal(usage)
	if err != nil {
		return err
	}

	// Set with 32-day expiration (longer than a month to handle edge cases)
	return redisClient.Set(ctx, key, data, 32*24*time.Hour).Err()
}

// GetUserUsageStats returns usage statistics for a user (for dashboard)
func GetUserUsageStats(ctx context.Context, userID string) (*UserUsage, error) {
	return getUserUsage(ctx, userID)
}

// HandleFreemiumError sends appropriate error response for freemium violations
func HandleFreemiumError(w http.ResponseWriter, err error) {
	response := map[string]interface{}{
		"error": map[string]interface{}{
			"message": err.Error(),
			"type":    "quota_exceeded",
			"code":    "freemium_limit_exceeded",
		},
		"upgrade_url": "/startups/upgrade",
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusTooManyRequests) // 429 status code
	json.NewEncoder(w).Encode(response)
}

// handleHealthCheck provides a simple health check endpoint
func handleHealthCheck(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{
		"status":  "healthy",
		"service": "proxy-gateway",
		"edition": "startup",
	})
}

// handleReadinessCheck provides a readiness check endpoint
func handleReadinessCheck(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	// Check Redis connection
	_, err := redisClient.Ping(ctx).Result()
	if err != nil {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusServiceUnavailable)
		json.NewEncoder(w).Encode(map[string]string{
			"status": "not ready",
			"error":  "redis connection failed",
		})
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{
		"status":  "ready",
		"service": "proxy-gateway",
	})
}

// handleUsageStats provides usage statistics for a user
func handleUsageStats(w http.ResponseWriter, r *http.Request) {
	userID := r.Header.Get("X-User-Id")
	if userID == "" {
		http.Error(w, "User ID required", http.StatusBadRequest)
		return
	}

	ctx := context.Background()
	usage, err := GetUserUsageStats(ctx, userID)
	if err != nil {
		http.Error(w, "Error fetching usage stats", http.StatusInternalServerError)
		return
	}

	// Get tier limits
	tier := FreemiumTier(usage.Tier)
	limits, exists := freemiumLimits[tier]
	if !exists {
		limits = freemiumLimits[TierFree]
	}

	response := map[string]interface{}{
		"user_id": userID,
		"tier":    usage.Tier,
		"usage": map[string]interface{}{
			"monthly": map[string]interface{}{
				"used":       usage.MonthlyUsage,
				"limit":      limits.MonthlyRequests,
				"remaining":  limits.MonthlyRequests - usage.MonthlyUsage,
				"percentage": float64(usage.MonthlyUsage) / float64(limits.MonthlyRequests) * 100,
			},
			"daily": map[string]interface{}{
				"used":      usage.DailyUsage,
				"limit":     limits.DailyRequests,
				"remaining": limits.DailyRequests - usage.DailyUsage,
			},
		},
		"limits": map[string]interface{}{
			"monthly_requests": limits.MonthlyRequests,
			"daily_requests":   limits.DailyRequests,
			"rate_limit_rpm":   limits.RateLimitRPM,
		},
		"last_request":  usage.LastRequestTime,
		"current_month": usage.CurrentMonth,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}
