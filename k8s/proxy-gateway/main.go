package main

import (
	"bufio"
	"bytes"
	"compress/flate"
	"compress/gzip"
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"math"
	"net/http" // For URL manipulation
	"net/url"
	"os"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	gocache "github.com/patrickmn/go-cache"
	"github.com/segmentio/kafka-go"
)

// --- Constants ---
const (
	kafkaBroker = "kafka:9092"
	kafkaTopic  = "inference-logs"
	redisAddr   = "redis:6379"
	// Policy Manager URL for fetching policies and model profiles
	// policyManagerURL = "http://policy-manager:8083"
	// AI Optimizer URL for routing decisions. Consider making this an environment variable.
	aiOptimizerURL = "http://ai-optimizer:8085" // Default, can be overridden by env var if desired

	// Redis keys for policies and model profiles (consistent with policy-manager)
	REDIS_POLICIES_KEY_PREFIX       = "policy:"
	REDIS_MODEL_PROFILES_KEY_PREFIX = "model_profile:"

	REDIS_CACHE_KEY_PREFIX = "llm_response_cache:" // Redis key prefix for LLM response cache
	CACHE_TTL              = 1 * time.Hour         // Time-to-live for cached responses

	// Redis channel for model profile updates (consistent with policy-manager)
	MODEL_PROFILE_UPDATES_CHANNEL = "model_profile_updates"
	// Redis channel for policy updates (consistent with policy-manager)
	POLICY_UPDATES_CHANNEL = "policy_updates"

	// Configuration for semantic caching
	SEMANTIC_CACHE_KEY_PREFIX     = "semantic_cache:" // Redis key prefix for semantic cache
	SEMANTIC_CACHE_TTL            = 24 * time.Hour    // Semantic cache entries live longer
	SEMANTIC_SIMILARITY_THRESHOLD = 0.85              // Threshold for semantic similarity (0.0-1.0)

	// Cache analytics keys
	CACHE_ANALYTICS_PREFIX         = "cache_analytics:" // Redis key prefix for cache analytics
	CACHE_HIT_COUNTER_KEY          = CACHE_ANALYTICS_PREFIX + "hits"
	CACHE_MISS_COUNTER_KEY         = CACHE_ANALYTICS_PREFIX + "misses"
	SEMANTIC_CACHE_HIT_COUNTER_KEY = CACHE_ANALYTICS_PREFIX + "semantic_hits"
	CACHE_ANALYTICS_TTL            = 30 * 24 * time.Hour // Cache analytics live for 30 days

	// In-memory cache configuration for hot items
	IN_MEMORY_CACHE_MAX_ITEMS = 1000 // Maximum number of items in the in-memory cache

	// Configuration for prompt optimization
	ENABLE_PROMPT_OPTIMIZATION    = true // Master switch for prompt optimization
	PROMPT_OPTIMIZATION_THRESHOLD = 500  // Only optimize prompts longer than this many characters
	ADVANCED_OPTIMIZATION_ENABLED = true // Enable more aggressive optimization techniques
	TOKEN_BUDGET_ENABLED          = true // Enable token budget controls
	DEFAULT_TOKEN_BUDGET          = 8000 // Default token budget (can be overridden per request)
	// Constants SEMANTIC_COMPRESSION_ENABLED, MAX_COMPRESSION_ITERATIONS, and COMPRESSION_MIN_CHUNK_SIZE
	// are defined in enhanced_functions.go

	// Configuration for intelligent prompt analysis
	ENABLE_PROMPT_ANALYSIS     = true                                                                                                     // Master switch for prompt analysis
	ANALYSIS_LLM_MODEL         = "gemini-2.5-flash-preview-05-20"                                                                         // LLM model to use for analysis (fast and cost-effective)
	ANALYSIS_CACHE_TTL         = 3600                                                                                                     // Cache analysis results for 1 hour
	ANALYSIS_MIN_PROMPT_LENGTH = 10                                                                                                       // Minimum prompt length to analyze
	ANALYSIS_LLM_ENDPOINT      = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent" // Endpoint for analysis LLM (Gemini)
	ANALYSIS_FALLBACK_ENABLED  = true                                                                                                     // Enable fallback to keyword-based analysis if LLM fails
)

// --- Structs (consistent with other services) ---

// PromptTemplate represents a reusable prompt pattern with variable placeholders
type PromptTemplate struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Template    string            `json:"template"`
	Description string            `json:"description,omitempty"`
	Variables   map[string]string `json:"variables,omitempty"` // Default values for variables
	Category    string            `json:"category,omitempty"`  // E.g., "summarization", "translation", etc.
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}

// CacheAnalytics tracks cache performance metrics
type CacheAnalytics struct {
	Hits           int     `json:"hits"`
	Misses         int     `json:"misses"`
	SemanticHits   int     `json:"semantic_hits"`
	TokensSaved    int64   `json:"tokens_saved"`
	CostSaved      float64 `json:"cost_saved"`
	AverageLatency int     `json:"average_latency_ms"`
}

// TokenBudget defines limits on token usage for optimization
type TokenBudget struct {
	MaxInputTokens  int `json:"max_input_tokens"`
	MaxOutputTokens int `json:"max_output_tokens"`
	Priority        int `json:"priority"` // 1-10, with 10 being highest priority
}

// InMemoryCache implements a simple in-memory LRU cache
type InMemoryCache struct {
	items map[string]string
	mu    sync.RWMutex
	cap   int
}

// NewInMemoryCache creates a new in-memory cache with the given capacity
func NewInMemoryCache(capacity int) *InMemoryCache {
	return &InMemoryCache{
		items: make(map[string]string, capacity),
		cap:   capacity,
	}
}

// Get retrieves a value from the cache
func (c *InMemoryCache) Get(key string) (string, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	val, found := c.items[key]
	return val, found
}

// Set adds a value to the cache
func (c *InMemoryCache) Set(key, value string, ttl time.Duration) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// If at capacity, remove a random item (simple eviction policy)
	if len(c.items) >= c.cap {
		for k := range c.items {
			delete(c.items, k)
			break
		}
	}

	c.items[key] = value
}

// Delete removes a value from the cache
func (c *InMemoryCache) Delete(key string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	delete(c.items, key)
}

// Policy defines a routing policy.
type Policy struct {
	ID           string          `json:"id"`
	Name         string          `json:"name"`
	Description  string          `json:"description"`
	Criteria     json.RawMessage `json:"criteria"`             // Changed to json.RawMessage to handle array or object
	Action       string          `json:"action"`               // "ROUTE", "OPTIMIZE", "BLOCK", etc.
	BackendID    string          `json:"backend_id,omitempty"` // Only if Action is ROUTE
	Priority     int             `json:"priority"`
	Rules        json.RawMessage `json:"rules,omitempty"` // For more complex routing rules
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`
	Metadata     json.RawMessage `json:"metadata,omitempty"`
	AllowedUsers []string        `json:"allowed_users,omitempty"`
	BlockedUsers []string        `json:"blocked_users,omitempty"`
	RateLimit    int             `json:"rate_limit,omitempty"` // Requests per minute
	Budget       float64         `json:"budget,omitempty"`     // Max cost per time period
}

// ModelProfile defines performance and cost characteristics for a specific model.
type ModelProfile struct {
	ID                 string    `json:"id"`
	Name               string    `json:"name"`
	Aliases            []string  `json:"aliases"`
	Capabilities       []string  `json:"capabilities"`
	PricingTier        string    `json:"pricing_tier"`
	DataSensitivity    string    `json:"data_sensitivity"`
	ExpectedLatencyMs  float64   `json:"expected_latency_ms"`
	ExpectedCost       float64   `json:"expected_cost"`
	BackendURL         string    `json:"url"`
	BackendType        string    `json:"backend_type"` // e.g., "openai", "google", "anthropic", "vllm"
	CostPerInputToken  float64   `json:"cost_per_input_token"`
	CostPerOutputToken float64   `json:"cost_per_output_token"`
	CPUCostPerHour     float64   `json:"cpu_cost_per_hour"`
	MemoryCostPerHour  float64   `json:"memory_cost_per_hour"`
	APIKey             string    `json:"api_key,omitempty"` // API Key for external models
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

// RouteResponse matches the response from the AI Optimizer's /route endpoint.
type AIOptimizerRouteResponse struct {
	SelectedBackendID  string `json:"selected_backend_id"`
	BackendType        string `json:"backend_type"`
	BackendURL         string `json:"backend_url"`
	APIKey             string `json:"api_key,omitempty"`
	PolicyIDApplied    string `json:"policy_id_applied"`
	ModelUsed          string `json:"model_used"`
	TaskType           string `json:"task_type"`
	Error              string `json:"error,omitempty"`
	FromCache          bool   `json:"from_from_cache,omitempty"`      // If AI Optimizer served from its cache
	CachedResponseBody string `json:"cached_response_body,omitempty"` // Cached body if FromCache is true
}

// PromptAnalysis contains intelligent analysis of the user's prompt
type PromptAnalysis struct {
	Intent       string            `json:"intent"`        // question, instruction, creative, analysis, etc.
	TaskType     string            `json:"task_type"`     // code_generation, creative_writing, factual_qa, etc.
	Complexity   string            `json:"complexity"`    // low, medium, high
	Domain       string            `json:"domain"`        // programming, creative, business, academic, etc.
	Keywords     []string          `json:"keywords"`      // extracted key terms
	Confidence   float64           `json:"confidence"`    // 0-1 confidence in analysis
	RequiredCaps []string          `json:"required_caps"` // required model capabilities
	Metadata     map[string]string `json:"metadata"`      // additional context
}

// OptimizationRequest is the payload sent FROM the proxy-gateway TO the AI Optimizer.
// This now includes the PreferredLLMID and PromptAnalysis.
type OptimizationRequest struct {
	Prompt          string          `json:"prompt"`
	Model           string          `json:"model"` // The model requested by the client (e.g., "gpt-3.5-turbo")
	UserID          string          `json:"user_id,omitempty"`
	UserRoles       []string        `json:"user_roles,omitempty"`
	DataSensitivity string          `json:"data_sensitivity,omitempty"`
	PreferredLLMID  string          `json:"preferred_llm_id,omitempty"` // NEW: For X-Preferred-LLM-ID header
	TokenBudget     *TokenBudget    `json:"token_budget,omitempty"`
	Analysis        *PromptAnalysis `json:"analysis,omitempty"` // NEW: Intelligent prompt analysis
	APIType         string          `json:"api_type,omitempty"` // NEW: API type (chat_completions, embeddings, image_generation, etc.)
}

// InferenceLog matches the structure of the log messages sent to Kafka.
// It includes user identification fields for RBAC and auditing.
type InferenceLog struct {
	RequestID           string                 `json:"request_id"`
	Timestamp           time.Time              `json:"timestamp"`
	Method              string                 `json:"method"`
	Path                string                 `json:"path"`
	ClientIP            string                 `json:"client_ip"`
	UserAgent           string                 `json:"user_agent"`
	SelectedBackendID   string                 `json:"selected_backend_id"`
	BackendURL          string                 `json:"backend_url"`
	BackendType         string                 `json:"backend_type,omitempty"`
	RequestHeaders      map[string][]string    `json:"request_headers,omitempty"`
	RequestBodySnippet  string                 `json:"request_body_snippet,omitempty"`
	ResponseTimestamp   time.Time              `json:"response_timestamp"`
	LatencyMs           float64                `json:"latency_ms"`
	StatusCode          int32                  `json:"status_code"`
	ResponseHeaders     map[string][]string    `json:"response_headers,omitempty"`
	ResponseBodySnippet string                 `json:"response_body_snippet,omitempty"`
	InputTokens         int64                  `json:"input_tokens,omitempty"`
	OutputTokens        int64                  `json:"output_tokens,omitempty"`
	TotalCost           float64                `json:"total_cost"` // Renamed from 'Cost' to 'TotalCost' for consistency
	Error               string                 `json:"error,omitempty"`
	PolicyIDApplied     string                 `json:"policy_id_applied,omitempty"`
	ModelRequested      string                 `json:"model_requested,omitempty"`
	ModelUsed           string                 `json:"model_used,omitempty"`
	Stream              bool                   `json:"stream,omitempty"`
	CPUUsage            float64                `json:"cpu_usage_rate"`
	MemoryUsage         float64                `json:"memory_usage_bytes"`
	TaskType            string                 `json:"task_type,omitempty"`
	ConversationID      string                 `json:"conversation_id"`
	UserID              string                 `json:"user_id,omitempty"`              // NEW: User ID from request header
	UserRoles           []string               `json:"user_roles,omitempty"`           // NEW: User roles from request header
	APIType             string                 `json:"api_type,omitempty"`             // NEW: API type (chat_completions, embeddings, etc.)
	APISpecificMetrics  map[string]interface{} `json:"api_specific_metrics,omitempty"` // NEW: API-specific performance metrics
	// A/B Testing fields
	ABTestID      string `json:"ab_test_id,omitempty"`      // A/B test ID if request is part of a test
	ABTestVariant string `json:"ab_test_variant,omitempty"` // A/B test variant ("A" or "B")
}

// ABTestRoute represents routing decision for A/B testing
type ABTestRoute struct {
	TestID      string
	Variant     string // "A" or "B"
	ModelID     string
	BackendURL  string
	BackendType string
	APIKey      string
}

// ABTest represents an A/B test configuration
type ABTest struct {
	ID            string  `json:"id"`
	Name          string  `json:"name"`
	PromptAID     string  `json:"prompt_a_id"`
	PromptBID     string  `json:"prompt_b_id"`
	TrafficSplit  int     `json:"traffic_split"` // Percentage for variant B
	Status        string  `json:"status"`        // "running", "completed", "deployed"
	VariantAScore float64 `json:"variant_a_score"`
	VariantBScore float64 `json:"variant_b_score"`
	VariantAUsage int64   `json:"variant_a_usage"`
	VariantBUsage int64   `json:"variant_b_usage"`
}

// OpenAI API request and response structs for parsing tokens/cost
// (Simplified for relevant fields for logging)
type OpenAIMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type OpenAICompletionRequest struct {
	Model       string          `json:"model"`
	Messages    []OpenAIMessage `json:"messages"`
	MaxTokens   int             `json:"max_tokens,omitempty"`
	Temperature *float64        `json:"temperature,omitempty"`
	Stream      bool            `json:"stream,omitempty"`
}

type OpenAICompletionResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index        int               `json:"index"`
		Message      map[string]string `json:"message"`
		LogProbs     interface{}       `json:"logprobs"`
		FinishReason string            `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

// Google Gemini API request and response structs for parsing tokens/cost
// (These match the expected format for generateContent)
type GeminiPart struct {
	Text string `json:"text"`
}

type GeminiContent struct {
	Parts []GeminiPart `json:"parts"`
	Role  string       `json:"role"` // "user" or "model"
}

type GeminiGenerationConfig struct {
	MaxOutputTokens int `json:"maxOutputTokens,omitempty"`
}

type GeminiGenerateContentRequest struct {
	Contents         []GeminiContent        `json:"contents"`
	GenerationConfig GeminiGenerationConfig `json:"generationConfig,omitempty"`
}

type GeminiGenerateContentResponse struct {
	Candidates []struct {
		Content struct {
			Parts []struct {
				Text string `json:"text"`
			} `json:"parts"`
			Role string `json:"role"`
		} `json:"content"`
		FinishReason string `json:"finishReason"`
		Index        int    `json:"index"`
	} `json:"candidates"`
	UsageMetadata struct {
		PromptTokenCount     int `json:"promptTokenCount"`
		CandidatesTokenCount int `json:"candidatesTokenCount"`
		TotalTokenCount      int `json:"totalTokenCount"`
	} `json:"usageMetadata"`
}

// Anthropic API request and response structs for parsing tokens/cost
type AnthropicMessagesRequest struct {
	Model    string `json:"model"`
	Messages []struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	} `json:"messages"`
	MaxTokens int  `json:"max_tokens"`
	Stream    bool `json:"stream,omitempty"` // Added stream field
}

type AnthropicMessagesResponse struct {
	ID    string `json:"id"`
	Model string `json:"model"`
	Usage struct {
		InputTokens  int `json:"input_tokens"`
		OutputTokens int `json:"output_tokens"`
	} `json:"usage"`
	Content []struct {
		Type string `json:"type"`
		Text string `json:"text"`
	} `json:"content"`
}

// Cohere API request and response structs
type CohereMessage struct {
	Role    string `json:"role"` // "USER" or "CHATBOT"
	Message string `json:"message"`
}

type CohereChatRequest struct {
	Model       string          `json:"model"`
	Message     string          `json:"message"`
	ChatHistory []CohereMessage `json:"chat_history,omitempty"`
	Preamble    string          `json:"preamble,omitempty"` // System message
	Temperature *float64        `json:"temperature,omitempty"`
	MaxTokens   int             `json:"max_tokens,omitempty"`
	Stream      bool            `json:"stream,omitempty"`
}

type CohereChatResponse struct {
	ResponseID   string `json:"response_id"`
	Text         string `json:"text"`
	GenerationID string `json:"generation_id"`
	TokenCount   struct {
		PromptTokens   int `json:"prompt_tokens"`
		ResponseTokens int `json:"response_tokens"`
		TotalTokens    int `json:"total_tokens"`
		BilledTokens   int `json:"billed_tokens"`
	} `json:"token_count"`
	Meta struct {
		APIVersion struct {
			Version string `json:"version"`
		} `json:"api_version"`
		BilledUnits struct {
			InputTokens  int `json:"input_tokens"`
			OutputTokens int `json:"output_tokens"`
		} `json:"billed_units"`
	} `json:"meta"`
}

// Hugging Face Inference API request and response structs
type HuggingFaceMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type HuggingFaceChatRequest struct {
	Model       string               `json:"model"`
	Messages    []HuggingFaceMessage `json:"messages"`
	MaxTokens   int                  `json:"max_tokens,omitempty"`
	Temperature *float64             `json:"temperature,omitempty"`
	Stream      bool                 `json:"stream,omitempty"`
}

type HuggingFaceChatResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

// Mistral AI request and response structs
type MistralMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type MistralChatRequest struct {
	Model       string           `json:"model"`
	Messages    []MistralMessage `json:"messages"`
	MaxTokens   int              `json:"max_tokens,omitempty"`
	Temperature *float64         `json:"temperature,omitempty"`
	Stream      bool             `json:"stream,omitempty"`
}

type MistralChatResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

// GROK (xAI) request and response structs
type GrokMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type GrokChatRequest struct {
	Model       string        `json:"model"`
	Messages    []GrokMessage `json:"messages"`
	MaxTokens   int           `json:"max_tokens,omitempty"`
	Temperature *float64      `json:"temperature,omitempty"`
	Stream      bool          `json:"stream,omitempty"`
}

type GrokChatResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

// --- Global Variables ---
// EmbeddingRequest is the structure for requesting embeddings
type EmbeddingRequest struct {
	Model string   `json:"model"`
	Input []string `json:"input"`
}

// EmbeddingResponse is the structure returned by embedding APIs
type EmbeddingResponse struct {
	Object string `json:"object"`
	Data   []struct {
		Object    string    `json:"object"`
		Embedding []float64 `json:"embedding"`
		Index     int       `json:"index"`
	} `json:"data"`
	Model string `json:"model"`
	Usage struct {
		PromptTokens int `json:"prompt_tokens"`
		TotalTokens  int `json:"total_tokens"`
	} `json:"usage"`
}

// SemanticCacheEntry represents an entry in the semantic cache
type SemanticCacheEntry struct {
	Prompt        string          `json:"prompt"`
	Response      json.RawMessage `json:"response"`
	Embedding     []float64       `json:"embedding"`
	Model         string          `json:"model"`
	Timestamp     time.Time       `json:"timestamp"`
	InputTokens   int64           `json:"input_tokens"`
	OutputTokens  int64           `json:"output_tokens"`
	TotalCost     float64         `json:"total_cost"`
	EmbeddingHash string          `json:"embedding_hash"`
}

var (
	kafkaWriter     *kafka.Writer
	redisClient     *redis.Client
	policies        map[string]Policy
	modelProfiles   map[string]ModelProfile
	promptTemplates map[string]PromptTemplate // Store for prompt templates
	// conversationHistory map[string][]map[string]string // Removed duplicate declaration
	mu             sync.RWMutex
	semanticCache  sync.Map       // Thread-safe map for in-memory semantic cache layer
	embedderLock   sync.Mutex     // Lock for embedder to avoid parallel calls
	inMemoryCache  *gocache.Cache // Fast in-memory cache for hot items
	cacheAnalytics CacheAnalytics // Cache performance analytics
	analyticsLock  sync.RWMutex   // Lock for updating cache analytics
)

// Init function runs once when the application starts
func init() {
	// Initialize Kafka writer
	kafkaWriter = kafka.NewWriter(kafka.WriterConfig{
		Brokers:  []string{kafkaBroker},
		Topic:    kafkaTopic,
		Balancer: &kafka.LeastBytes{},
	})
	log.Printf("Kafka writer initialized for topic %s on brokers %s", kafkaTopic, kafkaBroker)

	// Initialize Redis client
	redisClient = redis.NewClient(&redis.Options{
		Addr: redisAddr,
		DB:   0, // Default DB
	})

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := redisClient.Ping(ctx).Result()
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	log.Println("Successfully connected to Redis.")

	// Initialize caches and data structures
	policies = make(map[string]Policy)
	modelProfiles = make(map[string]ModelProfile)
	promptTemplates = make(map[string]PromptTemplate)
	inMemoryCache = gocache.New(5*time.Minute, 10*time.Minute)

	// Initialize cache analytics
	cacheAnalytics = CacheAnalytics{
		Hits:           0,
		Misses:         0,
		SemanticHits:   0,
		TokensSaved:    0,
		CostSaved:      0.0,
		AverageLatency: 0,
	}

	// Load initial policies and model profiles from Redis
	if err := loadPoliciesFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load policies from Redis on startup: %v", err)
	}
	if err := loadModelProfilesFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load model profiles from Redis on startup: %v", err)
	}

	// Load prompt templates from Redis
	if err := loadPromptTemplatesFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load prompt templates from Redis on startup: %v", err)
	}

	// Load cache analytics from Redis if available
	if err := loadCacheAnalyticsFromRedis(ctx); err != nil {
		log.Printf("Note: No existing cache analytics found in Redis or error loading: %v", err)
	}

	// Start goroutines to listen for Redis Pub/Sub updates
	go listenForRedisPolicyUpdates()
	go listenForRedisModelProfileUpdates()
	go listenForRedisPromptTemplateUpdates()

	// Start periodic cache analytics reporter
	go reportCacheAnalyticsRegularly(ctx)
}

// loadPromptTemplatesFromRedis fetches all prompt templates from Redis
func loadPromptTemplatesFromRedis(ctx context.Context) error {
	mu.Lock()
	defer mu.Unlock()

	newPromptTemplates := make(map[string]PromptTemplate)
	keys, err := redisClient.Keys(ctx, "prompt_template:*").Result()
	if err != nil && err != redis.Nil {
		return fmt.Errorf("failed to get prompt template keys from Redis: %w", err)
	}

	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("Error getting prompt template %s from Redis: %v", key, err)
			continue
		}

		var template PromptTemplate
		if err := json.Unmarshal([]byte(val), &template); err != nil {
			log.Printf("Error unmarshalling prompt template %s: %v", key, err)
			continue
		}

		newPromptTemplates[template.ID] = template
	}

	promptTemplates = newPromptTemplates
	log.Printf("Loaded %d prompt templates from Redis", len(promptTemplates))
	return nil
}

// listenForRedisPromptTemplateUpdates listens for Redis Pub/Sub messages for prompt template updates
func listenForRedisPromptTemplateUpdates() {
	pubsub := redisClient.Subscribe(context.Background(), "prompt_template_updates")
	defer pubsub.Close()
	log.Println("Listening for Redis prompt template updates...")

	for msg := range pubsub.Channel() {
		log.Printf("Received Redis Pub/Sub message for prompt templates: %s", msg.Payload)
		if err := loadPromptTemplatesFromRedis(context.Background()); err != nil {
			log.Printf("Error reloading prompt templates from Redis: %v", err)
		} else {
			log.Println("Prompt templates cache refreshed due to pending updates")
		}
	}
}

// loadCacheAnalyticsFromRedis loads cache analytics data from Redis
func loadCacheAnalyticsFromRedis(ctx context.Context) error {
	analyticsLock.Lock()
	defer analyticsLock.Unlock()

	// Get cache hit count
	hits, err := redisClient.Get(ctx, CACHE_HIT_COUNTER_KEY).Int()
	if err != nil && err != redis.Nil {
		return err
	} else if err == nil {
		cacheAnalytics.Hits = hits
	}

	// Get cache miss count
	misses, err := redisClient.Get(ctx, CACHE_MISS_COUNTER_KEY).Int()
	if err != nil && err != redis.Nil {
		return err
	} else if err == nil {
		cacheAnalytics.Misses = misses
	}

	// Get semantic cache hit count
	semanticHits, err := redisClient.Get(ctx, SEMANTIC_CACHE_HIT_COUNTER_KEY).Int()
	if err != nil && err != redis.Nil {
		return err
	} else if err == nil {
		cacheAnalytics.SemanticHits = semanticHits
	}

	// Get other analytics if they exist
	tokensStr, err := redisClient.Get(ctx, CACHE_ANALYTICS_PREFIX+"tokens_saved").Result()
	if err == nil {
		if tokens, parseErr := strconv.ParseInt(tokensStr, 10, 64); parseErr == nil {
			cacheAnalytics.TokensSaved = tokens
		}
	}

	costStr, err := redisClient.Get(ctx, CACHE_ANALYTICS_PREFIX+"cost_saved").Result()
	if err == nil {
		if cost, parseErr := strconv.ParseFloat(costStr, 64); parseErr == nil {
			cacheAnalytics.CostSaved = cost
		}
	}

	log.Printf("Loaded cache analytics: %d hits, %d misses, %d semantic hits, %d tokens saved, $%.2f cost saved",
		cacheAnalytics.Hits, cacheAnalytics.Misses, cacheAnalytics.SemanticHits,
		cacheAnalytics.TokensSaved, cacheAnalytics.CostSaved)

	return nil
}

// pruneSemanticCache removes old entries from the semantic cache.
func pruneSemanticCache() {
	log.Println("Pruning semantic cache...")
	var keysToDelete []string
	semanticCache.Range(func(key, value interface{}) bool {
		entry, ok := value.(SemanticCacheEntry)
		if !ok {
			log.Printf("Unexpected type in semantic cache: %T", value)
			return true // Continue to next entry
		}
		if time.Since(entry.Timestamp) > SEMANTIC_CACHE_TTL {
			keysToDelete = append(keysToDelete, key.(string))
		}
		return true // Continue to next entry
	})

	for _, key := range keysToDelete {
		log.Printf("Deleting expired entry from semantic cache: %s", key)
		semanticCache.Delete(key)
	}
	log.Printf("Pruned %d expired entries from semantic cache.", len(keysToDelete))
}

// reportCacheAnalyticsRegularly periodically saves cache analytics to Redis
// and performs cache maintenance
func reportCacheAnalyticsRegularly(ctx context.Context) {
	ticker := time.NewTicker(15 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			saveCacheAnalyticsToRedis(ctx)
			// Also prune the semantic cache to remove old entries
			pruneSemanticCache()
		case <-ctx.Done():
			return
		}
	}
}

// saveCacheAnalyticsToRedis saves the current cache analytics to Redis
func saveCacheAnalyticsToRedis(ctx context.Context) {
	analyticsLock.Lock()
	defer analyticsLock.Unlock()

	pipe := redisClient.Pipeline()

	// Save all analytics metrics
	pipe.Set(ctx, CACHE_HIT_COUNTER_KEY, cacheAnalytics.Hits, CACHE_ANALYTICS_TTL)
	pipe.Set(ctx, CACHE_MISS_COUNTER_KEY, cacheAnalytics.Misses, CACHE_ANALYTICS_TTL)
	pipe.Set(ctx, SEMANTIC_CACHE_HIT_COUNTER_KEY, cacheAnalytics.SemanticHits, CACHE_ANALYTICS_TTL)
	pipe.Set(ctx, CACHE_ANALYTICS_PREFIX+"tokens_saved", cacheAnalytics.TokensSaved, CACHE_ANALYTICS_TTL)
	pipe.Set(ctx, CACHE_ANALYTICS_PREFIX+"cost_saved", cacheAnalytics.CostSaved, CACHE_ANALYTICS_TTL)
	pipe.Set(ctx, CACHE_ANALYTICS_PREFIX+"average_latency", cacheAnalytics.AverageLatency, CACHE_ANALYTICS_TTL)

	// Calculate and save cache hit rate
	totalRequests := cacheAnalytics.Hits + cacheAnalytics.Misses + cacheAnalytics.SemanticHits
	var hitRate float64 = 0
	if totalRequests > 0 {
		hitRate = float64(cacheAnalytics.Hits+cacheAnalytics.SemanticHits) / float64(totalRequests)
	}
	pipe.Set(ctx, CACHE_ANALYTICS_PREFIX+"hit_rate", hitRate, CACHE_ANALYTICS_TTL)

	// Execute all commands
	_, err := pipe.Exec(ctx)
	if err != nil {
		log.Printf("Error saving cache analytics to Redis: %v", err)
	} else {
		log.Printf("Cache analytics saved to Redis: %d hits, %d misses, %.2f%% hit rate, $%.2f saved",
			cacheAnalytics.Hits, cacheAnalytics.Misses, hitRate*100, cacheAnalytics.CostSaved)
	}
}

// updateCacheAnalytics updates cache analytics for hits and misses
func updateCacheAnalytics(isHit bool, costSaved float64) {
	analyticsLock.Lock()
	defer analyticsLock.Unlock()

	if isHit {
		cacheAnalytics.Hits++
		cacheAnalytics.CostSaved += costSaved
	} else {
		cacheAnalytics.Misses++
	}
}

// --- Governance Integration Functions ---

// applyGovernancePolicies checks if the request complies with governance policies
func applyGovernancePolicies(r *http.Request, apiType, model, content string) error {
	// Extract user information
	userID := r.Header.Get("X-User-ID")
	userRoles := strings.Split(r.Header.Get("X-User-Roles"), ",")
	dataSensitivity := r.Header.Get("X-Data-Sensitivity")

	// Check for high-risk content patterns
	if err := checkContentRisks(content, apiType); err != nil {
		return fmt.Errorf("content risk detected: %w", err)
	}

	// Check data sensitivity policies
	if err := checkDataSensitivityPolicies(dataSensitivity, apiType, userRoles); err != nil {
		return fmt.Errorf("data sensitivity policy violation: %w", err)
	}

	// Check model access policies
	if err := checkModelAccessPolicies(model, userID, userRoles); err != nil {
		return fmt.Errorf("model access policy violation: %w", err)
	}

	// Log governance check for audit trail
	logGovernanceCheck(userID, apiType, model, "allowed", "")

	return nil
}

// checkContentRisks analyzes content for potential risks
func checkContentRisks(content, apiType string) error {
	// Basic content risk patterns
	riskPatterns := []string{
		"(?i)(hack|exploit|vulnerability)",
		"(?i)(illegal|criminal|fraud)",
		"(?i)(violence|harm|threat)",
		"(?i)(personal.*information|ssn|credit.*card)",
	}

	for _, pattern := range riskPatterns {
		if matched, _ := regexp.MatchString(pattern, content); matched {
			return fmt.Errorf("high-risk content pattern detected")
		}
	}

	// API-specific content checks
	switch apiType {
	case "image_generation":
		// Check for inappropriate image generation requests
		inappropriatePatterns := []string{
			"(?i)(nude|naked|explicit)",
			"(?i)(violence|gore|disturbing)",
			"(?i)(illegal|drugs|weapons)",
		}
		for _, pattern := range inappropriatePatterns {
			if matched, _ := regexp.MatchString(pattern, content); matched {
				return fmt.Errorf("inappropriate image generation request")
			}
		}
	}

	return nil
}

// checkDataSensitivityPolicies validates data sensitivity compliance
func checkDataSensitivityPolicies(dataSensitivity, apiType string, userRoles []string) error {
	// High sensitivity data requires special roles
	if dataSensitivity == "high" || dataSensitivity == "critical" {
		hasPermission := false
		for _, role := range userRoles {
			if role == "data-admin" || role == "security-admin" || role == "admin" {
				hasPermission = true
				break
			}
		}
		if !hasPermission {
			return fmt.Errorf("insufficient permissions for %s data sensitivity", dataSensitivity)
		}
	}

	// Certain API types may have restrictions for sensitive data
	if dataSensitivity == "critical" && apiType == "image_generation" {
		return fmt.Errorf("image generation not allowed for critical data")
	}

	return nil
}

// checkModelAccessPolicies validates model access permissions
func checkModelAccessPolicies(model, userID string, userRoles []string) error {
	// Example: Restrict certain models to specific roles
	restrictedModels := map[string][]string{
		"gpt-4":    {"premium-user", "admin", "developer"},
		"claude-3": {"premium-user", "admin"},
		"dall-e-3": {"image-admin", "admin", "creative-user"},
	}

	if requiredRoles, isRestricted := restrictedModels[model]; isRestricted {
		hasAccess := false
		for _, userRole := range userRoles {
			for _, requiredRole := range requiredRoles {
				if userRole == requiredRole {
					hasAccess = true
					break
				}
			}
			if hasAccess {
				break
			}
		}
		if !hasAccess {
			// Log the governance decision for audit trail
			logGovernanceCheck(userID, "model_access", model, "denied", "insufficient permissions")
			return fmt.Errorf("insufficient permissions for model %s", model)
		}
		// Log successful access
		logGovernanceCheck(userID, "model_access", model, "allowed", "user has required permissions")
	}

	return nil
}

// logGovernanceCheck logs governance decisions for audit trail
func logGovernanceCheck(userID, apiType, model, decision, reason string) {
	governanceLog := map[string]interface{}{
		"timestamp": time.Now(),
		"user_id":   userID,
		"api_type":  apiType,
		"model":     model,
		"decision":  decision,
		"reason":    reason,
		"service":   "proxy-gateway",
	}

	logData, _ := json.Marshal(governanceLog)
	log.Printf("Governance Check: %s", string(logData))

	// In a production system, you would also send this to the governance service
	// and/or a dedicated audit logging system
}

// analyzePromptIntelligently uses an external LLM to analyze the prompt and extract semantic information
func analyzePromptIntelligently(prompt string) (*PromptAnalysis, error) {
	if !ENABLE_PROMPT_ANALYSIS || len(prompt) < ANALYSIS_MIN_PROMPT_LENGTH {
		return nil, nil // Skip analysis for short prompts or if disabled
	}

	// Check cache first
	cacheKey := fmt.Sprintf("analysis:%s", hashString(prompt))
	if cachedResult, found := getFromCache(cacheKey); found {
		if analysis, ok := cachedResult.(PromptAnalysis); ok {
			return &analysis, nil
		}
	}

	// Enhanced analysis prompt for better task type detection
	analysisPrompt := fmt.Sprintf(`You are a prompt analysis expert. Analyze the user prompt and return ONLY a valid JSON object.

User Prompt: "%s"

CRITICAL: Your response must be ONLY a valid JSON object with no additional text, explanations, or markdown formatting.

Return this exact JSON structure:
{
  "intent": "question|instruction|creative|analysis|conversation|request|command",
  "task_type": "code_generation|creative_writing|factual_qa|reasoning|summarization|translation|mathematical|conversational|data_analysis|research|planning|optimization|debugging|explanation|comparison|classification|extraction|generation|multimodal|other",
  "complexity": "low|medium|high",
  "domain": "programming|creative|business|academic|technical|general|science|entertainment|data|finance|healthcare|education|legal|marketing",
  "keywords": ["key", "terms", "from", "prompt"],
  "confidence": 0.95,
  "required_caps": ["reasoning", "code_understanding", "creativity", "factual_knowledge", "analysis", "planning"],
  "metadata": {"language": "en", "urgency": "normal", "format": "text", "estimated_tokens": 100}
}

IMPORTANT: Return ONLY the JSON object above with appropriate values filled in. No other text.`, prompt)

	// Prepare request to analysis LLM (Gemini format)
	analysisReq := map[string]interface{}{
		"contents": []map[string]interface{}{
			{
				"parts": []map[string]string{
					{"text": "You are an expert prompt analyzer. Analyze user prompts and return structured JSON analysis. Be accurate and concise.\n\n" + analysisPrompt},
				},
			},
		},
		"generationConfig": map[string]interface{}{
			"temperature":      0.1, // Low temperature for consistent analysis
			"maxOutputTokens":  500,
			"responseMimeType": "application/json", // Request JSON response
		},
	}

	// Make request to analysis LLM
	reqBody, err := json.Marshal(analysisReq)
	if err != nil {
		return nil, fmt.Errorf("error marshaling analysis request: %w", err)
	}

	req, err := http.NewRequest("POST", ANALYSIS_LLM_ENDPOINT, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("error creating analysis request: %w", err)
	}

	// Set headers (use Google API key from environment)
	req.Header.Set("Content-Type", "application/json")
	if apiKey := os.Getenv("GOOGLE_API_KEY"); apiKey != "" {
		// For Gemini API, add API key as query parameter
		q := req.URL.Query()
		q.Add("key", apiKey)
		req.URL.RawQuery = q.Encode()
	}

	// Make the request with timeout
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Warning: Error calling analysis LLM: %v", err)
		if ANALYSIS_FALLBACK_ENABLED {
			return createFallbackAnalysis(prompt), nil
		}
		return nil, fmt.Errorf("error calling analysis LLM: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Printf("Warning: Analysis LLM returned status %d", resp.StatusCode)
		if ANALYSIS_FALLBACK_ENABLED {
			return createFallbackAnalysis(prompt), nil
		}
		return nil, fmt.Errorf("analysis LLM returned status %d", resp.StatusCode)
	}

	// Parse response
	var llmResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&llmResp); err != nil {
		return nil, fmt.Errorf("error decoding analysis response: %w", err)
	}

	// Extract the analysis from the Gemini response
	candidates, ok := llmResp["candidates"].([]interface{})
	if !ok || len(candidates) == 0 {
		return nil, fmt.Errorf("invalid analysis response format: no candidates")
	}

	candidate, ok := candidates[0].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid candidate format in analysis response")
	}

	content, ok := candidate["content"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid content format in analysis response")
	}

	parts, ok := content["parts"].([]interface{})
	if !ok || len(parts) == 0 {
		return nil, fmt.Errorf("invalid parts format in analysis response")
	}

	part, ok := parts[0].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid part format in analysis response")
	}

	text, ok := part["text"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid text format in analysis response")
	}

	// Parse the JSON analysis from the LLM
	var analysis PromptAnalysis

	// Clean the response text to handle common LLM response formats
	cleanedText := cleanLLMResponseForJSON(text)

	// Log the raw response for debugging (truncated)
	if len(text) > 200 {
		log.Printf("LLM analysis response (truncated): %s...", text[:200])
	} else {
		log.Printf("LLM analysis response: %s", text)
	}

	if err := json.Unmarshal([]byte(cleanedText), &analysis); err != nil {
		// Fallback to basic analysis if JSON parsing fails
		log.Printf("Warning: Failed to parse LLM analysis JSON, using fallback: %v", err)
		log.Printf("Cleaned text that failed to parse: %s", cleanedText)
		if ANALYSIS_FALLBACK_ENABLED {
			return createFallbackAnalysis(prompt), nil
		}
		return nil, fmt.Errorf("failed to parse LLM analysis and fallback disabled: %w", err)
	}

	// Validate the analysis results
	if analysis.TaskType == "" {
		analysis.TaskType = "other"
	}
	if analysis.Confidence == 0 {
		analysis.Confidence = 0.8
	}
	if len(analysis.RequiredCaps) == 0 {
		analysis.RequiredCaps = []string{"reasoning"}
	}

	// Cache the result
	storeInCache(cacheKey, analysis, ANALYSIS_CACHE_TTL)

	log.Printf("LLM analysis completed - TaskType: %s, Confidence: %.2f, Domain: %s",
		analysis.TaskType, analysis.Confidence, analysis.Domain)

	return &analysis, nil
}

// cleanLLMResponseForJSON cleans LLM response text to extract valid JSON
func cleanLLMResponseForJSON(text string) string {
	// Remove common prefixes and suffixes that LLMs might add
	text = strings.TrimSpace(text)

	// Remove markdown code blocks if present
	if strings.HasPrefix(text, "```json") {
		text = strings.TrimPrefix(text, "```json")
		text = strings.TrimSuffix(text, "```")
		text = strings.TrimSpace(text)
	} else if strings.HasPrefix(text, "```") {
		text = strings.TrimPrefix(text, "```")
		text = strings.TrimSuffix(text, "```")
		text = strings.TrimSpace(text)
	}

	// Remove common prefixes that LLMs might add
	prefixesToRemove := []string{
		"Here is the JSON analysis:",
		"Here's the analysis:",
		"Analysis:",
		"JSON:",
		"Result:",
		"Response:",
	}

	for _, prefix := range prefixesToRemove {
		if strings.HasPrefix(text, prefix) {
			text = strings.TrimPrefix(text, prefix)
			text = strings.TrimSpace(text)
			break
		}
	}

	// Find the first { and last } to extract JSON object
	firstBrace := strings.Index(text, "{")
	lastBrace := strings.LastIndex(text, "}")

	if firstBrace != -1 && lastBrace != -1 && lastBrace > firstBrace {
		text = text[firstBrace : lastBrace+1]
	}

	return strings.TrimSpace(text)
}

// createFallbackAnalysis creates an enhanced analysis when LLM analysis fails
func createFallbackAnalysis(prompt string) *PromptAnalysis {
	analysis := &PromptAnalysis{
		Intent:       "instruction",
		TaskType:     "other",
		Complexity:   "medium",
		Domain:       "general",
		Keywords:     extractBasicKeywords(prompt),
		Confidence:   0.5, // Low confidence for fallback
		RequiredCaps: []string{"general_knowledge"},
		Metadata: map[string]string{
			"analysis_type": "fallback",
			"language":      "en",
		},
	}

	// Enhanced keyword-based classification
	promptLower := strings.ToLower(prompt)

	// Detect task type with more comprehensive patterns
	if strings.Contains(promptLower, "code") || strings.Contains(promptLower, "function") ||
		strings.Contains(promptLower, "python") || strings.Contains(promptLower, "javascript") ||
		strings.Contains(promptLower, "programming") || strings.Contains(promptLower, "debug") ||
		strings.Contains(promptLower, "algorithm") || strings.Contains(promptLower, "script") {
		analysis.TaskType = "code_generation"
		analysis.Domain = "programming"
		analysis.RequiredCaps = []string{"code_understanding", "programming_knowledge"}
	} else if strings.Contains(promptLower, "write") || strings.Contains(promptLower, "story") ||
		strings.Contains(promptLower, "creative") || strings.Contains(promptLower, "poem") ||
		strings.Contains(promptLower, "novel") || strings.Contains(promptLower, "fiction") {
		analysis.TaskType = "creative_writing"
		analysis.Domain = "creative"
		analysis.RequiredCaps = []string{"creativity", "language_generation"}
	} else if strings.Contains(promptLower, "what") || strings.Contains(promptLower, "how") ||
		strings.Contains(promptLower, "why") || strings.Contains(promptLower, "explain") ||
		strings.Contains(promptLower, "define") || strings.Contains(promptLower, "describe") {
		analysis.Intent = "question"
		analysis.TaskType = "factual_qa"
		analysis.RequiredCaps = []string{"factual_knowledge", "reasoning"}
	} else if strings.Contains(promptLower, "summarize") || strings.Contains(promptLower, "summary") ||
		strings.Contains(promptLower, "brief") || strings.Contains(promptLower, "tldr") {
		analysis.TaskType = "summarization"
		analysis.RequiredCaps = []string{"comprehension", "summarization"}
	} else if strings.Contains(promptLower, "analyze") || strings.Contains(promptLower, "analysis") ||
		strings.Contains(promptLower, "data") || strings.Contains(promptLower, "insights") {
		analysis.TaskType = "data_analysis"
		analysis.Domain = "data"
		analysis.RequiredCaps = []string{"analysis", "reasoning", "data_processing"}
	} else if strings.Contains(promptLower, "translate") || strings.Contains(promptLower, "translation") {
		analysis.TaskType = "translation"
		analysis.RequiredCaps = []string{"language_understanding", "translation"}
	} else if strings.Contains(promptLower, "plan") || strings.Contains(promptLower, "planning") ||
		strings.Contains(promptLower, "strategy") || strings.Contains(promptLower, "roadmap") {
		analysis.TaskType = "planning"
		analysis.RequiredCaps = []string{"planning", "reasoning", "strategy"}
	} else if strings.Contains(promptLower, "compare") || strings.Contains(promptLower, "comparison") ||
		strings.Contains(promptLower, "versus") || strings.Contains(promptLower, "vs") {
		analysis.TaskType = "comparison"
		analysis.RequiredCaps = []string{"analysis", "reasoning", "comparison"}
	} else if strings.Contains(promptLower, "classify") || strings.Contains(promptLower, "categorize") ||
		strings.Contains(promptLower, "category") || strings.Contains(promptLower, "type") {
		analysis.TaskType = "classification"
		analysis.RequiredCaps = []string{"classification", "reasoning"}
	}

	// Assess complexity with more factors
	complexityFactors := 0
	if len(prompt) > 500 {
		complexityFactors++
	}
	if strings.Contains(promptLower, "complex") || strings.Contains(promptLower, "detailed") ||
		strings.Contains(promptLower, "comprehensive") || strings.Contains(promptLower, "thorough") {
		complexityFactors++
	}
	if strings.Contains(promptLower, "step by step") || strings.Contains(promptLower, "multiple") ||
		strings.Contains(promptLower, "various") || strings.Contains(promptLower, "different") {
		complexityFactors++
	}

	if complexityFactors >= 2 {
		analysis.Complexity = "high"
	} else if complexityFactors == 1 || len(prompt) > 100 {
		analysis.Complexity = "medium"
	} else {
		analysis.Complexity = "low"
	}

	// Detect domain more accurately
	if strings.Contains(promptLower, "business") || strings.Contains(promptLower, "marketing") ||
		strings.Contains(promptLower, "sales") || strings.Contains(promptLower, "finance") {
		analysis.Domain = "business"
	} else if strings.Contains(promptLower, "science") || strings.Contains(promptLower, "research") ||
		strings.Contains(promptLower, "academic") || strings.Contains(promptLower, "study") {
		analysis.Domain = "academic"
	} else if strings.Contains(promptLower, "technical") || strings.Contains(promptLower, "engineering") ||
		strings.Contains(promptLower, "system") || strings.Contains(promptLower, "architecture") {
		analysis.Domain = "technical"
	}

	return analysis
}

// extractBasicKeywords extracts important keywords from the prompt
func extractBasicKeywords(prompt string) []string {
	// Simple keyword extraction - remove common words and extract meaningful terms
	words := strings.Fields(strings.ToLower(prompt))
	stopWords := map[string]bool{
		"the": true, "a": true, "an": true, "and": true, "or": true, "but": true,
		"in": true, "on": true, "at": true, "to": true, "for": true, "of": true,
		"with": true, "by": true, "is": true, "are": true, "was": true, "were": true,
		"be": true, "been": true, "have": true, "has": true, "had": true, "do": true,
		"does": true, "did": true, "will": true, "would": true, "could": true, "should": true,
		"can": true, "may": true, "might": true, "must": true, "please": true, "i": true,
		"you": true, "he": true, "she": true, "it": true, "we": true, "they": true,
	}

	var keywords []string
	for _, word := range words {
		// Remove punctuation
		word = strings.Trim(word, ".,!?;:\"'()[]{}")
		if len(word) > 2 && !stopWords[word] {
			keywords = append(keywords, word)
		}
	}

	// Limit to top 10 keywords
	if len(keywords) > 10 {
		keywords = keywords[:10]
	}

	return keywords
}

// hashString creates a hash of the string for caching
func hashString(s string) string {
	h := sha256.Sum256([]byte(s))
	return fmt.Sprintf("%x", h)[:16] // Use first 16 chars of hash
}

// getFromCache retrieves an item from the analysis cache
func getFromCache(key string) (interface{}, bool) {
	// Use Redis for caching analysis results
	if redisClient != nil {
		val, err := redisClient.Get(context.Background(), key).Result()
		if err == nil {
			var result PromptAnalysis
			if err := json.Unmarshal([]byte(val), &result); err == nil {
				return result, true
			}
		}
	}
	return nil, false
}

// storeInCache stores an item in the analysis cache
func storeInCache(key string, value interface{}, ttlSeconds int) {
	// Use Redis for caching analysis results
	if redisClient != nil {
		if data, err := json.Marshal(value); err == nil {
			redisClient.Set(context.Background(), key, data, time.Duration(ttlSeconds)*time.Second)
		}
	}
}

// --- New LLM API Handlers ---

// handleCompletionsRequest handles the legacy /v1/completions endpoint
func handleCompletionsRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// For now, redirect to chat completions with a simple conversion
	// In a full implementation, you'd handle the different request format
	log.Printf("Completions request received, converting to chat completions format")

	// Read the original request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Error reading request body", http.StatusBadRequest)
		return
	}

	// Parse the completions request
	var completionsReq map[string]interface{}
	if err := json.Unmarshal(body, &completionsReq); err != nil {
		http.Error(w, "Invalid JSON in request body", http.StatusBadRequest)
		return
	}

	// Convert to chat completions format
	prompt, ok := completionsReq["prompt"].(string)
	if !ok {
		http.Error(w, "Missing or invalid prompt field", http.StatusBadRequest)
		return
	}

	// Create chat completions request
	chatReq := map[string]interface{}{
		"model": completionsReq["model"],
		"messages": []map[string]string{
			{"role": "user", "content": prompt},
		},
	}

	// Copy other parameters
	for key, value := range completionsReq {
		if key != "prompt" && key != "model" {
			chatReq[key] = value
		}
	}

	// Convert back to JSON
	chatReqBody, err := json.Marshal(chatReq)
	if err != nil {
		http.Error(w, "Error converting request", http.StatusInternalServerError)
		return
	}

	// Create new request with chat completions body
	r.Body = io.NopCloser(bytes.NewReader(chatReqBody))
	r.URL.Path = "/v1/chat/completions"

	// Forward to chat completions handler
	handleLLMRequest(w, r)
}

// handleEmbeddingsRequest handles the /v1/embeddings endpoint
func handleEmbeddingsRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	requestID := uuid.New().String()
	log.Printf("Request %s: Embeddings request received", requestID)

	// Create a generic LLM request for routing
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Error reading request body", http.StatusBadRequest)
		return
	}

	var embeddingReq map[string]interface{}
	if err := json.Unmarshal(body, &embeddingReq); err != nil {
		http.Error(w, "Invalid JSON in request body", http.StatusBadRequest)
		return
	}

	// Extract input for caching
	input, _ := embeddingReq["input"].(string)
	if inputArray, ok := embeddingReq["input"].([]interface{}); ok && len(inputArray) > 0 {
		if firstInput, ok := inputArray[0].(string); ok {
			input = firstInput
		}
	}

	model := getString(embeddingReq, "model")

	// Check cache for embeddings
	ctx := context.Background()
	cacheKey := getCacheKeyWithAPIType(input, model, "embeddings")

	if cachedResponse, err := getLLMResponseFromCache(ctx, cacheKey); err == nil && cachedResponse != nil {
		log.Printf("Request %s: Cache hit for embeddings request", requestID)
		w.Header().Set("Content-Type", "application/json")
		w.Header().Set("X-Cache-Status", "hit")
		w.Write(cachedResponse)

		// Update cache analytics
		updateCacheAnalytics(true, 0.0) // Embeddings typically have lower cost than chat
		return
	}

	// Apply governance policies for embeddings
	if err := applyGovernancePolicies(r, "embeddings", model, input); err != nil {
		log.Printf("Request %s: Governance policy violation for embeddings: %v", requestID, err)
		http.Error(w, "Request blocked by governance policy: "+err.Error(), http.StatusForbidden)
		return
	}

	// Route through AI optimizer for embeddings
	routeResp, err := routeEmbeddingRequest(requestID, embeddingReq, r)
	if err != nil {
		log.Printf("Request %s: Error routing embeddings request: %v", requestID, err)
		http.Error(w, "Error routing request", http.StatusInternalServerError)
		return
	}

	// Forward to selected backend
	forwardEmbeddingRequest(w, r, routeResp, body, requestID, cacheKey)
}

// handleImageGenerationRequest handles the /v1/images/generations endpoint
func handleImageGenerationRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	requestID := uuid.New().String()
	log.Printf("Request %s: Image generation request received", requestID)

	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Error reading request body", http.StatusBadRequest)
		return
	}

	var imageReq map[string]interface{}
	if err := json.Unmarshal(body, &imageReq); err != nil {
		http.Error(w, "Invalid JSON in request body", http.StatusBadRequest)
		return
	}

	// Extract prompt for governance check
	prompt := getString(imageReq, "prompt")
	model := getString(imageReq, "model")

	// Apply governance policies for image generation
	if err := applyGovernancePolicies(r, "image_generation", model, prompt); err != nil {
		log.Printf("Request %s: Governance policy violation for image generation: %v", requestID, err)
		http.Error(w, "Request blocked by governance policy: "+err.Error(), http.StatusForbidden)
		return
	}

	// Route through AI optimizer for image generation
	routeResp, err := routeImageRequest(requestID, imageReq, r)
	if err != nil {
		log.Printf("Request %s: Error routing image request: %v", requestID, err)
		http.Error(w, "Error routing request", http.StatusInternalServerError)
		return
	}

	// Forward to selected backend
	forwardImageRequest(w, r, routeResp, body, requestID)
}

// handleAudioSpeechRequest handles the /v1/audio/speech endpoint
func handleAudioSpeechRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	requestID := uuid.New().String()
	log.Printf("Request %s: Audio speech request received", requestID)

	// For now, return a simple response indicating the feature is available
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message":    "Audio speech generation endpoint available",
		"status":     "not_implemented",
		"request_id": requestID,
	})
}

// handleAudioTranscriptionRequest handles the /v1/audio/transcriptions endpoint
func handleAudioTranscriptionRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	requestID := uuid.New().String()
	log.Printf("Request %s: Audio transcription request received", requestID)

	// For now, return a simple response indicating the feature is available
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message":    "Audio transcription endpoint available",
		"status":     "not_implemented",
		"request_id": requestID,
	})
}

// handleAudioTranslationRequest handles the /v1/audio/translations endpoint
func handleAudioTranslationRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	requestID := uuid.New().String()
	log.Printf("Request %s: Audio translation request received", requestID)

	// For now, return a simple response indicating the feature is available
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message":    "Audio translation endpoint available",
		"status":     "not_implemented",
		"request_id": requestID,
	})
}

// handleModerationsRequest handles the /v1/moderations endpoint
func handleModerationsRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	requestID := uuid.New().String()
	log.Printf("Request %s: Moderation request received", requestID)

	// For now, return a simple response indicating the feature is available
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message":    "Content moderation endpoint available",
		"status":     "not_implemented",
		"request_id": requestID,
	})
}

// handleFineTuningRequest handles the /v1/fine-tuning/jobs endpoint
func handleFineTuningRequest(w http.ResponseWriter, r *http.Request) {
	requestID := uuid.New().String()
	log.Printf("Request %s: Fine-tuning request received", requestID)

	// For now, return a simple response indicating the feature is available
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message":    "Fine-tuning endpoint available",
		"status":     "not_implemented",
		"request_id": requestID,
	})
}

// --- Routing and Forwarding Functions for New API Types ---

// routeEmbeddingRequest routes an embedding request through the AI optimizer
func routeEmbeddingRequest(requestID string, embeddingReq map[string]interface{}, r *http.Request) (AIOptimizerRouteResponse, error) {
	// Log the request for tracing
	log.Printf("Processing embedding request %s", requestID)

	// Extract input for routing decision
	input, _ := embeddingReq["input"].(string)
	if inputArray, ok := embeddingReq["input"].([]interface{}); ok && len(inputArray) > 0 {
		if firstInput, ok := inputArray[0].(string); ok {
			input = firstInput
		}
	}

	// Analyze the input for embeddings
	var promptAnalysis *PromptAnalysis
	if ENABLE_PROMPT_ANALYSIS && len(input) >= ANALYSIS_MIN_PROMPT_LENGTH {
		if analysis, err := analyzePromptIntelligently(input); err == nil && analysis != nil {
			promptAnalysis = analysis
		}
	}

	// Create optimization request for embeddings
	optReq := OptimizationRequest{
		Prompt:          input,
		Model:           getString(embeddingReq, "model"),
		UserID:          r.Header.Get("X-User-ID"),
		UserRoles:       strings.Split(r.Header.Get("X-User-Roles"), ","),
		DataSensitivity: r.Header.Get("X-Data-Sensitivity"),
		PreferredLLMID:  r.Header.Get("X-Preferred-LLM-ID"),
		APIType:         "embeddings",   // New field to indicate API type
		Analysis:        promptAnalysis, // Include prompt analysis
	}

	optReqJSON, err := json.Marshal(optReq)
	if err != nil {
		return AIOptimizerRouteResponse{}, fmt.Errorf("error marshaling optimization request: %w", err)
	}

	// Call AI Optimizer
	aiOptResp, err := http.Post(aiOptimizerURL+"/route", "application/json", bytes.NewBuffer(optReqJSON))
	if err != nil {
		return AIOptimizerRouteResponse{}, fmt.Errorf("error calling AI Optimizer: %w", err)
	}
	defer aiOptResp.Body.Close()

	var routeResp AIOptimizerRouteResponse
	if err := json.NewDecoder(aiOptResp.Body).Decode(&routeResp); err != nil {
		return AIOptimizerRouteResponse{}, fmt.Errorf("error decoding AI Optimizer response: %w", err)
	}

	return routeResp, nil
}

// routeImageRequest routes an image generation request through the AI optimizer
func routeImageRequest(requestID string, imageReq map[string]interface{}, r *http.Request) (AIOptimizerRouteResponse, error) {
	// Log the request for tracing
	log.Printf("Processing image generation request %s", requestID)

	// Extract prompt for routing decision
	prompt, _ := imageReq["prompt"].(string)

	// Analyze the prompt for image generation
	var promptAnalysis *PromptAnalysis
	if ENABLE_PROMPT_ANALYSIS && len(prompt) >= ANALYSIS_MIN_PROMPT_LENGTH {
		if analysis, err := analyzePromptIntelligently(prompt); err == nil && analysis != nil {
			promptAnalysis = analysis
			// Override task type for image generation
			promptAnalysis.TaskType = "image_generation"
			promptAnalysis.RequiredCaps = append(promptAnalysis.RequiredCaps, "image_generation", "visual_creativity")
		}
	}

	// Create optimization request for image generation
	optReq := OptimizationRequest{
		Prompt:          prompt,
		Model:           getString(imageReq, "model"),
		UserID:          r.Header.Get("X-User-ID"),
		UserRoles:       strings.Split(r.Header.Get("X-User-Roles"), ","),
		DataSensitivity: r.Header.Get("X-Data-Sensitivity"),
		PreferredLLMID:  r.Header.Get("X-Preferred-LLM-ID"),
		APIType:         "image_generation", // New field to indicate API type
		Analysis:        promptAnalysis,     // Include prompt analysis
	}

	optReqJSON, err := json.Marshal(optReq)
	if err != nil {
		return AIOptimizerRouteResponse{}, fmt.Errorf("error marshaling optimization request: %w", err)
	}

	// Call AI Optimizer
	aiOptResp, err := http.Post(aiOptimizerURL+"/route", "application/json", bytes.NewBuffer(optReqJSON))
	if err != nil {
		return AIOptimizerRouteResponse{}, fmt.Errorf("error calling AI Optimizer: %w", err)
	}
	defer aiOptResp.Body.Close()

	var routeResp AIOptimizerRouteResponse
	if err := json.NewDecoder(aiOptResp.Body).Decode(&routeResp); err != nil {
		return AIOptimizerRouteResponse{}, fmt.Errorf("error decoding AI Optimizer response: %w", err)
	}

	return routeResp, nil
}

// forwardEmbeddingRequest forwards an embedding request to the selected backend
func forwardEmbeddingRequest(w http.ResponseWriter, r *http.Request, routeResp AIOptimizerRouteResponse, body []byte, requestID string, cacheKey string) {
	// Create request to backend
	backendReq, err := http.NewRequestWithContext(r.Context(), "POST", routeResp.BackendURL, bytes.NewBuffer(body))
	if err != nil {
		log.Printf("Request %s: Error creating backend request: %v", requestID, err)
		http.Error(w, "Error preparing request for backend", http.StatusInternalServerError)
		return
	}

	// Copy headers and set authentication
	copyHeaders(r, backendReq)
	setAuthentication(backendReq, routeResp)

	// Call backend
	client := &http.Client{Timeout: 60 * time.Second}
	backendResp, err := client.Do(backendReq)
	if err != nil {
		log.Printf("Request %s: Error calling backend: %v", requestID, err)
		http.Error(w, "Error from backend: "+err.Error(), http.StatusInternalServerError)
		return
	}
	defer backendResp.Body.Close()

	// Read response body for caching
	respBody, err := io.ReadAll(backendResp.Body)
	if err != nil {
		log.Printf("Request %s: Error reading response body: %v", requestID, err)
		http.Error(w, "Error reading response", http.StatusInternalServerError)
		return
	}

	// Cache the response if successful
	if backendResp.StatusCode == http.StatusOK && cacheKey != "" {
		ctx := context.Background()
		if err := setLLMResponseInCache(ctx, cacheKey, respBody); err != nil {
			log.Printf("Request %s: Error caching embeddings response: %v", requestID, err)
		} else {
			log.Printf("Request %s: Embeddings response cached successfully", requestID)
		}
	}

	// Copy headers
	for key, values := range backendResp.Header {
		for _, value := range values {
			w.Header().Add(key, value)
		}
	}

	// Set cache status
	w.Header().Set("X-Cache-Status", "miss")

	// Copy status code and body
	w.WriteHeader(backendResp.StatusCode)
	w.Write(respBody)

	log.Printf("Request %s: Embeddings request completed successfully", requestID)
}

// forwardImageRequest forwards an image generation request to the selected backend
func forwardImageRequest(w http.ResponseWriter, r *http.Request, routeResp AIOptimizerRouteResponse, body []byte, requestID string) {
	// Create request to backend
	backendReq, err := http.NewRequestWithContext(r.Context(), "POST", routeResp.BackendURL, bytes.NewBuffer(body))
	if err != nil {
		log.Printf("Request %s: Error creating backend request: %v", requestID, err)
		http.Error(w, "Error preparing request for backend", http.StatusInternalServerError)
		return
	}

	// Copy headers and set authentication
	copyHeaders(r, backendReq)
	setAuthentication(backendReq, routeResp)

	// Call backend
	client := &http.Client{Timeout: 120 * time.Second} // Longer timeout for image generation
	backendResp, err := client.Do(backendReq)
	if err != nil {
		log.Printf("Request %s: Error calling backend: %v", requestID, err)
		http.Error(w, "Error from backend: "+err.Error(), http.StatusInternalServerError)
		return
	}
	defer backendResp.Body.Close()

	// Copy response
	copyResponse(w, backendResp)

	log.Printf("Request %s: Image generation request completed successfully", requestID)
}

// Helper functions
func getString(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}

func copyHeaders(src *http.Request, dst *http.Request) {
	for key, values := range src.Header {
		// Skip or modify Accept-Encoding to only include compression formats we can handle
		if key == "Accept-Encoding" {
			// Only request gzip compression, which we can handle
			// Remove brotli (br) and other compression formats that cause issues
			dst.Header.Set("Accept-Encoding", "gzip, deflate")
			continue
		}
		for _, value := range values {
			dst.Header.Add(key, value)
		}
	}
}

func setAuthentication(req *http.Request, routeResp AIOptimizerRouteResponse) {
	if routeResp.APIKey != "" {
		// Handle Google/Gemini API authentication differently
		if routeResp.BackendType == "google-external" || routeResp.BackendType == "google" {
			// For Google APIs, add API key as query parameter
			q := req.URL.Query()
			q.Add("key", routeResp.APIKey)
			req.URL.RawQuery = q.Encode()
			log.Printf("Set Google API authentication - Backend type: %s, Final URL: %s", routeResp.BackendType, req.URL.String())
		} else {
			// For other APIs, use Bearer token
			req.Header.Set("Authorization", "Bearer "+routeResp.APIKey)
			log.Printf("Set Bearer token authentication - Backend type: %s", routeResp.BackendType)
		}
	}
	req.Header.Set("Content-Type", "application/json")
}

func copyResponse(w http.ResponseWriter, resp *http.Response) {
	// Copy headers
	for key, values := range resp.Header {
		for _, value := range values {
			w.Header().Add(key, value)
		}
	}

	// Copy status code
	w.WriteHeader(resp.StatusCode)

	// Copy body
	io.Copy(w, resp.Body)
}

// handleModelProfilesProxy proxies model profiles requests to policy-manager
func handleModelProfilesProxy(w http.ResponseWriter, r *http.Request) {
	// Set CORS headers
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// Get policy manager URL from environment or use default
	policyManagerURL := os.Getenv("POLICY_MANAGER_URL")
	if policyManagerURL == "" {
		policyManagerURL = "http://policy-manager:8083"
	}

	// Construct the target URL
	targetURL := policyManagerURL + "/api" + r.URL.Path
	if r.URL.RawQuery != "" {
		targetURL += "?" + r.URL.RawQuery
	}

	// Create the proxy request
	proxyReq, err := http.NewRequestWithContext(r.Context(), r.Method, targetURL, r.Body)
	if err != nil {
		log.Printf("Error creating proxy request to policy-manager: %v", err)
		http.Error(w, "Error creating proxy request", http.StatusInternalServerError)
		return
	}

	// Copy headers
	for key, values := range r.Header {
		for _, value := range values {
			proxyReq.Header.Add(key, value)
		}
	}

	// Make the request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(proxyReq)
	if err != nil {
		log.Printf("Error proxying request to policy-manager: %v", err)
		http.Error(w, "Error proxying request", http.StatusInternalServerError)
		return
	}
	defer resp.Body.Close()

	// Copy response headers
	for key, values := range resp.Header {
		for _, value := range values {
			w.Header().Add(key, value)
		}
	}

	// Set content type
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(resp.StatusCode)

	// Copy response body
	io.Copy(w, resp.Body)
}

// handleModelCapabilitiesProxy proxies model capabilities requests to ai-optimizer
func handleModelCapabilitiesProxy(w http.ResponseWriter, r *http.Request) {
	// Set CORS headers
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "GET" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get AI optimizer URL from environment or use default
	aiOptimizerURL := os.Getenv("AI_OPTIMIZER_URL")
	if aiOptimizerURL == "" {
		aiOptimizerURL = "http://ai-optimizer:8085"
	}

	// Construct the target URL
	targetURL := aiOptimizerURL + "/model-capabilities"
	if r.URL.RawQuery != "" {
		targetURL += "?" + r.URL.RawQuery
	}

	// Create the proxy request
	proxyReq, err := http.NewRequestWithContext(r.Context(), "GET", targetURL, nil)
	if err != nil {
		log.Printf("Error creating proxy request to ai-optimizer: %v", err)
		http.Error(w, "Error creating proxy request", http.StatusInternalServerError)
		return
	}

	// Copy headers
	for key, values := range r.Header {
		for _, value := range values {
			proxyReq.Header.Add(key, value)
		}
	}

	// Make the request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(proxyReq)
	if err != nil {
		log.Printf("Error proxying request to ai-optimizer: %v", err)
		http.Error(w, "Error proxying request", http.StatusInternalServerError)
		return
	}
	defer resp.Body.Close()

	// Copy response headers
	for key, values := range resp.Header {
		for _, value := range values {
			w.Header().Add(key, value)
		}
	}

	// Set content type
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(resp.StatusCode)

	// Copy response body
	io.Copy(w, resp.Body)
}

// handleEvaluationServiceProxy proxies evaluation service requests
func handleEvaluationServiceProxy(w http.ResponseWriter, r *http.Request) {
	// Set CORS headers
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// Get evaluation service URL from environment or use default
	evaluationServiceURL := os.Getenv("EVALUATION_SERVICE_URL")
	if evaluationServiceURL == "" {
		evaluationServiceURL = "http://evaluation-service:8087"
	}

	// Remove /evaluation prefix from the path
	targetPath := strings.TrimPrefix(r.URL.Path, "/evaluation")
	if targetPath == "" {
		targetPath = "/"
	}

	// Construct the target URL
	targetURL := evaluationServiceURL + targetPath
	if r.URL.RawQuery != "" {
		targetURL += "?" + r.URL.RawQuery
	}

	// Create the proxy request
	proxyReq, err := http.NewRequestWithContext(r.Context(), r.Method, targetURL, r.Body)
	if err != nil {
		log.Printf("Error creating proxy request to evaluation service: %v", err)
		http.Error(w, "Error creating proxy request", http.StatusInternalServerError)
		return
	}

	// Copy headers
	for key, values := range r.Header {
		for _, value := range values {
			proxyReq.Header.Add(key, value)
		}
	}

	// Make the request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(proxyReq)
	if err != nil {
		log.Printf("Error calling evaluation service: %v", err)
		http.Error(w, "Error calling evaluation service", http.StatusInternalServerError)
		return
	}
	defer resp.Body.Close()

	// Copy response headers
	for key, values := range resp.Header {
		for _, value := range values {
			w.Header().Add(key, value)
		}
	}

	// Copy status code
	w.WriteHeader(resp.StatusCode)

	// Copy response body
	io.Copy(w, resp.Body)
}

// handlePlanningServiceProxy proxies planning service requests
func handlePlanningServiceProxy(w http.ResponseWriter, r *http.Request) {
	// Set CORS headers
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// Get planning service URL from environment or use default
	planningServiceURL := os.Getenv("PLANNING_SERVICE_URL")
	if planningServiceURL == "" {
		planningServiceURL = "http://planning-service:8080"
	}

	// Remove /planning prefix from the path
	targetPath := strings.TrimPrefix(r.URL.Path, "/planning")
	if targetPath == "" {
		targetPath = "/"
	}

	// Construct the target URL
	targetURL := planningServiceURL + targetPath
	if r.URL.RawQuery != "" {
		targetURL += "?" + r.URL.RawQuery
	}

	// Create the proxy request
	proxyReq, err := http.NewRequestWithContext(r.Context(), r.Method, targetURL, r.Body)
	if err != nil {
		log.Printf("Error creating proxy request to planning service: %v", err)
		http.Error(w, "Error creating proxy request", http.StatusInternalServerError)
		return
	}

	// Copy headers
	for key, values := range r.Header {
		for _, value := range values {
			proxyReq.Header.Add(key, value)
		}
	}

	// Make the request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(proxyReq)
	if err != nil {
		log.Printf("Error calling planning service: %v", err)
		http.Error(w, "Error calling planning service", http.StatusInternalServerError)
		return
	}
	defer resp.Body.Close()

	// Copy response headers
	for key, values := range resp.Header {
		for _, value := range values {
			w.Header().Add(key, value)
		}
	}

	// Copy status code
	w.WriteHeader(resp.StatusCode)

	// Copy response body
	io.Copy(w, resp.Body)
}

// handleMultiAgentAuthProxy proxies multi-agent auth requests (no authentication required)
func handleMultiAgentAuthProxy(w http.ResponseWriter, r *http.Request) {
	// Set CORS headers
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// Get multi-agent service URL from environment or use default
	multiAgentURL := os.Getenv("MULTI_AGENT_URL")
	if multiAgentURL == "" {
		multiAgentURL = "http://multi-agent-service:8089"
	}

	// Remove /multi-agent prefix from the path
	targetPath := strings.TrimPrefix(r.URL.Path, "/multi-agent")
	if targetPath == "" {
		targetPath = "/"
	}

	// Construct the target URL
	targetURL := multiAgentURL + targetPath
	if r.URL.RawQuery != "" {
		targetURL += "?" + r.URL.RawQuery
	}

	// Create the proxy request
	proxyReq, err := http.NewRequestWithContext(r.Context(), r.Method, targetURL, r.Body)
	if err != nil {
		log.Printf("Error creating proxy request to multi-agent service: %v", err)
		http.Error(w, "Error creating proxy request", http.StatusInternalServerError)
		return
	}

	// Copy headers
	for key, values := range r.Header {
		for _, value := range values {
			proxyReq.Header.Add(key, value)
		}
	}

	// Make the request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(proxyReq)
	if err != nil {
		log.Printf("Error calling multi-agent service: %v", err)
		http.Error(w, "Error calling multi-agent service", http.StatusInternalServerError)
		return
	}
	defer resp.Body.Close()

	// Copy response headers
	for key, values := range resp.Header {
		for _, value := range values {
			w.Header().Add(key, value)
		}
	}

	// Copy status code
	w.WriteHeader(resp.StatusCode)

	// Copy response body
	io.Copy(w, resp.Body)
}

// handleMultiAgentServiceProxy proxies multi-agent service requests (authentication required)
func handleMultiAgentServiceProxy(w http.ResponseWriter, r *http.Request) {
	// Set CORS headers
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// Get multi-agent service URL from environment or use default
	multiAgentURL := os.Getenv("MULTI_AGENT_URL")
	if multiAgentURL == "" {
		multiAgentURL = "http://multi-agent-service:8089"
	}

	// Remove /multi-agent prefix from the path
	targetPath := strings.TrimPrefix(r.URL.Path, "/multi-agent")
	if targetPath == "" {
		targetPath = "/"
	}

	// Construct the target URL
	targetURL := multiAgentURL + targetPath
	if r.URL.RawQuery != "" {
		targetURL += "?" + r.URL.RawQuery
	}

	// Create the proxy request
	proxyReq, err := http.NewRequestWithContext(r.Context(), r.Method, targetURL, r.Body)
	if err != nil {
		log.Printf("Error creating proxy request to multi-agent service: %v", err)
		http.Error(w, "Error creating proxy request", http.StatusInternalServerError)
		return
	}

	// Copy headers
	for key, values := range r.Header {
		for _, value := range values {
			proxyReq.Header.Add(key, value)
		}
	}

	// Make the request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(proxyReq)
	if err != nil {
		log.Printf("Error calling multi-agent service: %v", err)
		http.Error(w, "Error calling multi-agent service", http.StatusInternalServerError)
		return
	}
	defer resp.Body.Close()

	// Copy response headers
	for key, values := range resp.Header {
		for _, value := range values {
			w.Header().Add(key, value)
		}
	}

	// Copy status code
	w.WriteHeader(resp.StatusCode)

	// Copy response body
	io.Copy(w, resp.Body)
}

func main() {
	defer kafkaWriter.Close()
	defer redisClient.Close()

	// Initialize the cache warming system
	ctx := context.Background()
	initCacheWarming(ctx)

	// Initialize freemium system for startup edition
	InitializeFreemium()

	mux := http.NewServeMux()

	// LLM API endpoints - require authentication
	mux.HandleFunc("/v1/chat/completions", authMiddleware(handleLLMRequest))
	mux.HandleFunc("/v1/completions", authMiddleware(handleCompletionsRequest))
	mux.HandleFunc("/v1/embeddings", authMiddleware(handleEmbeddingsRequest))
	mux.HandleFunc("/v1/images/generations", authMiddleware(handleImageGenerationRequest))
	mux.HandleFunc("/v1/audio/speech", authMiddleware(handleAudioSpeechRequest))
	mux.HandleFunc("/v1/audio/transcriptions", authMiddleware(handleAudioTranscriptionRequest))
	mux.HandleFunc("/v1/audio/translations", authMiddleware(handleAudioTranslationRequest))
	mux.HandleFunc("/v1/moderations", authMiddleware(handleModerationsRequest))
	mux.HandleFunc("/v1/fine-tuning/jobs", authMiddleware(handleFineTuningRequest))

	// Analytics and management endpoints - require authentication
	mux.HandleFunc("/v1/analytics/cache", authMiddleware(handleCacheAnalyticsRequest))
	mux.HandleFunc("/v1/cache/manage", adminMiddleware(handleCacheManagementRequest))

	// Public endpoints - no authentication required
	mux.HandleFunc("/health", handleHealthCheck)
	mux.HandleFunc("/ready", handleReadinessCheck)

	// Usage stats - optional authentication (better stats if authenticated)
	mux.HandleFunc("/v1/usage/stats", optionalAuthMiddleware(handleUsageStats))

	// Add batch statistics endpoint
	// mux.HandleFunc("/v1/analytics/batch", GetBatchStatisticsHandler)

	// Register cache warming handlers
	RegisterCacheWarmingHandlers(mux)

	// Initialize MCP Host
	InitializeMCPHost()

	// MCP endpoints - require authentication
	mux.HandleFunc("/mcp/connect", authMiddleware(mcpHost.HandleMCPConnection))
	mux.HandleFunc("/mcp/status", authMiddleware(mcpHost.HandleMCPStatus))

	// Proxy endpoints for frontend API requests - require authentication
	mux.HandleFunc("/model-profiles", authMiddleware(handleModelProfilesProxy))
	mux.HandleFunc("/model-profiles/", authMiddleware(handleModelProfilesProxy))
	mux.HandleFunc("/model-capabilities", authMiddleware(handleModelCapabilitiesProxy))
	mux.HandleFunc("/model-capabilities/", authMiddleware(handleModelCapabilitiesProxy))

	// Evaluation service proxy endpoints
	mux.HandleFunc("/evaluation/", authMiddleware(handleEvaluationServiceProxy))

	// Planning service proxy endpoints
	mux.HandleFunc("/planning/", authMiddleware(handlePlanningServiceProxy))

	// Multi-agent service proxy endpoints (auth endpoints don't require authentication)
	mux.HandleFunc("/multi-agent/auth/", handleMultiAgentAuthProxy)
	mux.HandleFunc("/multi-agent/", authMiddleware(handleMultiAgentServiceProxy))

	port := os.Getenv("PORT")
	if port == "" {
		port = "8080" // Default port for proxy-gateway
	}
	log.Printf("Proxy Gateway starting on port %s with MCP support...", port)
	log.Fatal(http.ListenAndServe(":"+port, mux))
}

// loadPoliciesFromRedis fetches all policies from Redis into the cache.
func loadPoliciesFromRedis(ctx context.Context) error {
	mu.Lock()
	defer mu.Unlock()

	newPolicies := make(map[string]Policy)
	keys, err := redisClient.Keys(ctx, REDIS_POLICIES_KEY_PREFIX+"*").Result()
	if err != nil && err != redis.Nil {
		return fmt.Errorf("failed to get policy keys from Redis: %w", err)
	}

	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("Error getting policy %s from Redis: %v", key, err)
			continue
		}
		var policy Policy
		if err := json.Unmarshal([]byte(val), &policy); err != nil {
			log.Printf("Error unmarshalling policy %s: %v", key, err)
			continue
		}
		newPolicies[policy.ID] = policy
	}
	policies = newPolicies
	log.Printf("Loaded %d policies from Redis.", len(policies))
	return nil
}

// loadModelProfilesFromRedis fetches all model profiles from Redis into the cache.
func loadModelProfilesFromRedis(ctx context.Context) error {
	mu.Lock()
	defer mu.Unlock()

	newModelProfiles := make(map[string]ModelProfile)
	keys, err := redisClient.Keys(ctx, REDIS_MODEL_PROFILES_KEY_PREFIX+"*").Result()
	if err != nil && err != redis.Nil {
		return fmt.Errorf("failed to get model profile keys from Redis: %w", err)
	}

	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("Error getting model profile %s from Redis: %v", key, err)
			continue
		}
		var profile ModelProfile
		if err := json.Unmarshal([]byte(val), &profile); err != nil {
			log.Printf("Error unmarshalling model profile %s: %v", key, err)
			continue
		}
		newModelProfiles[profile.ID] = profile
	}
	modelProfiles = newModelProfiles
	log.Printf("Loaded %d model profiles from Redis.", len(modelProfiles))
	return nil
}

// listenForRedisPolicyUpdates listens for Redis Pub/Sub messages for policy updates.
func listenForRedisPolicyUpdates() {
	pubsub := redisClient.Subscribe(context.Background(), POLICY_UPDATES_CHANNEL)
	defer pubsub.Close()
	log.Println("Listening for Redis policy updates...")

	for msg := range pubsub.Channel() {
		log.Printf("Received Redis Pub/Sub message: Channel=%s, Payload=%s", msg.Channel, msg.Payload)
		if err := loadPoliciesFromRedis(context.Background()); err != nil {
			log.Printf("Error reloading policies from Redis: %v", err)
		} else {
			log.Println("Policies cache refreshed due to pending updates...")
		}
	}
}

// listenForRedisModelProfileUpdates listens for Redis Pub/Sub messages for model profile updates.
func listenForRedisModelProfileUpdates() {
	pubsub := redisClient.Subscribe(context.Background(), MODEL_PROFILE_UPDATES_CHANNEL)
	defer pubsub.Close()
	log.Println("Listening for Redis model profile updates...")

	for msg := range pubsub.Channel() {
		log.Printf("Received Redis Pub/Sub message: Channel=%s, Payload=%s", msg.Channel, msg.Payload)
		if err := loadModelProfilesFromRedis(context.Background()); err != nil {
			log.Printf("Error reloading model profiles from Redis: %v", err)
		} else {
			log.Println("Model profiles cache refreshed due to pending updates...")
		}
	}
}

// getModelProfileByID safely retrieves a model profile from the cache.
// This function IS used by parseTokensAndCost and also in the cache hit path directly.
func getModelProfileByID(id string) (ModelProfile, bool) {
	mu.RLock()
	defer mu.RUnlock()
	mp, ok := modelProfiles[id]
	return mp, ok
}

// getCacheKey generates a cache key based on the prompt and model.
func getCacheKey(prompt, model string) string {
	return getCacheKeyWithAPIType(prompt, model, "chat_completions")
}

// getCacheKeyWithAPIType generates a cache key based on the prompt, model, and API type.
func getCacheKeyWithAPIType(prompt, model, apiType string) string {
	// Normalize prompt for consistent caching (e.g., lowercase, remove non-alphanumeric)
	normalizedPrompt := strings.ToLower(prompt)
	// Remove non-alphanumeric characters and collapse spaces
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	normalizedPrompt = reg.ReplaceAllString(normalizedPrompt, "")
	return REDIS_CACHE_KEY_PREFIX + apiType + ":" + normalizedPrompt + ":" + model
}

// getSemanticCacheKey generates a cache key for the semantic cache.
func getSemanticCacheKey(embedding []float64, model string) string {
	// Create a hash of the embedding
	hasher := sha256.New()
	for _, val := range embedding {
		hasher.Write([]byte(fmt.Sprintf("%f", val)))
	}
	embeddingHash := fmt.Sprintf("%x", hasher.Sum(nil))

	return SEMANTIC_CACHE_KEY_PREFIX + embeddingHash + ":" + model
}

// generateEmbedding creates an embedding vector for a
func generateEmbedding(ctx context.Context, text string) ([]float64, error) {
	// Lock to prevent parallel embedding requests
	embedderLock.Lock()
	defer embedderLock.Unlock()

	// Get the embedding model profile (using a fixed ID for the embedder)
	embeddingModelID := "text-embedding-3-small" // Default embedding model
	profile, ok := getModelProfileByID(embeddingModelID)
	if !ok {
		embeddingModelID = "text-embedding-ada-002" // Fallback embedding model
		profile, ok = getModelProfileByID(embeddingModelID)
		if !ok {
			return nil, fmt.Errorf("embedding model profile not found for %s or fallback", embeddingModelID)
		}
	}

	// Prepare the embedding request
	embeddingReq := EmbeddingRequest{
		Model: embeddingModelID,
		Input: []string{text},
	}

	reqBody, err := json.Marshal(embeddingReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal embedding request: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", profile.BackendURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create embedding request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if profile.APIKey != "" {
		req.Header.Set("Authorization", "Bearer "+profile.APIKey)
	}

	// Call the embedding API
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("embedding API call failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("embedding API returned non-200 status: %d, body: %s", resp.StatusCode, string(body))
	}

	// Parse the response
	var embeddingResp EmbeddingResponse
	if err := json.NewDecoder(resp.Body).Decode(&embeddingResp); err != nil {
		return nil, fmt.Errorf("failed to decode embedding response: %w", err)
	}

	if len(embeddingResp.Data) == 0 || len(embeddingResp.Data[0].Embedding) == 0 {
		return nil, fmt.Errorf("embedding response contains no embeddings")
	}

	return embeddingResp.Data[0].Embedding, nil
}

// cosineSimilarity calculates the cosine similarity between two embedding vectors.
func cosineSimilarity(a, b []float64) (float64, error) {
	if len(a) != len(b) {
		return 0, fmt.Errorf("embedding vectors have different dimensions: %d vs %d", len(a), len(b))
	}

	var dotProduct, normA, normB float64
	for i := range a {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}

	normA = math.Sqrt(normA)
	normB = math.Sqrt(normB)

	if normA == 0 || normB == 0 {
		return 0, fmt.Errorf("one of the embedding vectors has zero norm")
	}

	return dotProduct / (normA * normB), nil
}

// getLLMResponseFromCache attempts to retrieve an LLM response from Redis cache.
func getLLMResponseFromCache(ctx context.Context, cacheKey string) ([]byte, error) {
	val, err := redisClient.Get(ctx, cacheKey).Result()
	if err == redis.Nil {
		return nil, nil // Not found in cache
	}
	if err != nil {
		return nil, fmt.Errorf("error retrieving from cache: %w", err)
	}
	return []byte(val), nil
}

// findSimilarPromptInCache attempts to find a semantically similar prompt in the cache.
func findSimilarPromptInCache(ctx context.Context, prompt, model string) ([]byte, error) {
	// First, generate embedding for the current prompt
	promptEmbedding, err := generateEmbedding(ctx, prompt)
	if err != nil {
		log.Printf("Error generating embedding for semantic cache lookup: %v", err)
		return nil, err
	}

	// Generate cache key for the current embedding
	semanticKey := getSemanticCacheKey(promptEmbedding, model)

	// Try in-memory cache first (faster)
	if cachedEntry, found := semanticCache.Load(semanticKey); found {
		entry := cachedEntry.(SemanticCacheEntry)
		log.Printf("Semantic cache HIT (in-memory) for key %s", semanticKey)
		return entry.Response, nil
	}

	// Check Redis for exact match on embedding hash
	redisEntry, err := redisClient.Get(ctx, semanticKey).Result()
	if err == nil {
		// Found an exact match on embedding hash
		var entry SemanticCacheEntry
		if err := json.Unmarshal([]byte(redisEntry), &entry); err == nil {
			// Store in memory cache for faster future access
			semanticCache.Store(semanticKey, entry)
			log.Printf("Semantic cache HIT (Redis exact) for key %s", semanticKey)
			return entry.Response, nil
		}
	}

	// Scan for similar embeddings (slower operation)
	keys, err := redisClient.Keys(ctx, SEMANTIC_CACHE_KEY_PREFIX+"*:"+model).Result()
	if err != nil || len(keys) == 0 {
		return nil, nil // No semantic cache entries or error
	}

	// Fetch all semantic cache entries and check similarity
	for _, key := range keys {
		redisEntry, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			continue
		}

		var entry SemanticCacheEntry
		if err := json.Unmarshal([]byte(redisEntry), &entry); err != nil {
			continue
		}

		// Calculate similarity
		similarity, err := cosineSimilarity(promptEmbedding, entry.Embedding)
		if err != nil {
			continue
		}

		if similarity >= SEMANTIC_SIMILARITY_THRESHOLD {
			log.Printf("Semantic cache HIT (similarity=%.4f) for prompt '%s'",
				similarity, truncateString(prompt, 50))

			// Store in memory cache for faster future access
			semanticCache.Store(semanticKey, entry)

			return entry.Response, nil
		}
	}

	return nil, nil // No similar entries found
}

// truncateString limits a string to a maximum length for logging.
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

// setLLMResponseInCache stores an LLM response in Redis cache with a TTL.
func setLLMResponseInCache(ctx context.Context, cacheKey string, responseBody []byte) error {
	err := redisClient.Set(ctx, cacheKey, responseBody, CACHE_TTL).Err()
	if err != nil {
		return fmt.Errorf("error setting cache: %w", err)
	}
	return nil
}

// storeInSemanticCache stores a response in the semantic cache.
func storeInSemanticCache(ctx context.Context, prompt, model string, responseBody []byte,
	inputTokens, outputTokens int64, totalCost float64) error {

	// Generate embedding for the prompt
	embedding, err := generateEmbedding(ctx, prompt)
	if err != nil {
		return fmt.Errorf("failed to generate embedding for semantic cache: %w", err)
	}

	// Create cache entry
	entry := SemanticCacheEntry{
		Prompt:       prompt,
		Response:     responseBody,
		Embedding:    embedding,
		Model:        model,
		Timestamp:    time.Now(),
		InputTokens:  inputTokens,
		OutputTokens: outputTokens,
		TotalCost:    totalCost,
	}

	// Serialize the entry
	entryBytes, err := json.Marshal(entry)
	if err != nil {
		return fmt.Errorf("failed to marshal semantic cache entry: %w", err)
	}

	// Generate cache key based on embedding
	semanticKey := getSemanticCacheKey(embedding, model)

	// Store in Redis with longer TTL
	if err := redisClient.Set(ctx, semanticKey, entryBytes, SEMANTIC_CACHE_TTL).Err(); err != nil {
		return fmt.Errorf("failed to store semantic cache entry in Redis: %w", err)
	}

	// Also store in memory for faster lookup
	entry.EmbeddingHash = semanticKey // Store key for reference
	semanticCache.Store(semanticKey, entry)

	log.Printf("Stored response in semantic cache with key: %s", semanticKey)
	return nil
}

// processPromptTemplate applies variable substitution to a prompt template
// It looks up the template by ID in the promptTemplates map and replaces
// placeholders like {{variable}} with values from the provided variables map.
// Falls back to default values defined in the template if a variable is not provided.
func processPromptTemplate(templateID string, variables map[string]string) (string, error) {
	mu.RLock()
	template, exists := promptTemplates[templateID]
	mu.RUnlock()

	if !exists {
		return "", fmt.Errorf("prompt template with ID %s not found", templateID)
	}

	// Start with the template text
	result := template.Template

	// Apply variable substitution
	for key, defaultValue := range template.Variables {
		// Check if the variable is provided in the input variables
		value, exists := variables[key]
		if !exists {
			// Use default value if no custom value provided
			value = defaultValue
		}

		// Replace the variable placeholder with the value
		placeholder := fmt.Sprintf("{{%s}}", key)
		result = strings.ReplaceAll(result, placeholder, value)
	}

	return result, nil
}

// estimateTokenCount provides a rough estimate of token count based on whitespace
// This is a simple heuristic and not a precise count
func estimateTokenCount(text string) int {
	// Simple approximation: count words and add 20%
	words := strings.Fields(text)
	return int(float64(len(words)) * 1.2)
}

// detectAndReduceRepetition identifies and reduces repetitive patterns in text
func detectAndReduceRepetition(text string) string {
	// Simple repetition detection for phrases
	// Look for repeated phrases of 5+ words that appear multiple times
	words := strings.Fields(text)
	if len(words) < 10 {
		return text // Too short to have meaningful repetition
	}

	// Construct sliding windows of phrases and count them
	phrases := make(map[string]int)

	// Check different phrase lengths
	for phraseLen := 5; phraseLen < 15 && phraseLen < len(words)/2; phraseLen++ {
		for i := 0; i <= len(words)-phraseLen; i++ {
			phrase := strings.Join(words[i:i+phraseLen], " ")
			phrases[phrase]++
		}
	}

	// Replace repeated phrases with a single instance
	result := text
	for phrase, count := range phrases {
		if count > 1 && len(phrase) > 20 { // Only meaningful phrases
			// Replace all but the first occurrence
			parts := strings.SplitN(result, phrase, 2)
			if len(parts) == 2 {
				// Keep the first occurrence and eliminate others
				result = parts[0] + phrase + strings.ReplaceAll(parts[1], phrase, "")
			}
		}
	}

	return result
}

// Constants for prompt compression are defined in prompt_optimization.go

// transformRequestPayload transforms the incoming OpenAI-like request body
// into the format expected by the target LLM backend.
func transformRequestPayload(originalBody []byte, backendType string) ([]byte, error) {
	if backendType == "cohere-external" || backendType == "cohere" {
		// Transform OpenAI-like request to Cohere format
		var openaiReq OpenAICompletionRequest
		if err := json.Unmarshal(originalBody, &openaiReq); err != nil {
			return nil, fmt.Errorf("failed to unmarshal OpenAI-like request: %w", err)
		}

		// Convert messages to Cohere chat format
		var cohereMessages []CohereMessage
		var systemMessage string

		for _, msg := range openaiReq.Messages {
			switch msg.Role {
			case "system":
				systemMessage = msg.Content
			case "user":
				cohereMessages = append(cohereMessages, CohereMessage{
					Role:    "USER",
					Message: msg.Content,
				})
			case "assistant":
				cohereMessages = append(cohereMessages, CohereMessage{
					Role:    "CHATBOT",
					Message: msg.Content,
				})
			}
		}

		cohereReq := CohereChatRequest{
			Model:       openaiReq.Model,
			Message:     cohereMessages[len(cohereMessages)-1].Message, // Last user message
			ChatHistory: cohereMessages[:len(cohereMessages)-1],        // All previous messages
			Temperature: openaiReq.Temperature,
			MaxTokens:   openaiReq.MaxTokens,
			Stream:      openaiReq.Stream,
		}

		if systemMessage != "" {
			cohereReq.Preamble = systemMessage
		}

		translatedBody, err := json.Marshal(cohereReq)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal Cohere-format request: %w", err)
		}
		log.Printf("Successfully translated OpenAI-like payload to Cohere format for backend type: %s", backendType)
		return translatedBody, nil
	} else if backendType == "huggingface-external" || backendType == "huggingface" {
		// Transform OpenAI-like request to Hugging Face format
		var openaiReq OpenAICompletionRequest
		if err := json.Unmarshal(originalBody, &openaiReq); err != nil {
			return nil, fmt.Errorf("failed to unmarshal OpenAI-like request: %w", err)
		}

		// Convert messages to Hugging Face format (which is similar to OpenAI)
		var hfMessages []HuggingFaceMessage
		for _, msg := range openaiReq.Messages {
			hfMessages = append(hfMessages, HuggingFaceMessage(msg))
		}

		hfReq := HuggingFaceChatRequest{
			Model:       openaiReq.Model,
			Messages:    hfMessages,
			MaxTokens:   openaiReq.MaxTokens,
			Temperature: openaiReq.Temperature,
			Stream:      openaiReq.Stream,
		}

		translatedBody, err := json.Marshal(hfReq)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal Hugging Face-format request: %w", err)
		}
		log.Printf("Successfully translated OpenAI-like payload to Hugging Face format for backend type: %s", backendType)
		return translatedBody, nil
	} else if backendType == "mistral-external" || backendType == "mistral" {
		// Transform OpenAI-like request to Mistral format
		var openaiReq OpenAICompletionRequest
		if err := json.Unmarshal(originalBody, &openaiReq); err != nil {
			return nil, fmt.Errorf("failed to unmarshal OpenAI-like request: %w", err)
		}

		// Convert messages to Mistral format (which is similar to OpenAI)
		var mistralMessages []MistralMessage
		for _, msg := range openaiReq.Messages {
			mistralMessages = append(mistralMessages, MistralMessage(msg))
		}

		mistralReq := MistralChatRequest{
			Model:       openaiReq.Model,
			Messages:    mistralMessages,
			MaxTokens:   openaiReq.MaxTokens,
			Temperature: openaiReq.Temperature,
			Stream:      openaiReq.Stream,
		}

		translatedBody, err := json.Marshal(mistralReq)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal Mistral-format request: %w", err)
		}
		log.Printf("Successfully translated OpenAI-like payload to Mistral format for backend type: %s", backendType)
		return translatedBody, nil
	} else if backendType == "grok-external" || backendType == "grok" || backendType == "xai-external" || backendType == "xai" {
		// Transform OpenAI-like request to GROK format
		var openaiReq OpenAICompletionRequest
		if err := json.Unmarshal(originalBody, &openaiReq); err != nil {
			return nil, fmt.Errorf("failed to unmarshal OpenAI-like request: %w", err)
		}

		// Convert messages to GROK format (which is similar to OpenAI)
		var grokMessages []GrokMessage
		for _, msg := range openaiReq.Messages {
			grokMessages = append(grokMessages, GrokMessage(msg))
		}

		grokReq := GrokChatRequest{
			Model:       openaiReq.Model,
			Messages:    grokMessages,
			MaxTokens:   openaiReq.MaxTokens,
			Temperature: openaiReq.Temperature,
			Stream:      openaiReq.Stream,
		}

		translatedBody, err := json.Marshal(grokReq)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal GROK-format request: %w", err)
		}
		log.Printf("Successfully translated OpenAI-like payload to GROK format for backend type: %s", backendType)
		return translatedBody, nil
	} else if backendType == "google-external" || backendType == "google" {
		// Incoming request is OpenAI-like
		var openaiReq OpenAICompletionRequest
		if err := json.Unmarshal(originalBody, &openaiReq); err != nil {
			return nil, fmt.Errorf("failed to unmarshal OpenAI-like request: %w", err)
		}

		// Convert to Gemini format
		var geminiContents []GeminiContent
		for _, msg := range openaiReq.Messages {
			role := msg.Role
			// Map OpenAI roles to Gemini roles
			if role == "assistant" {
				role = "model"
			} else if role == "system" {
				// System messages in Gemini are handled differently
				// For now, convert to user message with system prefix
				content := "System: " + msg.Content
				geminiContents = append(geminiContents, GeminiContent{
					Parts: []GeminiPart{{Text: content}},
					Role:  "user",
				})
				continue
			}
			// "user" role stays the same

			geminiContents = append(geminiContents, GeminiContent{
				Parts: []GeminiPart{{Text: msg.Content}},
				Role:  role,
			})
		}

		geminiReq := GeminiGenerateContentRequest{
			Contents: geminiContents,
		}

		if openaiReq.MaxTokens > 0 {
			geminiReq.GenerationConfig.MaxOutputTokens = openaiReq.MaxTokens
		}

		translatedBody, err := json.Marshal(geminiReq)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal Gemini-format request: %w", err)
		}
		log.Printf("Successfully translated OpenAI-like payload to Gemini format for backend type: %s", backendType)
		return translatedBody, nil
	}
	// For other backend types (OpenAI, Anthropic, VLLM), assume the incoming format is compatible.
	// In a real robust system, you'd have similar translation logic for all non-standard backends.
	return originalBody, nil
}

// handleLLMRequest handles incoming LLM requests, routes them, and logs inference.
func handleLLMRequest(w http.ResponseWriter, r *http.Request) {
	requestID := uuid.New().String()
	logEntry := InferenceLog{
		RequestID: requestID,
		Timestamp: time.Now(),
		Method:    r.Method,
		Path:      r.URL.Path,
		ClientIP:  r.RemoteAddr,
		UserAgent: r.UserAgent(),
	}

	log.Printf("Handling request %s: %s %s", requestID, r.Method, r.URL.Path)

	// NEW: Extract UserID and UserRoles from headers
	logEntry.UserID = r.Header.Get("X-User-Id")
	userRolesHeader := r.Header.Get("X-User-Roles")
	if userRolesHeader != "" {
		// Split by comma and trim spaces for each role
		roles := strings.Split(userRolesHeader, ",")
		cleanedRoles := make([]string, 0, len(roles))
		for _, role := range roles {
			trimmedRole := strings.TrimSpace(role)
			if trimmedRole != "" {
				cleanedRoles = append(cleanedRoles, trimmedRole)
			}
		}
		logEntry.UserRoles = cleanedRoles
	} else {
		logEntry.UserRoles = []string{} // Ensure it's an empty slice if header is not present
	}

	// Check freemium limits for startup edition
	userTier := r.Header.Get("X-User-Tier")
	if userTier == "" {
		userTier = "free" // Default to free tier
	}

	ctx := context.Background()
	if err := CheckFreemiumLimits(ctx, logEntry.UserID, userTier); err != nil {
		log.Printf("Request %s: Freemium limit exceeded for user %s: %v", requestID, logEntry.UserID, err)
		HandleFreemiumError(w, err)
		return
	}

	// Read the request body once and store it locally
	requestBodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		log.Printf("Request %s: Error reading request body: %v", requestID, err)
		http.Error(w, "Error reading request body", http.StatusInternalServerError)
		return
	}
	r.Body = io.NopCloser(bytes.NewBuffer(requestBodyBytes)) // Restore body for later reads

	// Set the request body snippet immediately after reading
	logEntry.RequestBodySnippet = string(requestBodyBytes)

	// Parse the model requested by the client (if available in the request body)
	var reqBodyMap map[string]interface{}
	modelRequested := "unknown"
	prompt := "unknown"
	streamRequested := false // Flag for streaming responses
	var conversationID string

	if err := json.Unmarshal(requestBodyBytes, &reqBodyMap); err == nil {
		// Extract conversation_id from the request body first
		if convID, ok := reqBodyMap["conversation_id"].(string); ok && convID != "" {
			conversationID = convID
		}
		if model, ok := reqBodyMap["model"].(string); ok {
			modelRequested = model
		}

		// Check for template ID in the request
		templateID, hasTemplate := reqBodyMap["template_id"].(string)
		templateVars, hasVars := reqBodyMap["template_variables"].(map[string]interface{})

		// Process via template if template_id is provided
		if hasTemplate && templateID != "" {
			// Convert variables to proper format
			vars := make(map[string]string)
			if hasVars {
				for k, v := range templateVars {
					if strVal, ok := v.(string); ok {
						vars[k] = strVal
					} else if val, err := json.Marshal(v); err == nil {
						vars[k] = string(val)
					}
				}
			}

			// Process the template
			processedPrompt, err := processPromptTemplate(templateID, vars)
			if err != nil {
				log.Printf("Request %s: Error processing template %s: %v", requestID, templateID, err)
				// Fall back to regular prompt processing if template fails
			} else {
				// If template processing succeeded, use the processed prompt
				prompt = processedPrompt
				log.Printf("Request %s: Successfully processed template %s", requestID, templateID)

				// Update request body with processed prompt if it contains messages
				if messages, ok := reqBodyMap["messages"].([]interface{}); ok && len(messages) > 0 {
					if firstMessage, ok := messages[0].(map[string]interface{}); ok {
						firstMessage["content"] = processedPrompt
						if modifiedBody, err := json.Marshal(reqBodyMap); err == nil {
							requestBodyBytes = modifiedBody
							r.Body = io.NopCloser(bytes.NewBuffer(requestBodyBytes))
						}
					}
				}
			}
		} else {
			// Standard prompt extraction
			if messages, ok := reqBodyMap["messages"].([]interface{}); ok && len(messages) > 0 {
				if firstMessage, ok := messages[0].(map[string]interface{}); ok {
					if content, ok := firstMessage["content"].(string); ok {
						prompt = content
					}
				}
			}
		}

		if stream, ok := reqBodyMap["stream"].(bool); ok {
			streamRequested = stream
		}
	}

	// TEMPORARY DEBUG: Force disable streaming for OpenAI to test non-streaming response
	if streamRequested && (modelRequested == "gpt-4" || modelRequested == "gpt-3.5-turbo" || modelRequested == "gpt-4o-mini") {
		log.Printf("Request %s: DEBUG - Forcing non-streaming for OpenAI model: %s", requestID, modelRequested)
		streamRequested = false
	}

	logEntry.ModelRequested = modelRequested
	logEntry.Stream = streamRequested // Ensure stream status is logged

	// Fallback to header if not in body
	if conversationID == "" {
		conversationID = r.Header.Get("X-Conversation-Id")
	}

	// If still not found, generate a new one
	if conversationID == "" {
		conversationID = uuid.New().String()
		log.Printf("Request %s: No conversation_id provided. Generated new ID: %s", requestID, conversationID)
	}
	logEntry.ConversationID = conversationID

	// --- Step 0: Check Cache for Response ---
	// First check the traditional exact-match cache
	cacheKey := getCacheKey(prompt, modelRequested)

	// First check in-memory cache for hot items
	inMemoryCacheItem, inMemoryHit := inMemoryCache.Get(cacheKey)
	if inMemoryHit {
		inMemoryValue := inMemoryCacheItem.(string)
		log.Printf("Request %s: In-memory Cache HIT for prompt '%s'. Serving from memory.", requestID, truncateString(prompt, 50))

		// Update cache analytics
		analyticsLock.Lock()
		cacheAnalytics.Hits++
		analyticsLock.Unlock()

		w.Header().Set("Content-Type", "application/json")
		w.Header().Set("X-Cache-Hit", "true")
		w.Header().Set("X-Cache-Level", "memory")

		// Inject conversation_id into the response
		finalResponse, err := injectConversationID([]byte(inMemoryValue), conversationID)
		if err != nil {
			log.Printf("Request %s: Error injecting conversation ID into memory cache response: %v", requestID, err)
			finalResponse = []byte(inMemoryValue) // Fallback to original
		}
		w.Write(finalResponse)

		logEntry.StatusCode = int32(http.StatusOK)
		logEntry.ResponseTimestamp = time.Now()
		logEntry.LatencyMs = float64(time.Since(logEntry.Timestamp).Milliseconds())
		logEntry.ResponseBodySnippet = inMemoryValue

		// For cache hits, the policy applied is "cache-hit" and model used is the model requested
		logEntry.PolicyIDApplied = "memory-cache-hit"
		logEntry.SelectedBackendID = "memory-cache"
		logEntry.ModelUsed = modelRequested

		profile, ok := getModelProfileByID(modelRequested)
		if ok {
			inputTokens, outputTokens, cost := parseTokensAndCost([]byte(inMemoryValue), modelRequested, profile)
			logEntry.InputTokens = inputTokens
			logEntry.OutputTokens = outputTokens
			logEntry.TotalCost = cost

			// Update tokens and cost saved in analytics
			analyticsLock.Lock()
			cacheAnalytics.TokensSaved += inputTokens + outputTokens
			cacheAnalytics.CostSaved += cost
			analyticsLock.Unlock()

			log.Printf("Request %s: Memory cache hit saved %d tokens and $%.6f",
				requestID, inputTokens+outputTokens, cost)
		}

		logEntry.TaskType = "memory_cached_response"
		logInferenceLog(logEntry)
		return
	}

	// Then check Redis cache
	cachedResponse, cacheErr := getLLMResponseFromCache(r.Context(), cacheKey)
	if cacheErr != nil {
		log.Printf("Request %s: Error checking cache for key '%s': %v", requestID, cacheKey, cacheErr)
	} else if cachedResponse != nil {
		log.Printf("Request %s: Redis Cache HIT for prompt '%s'. Serving from Redis cache.", requestID, truncateString(prompt, 50))

		// Update cache analytics
		analyticsLock.Lock()
		cacheAnalytics.Hits++
		analyticsLock.Unlock()

		// Store in memory cache for faster future access
		inMemoryCache.Set(cacheKey, string(cachedResponse), CACHE_TTL)

		w.Header().Set("Content-Type", "application/json")
		w.Header().Set("X-Cache-Hit", "true")
		w.Header().Set("X-Cache-Level", "redis")

		// Inject conversation_id into the response
		finalResponse, err := injectConversationID(cachedResponse, conversationID)
		if err != nil {
			log.Printf("Request %s: Error injecting conversation ID into Redis cache response: %v", requestID, err)
			finalResponse = cachedResponse // Fallback to original
		}
		w.Write(finalResponse)

		logEntry.StatusCode = int32(http.StatusOK)
		logEntry.ResponseTimestamp = time.Now()
		logEntry.LatencyMs = float64(time.Since(logEntry.Timestamp).Milliseconds())
		logEntry.ResponseBodySnippet = string(cachedResponse) // Store full cached response for now

		// For cache hits, the policy applied is "cache-hit" and model used is the model requested
		logEntry.PolicyIDApplied = "redis-cache-hit"
		logEntry.SelectedBackendID = "redis-cache" // Designate a special "cache" backend ID
		logEntry.ModelUsed = modelRequested        // Use the model that was requested for cache hit logging

		// Attempt to parse tokens from cached response for logging
		profile, ok := getModelProfileByID(modelRequested) // Use modelRequested here
		if ok {
			inputTokens, outputTokens, cost := parseTokensAndCost(cachedResponse, modelRequested, profile)
			logEntry.InputTokens = inputTokens
			logEntry.OutputTokens = outputTokens
			logEntry.TotalCost = cost

			// Update tokens and cost saved in analytics
			analyticsLock.Lock()
			cacheAnalytics.TokensSaved += inputTokens + outputTokens
			cacheAnalytics.CostSaved += cost
			analyticsLock.Unlock()

			log.Printf("Request %s: Redis cache hit saved %d tokens and $%.6f",
				requestID, inputTokens+outputTokens, cost)
		} else {
			log.Printf("Request %s: Model profile for cached response model '%s' not found. Cannot calculate tokens/cost for cache hit.", requestID, modelRequested)
			// Ensure these are zeroed out if profile not found, they default to 0 in struct but good to be explicit
			logEntry.InputTokens = 0
			logEntry.OutputTokens = 0
			logEntry.TotalCost = 0.0
		}
		logEntry.TaskType = "redis_cached_response" // Set task type for cached response
		logInferenceLog(logEntry)                   // Log cache hit
		return                                      // Request handled
	}

	// If exact match cache miss, try semantic cache for similar queries
	// Only for non-streaming factual queries and code generation for better accuracy
	if !streamRequested && (reqBodyMap["stream"] == nil || reqBodyMap["stream"] == false) &&
		(logEntry.TaskType == "factual_query" || logEntry.TaskType == "code_generation" ||
			logEntry.TaskType == "unknown" || logEntry.TaskType == "other") {

		semanticCachedResponse, semanticErr := findSimilarPromptInCache(r.Context(), prompt, modelRequested)
		if semanticErr != nil {
			log.Printf("Request %s: Error checking semantic cache: %v", requestID, semanticErr)
		} else if semanticCachedResponse != nil {
			log.Printf("Request %s: Semantic Cache HIT for prompt '%s'. Serving from semantic cache.",
				requestID, truncateString(prompt, 50))

			// Update cache analytics
			analyticsLock.Lock()
			cacheAnalytics.SemanticHits++
			analyticsLock.Unlock()

			// Also add to in-memory cache for faster future access if exact match requested
			exactCacheKey := getCacheKey(prompt, modelRequested)
			inMemoryCache.Set(exactCacheKey, string(semanticCachedResponse), CACHE_TTL)

			w.Header().Set("Content-Type", "application/json")
			w.Header().Set("X-Cache-Hit", "true")
			w.Header().Set("X-Cache-Level", "semantic")

			// Inject conversation_id into the response
			finalResponse, err := injectConversationID(semanticCachedResponse, conversationID)
			if err != nil {
				log.Printf("Request %s: Error injecting conversation ID into semantic cache response: %v", requestID, err)
				finalResponse = semanticCachedResponse // Fallback to original
			}
			w.Write(finalResponse)

			logEntry.StatusCode = int32(http.StatusOK)
			logEntry.ResponseTimestamp = time.Now()
			logEntry.LatencyMs = float64(time.Since(logEntry.Timestamp).Milliseconds())
			logEntry.ResponseBodySnippet = string(semanticCachedResponse)

			// For semantic cache hits, the policy applied is "semantic-cache-hit"
			logEntry.PolicyIDApplied = "semantic-cache-hit"
			logEntry.SelectedBackendID = "semantic-cache"
			logEntry.ModelUsed = modelRequested

			// Attempt to parse tokens from cached response for logging
			profile, ok := getModelProfileByID(modelRequested)
			if ok {
				inputTokens, outputTokens, cost := parseTokensAndCost(semanticCachedResponse, modelRequested, profile)
				logEntry.InputTokens = inputTokens
				logEntry.OutputTokens = outputTokens
				logEntry.TotalCost = cost

				// Update tokens and cost saved in analytics
				analyticsLock.Lock()
				cacheAnalytics.TokensSaved += inputTokens + outputTokens
				cacheAnalytics.CostSaved += cost
				analyticsLock.Unlock()

				log.Printf("Request %s: Semantic cache hit saved %d tokens and $%.6f",
					requestID, inputTokens+outputTokens, cost)
			}

			logEntry.TaskType = "semantic_cached_response"
			logInferenceLog(logEntry)
			return
		}
	}
	// Optimize the prompt if enabled
	optimizedPrompt := enhancedOptimizePrompt(prompt, true)
	originalPromptLength := len(prompt)
	optimizedPromptLength := len(optimizedPrompt)

	if optimizedPrompt != prompt {
		log.Printf("Request %s: Prompt optimized from %d to %d chars",
			requestID, originalPromptLength, optimizedPromptLength)

		// Update the request body with optimized prompt
		if reqBodyMap != nil {
			if messages, ok := reqBodyMap["messages"].([]interface{}); ok && len(messages) > 0 {
				if firstMessage, ok := messages[0].(map[string]interface{}); ok {
					firstMessage["content"] = optimizedPrompt
					if modifiedBody, err := json.Marshal(reqBodyMap); err == nil {
						requestBodyBytes = modifiedBody
					}
				}
			}
		}
	}

	// --- Step 1: Intelligent Prompt Analysis ---
	// Analyze the prompt to understand intent, complexity, and task type
	var promptAnalysis *PromptAnalysis
	if ENABLE_PROMPT_ANALYSIS {
		analysis, err := analyzePromptIntelligently(prompt)
		if err != nil {
			log.Printf("Request %s: Warning - Prompt analysis failed: %v, using fallback", requestID, err)
			promptAnalysis = createFallbackAnalysis(prompt)
		} else if analysis != nil {
			promptAnalysis = analysis
			log.Printf("Request %s: Prompt analysis - Intent: %s, TaskType: %s, Complexity: %s, Domain: %s, Confidence: %.2f",
				requestID, analysis.Intent, analysis.TaskType, analysis.Complexity, analysis.Domain, analysis.Confidence)
		}
	}

	// --- Step 1.5: Check for Active A/B Tests ---
	var routeResp AIOptimizerRouteResponse
	abTestRoute, isABTest := checkActiveABTests(prompt, modelRequested, logEntry.UserID)
	if isABTest {
		log.Printf("Request %s: Routing via A/B test %s to variant %s", requestID, abTestRoute.TestID, abTestRoute.Variant)
		// Use A/B test routing decision
		routeResp = AIOptimizerRouteResponse{
			SelectedBackendID: abTestRoute.ModelID,
			BackendURL:        abTestRoute.BackendURL,
			BackendType:       abTestRoute.BackendType,
			APIKey:            abTestRoute.APIKey,
			PolicyIDApplied:   "ab-test-" + abTestRoute.TestID,
			ModelUsed:         abTestRoute.ModelID,
			TaskType:          "ab_test",
		}
		// Store A/B test info for later tracking
		logEntry.ABTestID = abTestRoute.TestID
		logEntry.ABTestVariant = abTestRoute.Variant
	} else {
		// --- Step 2: Call AI Optimizer for Routing Decision ---
		// Prepare the request to AI Optimizer's /route endpoint
		// This is where the intelligent routing decision happens with prompt analysis
		optReq := OptimizationRequest{
			Prompt:          prompt,
			Model:           modelRequested,
			UserID:          logEntry.UserID,
			UserRoles:       logEntry.UserRoles,
			DataSensitivity: r.Header.Get("X-Data-Sensitivity"),
			PreferredLLMID:  r.Header.Get("X-Preferred-LLM-ID"), // Pass through preferred LLM if specified
			TokenBudget:     getTokenBudget(r),                  // Extract token budget from headers
			APIType:         "chat_completions",                 // Specify API type for chat completions
			Analysis:        promptAnalysis,                     // NEW: Include intelligent prompt analysis
		}

		// Log token budget information
		if optReq.TokenBudget != nil {
			log.Printf("Request %s: Token budget - MaxInputTokens: %d, MaxOutputTokens: %d, Priority: %d",
				requestID, optReq.TokenBudget.MaxInputTokens, optReq.TokenBudget.MaxOutputTokens, optReq.TokenBudget.Priority)
		} else {
			log.Printf("Request %s: No token budget provided (this should not happen with auto-detection)", requestID)
		}

		// Update cache analytics for cache miss
		analyticsLock.Lock()
		cacheAnalytics.Misses++
		analyticsLock.Unlock()

		optReqJSON, err := json.Marshal(optReq)
		if err != nil {
			log.Printf("Request %s: Error marshaling optimizer request: %v", requestID, err)
			http.Error(w, "Internal server error", http.StatusInternalServerError)
			return
		}

		// Optimize the prompt if enabled
		optimizedPrompt = enhancedOptimizePrompt(prompt, true)
		if optimizedPrompt != prompt {
			log.Printf("Request %s: Prompt optimized from %d to %d chars",
				requestID, len(prompt), len(optimizedPrompt))

			// Update the request body with optimized prompt
			if reqBodyMap != nil {
				if messages, ok := reqBodyMap["messages"].([]interface{}); ok && len(messages) > 0 {
					if firstMessage, ok := messages[0].(map[string]interface{}); ok {
						firstMessage["content"] = optimizedPrompt
						if modifiedBody, err := json.Marshal(reqBodyMap); err == nil {
							requestBodyBytes = modifiedBody
						}
					}
				}
			}
		}

		// Enforce token budget if provided

		// Call AI Optimizer's /route endpoint
		log.Printf("Request %s: Calling AI Optimizer for routing decision", requestID)
		aiOptResp, err := http.Post(aiOptimizerURL+"/route", "application/json", bytes.NewBuffer(optReqJSON))
		if err != nil {
			log.Printf("Request %s: Error calling AI Optimizer: %v", requestID, err)
			http.Error(w, "Error determining optimal backend", http.StatusInternalServerError)
			return
		}
		defer aiOptResp.Body.Close()

		// Parse AI Optimizer response
		// Note: routeResp is already declared above for A/B testing
		if err := json.NewDecoder(aiOptResp.Body).Decode(&routeResp); err != nil {
			log.Printf("Request %s: Error parsing AI Optimizer response: %v", requestID, err)
			http.Error(w, "Error processing routing decision", http.StatusInternalServerError)
			return
		}

		// Check if the AI Optimizer returned an error
		if routeResp.Error != "" {
			log.Printf("Request %s: AI Optimizer returned error: %s", requestID, routeResp.Error)
			http.Error(w, "Error from routing service: "+routeResp.Error, http.StatusInternalServerError)
			return
		}

		// Check if AI Optimizer has a cached response
		if routeResp.FromCache && routeResp.CachedResponseBody != "" {
			log.Printf("Request %s: AI Optimizer returned cached response for prompt '%s'",
				requestID, truncateString(prompt, 50))

			// Write cached response from AI Optimizer
			w.Header().Set("Content-Type", "application/json")
			w.Header().Set("X-Cache-Hit", "true")
			w.Header().Set("X-Cache-Level", "ai-optimizer")

			// Inject conversation_id into the response
			finalResponse, err := injectConversationID([]byte(routeResp.CachedResponseBody), conversationID)
			if err != nil {
				log.Printf("Request %s: Error injecting conversation ID into AI Optimizer cache response: %v", requestID, err)
				finalResponse = []byte(routeResp.CachedResponseBody) // Fallback to original
			}
			w.Write(finalResponse)

			// Complete logging and return
			logEntry.StatusCode = int32(http.StatusOK)
			logEntry.ResponseTimestamp = time.Now()
			logEntry.LatencyMs = float64(time.Since(logEntry.Timestamp).Milliseconds())
			logEntry.ResponseBodySnippet = routeResp.CachedResponseBody
			logEntry.SelectedBackendID = "ai-optimizer-cache"
			logEntry.PolicyIDApplied = routeResp.PolicyIDApplied
			logEntry.ModelUsed = routeResp.ModelUsed
			logEntry.TaskType = "ai_optimizer_cached_response"

			// Attempt to parse tokens from cached response for logging
			profile, ok := getModelProfileByID(routeResp.ModelUsed)
			if ok {
				inputTokens, outputTokens, cost := parseTokensAndCost(
					[]byte(routeResp.CachedResponseBody), routeResp.ModelUsed, profile)
				logEntry.InputTokens = inputTokens
				logEntry.OutputTokens = outputTokens
				logEntry.TotalCost = cost

				// Update tokens and cost saved in analytics
				analyticsLock.Lock()
				cacheAnalytics.TokensSaved += inputTokens + outputTokens
				cacheAnalytics.CostSaved += cost
				analyticsLock.Unlock()
			}

			logInferenceLog(logEntry)
			return
		}
	} // End of else block for AI Optimizer call

	// --- Step 2: Route Request to Selected Backend ---
	log.Printf("Request %s: Routing to %s (%s) via policy %s",
		requestID, routeResp.SelectedBackendID, routeResp.BackendURL, routeResp.PolicyIDApplied)

	// Save decision from AI Optimizer in our log entry
	logEntry.SelectedBackendID = routeResp.SelectedBackendID
	logEntry.BackendURL = routeResp.BackendURL
	logEntry.BackendType = routeResp.BackendType
	logEntry.PolicyIDApplied = routeResp.PolicyIDApplied
	logEntry.ModelUsed = routeResp.ModelUsed
	logEntry.TaskType = routeResp.TaskType

	// Transform request payload for the target backend if needed
	transformedBody, err := transformRequestPayload(requestBodyBytes, routeResp.BackendType)
	if err != nil {
		log.Printf("Request %s: Error transforming request payload: %v", requestID, err)
		http.Error(w, "Error preparing request for backend", http.StatusInternalServerError)
		return
	}

	// Create request to backend
	backendReq, err := http.NewRequestWithContext(r.Context(), "POST", routeResp.BackendURL, bytes.NewBuffer(transformedBody))
	if err != nil {
		log.Printf("Request %s: Error creating backend request: %v", requestID, err)
		http.Error(w, "Error preparing request for backend", http.StatusInternalServerError)
		return
	}

	// Copy original headers to backend request (excluding Authorization for Google APIs)
	for key, values := range r.Header {
		// Skip Authorization header for Google APIs as we'll use query parameter instead
		if key == "Authorization" && (routeResp.BackendType == "google-external" || routeResp.BackendType == "google") {
			continue
		}
		// Skip or modify Accept-Encoding to only include compression formats we can handle
		if key == "Accept-Encoding" {
			// Only request gzip compression, which we can handle
			// Remove brotli (br) and other compression formats that cause issues
			backendReq.Header.Set("Accept-Encoding", "gzip, deflate")
			continue
		}
		for _, value := range values {
			backendReq.Header.Add(key, value)
		}
	}

	// Get the model profile to handle different auth methods and ensure it's in scope for later use.
	profile, profileExists := getModelProfileByID(routeResp.SelectedBackendID)
	apiKey := routeResp.APIKey
	if apiKey == "" && profileExists {
		apiKey = profile.APIKey
	}

	// Handle different authentication schemes based on backend type
	log.Printf("Request %s: Authentication debug - profileExists: %t, backendType: %s, apiKey: %s",
		requestID, profileExists,
		func() string {
			if profileExists {
				return profile.BackendType
			} else {
				return "N/A"
			}
		}(),
		func() string {
			if apiKey != "" {
				return "[REDACTED]"
			} else {
				return "[EMPTY]"
			}
		}())

	if (profileExists && (profile.BackendType == "google-external" || profile.BackendType == "google")) ||
		(!profileExists && (routeResp.BackendType == "google-external" || routeResp.BackendType == "google")) {
		// Google Gemini API requires the key as a URL parameter
		if apiKey == "" {
			// Fallback to environment variable if no API key from profile
			apiKey = os.Getenv("GOOGLE_API_KEY")
		}
		if apiKey != "" {
			parsedURL, err := url.Parse(backendReq.URL.String())
			if err == nil {
				q := parsedURL.Query()
				q.Set("key", apiKey)
				parsedURL.RawQuery = q.Encode()
				backendReq.URL = parsedURL
				log.Printf("Request %s: Set Google API key in URL: %s", requestID, backendReq.URL.String())
			} else {
				log.Printf("Request %s: Error parsing URL for API key injection: %v", requestID, err)
			}
		} else {
			log.Printf("Request %s: No API key available for Google authentication", requestID)
		}
	} else if profileExists && (profile.BackendType == "cohere-external" || profile.BackendType == "cohere") {
		// Cohere API uses Bearer token authentication
		if apiKey != "" {
			backendReq.Header.Set("Authorization", "Bearer "+apiKey)
		}
	} else {
		// Other backends (OpenAI, Anthropic, etc.) use Bearer token
		if apiKey != "" {
			backendReq.Header.Set("Authorization", "Bearer "+apiKey)
		}
	}

	// Ensure content type is set
	backendReq.Header.Set("Content-Type", "application/json")

	// Debug: Log the request details for Google and OpenAI APIs
	if profileExists && (profile.BackendType == "google-external" || profile.BackendType == "google") {
		log.Printf("Request %s: Google API request details - URL: %s", requestID, backendReq.URL.String())
		log.Printf("Request %s: Google API request headers: %v", requestID, backendReq.Header)
		log.Printf("Request %s: Google API request body: %s", requestID, string(transformedBody))
	}
	if profileExists && (profile.BackendType == "openai-external" || profile.BackendType == "openai") {
		log.Printf("Request %s: OpenAI API request details - URL: %s", requestID, backendReq.URL.String())
		log.Printf("Request %s: OpenAI API request headers: %v", requestID, backendReq.Header)
		log.Printf("Request %s: OpenAI API request body: %s", requestID, string(transformedBody))
	}

	// Call the backend LLM API
	client := &http.Client{Timeout: 60 * time.Second} // Longer timeout for LLM requests
	startTime := time.Now()
	backendResp, err := client.Do(backendReq)
	requestDuration := time.Since(startTime)

	if err != nil {
		log.Printf("Request %s: Error calling backend: %v", requestID, err)
		http.Error(w, "Error from LLM backend: "+err.Error(), http.StatusInternalServerError)
		logEntry.Error = err.Error()
		logEntry.StatusCode = http.StatusInternalServerError
		logEntry.ResponseTimestamp = time.Now()
		logEntry.LatencyMs = float64(requestDuration.Milliseconds())
		logInferenceLog(logEntry)
		return
	}
	defer backendResp.Body.Close()

	// Handle streaming vs non-streaming responses
	if streamRequested {
		// Handle streaming response
		handleStreamingResponse(w, backendResp, requestID, routeResp.BackendType, conversationID, &logEntry, requestDuration)
		return
	}

	// Handle compressed responses before reading
	var reader io.Reader = backendResp.Body
	contentEncoding := backendResp.Header.Get("Content-Encoding")
	switch contentEncoding {
	case "gzip":
		gzipReader, err := gzip.NewReader(backendResp.Body)
		if err != nil {
			log.Printf("Request %s: Error creating gzip reader: %v", requestID, err)
			http.Error(w, "Error decompressing response", http.StatusInternalServerError)
			return
		}
		defer gzipReader.Close()
		reader = gzipReader
	case "deflate":
		deflateReader := flate.NewReader(backendResp.Body)
		defer deflateReader.Close()
		reader = deflateReader
	case "br":
		log.Printf("Request %s: Brotli compression detected but not supported. This should not happen with our Accept-Encoding header.", requestID)
		http.Error(w, "Unsupported compression format received from backend", http.StatusInternalServerError)
		logEntry.Error = "Brotli compression not supported"
		logEntry.StatusCode = http.StatusInternalServerError
		logEntry.ResponseTimestamp = time.Now()
		logEntry.LatencyMs = float64(time.Since(logEntry.Timestamp).Milliseconds())
		logInferenceLog(logEntry)
		return
	}

	// Read the backend response for non-streaming
	backendResponseBody, err := io.ReadAll(reader)
	if err != nil {
		log.Printf("Request %s: Error reading backend response: %v", requestID, err)
		http.Error(w, "Error reading LLM response", http.StatusInternalServerError)
		logEntry.Error = err.Error()
		logEntry.StatusCode = http.StatusInternalServerError
		logEntry.ResponseTimestamp = time.Now()
		logEntry.LatencyMs = float64(requestDuration.Milliseconds())
		logInferenceLog(logEntry)
		return
	}

	// Log backend response status for debugging
	log.Printf("Request %s: Backend response status: %d", requestID, backendResp.StatusCode)
	log.Printf("Request %s: Backend response headers: %v", requestID, backendResp.Header)
	log.Printf("Request %s: Backend response body length: %d", requestID, len(backendResponseBody))
	previewLen := 200
	if len(backendResponseBody) < previewLen {
		previewLen = len(backendResponseBody)
	}
	log.Printf("Request %s: Backend response body preview: %s", requestID, string(backendResponseBody[:previewLen]))

	// Transform the response payload back to OpenAI format if necessary
	finalResponseBody, err := transformResponsePayload(backendResponseBody, routeResp.BackendType)
	if err != nil {
		log.Printf("Request %s: Error transforming backend response payload: %v", requestID, err)
		// Return the original body on error
		finalResponseBody = backendResponseBody
	}

	// Parse token usage and cost from the original response body
	inputTokens, outputTokens, totalCost := parseTokensAndCost(backendResponseBody, routeResp.ModelUsed, profile)
	logEntry.InputTokens = inputTokens
	logEntry.OutputTokens = outputTokens
	logEntry.TotalCost = totalCost

	// Log token usage
	log.Printf("Request %s: Used %d input tokens, %d output tokens, $%.6f cost",
		requestID, inputTokens, outputTokens, totalCost)

	// Store transformed response in cache for future requests if not a streaming request
	if !streamRequested {
		if err := setLLMResponseInCache(r.Context(), cacheKey, finalResponseBody); err != nil { // Use finalResponseBody
			log.Printf("Request %s: Warning - Failed to store response in cache: %v", requestID, err)
		} else {
			// Also store in memory cache for faster access
			inMemoryCache.Set(cacheKey, string(finalResponseBody), CACHE_TTL) // Use finalResponseBody
			log.Printf("Request %s: Response stored in cache with key: %s", requestID, cacheKey)
		}

		// Also store in semantic cache for similar future queries
		if logEntry.TaskType == "factual_query" || logEntry.TaskType == "code_generation" ||
			logEntry.TaskType == "unknown" || logEntry.TaskType == "other" {
			if err := storeInSemanticCache(r.Context(), prompt, routeResp.ModelUsed, finalResponseBody, // Use finalResponseBody
				inputTokens, outputTokens, totalCost); err != nil {
				log.Printf("Request %s: Warning - Failed to store in semantic cache: %v", requestID, err)
			}
		}
	}

	// Copy all headers from backend response
	for key, values := range backendResp.Header {
		for _, value := range values {
			w.Header().Add(key, value)
		}
	}

	// IMPORTANT: Delete the original Content-Length header.
	// We are modifying the response by injecting the conversation_id, so the length
	// will change. The http.ResponseWriter will automatically calculate the correct
	// Content-Length for the final response.
	w.Header().Del("Content-Length")

	// Update A/B test performance metrics if this was an A/B test request
	if logEntry.ABTestID != "" {
		go updateABTestPerformance(logEntry.ABTestID, logEntry.ABTestVariant,
			float64(requestDuration.Milliseconds()), totalCost, backendResp.StatusCode == 200)
	}

	// Complete logging
	logEntry.StatusCode = int32(backendResp.StatusCode)
	logEntry.ResponseTimestamp = time.Now()
	logEntry.LatencyMs = float64(requestDuration.Milliseconds())
	logEntry.ResponseBodySnippet = string(finalResponseBody) // Log the transformed response
	logInferenceLog(logEntry)

	// Inject conversation_id into the final response if it's not a streaming request
	finalResponseWithConvID := finalResponseBody
	if !streamRequested {
		var err error
		finalResponseWithConvID, err = injectConversationID(finalResponseBody, conversationID)
		if err != nil {
			log.Printf("Request %s: Error injecting conversation ID into final response: %v", requestID, err)
			finalResponseWithConvID = finalResponseBody // Fallback to original response
		}
	}

	// Return the backend response
	w.WriteHeader(backendResp.StatusCode)
	w.Write(finalResponseWithConvID)

	// Increment user usage for successful requests in startup edition
	if backendResp.StatusCode == http.StatusOK {
		if err := IncrementUserUsage(context.Background(), logEntry.UserID, userTier); err != nil {
			log.Printf("Request %s: Error incrementing user usage: %v", requestID, err)
		}
	}
}

// parseTokensAndCost extracts token usage and calculates cost from response.
func parseTokensAndCost(responseBody []byte, _ string, profile ModelProfile) (int64, int64, float64) {
	var inputTokens, outputTokens int64
	var totalCost float64

	// Try to parse the response based on the API format
	var openaiResp OpenAICompletionResponse
	var anthropicResp AnthropicMessagesResponse
	var geminiResp GeminiGenerateContentResponse
	var cohereResp CohereChatResponse
	var hfResp HuggingFaceChatResponse
	var mistralResp MistralChatResponse
	var grokResp GrokChatResponse

	// Try to parse as OpenAI format first (most common)
	if err := json.Unmarshal(responseBody, &openaiResp); err == nil {
		if openaiResp.Usage.PromptTokens > 0 || openaiResp.Usage.CompletionTokens > 0 {
			inputTokens = int64(openaiResp.Usage.PromptTokens)
			outputTokens = int64(openaiResp.Usage.CompletionTokens)
		}
	} else if err := json.Unmarshal(responseBody, &anthropicResp); err == nil {
		// Try Anthropic format
		if anthropicResp.Usage.InputTokens > 0 || anthropicResp.Usage.OutputTokens > 0 {
			inputTokens = int64(anthropicResp.Usage.InputTokens)
			outputTokens = int64(anthropicResp.Usage.OutputTokens)
		}
	} else if err := json.Unmarshal(responseBody, &cohereResp); err == nil {
		// Try Cohere format
		if cohereResp.Meta.BilledUnits.InputTokens > 0 || cohereResp.Meta.BilledUnits.OutputTokens > 0 {
			inputTokens = int64(cohereResp.Meta.BilledUnits.InputTokens)
			outputTokens = int64(cohereResp.Meta.BilledUnits.OutputTokens)
		}
	} else if err := json.Unmarshal(responseBody, &hfResp); err == nil {
		// Try Hugging Face format
		if hfResp.Usage.PromptTokens > 0 || hfResp.Usage.CompletionTokens > 0 {
			inputTokens = int64(hfResp.Usage.PromptTokens)
			outputTokens = int64(hfResp.Usage.CompletionTokens)
		}
	} else if err := json.Unmarshal(responseBody, &mistralResp); err == nil {
		// Try Mistral format
		if mistralResp.Usage.PromptTokens > 0 || mistralResp.Usage.CompletionTokens > 0 {
			inputTokens = int64(mistralResp.Usage.PromptTokens)
			outputTokens = int64(mistralResp.Usage.CompletionTokens)
		}
	} else if err := json.Unmarshal(responseBody, &grokResp); err == nil {
		// Try GROK format
		if grokResp.Usage.PromptTokens > 0 || grokResp.Usage.CompletionTokens > 0 {
			inputTokens = int64(grokResp.Usage.PromptTokens)
			outputTokens = int64(grokResp.Usage.CompletionTokens)
		}
	} else if err := json.Unmarshal(responseBody, &geminiResp); err == nil {
		// Try Gemini format
		if geminiResp.UsageMetadata.PromptTokenCount > 0 || geminiResp.UsageMetadata.CandidatesTokenCount > 0 {
			inputTokens = int64(geminiResp.UsageMetadata.PromptTokenCount)
			outputTokens = int64(geminiResp.UsageMetadata.CandidatesTokenCount)
		}
	}

	// Calculate cost based on the model profile's pricing
	if inputTokens > 0 || outputTokens > 0 {
		// Use the provided profile directly
		totalCost = float64(inputTokens)*profile.CostPerInputToken +
			float64(outputTokens)*profile.CostPerOutputToken
	}

	return inputTokens, outputTokens, totalCost
}

// logInferenceLog sends the inference log to Kafka for analysis.
func logInferenceLog(log InferenceLog) {
	// Marshal the log entry to JSON
	logJSON, err := json.Marshal(log)
	if err != nil {
		fmt.Printf("Error marshaling inference log: %v\n", err)
		return
	}

	// Write to Kafka
	err = kafkaWriter.WriteMessages(context.Background(),
		kafka.Message{
			Key:   []byte(log.RequestID),
			Value: logJSON,
		},
	)
	if err != nil {
		fmt.Printf("Error writing inference log to Kafka: %v\n", err)
	}
}

// handleCacheAnalyticsRequest returns cache performance metrics.
func handleCacheAnalyticsRequest(w http.ResponseWriter, r *http.Request) {
	analyticsLock.RLock()
	defer analyticsLock.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(cacheAnalytics)
}

// handleCacheManagementRequest handles cache administrative operations.
func handleCacheManagementRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		Action string `json:"action"`
		Key    string `json:"key,omitempty"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	ctx := r.Context()

	switch req.Action {
	case "clear_all":
		// Clear all cache entries
		keys, err := redisClient.Keys(ctx, REDIS_CACHE_KEY_PREFIX+"*").Result()
		if err != nil {
			http.Error(w, fmt.Sprintf("Error retrieving cache keys: %v", err), http.StatusInternalServerError)
			return
		}

		if len(keys) > 0 {
			if err := redisClient.Del(ctx, keys...).Err(); err != nil {
				http.Error(w, fmt.Sprintf("Error clearing cache: %v", err), http.StatusInternalServerError)
				return
			}
		}

		// Clear semantic cache
		semanticKeys, err := redisClient.Keys(ctx, SEMANTIC_CACHE_KEY_PREFIX+"*").Result()
		if err == nil && len(semanticKeys) > 0 {
			_ = redisClient.Del(ctx, semanticKeys...)
		}

		// Clear in-memory cache
		inMemoryCache.Flush()
		semanticCache = sync.Map{}

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"success","message":"Cache cleared"}`))

	case "delete_key":
		if req.Key == "" {
			http.Error(w, "Key parameter required", http.StatusBadRequest)
			return
		}

		// Delete from Redis
		deleted, err := redisClient.Del(ctx, req.Key).Result()
		if err != nil {
			http.Error(w, fmt.Sprintf("Error deleting key: %v", err), http.StatusInternalServerError)
			return
		}

		// Delete from in-memory cache too
		inMemoryCache.Delete(req.Key)

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(fmt.Sprintf(`{"status":"success","deleted":%d}`, deleted)))

	case "reset_analytics":
		// Reset analytics counters
		analyticsLock.Lock()
		cacheAnalytics = CacheAnalytics{
			Hits:           0,
			Misses:         0,
			SemanticHits:   0,
			TokensSaved:    0,
			CostSaved:      0.0,
			AverageLatency: 0,
		}
		analyticsLock.Unlock()

		// Also reset Redis counters
		pipe := redisClient.Pipeline()
		pipe.Del(ctx, CACHE_HIT_COUNTER_KEY)
		pipe.Del(ctx, CACHE_MISS_COUNTER_KEY)
		pipe.Del(ctx, SEMANTIC_CACHE_HIT_COUNTER_KEY)
		pipe.Del(ctx, CACHE_ANALYTICS_PREFIX+"tokens_saved")
		pipe.Del(ctx, CACHE_ANALYTICS_PREFIX+"cost_saved")
		pipe.Exec(ctx)

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"success","message":"Analytics reset"}`))

	default:
		http.Error(w, "Unknown action", http.StatusBadRequest)
	}
}

// semanticPromptCompression is implemented in prompt_optimization.go

// preserveStructureCompression is implemented in prompt_optimization.go

// essentialContentCompression is implemented in prompt_optimization.go

// getTokenBudget extracts token budget from request headers or creates intelligent defaults.
func getTokenBudget(r *http.Request) *TokenBudget {
	// Check for X-Max-Input-Tokens and X-Max-Output-Tokens headers
	maxInputTokensStr := r.Header.Get("X-Max-Input-Tokens")
	maxOutputTokensStr := r.Header.Get("X-Max-Output-Tokens")

	// If either header is present, attempt to parse them
	if maxInputTokensStr != "" || maxOutputTokensStr != "" {
		maxInputTokens, err1 := strconv.Atoi(maxInputTokensStr)
		maxOutputTokens, err2 := strconv.Atoi(maxOutputTokensStr)

		// If there's an error parsing either value, log it and return nil
		if err1 != nil || err2 != nil {
			log.Printf("Error parsing token budget headers: inputErr=%v, outputErr=%v", err1, err2)
			return createDefaultTokenBudget(r)
		}

		// If parsing was successful, return a TokenBudget struct
		return &TokenBudget{
			MaxInputTokens:  maxInputTokens,
			MaxOutputTokens: maxOutputTokens,
			Priority:        10, // Default priority
		}
	}

	// If neither header is present, create intelligent defaults
	return createDefaultTokenBudget(r)
}

// createDefaultTokenBudget creates intelligent token budget defaults based on request analysis
func createDefaultTokenBudget(r *http.Request) *TokenBudget {
	// Default budget values
	defaultInputTokens := 4000  // Conservative default for input
	defaultOutputTokens := 1000 // Conservative default for output
	priority := 5               // Medium priority by default

	// Try to get the request body to analyze content
	if r.Body != nil {
		// Read the body (we'll need to restore it later)
		bodyBytes, err := io.ReadAll(r.Body)
		if err == nil {
			// Restore the body for later use
			r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

			// Parse the request to understand the content
			var requestData map[string]interface{}
			if err := json.Unmarshal(bodyBytes, &requestData); err == nil {
				// Analyze the request to set appropriate budgets
				if messages, ok := requestData["messages"].([]interface{}); ok {
					// Estimate input tokens based on message content
					totalInputLength := 0
					for _, msg := range messages {
						if msgMap, ok := msg.(map[string]interface{}); ok {
							if content, ok := msgMap["content"].(string); ok {
								totalInputLength += len(content)
							}
						}
					}

					// Rough token estimation (1 token ≈ 4 characters)
					estimatedInputTokens := totalInputLength / 4

					// Set input budget with some buffer
					if estimatedInputTokens > 0 {
						defaultInputTokens = int(float64(estimatedInputTokens) * 1.2) // 20% buffer
						if defaultInputTokens < 100 {
							defaultInputTokens = 100 // Minimum
						} else if defaultInputTokens > 32000 {
							defaultInputTokens = 32000 // Maximum for most models
						}
					}

					// Adjust output tokens based on input complexity
					if estimatedInputTokens > 8000 {
						defaultOutputTokens = 4000 // Large responses for complex inputs
						priority = 3               // Lower priority for large requests
					} else if estimatedInputTokens > 2000 {
						defaultOutputTokens = 2000 // Medium responses
						priority = 5               // Medium priority
					} else {
						defaultOutputTokens = 1000 // Standard responses
						priority = 7               // Higher priority for smaller requests
					}
				}

				// Check for specific request types that might need different budgets
				if model, ok := requestData["model"].(string); ok {
					// Adjust based on model capabilities
					if strings.Contains(model, "gpt-4") {
						// GPT-4 models can handle larger contexts
						if defaultInputTokens < 8000 {
							defaultInputTokens = 8000
						}
					} else if strings.Contains(model, "claude") {
						// Claude models have large context windows
						if defaultInputTokens < 16000 {
							defaultInputTokens = 16000
						}
					}
				}

				// Check for streaming preference
				if stream, ok := requestData["stream"].(bool); ok && stream {
					// Streaming requests might want more output tokens
					defaultOutputTokens = int(float64(defaultOutputTokens) * 1.5)
				}
			}
		}
	}

	return &TokenBudget{
		MaxInputTokens:  defaultInputTokens,
		MaxOutputTokens: defaultOutputTokens,
		Priority:        priority,
	}
}

// injectConversationID adds the conversation_id to a JSON response body.
// If the body is not a valid JSON object, it returns the original body.
func injectConversationID(responseBody []byte, conversationID string) ([]byte, error) {
	if conversationID == "" {
		return responseBody, nil // Nothing to inject
	}

	var responseMap map[string]interface{}
	if err := json.Unmarshal(responseBody, &responseMap); err != nil {
		// Not a valid JSON object, cannot inject. Return original.
		// This is expected for non-JSON or malformed JSON responses.
		return responseBody, nil
	}

	// Add or overwrite the conversation_id
	responseMap["conversation_id"] = conversationID

	// Marshal back to JSON
	modifiedBody, err := json.Marshal(responseMap)
	if err != nil {
		// This should be rare, but handle it.
		return nil, fmt.Errorf("failed to re-marshal response with conversation_id: %w", err)
	}

	return modifiedBody, nil
}

// transformResponsePayload converts responses from different backends to a standard OpenAI format.
func transformResponsePayload(backendBody []byte, backendType string) ([]byte, error) {
	if backendType == "cohere-external" || backendType == "cohere" {
		// Transform Cohere response to OpenAI format
		var cohereResp CohereChatResponse
		if err := json.Unmarshal(backendBody, &cohereResp); err != nil {
			return nil, fmt.Errorf("failed to unmarshal Cohere response: %w", err)
		}

		// Create OpenAI-compatible response
		openAIResp := OpenAICompletionResponse{
			ID:      fmt.Sprintf("chatcmpl-%s", strings.ReplaceAll(uuid.New().String(), "-", "")),
			Object:  "chat.completion",
			Created: time.Now().Unix(),
			Model:   "cohere-proxy-transformed",
			Choices: []struct {
				Index        int               `json:"index"`
				Message      map[string]string `json:"message"`
				LogProbs     interface{}       `json:"logprobs"`
				FinishReason string            `json:"finish_reason"`
			}{
				{
					Index: 0,
					Message: map[string]string{
						"role":    "assistant",
						"content": cohereResp.Text,
					},
					LogProbs:     nil,
					FinishReason: "stop",
				},
			},
			Usage: struct {
				PromptTokens     int `json:"prompt_tokens"`
				CompletionTokens int `json:"completion_tokens"`
				TotalTokens      int `json:"total_tokens"`
			}{
				PromptTokens:     cohereResp.Meta.BilledUnits.InputTokens,
				CompletionTokens: cohereResp.Meta.BilledUnits.OutputTokens,
				TotalTokens:      cohereResp.Meta.BilledUnits.InputTokens + cohereResp.Meta.BilledUnits.OutputTokens,
			},
		}

		transformedBody, err := json.Marshal(openAIResp)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal OpenAI-compatible response: %w", err)
		}
		return transformedBody, nil
	} else if backendType == "huggingface-external" || backendType == "huggingface" {
		// Hugging Face Inference API typically returns OpenAI-compatible responses
		// but let's handle any potential differences
		var hfResp HuggingFaceChatResponse
		if err := json.Unmarshal(backendBody, &hfResp); err != nil {
			// If it fails to parse as HF format, assume it's already OpenAI-compatible
			return backendBody, nil
		}

		// If it parsed successfully, it's likely already in OpenAI format
		// but we can transform it to ensure consistency
		openAIResp := OpenAICompletionResponse{
			ID:      hfResp.ID,
			Object:  hfResp.Object,
			Created: hfResp.Created,
			Model:   hfResp.Model,
			Choices: []struct {
				Index        int               `json:"index"`
				Message      map[string]string `json:"message"`
				LogProbs     interface{}       `json:"logprobs"`
				FinishReason string            `json:"finish_reason"`
			}{},
			Usage: struct {
				PromptTokens     int `json:"prompt_tokens"`
				CompletionTokens int `json:"completion_tokens"`
				TotalTokens      int `json:"total_tokens"`
			}{
				PromptTokens:     hfResp.Usage.PromptTokens,
				CompletionTokens: hfResp.Usage.CompletionTokens,
				TotalTokens:      hfResp.Usage.TotalTokens,
			},
		}

		// Convert choices
		for _, choice := range hfResp.Choices {
			openAIResp.Choices = append(openAIResp.Choices, struct {
				Index        int               `json:"index"`
				Message      map[string]string `json:"message"`
				LogProbs     interface{}       `json:"logprobs"`
				FinishReason string            `json:"finish_reason"`
			}{
				Index: choice.Index,
				Message: map[string]string{
					"role":    choice.Message.Role,
					"content": choice.Message.Content,
				},
				LogProbs:     nil,
				FinishReason: choice.FinishReason,
			})
		}

		transformedBody, err := json.Marshal(openAIResp)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal OpenAI-compatible response: %w", err)
		}
		return transformedBody, nil
	} else if backendType == "mistral-external" || backendType == "mistral" {
		// Mistral API returns OpenAI-compatible responses, but let's handle any potential differences
		var mistralResp MistralChatResponse
		if err := json.Unmarshal(backendBody, &mistralResp); err != nil {
			// If it fails to parse as Mistral format, assume it's already OpenAI-compatible
			return backendBody, nil
		}

		// Transform to ensure OpenAI compatibility
		openAIResp := OpenAICompletionResponse{
			ID:      mistralResp.ID,
			Object:  mistralResp.Object,
			Created: mistralResp.Created,
			Model:   mistralResp.Model,
			Choices: []struct {
				Index        int               `json:"index"`
				Message      map[string]string `json:"message"`
				LogProbs     interface{}       `json:"logprobs"`
				FinishReason string            `json:"finish_reason"`
			}{},
			Usage: struct {
				PromptTokens     int `json:"prompt_tokens"`
				CompletionTokens int `json:"completion_tokens"`
				TotalTokens      int `json:"total_tokens"`
			}{
				PromptTokens:     mistralResp.Usage.PromptTokens,
				CompletionTokens: mistralResp.Usage.CompletionTokens,
				TotalTokens:      mistralResp.Usage.TotalTokens,
			},
		}

		// Convert choices
		for _, choice := range mistralResp.Choices {
			openAIResp.Choices = append(openAIResp.Choices, struct {
				Index        int               `json:"index"`
				Message      map[string]string `json:"message"`
				LogProbs     interface{}       `json:"logprobs"`
				FinishReason string            `json:"finish_reason"`
			}{
				Index: choice.Index,
				Message: map[string]string{
					"role":    choice.Message.Role,
					"content": choice.Message.Content,
				},
				LogProbs:     nil,
				FinishReason: choice.FinishReason,
			})
		}

		transformedBody, err := json.Marshal(openAIResp)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal OpenAI-compatible response: %w", err)
		}
		return transformedBody, nil
	} else if backendType == "grok-external" || backendType == "grok" || backendType == "xai-external" || backendType == "xai" {
		// GROK API returns OpenAI-compatible responses, but let's handle any potential differences
		var grokResp GrokChatResponse
		if err := json.Unmarshal(backendBody, &grokResp); err != nil {
			// If it fails to parse as GROK format, assume it's already OpenAI-compatible
			return backendBody, nil
		}

		// Transform to ensure OpenAI compatibility
		openAIResp := OpenAICompletionResponse{
			ID:      grokResp.ID,
			Object:  grokResp.Object,
			Created: grokResp.Created,
			Model:   grokResp.Model,
			Choices: []struct {
				Index        int               `json:"index"`
				Message      map[string]string `json:"message"`
				LogProbs     interface{}       `json:"logprobs"`
				FinishReason string            `json:"finish_reason"`
			}{},
			Usage: struct {
				PromptTokens     int `json:"prompt_tokens"`
				CompletionTokens int `json:"completion_tokens"`
				TotalTokens      int `json:"total_tokens"`
			}{
				PromptTokens:     grokResp.Usage.PromptTokens,
				CompletionTokens: grokResp.Usage.CompletionTokens,
				TotalTokens:      grokResp.Usage.TotalTokens,
			},
		}

		// Convert choices
		for _, choice := range grokResp.Choices {
			openAIResp.Choices = append(openAIResp.Choices, struct {
				Index        int               `json:"index"`
				Message      map[string]string `json:"message"`
				LogProbs     interface{}       `json:"logprobs"`
				FinishReason string            `json:"finish_reason"`
			}{
				Index: choice.Index,
				Message: map[string]string{
					"role":    choice.Message.Role,
					"content": choice.Message.Content,
				},
				LogProbs:     nil,
				FinishReason: choice.FinishReason,
			})
		}

		transformedBody, err := json.Marshal(openAIResp)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal OpenAI-compatible response: %w", err)
		}
		return transformedBody, nil
	} else if backendType != "google-external" && backendType != "google" {
		// If not a Google, Cohere, Hugging Face, Mistral, or GROK backend, assume it's already OpenAI-compatible.
		return backendBody, nil
	}

	// It's a Google response, so we need to transform it.
	log.Printf("Raw Gemini response body: %s", string(backendBody))

	var geminiResp GeminiGenerateContentResponse
	if err := json.Unmarshal(backendBody, &geminiResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal Gemini response: %w", err)
	}

	// Debug logging for Gemini response
	log.Printf("Gemini response debug - Candidates count: %d", len(geminiResp.Candidates))
	if len(geminiResp.Candidates) > 0 {
		log.Printf("Gemini response debug - First candidate parts count: %d", len(geminiResp.Candidates[0].Content.Parts))
		if len(geminiResp.Candidates[0].Content.Parts) > 0 {
			log.Printf("Gemini response debug - First part text: %s", geminiResp.Candidates[0].Content.Parts[0].Text)
		}
	}

	// Create a new OpenAI-compatible response.
	openAIResp := OpenAICompletionResponse{
		ID:      fmt.Sprintf("chatcmpl-%s", strings.ReplaceAll(uuid.New().String(), "-", "")),
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   "gemini-proxy-transformed", // Indicate that the response was transformed.
		Choices: []struct {
			Index        int               `json:"index"`
			Message      map[string]string `json:"message"`
			LogProbs     interface{}       `json:"logprobs"`
			FinishReason string            `json:"finish_reason"`
		}{},
		Usage: struct {
			PromptTokens     int `json:"prompt_tokens"`
			CompletionTokens int `json:"completion_tokens"`
			TotalTokens      int `json:"total_tokens"`
		}{
			PromptTokens:     geminiResp.UsageMetadata.PromptTokenCount,
			CompletionTokens: geminiResp.UsageMetadata.CandidatesTokenCount,
			TotalTokens:      geminiResp.UsageMetadata.TotalTokenCount,
		},
	}

	if len(geminiResp.Candidates) > 0 && len(geminiResp.Candidates[0].Content.Parts) > 0 {
		choice := struct {
			Index        int               `json:"index"`
			Message      map[string]string `json:"message"`
			LogProbs     interface{}       `json:"logprobs"`
			FinishReason string            `json:"finish_reason"`
		}{
			Index: 0,
			Message: map[string]string{
				"role":    "assistant",
				"content": geminiResp.Candidates[0].Content.Parts[0].Text,
			},
			FinishReason: "stop",
		}
		openAIResp.Choices = append(openAIResp.Choices, choice)
	}

	// Marshal the new OpenAI-compatible response back to JSON.
	finalBody, err := json.Marshal(openAIResp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal transformed OpenAI response: %w", err)
	}

	log.Println("Successfully transformed Gemini response to OpenAI format.")
	return finalBody, nil
}

// handleStreamingResponse handles streaming responses from LLM backends
func handleStreamingResponse(w http.ResponseWriter, backendResp *http.Response, requestID, backendType, conversationID string, logEntry *InferenceLog, requestDuration time.Duration) {
	// Copy headers from backend response, excluding compression-related ones
	for key, values := range backendResp.Header {
		if key != "Content-Encoding" && key != "Content-Length" && key != "Transfer-Encoding" {
			for _, value := range values {
				w.Header().Add(key, value)
			}
		}
	}

	// Set headers for Server-Sent Events (override any conflicting headers)
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Headers", "Cache-Control")
	// Ensure no compression for streaming
	w.Header().Set("Content-Encoding", "identity")
	w.Header().Del("Content-Length")

	// For streaming responses, always return 200 OK and handle errors in the stream
	w.WriteHeader(http.StatusOK)

	// Create a flusher for real-time streaming
	flusher, ok := w.(http.Flusher)
	if !ok {
		log.Printf("Request %s: Streaming not supported", requestID)
		http.Error(w, "Streaming not supported", http.StatusInternalServerError)
		return
	}

	log.Printf("Request %s: Starting streaming response", requestID)

	// Handle different backend types
	if backendType == "google-external" || backendType == "google" {
		handleGeminiStreamingResponse(w, backendResp, requestID, conversationID, flusher, logEntry, requestDuration)
	} else {
		// For OpenAI-compatible backends, pass through the stream
		handleOpenAIStreamingResponse(w, backendResp, requestID, conversationID, flusher, logEntry, requestDuration)
	}
}

// handleOpenAIStreamingResponse handles streaming responses from OpenAI-compatible backends
func handleOpenAIStreamingResponse(w http.ResponseWriter, backendResp *http.Response, requestID, conversationID string, flusher http.Flusher, logEntry *InferenceLog, requestDuration time.Duration) {
	defer backendResp.Body.Close()

	// Handle compressed responses
	var reader io.Reader = backendResp.Body
	if backendResp.Header.Get("Content-Encoding") == "gzip" {
		gzipReader, err := gzip.NewReader(backendResp.Body)
		if err != nil {
			log.Printf("Request %s: Error creating gzip reader: %v", requestID, err)
			fmt.Fprintf(w, "data: {\"error\": \"Error decompressing response\"}\n\n")
			flusher.Flush()
			return
		}
		defer gzipReader.Close()
		reader = gzipReader
	}

	scanner := bufio.NewScanner(reader)
	var totalTokens int
	var completionTokens int

	for scanner.Scan() {
		line := scanner.Text()

		// Pass through SSE data lines
		if strings.HasPrefix(line, "data: ") {
			// Inject conversation_id into streaming chunks if needed
			if strings.Contains(line, `"object":"chat.completion.chunk"`) {
				// Parse and inject conversation_id
				if modifiedLine := injectConversationIDIntoStreamChunk(line, conversationID); modifiedLine != "" {
					line = modifiedLine
				}
			}

			// Extract token usage for logging
			if strings.Contains(line, `"usage"`) {
				extractTokenUsageFromStreamChunk(line, &totalTokens, &completionTokens)
			}
		}

		// Write the line
		fmt.Fprintf(w, "%s\n", line)
		flusher.Flush()

		// Check for end of stream
		if line == "data: [DONE]" {
			break
		}
	}

	if err := scanner.Err(); err != nil {
		log.Printf("Request %s: Error reading streaming response: %v", requestID, err)
		fmt.Fprintf(w, "data: {\"error\": \"Stream reading error\"}\n\n")
		flusher.Flush()
	}

	// Update log entry
	logEntry.StatusCode = int32(backendResp.StatusCode)
	logEntry.ResponseTimestamp = time.Now()
	logEntry.LatencyMs = float64(requestDuration.Milliseconds())
	logEntry.InputTokens = int64(totalTokens - completionTokens)
	logEntry.OutputTokens = int64(completionTokens)

	log.Printf("Request %s: Streaming response completed", requestID)
	logInferenceLog(*logEntry)
}

// handleGeminiStreamingResponse handles streaming responses from Gemini backends
func handleGeminiStreamingResponse(w http.ResponseWriter, backendResp *http.Response, requestID, conversationID string, flusher http.Flusher, logEntry *InferenceLog, requestDuration time.Duration) {
	defer backendResp.Body.Close()

	// For now, Gemini doesn't support streaming in the same way as OpenAI
	// We'll read the full response and simulate streaming by chunking it

	// Handle compressed responses
	var reader io.Reader = backendResp.Body
	if backendResp.Header.Get("Content-Encoding") == "gzip" {
		gzipReader, err := gzip.NewReader(backendResp.Body)
		if err != nil {
			log.Printf("Request %s: Error creating gzip reader: %v", requestID, err)
			fmt.Fprintf(w, "data: {\"error\": \"Error decompressing response\"}\n\n")
			flusher.Flush()
			return
		}
		defer gzipReader.Close()
		reader = gzipReader
	}

	body, err := io.ReadAll(reader)
	if err != nil {
		log.Printf("Request %s: Error reading Gemini response for streaming: %v", requestID, err)
		fmt.Fprintf(w, "data: {\"error\": \"Error reading response\"}\n\n")
		flusher.Flush()
		return
	}

	// Transform the response to OpenAI format
	transformedBody, err := transformResponsePayload(body, "google-external")
	if err != nil {
		log.Printf("Request %s: Error transforming Gemini response for streaming: %v", requestID, err)
		fmt.Fprintf(w, "data: {\"error\": \"Error transforming response\"}\n\n")
		flusher.Flush()
		return
	}

	// Parse the transformed response
	var openAIResp map[string]interface{}
	if err := json.Unmarshal(transformedBody, &openAIResp); err != nil {
		log.Printf("Request %s: Error parsing transformed response: %v", requestID, err)
		fmt.Fprintf(w, "data: {\"error\": \"Error parsing response\"}\n\n")
		flusher.Flush()
		return
	}

	// Debug: Log the response structure
	log.Printf("Request %s: Transformed response structure: %+v", requestID, openAIResp)

	// Inject conversation_id
	openAIResp["conversation_id"] = conversationID

	// Get the content to stream
	choices, ok := openAIResp["choices"].([]interface{})
	if !ok || len(choices) == 0 {
		log.Printf("Request %s: No choices found in response. Response keys: %v", requestID, getMapKeys(openAIResp))

		// Send a proper error response in streaming format
		errorResponse := map[string]interface{}{
			"id":      openAIResp["id"],
			"object":  "chat.completion.chunk",
			"created": openAIResp["created"],
			"model":   openAIResp["model"],
			"choices": []map[string]interface{}{
				{
					"index": 0,
					"delta": map[string]string{
						"content": "I apologize, but I'm unable to generate a response at the moment. Please try again.",
					},
					"finish_reason": "error",
				},
			},
		}

		errorJSON, _ := json.Marshal(errorResponse)
		fmt.Fprintf(w, "data: %s\n\n", errorJSON)
		fmt.Fprintf(w, "data: [DONE]\n\n")
		flusher.Flush()
		return
	}

	choice := choices[0].(map[string]interface{})
	message := choice["message"].(map[string]interface{})
	content := message["content"].(string)

	// Create streaming chunks
	chunkID := fmt.Sprintf("chatcmpl-%s", strings.ReplaceAll(requestID, "-", "")[:8])

	// Send chunks word by word for a streaming effect
	words := strings.Fields(content)
	for _, word := range words {
		chunk := map[string]interface{}{
			"id":              chunkID,
			"object":          "chat.completion.chunk",
			"created":         openAIResp["created"],
			"model":           openAIResp["model"],
			"conversation_id": conversationID,
			"choices": []map[string]interface{}{
				{
					"index": 0,
					"delta": map[string]interface{}{
						"content": word + " ",
					},
					"finish_reason": nil,
				},
			},
		}

		chunkJSON, _ := json.Marshal(chunk)
		fmt.Fprintf(w, "data: %s\n\n", string(chunkJSON))
		flusher.Flush()

		// Small delay to simulate real streaming
		time.Sleep(50 * time.Millisecond)
	}

	// Send final chunk with finish_reason
	finalChunk := map[string]interface{}{
		"id":              chunkID,
		"object":          "chat.completion.chunk",
		"created":         openAIResp["created"],
		"model":           openAIResp["model"],
		"conversation_id": conversationID,
		"choices": []map[string]interface{}{
			{
				"index":         0,
				"delta":         map[string]interface{}{},
				"finish_reason": "stop",
			},
		},
		"usage": openAIResp["usage"],
	}

	finalChunkJSON, _ := json.Marshal(finalChunk)
	fmt.Fprintf(w, "data: %s\n\n", string(finalChunkJSON))
	fmt.Fprintf(w, "data: [DONE]\n\n")
	flusher.Flush()

	// Update log entry
	logEntry.StatusCode = int32(backendResp.StatusCode)
	logEntry.ResponseTimestamp = time.Now()
	logEntry.LatencyMs = float64(requestDuration.Milliseconds())

	// Extract usage information
	if usage, ok := openAIResp["usage"].(map[string]interface{}); ok {
		if promptTokens, ok := usage["prompt_tokens"].(float64); ok {
			logEntry.InputTokens = int64(promptTokens)
		}
		if completionTokens, ok := usage["completion_tokens"].(float64); ok {
			logEntry.OutputTokens = int64(completionTokens)
		}
		// Total tokens can be calculated from input + output tokens if needed
	}

	log.Printf("Request %s: Gemini streaming response completed", requestID)
	logInferenceLog(*logEntry)
}

// injectConversationIDIntoStreamChunk injects conversation_id into a streaming chunk
func injectConversationIDIntoStreamChunk(line, conversationID string) string {
	if !strings.HasPrefix(line, "data: ") {
		return ""
	}

	dataContent := line[6:] // Remove "data: " prefix
	if dataContent == "[DONE]" {
		return line
	}

	var chunk map[string]interface{}
	if err := json.Unmarshal([]byte(dataContent), &chunk); err != nil {
		return ""
	}

	// Inject conversation_id
	chunk["conversation_id"] = conversationID

	// Marshal back to JSON
	modifiedJSON, err := json.Marshal(chunk)
	if err != nil {
		return ""
	}

	return "data: " + string(modifiedJSON)
}

// extractTokenUsageFromStreamChunk extracts token usage from a streaming chunk
func extractTokenUsageFromStreamChunk(line string, totalTokens, completionTokens *int) {
	if !strings.HasPrefix(line, "data: ") {
		return
	}

	dataContent := line[6:] // Remove "data: " prefix
	var chunk map[string]interface{}
	if err := json.Unmarshal([]byte(dataContent), &chunk); err != nil {
		return
	}

	if usage, ok := chunk["usage"].(map[string]interface{}); ok {
		if total, ok := usage["total_tokens"].(float64); ok {
			*totalTokens = int(total)
		}
		if completion, ok := usage["completion_tokens"].(float64); ok {
			*completionTokens = int(completion)
		}
	}
}

// getMapKeys returns the keys of a map for debugging
func getMapKeys(m map[string]interface{}) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

// checkActiveABTests checks if there are any active A/B tests that should handle this request
func checkActiveABTests(prompt, modelRequested, userID string) (ABTestRoute, bool) {
	// Get active A/B tests from policy-manager
	policyManagerURL := os.Getenv("POLICY_MANAGER_URL")
	if policyManagerURL == "" {
		policyManagerURL = "http://policy-manager:8083"
	}

	resp, err := http.Get(policyManagerURL + "/api/prompts/ab-tests")
	if err != nil {
		log.Printf("Error fetching A/B tests: %v", err)
		return ABTestRoute{}, false
	}
	defer resp.Body.Close()

	var abTests []ABTest
	if err := json.NewDecoder(resp.Body).Decode(&abTests); err != nil {
		log.Printf("Error decoding A/B tests: %v", err)
		return ABTestRoute{}, false
	}

	// Find running A/B tests
	for _, test := range abTests {
		if test.Status != "running" {
			continue
		}

		// Use prompt and modelRequested in the hash for more deterministic routing
		// This ensures that the same prompt+model combination gets consistent A/B test assignment
		hashInput := userID + test.ID + prompt + modelRequested
		hash := hashStringToInt(hashInput)
		trafficPercentage := hash % 100

		// Log A/B test consideration for debugging
		log.Printf("Considering A/B test %s for user %s, model %s, traffic: %d%%",
			test.ID, userID, modelRequested, trafficPercentage)

		var selectedVariant string
		var promptID string

		if trafficPercentage < test.TrafficSplit {
			selectedVariant = "B"
			promptID = test.PromptBID
		} else {
			selectedVariant = "A"
			promptID = test.PromptAID
		}

		// Get model profile for the selected prompt
		// For now, we'll use a default model mapping
		// In production, you'd want to store model associations with prompts
		modelID := getModelForPrompt(promptID)
		if modelID == "" {
			continue // Skip if no model mapping found
		}

		profile, exists := getModelProfileByID(modelID)
		if !exists {
			continue // Skip if model profile doesn't exist
		}

		// Update A/B test usage statistics
		go updateABTestUsage(test.ID, selectedVariant)

		return ABTestRoute{
			TestID:      test.ID,
			Variant:     selectedVariant,
			ModelID:     profile.ID,
			BackendURL:  profile.BackendURL,
			BackendType: profile.BackendType,
			APIKey:      profile.APIKey,
		}, true
	}

	return ABTestRoute{}, false
}

// hashStringToInt creates a simple hash of a string for traffic splitting
func hashStringToInt(s string) int {
	hash := 0
	for _, c := range s {
		hash = hash*31 + int(c)
	}
	if hash < 0 {
		hash = -hash
	}
	return hash
}

// getModelForPrompt returns the model ID associated with a prompt
// This is a simplified implementation - in production you'd store this mapping
func getModelForPrompt(promptID string) string {
	// Simple mapping for demo purposes
	// In production, this would be stored in the database
	promptModelMap := map[string]string{
		"prompt-a-1752210317238": "gemini-2.5-flash-preview-05-20",
		"prompt-b-1752210317238": "gpt-3.5-turbo",
		"prompt-a-1752211189223": "gpt-4o-mini",
		"prompt-b-1752211189223": "claude-3-haiku",
		"summarization-v1":       "gpt-3.5-turbo",
		"summarization-v2":       "gemini-2.5-flash-preview-05-20",
	}

	if modelID, exists := promptModelMap[promptID]; exists {
		return modelID
	}

	// Default fallback
	return "gemini-2.5-flash-preview-05-20"
}

// updateABTestUsage updates the usage statistics for an A/B test variant
func updateABTestUsage(testID, variant string) {
	policyManagerURL := os.Getenv("POLICY_MANAGER_URL")
	if policyManagerURL == "" {
		policyManagerURL = "http://policy-manager:8083"
	}

	// Get current test data
	resp, err := http.Get(policyManagerURL + "/api/prompts/ab-tests/" + testID)
	if err != nil {
		log.Printf("Error fetching A/B test %s: %v", testID, err)
		return
	}
	defer resp.Body.Close()

	var test ABTest
	if err := json.NewDecoder(resp.Body).Decode(&test); err != nil {
		log.Printf("Error decoding A/B test %s: %v", testID, err)
		return
	}

	// Update usage count
	if variant == "A" {
		test.VariantAUsage++
	} else {
		test.VariantBUsage++
	}

	// Send update back to policy-manager
	testJSON, err := json.Marshal(test)
	if err != nil {
		log.Printf("Error marshaling A/B test update: %v", err)
		return
	}

	req, err := http.NewRequest("PUT", policyManagerURL+"/api/prompts/ab-tests/"+testID, bytes.NewBuffer(testJSON))
	if err != nil {
		log.Printf("Error creating A/B test update request: %v", err)
		return
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 5 * time.Second}
	updateResp, err := client.Do(req)
	if err != nil {
		log.Printf("Error updating A/B test %s: %v", testID, err)
		return
	}
	defer updateResp.Body.Close()

	if updateResp.StatusCode != http.StatusOK {
		log.Printf("A/B test update returned status %d for test %s", updateResp.StatusCode, testID)
	}
}

// updateABTestPerformance updates the performance metrics for an A/B test variant
func updateABTestPerformance(testID, variant string, latency, cost float64, success bool) {
	policyManagerURL := os.Getenv("POLICY_MANAGER_URL")
	if policyManagerURL == "" {
		policyManagerURL = "http://policy-manager:8083"
	}

	// Get current test data
	resp, err := http.Get(policyManagerURL + "/api/prompts/ab-tests/" + testID)
	if err != nil {
		log.Printf("Error fetching A/B test %s for performance update: %v", testID, err)
		return
	}
	defer resp.Body.Close()

	var test ABTest
	if err := json.NewDecoder(resp.Body).Decode(&test); err != nil {
		log.Printf("Error decoding A/B test %s for performance update: %v", testID, err)
		return
	}

	// Calculate quality score based on success rate and cost efficiency
	// This is a simplified scoring algorithm - in production you'd want more sophisticated metrics
	qualityScore := 0.0
	if success {
		// Base score for success, adjusted by cost efficiency
		qualityScore = 0.8 + (0.2 * math.Max(0, (0.01-cost)/0.01)) // Higher score for lower cost
	} else {
		qualityScore = 0.1 // Low score for failures
	}

	// Update the appropriate variant's score
	if variant == "A" {
		// Simple moving average for score updates
		currentUsage := test.VariantAUsage
		if currentUsage == 0 {
			test.VariantAScore = qualityScore
		} else {
			// Weighted average: give more weight to recent results
			weight := math.Min(0.3, 1.0/float64(currentUsage+1))
			test.VariantAScore = (1-weight)*test.VariantAScore + weight*qualityScore
		}
	} else {
		currentUsage := test.VariantBUsage
		if currentUsage == 0 {
			test.VariantBScore = qualityScore
		} else {
			weight := math.Min(0.3, 1.0/float64(currentUsage+1))
			test.VariantBScore = (1-weight)*test.VariantBScore + weight*qualityScore
		}
	}

	// Send update back to policy-manager
	testJSON, err := json.Marshal(test)
	if err != nil {
		log.Printf("Error marshaling A/B test performance update: %v", err)
		return
	}

	req, err := http.NewRequest("PUT", policyManagerURL+"/api/prompts/ab-tests/"+testID, bytes.NewBuffer(testJSON))
	if err != nil {
		log.Printf("Error creating A/B test performance update request: %v", err)
		return
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 5 * time.Second}
	updateResp, err := client.Do(req)
	if err != nil {
		log.Printf("Error updating A/B test performance %s: %v", testID, err)
		return
	}
	defer updateResp.Body.Close()

	if updateResp.StatusCode != http.StatusOK {
		log.Printf("A/B test performance update returned status %d for test %s", updateResp.StatusCode, testID)
	} else {
		log.Printf("Updated A/B test %s variant %s: score=%.3f, latency=%.1fms, cost=$%.6f, success=%t",
			testID, variant, qualityScore, latency, cost, success)
	}
}
