package main

import (
	"context"
	"encoding/json"
	"log"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

// MCPImplementation represents the MCP implementation details
type MCPImplementation struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}

// MCPHost manages MCP client connections and coordinates with existing proxy-gateway functionality
type MC<PERSON>Host struct {
	implementation *MCPImplementation
	clients        map[string]*MCPClientConnection
	clientsMutex   sync.RWMutex
	servers        map[string]*MCPServerConfig
	serversMutex   sync.RWMutex
	upgrader       websocket.Upgrader
}

// MCPClientConnection represents an active MCP client connection
type MCPClientConnection struct {
	ID          string
	Conn        *websocket.Conn
	ConnectedAt time.Time
	LastActive  time.Time
	UserID      string
	Permissions []string
	Context     context.Context
	Cancel      context.CancelFunc
}

// MCPServerConfig represents configuration for internal MCP servers
type MCPServerConfig struct {
	Name         string
	URL          string
	Enabled      bool
	Capabilities []string
}

// NewMCPHost creates a new MCP host instance
func NewMCPHost() *MCPHost {
	return &MCPHost{
		implementation: &MCPImplementation{
			Name:    "ai-operations-hub-proxy",
			Version: "1.0.0",
		},
		clients: make(map[string]*MCPClientConnection),
		servers: make(map[string]*MCPServerConfig),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// Allow connections from any origin for now
				// In production, implement proper origin checking
				return true
			},
		},
	}
}

// InitializeInternalServers configures the internal MCP servers
func (h *MCPHost) InitializeInternalServers() {
	h.serversMutex.Lock()
	defer h.serversMutex.Unlock()

	// Configure internal services as MCP servers
	h.servers["ai-optimizer"] = &MCPServerConfig{
		Name:         "AI Optimizer",
		URL:          "http://ai-optimizer:8085/mcp",
		Enabled:      true,
		Capabilities: []string{"tools", "resources"},
	}

	h.servers["planning-service"] = &MCPServerConfig{
		Name:         "Planning Service",
		URL:          "http://planning-service:8082/mcp",
		Enabled:      true,
		Capabilities: []string{"tools", "prompts", "resources"},
	}

	h.servers["evaluation-service"] = &MCPServerConfig{
		Name:         "Evaluation Service",
		URL:          "http://evaluation-service:8088/mcp",
		Enabled:      true,
		Capabilities: []string{"tools", "resources"},
	}

	h.servers["integration-service"] = &MCPServerConfig{
		Name:         "Integration Service",
		URL:          "http://integration-service:8080/mcp",
		Enabled:      true,
		Capabilities: []string{"tools", "resources"},
	}

	log.Printf("MCP Host: Initialized %d internal servers", len(h.servers))
}

// HandleMCPConnection handles new MCP client connections via WebSocket
func (h *MCPHost) HandleMCPConnection(w http.ResponseWriter, r *http.Request) {
	// Validate authentication
	userID := r.Header.Get("X-User-ID")
	if userID == "" {
		http.Error(w, "Authentication required", http.StatusUnauthorized)
		return
	}

	// Upgrade to WebSocket
	conn, err := h.upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("MCP Host: Failed to upgrade to WebSocket: %v", err)
		http.Error(w, "Failed to establish WebSocket connection", http.StatusInternalServerError)
		return
	}

	// Create client connection record
	connectionID := uuid.New().String()
	ctx, cancel := context.WithCancel(context.Background())

	connection := &MCPClientConnection{
		ID:          connectionID,
		Conn:        conn,
		ConnectedAt: time.Now(),
		LastActive:  time.Now(),
		UserID:      userID,
		Permissions: h.getUserPermissions(userID),
		Context:     ctx,
		Cancel:      cancel,
	}

	// Store connection
	h.clientsMutex.Lock()
	h.clients[connectionID] = connection
	h.clientsMutex.Unlock()

	log.Printf("MCP Host: New client connected - ID: %s, User: %s", connectionID, userID)

	// Handle connection lifecycle
	go h.handleClientSession(connection)
}

// handleClientSession manages the lifecycle of an MCP client session
func (h *MCPHost) handleClientSession(connection *MCPClientConnection) {
	defer func() {
		// Clean up connection
		h.clientsMutex.Lock()
		delete(h.clients, connection.ID)
		h.clientsMutex.Unlock()

		// Close WebSocket connection
		if connection.Conn != nil {
			connection.Conn.Close()
		}

		// Cancel context
		if connection.Cancel != nil {
			connection.Cancel()
		}

		log.Printf("MCP Host: Client disconnected - ID: %s", connection.ID)
	}()

	// Handle incoming messages
	for {
		select {
		case <-connection.Context.Done():
			// Context cancelled, exit
			return
		default:
			// Set read deadline
			connection.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))

			// Read message
			messageType, message, err := connection.Conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					log.Printf("MCP Host: WebSocket error for client %s: %v", connection.ID, err)
				}
				return
			}

			// Update last active time
			connection.LastActive = time.Now()

			// Handle message based on type
			if messageType == websocket.TextMessage {
				h.handleMCPMessage(connection, message)
			}
		}
	}
}

// handleMCPMessage processes incoming MCP messages
func (h *MCPHost) handleMCPMessage(connection *MCPClientConnection, message []byte) {
	log.Printf("MCP Host: Received message from client %s: %s", connection.ID, string(message))

	// For now, just echo back a simple response
	// In a full implementation, this would parse JSON-RPC and route to appropriate handlers
	response := map[string]interface{}{
		"jsonrpc": "2.0",
		"id":      1,
		"result": map[string]interface{}{
			"status":  "received",
			"message": "MCP message processing not yet implemented",
		},
	}

	responseBytes, err := json.Marshal(response)
	if err != nil {
		log.Printf("MCP Host: Error marshaling response: %v", err)
		return
	}

	err = connection.Conn.WriteMessage(websocket.TextMessage, responseBytes)
	if err != nil {
		log.Printf("MCP Host: Error sending response to client %s: %v", connection.ID, err)
	}
}

// getUserPermissions returns the MCP permissions for a user
func (h *MCPHost) getUserPermissions(userID string) []string {
	// In production, this would be fetched from user management based on userID
	// For now, we'll use a simple role-based mapping

	// Log the permission request for audit
	log.Printf("Fetching MCP permissions for user: %s", userID)

	// Default permissions for authenticated users
	defaultPermissions := []string{"tools", "resources", "prompts"}

	// Example: Admin users get additional permissions
	if strings.HasPrefix(userID, "admin_") {
		return append(defaultPermissions, "admin", "system")
	}

	// Example: Premium users get enhanced permissions
	if strings.HasPrefix(userID, "premium_") {
		return append(defaultPermissions, "premium_tools")
	}

	return defaultPermissions
}

// GetConnectedClients returns information about currently connected MCP clients
func (h *MCPHost) GetConnectedClients() []map[string]interface{} {
	h.clientsMutex.RLock()
	defer h.clientsMutex.RUnlock()

	clients := make([]map[string]interface{}, 0, len(h.clients))
	for _, connection := range h.clients {
		clients = append(clients, map[string]interface{}{
			"id":           connection.ID,
			"user_id":      connection.UserID,
			"connected_at": connection.ConnectedAt,
			"last_active":  connection.LastActive,
			"permissions":  connection.Permissions,
		})
	}

	return clients
}

// GetAvailableServers returns information about available internal MCP servers
func (h *MCPHost) GetAvailableServers() []map[string]interface{} {
	h.serversMutex.RLock()
	defer h.serversMutex.RUnlock()

	servers := make([]map[string]interface{}, 0, len(h.servers))
	for id, server := range h.servers {
		servers = append(servers, map[string]interface{}{
			"id":           id,
			"name":         server.Name,
			"url":          server.URL,
			"enabled":      server.Enabled,
			"capabilities": server.Capabilities,
		})
	}

	return servers
}

// HandleMCPStatus provides status information about MCP host
func (h *MCPHost) HandleMCPStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	status := map[string]interface{}{
		"host": map[string]interface{}{
			"name":    h.implementation.Name,
			"version": h.implementation.Version,
		},
		"clients": h.GetConnectedClients(),
		"servers": h.GetAvailableServers(),
		"stats": map[string]interface{}{
			"active_connections": len(h.clients),
			"available_servers":  len(h.servers),
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(status)
}

// Global MCP host instance
var mcpHost *MCPHost

// InitializeMCPHost initializes the global MCP host
func InitializeMCPHost() {
	mcpHost = NewMCPHost()
	mcpHost.InitializeInternalServers()
	log.Printf("MCP Host: Initialized successfully")
}
