# Configuration for the AI Cost & Performance Optimization Proxy Gateway

# The address and port the proxy will listen on *inside the container*
listen_addr: ":8080" # Listen internally on port 8080

# Backend configuration moved to Redis model profiles
# Using external APIs only - no mock backends
backends: []

# Kafka Configuration for logging inference events
kafka:
  brokers: ["kafka:9092"]
  topic: "inference-logs"

# Redis Configuration for fetching live latency metrics
redis:
  addr: "redis:6379"
  password: ""
  db: 0

# New: Routing Policy Definition
policy:
  name: "Default Routing Policy"
  max_latency_ms: 200.0 # Only consider backends with latency <= 200ms
  max_cost: 0.******** # Only consider backends with cost <= 0.********
  required_capabilities: ["text-generation"] # Backends must have this capability
  forbidden_data_sensitivity_levels: ["PHI"] # Backends must NOT handle PHI data
