#!/bin/bash

# Script to validate deployment and fix common issues
# Usage: ./validate-deployment.sh [standard|enterprise|both]

set -e

DEPLOYMENT_TYPE=${1:-both}

echo "🔍 Validating deployment for: $DEPLOYMENT_TYPE"

# Function to check if a service exists and is ready
check_service() {
    local service_name=$1
    local namespace=${2:-default}
    local expected_replicas=${3:-1}
    
    if kubectl get service "$service_name" -n "$namespace" >/dev/null 2>&1; then
        echo "✅ Service $service_name exists in namespace $namespace"
        
        # Check if deployment exists and is ready
        if kubectl get deployment "$service_name" -n "$namespace" >/dev/null 2>&1; then
            local ready_replicas=$(kubectl get deployment "$service_name" -n "$namespace" -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
            if [ "$ready_replicas" -ge "$expected_replicas" ]; then
                echo "✅ Deployment $service_name is ready ($ready_replicas/$expected_replicas replicas)"
            else
                echo "⚠️  Deployment $service_name not ready ($ready_replicas/$expected_replicas replicas)"
                return 1
            fi
        fi
    else
        echo "❌ Service $service_name not found in namespace $namespace"
        return 1
    fi
}

# Function to check ingress routes
check_ingress_routes() {
    echo "🌐 Checking ingress routes..."

    # Check main unified ingress
    if kubectl get ingress unified-ai-operations-hub-ingress -n default >/dev/null 2>&1; then
        echo "✅ unified-ai-operations-hub-ingress exists"
    else
        echo "❌ unified-ai-operations-hub-ingress not found"
    fi

    # Check standard edition ingress
    if kubectl get ingress standard-api-ingress-fixed -n default >/dev/null 2>&1; then
        echo "✅ standard-api-ingress-fixed exists"

        # Check standard routes
        if kubectl describe ingress standard-api-ingress-fixed -n default | grep -q "/standard/api"; then
            echo "✅ Standard API route configured"
        else
            echo "❌ Standard API route missing"
        fi

        if kubectl describe ingress standard-api-ingress-fixed -n default | grep -q "/standard/dashboard"; then
            echo "✅ Standard dashboard route configured"
        else
            echo "❌ Standard dashboard route missing"
        fi
    else
        echo "❌ standard-api-ingress-fixed not found"
    fi

    # Check enterprise edition ingress
    if kubectl get ingress enterprise-frontend-ingress -n default >/dev/null 2>&1; then
        echo "✅ enterprise-frontend-ingress exists"

        # Check enterprise routes (only if enterprise services exist)
        if check_service "proxy-gateway" "default" >/dev/null 2>&1; then
            if kubectl describe ingress enterprise-frontend-ingress -n default | grep -q "/enterprise/api"; then
                echo "✅ Enterprise API route configured"
            else
                echo "⚠️  Enterprise API route missing (but service exists)"
            fi
        fi

        if check_service "dashboard-api" "default" >/dev/null 2>&1; then
            if kubectl describe ingress enterprise-frontend-ingress -n default | grep -q "/enterprise/dashboard"; then
                echo "✅ Enterprise dashboard route configured"
            else
                echo "⚠️  Enterprise dashboard route missing (but service exists)"
            fi
        fi
    else
        echo "❌ enterprise-frontend-ingress not found"
    fi

    # Check for legacy ingress (should not exist)
    if kubectl get ingress api-rewrite-ingress -n default >/dev/null 2>&1; then
        echo "⚠️  Legacy api-rewrite-ingress still exists (should be removed)"
    fi

    if kubectl get ingress ai-operations-hub-ingress -n default >/dev/null 2>&1; then
        echo "⚠️  Legacy ai-operations-hub-ingress still exists (should be removed)"
    fi
}

# Function to check secrets
check_secrets() {
    local namespace=$1
    echo "🔑 Checking secrets in namespace $namespace..."
    
    if kubectl get secret llm-api-keys -n "$namespace" >/dev/null 2>&1; then
        echo "✅ llm-api-keys secret exists"
        
        # Check for required keys
        local required_keys=("openai-api-key" "google-api-key" "anthropic-api-key" "cohere-api-key" "huggingface-api-key" "mistral-api-key" "grok-api-key")
        for key in "${required_keys[@]}"; do
            if kubectl get secret llm-api-keys -n "$namespace" -o jsonpath="{.data.$key}" >/dev/null 2>&1; then
                echo "✅ $key present"
            else
                echo "❌ $key missing"
            fi
        done
    else
        echo "❌ llm-api-keys secret not found"
    fi
}

# Function to fix common issues
fix_issues() {
    echo "🔧 Attempting to fix common issues..."
    
    # Fix missing API keys
    if [ "$DEPLOYMENT_TYPE" = "standard" ] || [ "$DEPLOYMENT_TYPE" = "both" ]; then
        echo "Fixing standard edition secrets..."
        bash k8s/scripts/create-llm-secrets.sh standard-edition
    fi
    
    if [ "$DEPLOYMENT_TYPE" = "enterprise" ] || [ "$DEPLOYMENT_TYPE" = "both" ]; then
        echo "Fixing enterprise edition secrets..."
        bash k8s/scripts/create-llm-secrets.sh default
    fi
    
    # Fix ingress routes
    echo "Fixing ingress routes..."
    bash k8s/scripts/manage-ingress-routes.sh "$DEPLOYMENT_TYPE"
    
    # Fix external service mappings for standard edition
    if [ "$DEPLOYMENT_TYPE" = "standard" ] || [ "$DEPLOYMENT_TYPE" = "both" ]; then
        if kubectl get namespace standard-edition >/dev/null 2>&1; then
            echo "Fixing standard edition external services..."
            bash k8s/scripts/update-standard-external-services.sh
        fi
    fi
}

# Main validation logic
case $DEPLOYMENT_TYPE in
    "standard")
        echo "📋 Validating Standard Edition deployment..."
        check_secrets "standard-edition"
        check_service "proxy-gateway-standard" "standard-edition" 2
        check_service "dashboard-api-standard" "standard-edition" 1
        check_service "frontend-standard" "standard-edition" 2
        check_service "landing-page" "standard-edition" 2
        check_service "ai-optimizer-standard" "standard-edition" 1
        check_service "redis" "standard-edition" 1
        check_ingress_routes
        ;;
    "enterprise")
        echo "📋 Validating Enterprise Edition deployment..."
        check_secrets "default"
        check_service "proxy-gateway" "default" 2
        check_service "dashboard-api" "default" 1
        check_service "frontend" "default" 2
        check_service "landing-page" "default" 2
        check_service "ai-optimizer" "default" 1
        check_ingress_routes
        ;;
    "both")
        echo "📋 Validating both Standard and Enterprise Edition deployments..."
        if kubectl get namespace standard-edition >/dev/null 2>&1; then
            echo "Standard edition namespace found, validating..."
            check_secrets "standard-edition"
            check_service "proxy-gateway-standard" "standard-edition" 2
            check_service "dashboard-api-standard" "standard-edition" 1
            check_service "frontend-standard" "standard-edition" 2
        fi
        
        if kubectl get namespace default >/dev/null 2>&1; then
            echo "Default namespace found, checking for enterprise services..."
            check_secrets "default"
            if kubectl get service proxy-gateway -n default >/dev/null 2>&1; then
                check_service "proxy-gateway" "default" 2
                check_service "dashboard-api" "default" 1
                check_service "frontend" "default" 2
            fi
        fi
        check_ingress_routes
        ;;
    *)
        echo "❌ Invalid deployment type: $DEPLOYMENT_TYPE"
        echo "Usage: $0 [standard|enterprise|both]"
        exit 1
        ;;
esac

echo ""
echo "🔧 Would you like to attempt to fix any issues found? (y/n)"
read -r response
if [[ "$response" =~ ^[Yy]$ ]]; then
    fix_issues
    echo "✅ Fix attempts completed. Re-run validation to check results."
fi

echo ""
echo "🔍 Validation completed for $DEPLOYMENT_TYPE edition(s)"
