#!/bin/bash

# <PERSON>ript to automatically update external service IPs for standard edition
# This ensures the ingress can properly route to standard edition services

set -e

echo "🔄 Updating Standard Edition External Service IPs..."

# Get the ClusterIPs of the standard edition services
FRONTEND_IP=$(kubectl get service frontend-standard -n standard-edition -o jsonpath='{.spec.clusterIP}')
PROXY_GATEWAY_IP=$(kubectl get service proxy-gateway-standard -n standard-edition -o jsonpath='{.spec.clusterIP}')
DASHBOARD_API_IP=$(kubectl get service dashboard-api-standard -n standard-edition -o jsonpath='{.spec.clusterIP}')
LANDING_PAGE_IP=$(kubectl get service landing-page -n standard-edition -o jsonpath='{.spec.clusterIP}')

echo "📋 Detected Service IPs:"
echo "   Frontend: $FRONTEND_IP"
echo "   Proxy Gateway: $PROXY_GATEWAY_IP"
echo "   Dashboard API: $DASHBOARD_API_IP"
echo "   Landing Page: $LANDING_PAGE_IP"

# Create the external services configuration
cat > k8s/standard-edition-external-services.yaml << EOF
apiVersion: v1
kind: Service
metadata:
  name: frontend-standard-external
  namespace: default
spec:
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP

---
apiVersion: v1
kind: Endpoints
metadata:
  name: frontend-standard-external
  namespace: default
subsets:
- addresses:
  - ip: $FRONTEND_IP  # ClusterIP of frontend-standard service
  ports:
  - port: 80
    protocol: TCP

---
apiVersion: v1
kind: Service
metadata:
  name: proxy-gateway-standard-external
  namespace: default
spec:
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP

---
apiVersion: v1
kind: Endpoints
metadata:
  name: proxy-gateway-standard-external
  namespace: default
subsets:
- addresses:
  - ip: $PROXY_GATEWAY_IP  # ClusterIP of proxy-gateway-standard service
  ports:
  - port: 8080
    protocol: TCP

---
apiVersion: v1
kind: Service
metadata:
  name: dashboard-api-standard-external
  namespace: default
spec:
  ports:
  - port: 8081
    targetPort: 8081
    protocol: TCP

---
apiVersion: v1
kind: Endpoints
metadata:
  name: dashboard-api-standard-external
  namespace: default
subsets:
- addresses:
  - ip: $DASHBOARD_API_IP  # ClusterIP of dashboard-api-standard service
  ports:
  - port: 8081
    protocol: TCP

---
# Landing page external service (for root path routing)
apiVersion: v1
kind: Service
metadata:
  name: landing-page
  namespace: default
spec:
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP

---
apiVersion: v1
kind: Endpoints
metadata:
  name: landing-page
  namespace: default
subsets:
- addresses:
  - ip: $LANDING_PAGE_IP  # ClusterIP of landing-page service
  ports:
  - port: 80
    protocol: TCP
EOF

# Apply the external services
echo "🚀 Applying external service configurations..."
kubectl apply -f k8s/standard-edition-external-services.yaml

echo "✅ Standard Edition external services updated successfully!"

# Clean up enterprise API routes that point to non-existent services
echo "🧹 Cleaning up enterprise API routes for standard-only deployment..."
kubectl patch ingress api-rewrite-ingress -n default --type='json' -p='[
  {"op": "test", "path": "/spec/rules/0/http/paths/2/backend/service/name", "value": "proxy-gateway"},
  {"op": "remove", "path": "/spec/rules/0/http/paths/2"}
]' 2>/dev/null || echo "   Enterprise /api route already removed or not found"

kubectl patch ingress api-rewrite-ingress -n default --type='json' -p='[
  {"op": "test", "path": "/spec/rules/0/http/paths/2/backend/service/name", "value": "dashboard-api"},
  {"op": "remove", "path": "/spec/rules/0/http/paths/2"}
]' 2>/dev/null || echo "   Enterprise /dashboard route already removed or not found"

echo "✅ Enterprise routes cleanup completed!"
echo ""
echo "🔗 External service mappings:"
echo "   frontend-standard-external -> $FRONTEND_IP:80"
echo "   proxy-gateway-standard-external -> $PROXY_GATEWAY_IP:8080"
echo "   dashboard-api-standard-external -> $DASHBOARD_API_IP:8081"
echo "   landing-page -> $LANDING_PAGE_IP:80"
