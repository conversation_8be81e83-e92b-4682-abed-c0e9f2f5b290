#!/bin/bash

# Script to manage ingress routes based on deployment type
# Usage: ./manage-ingress-routes.sh [standard|enterprise|both]

set -e

DEPLOYMENT_TYPE=${1:-standard}

echo "🌐 Managing ingress routes for deployment type: $DEPLOYMENT_TYPE"

# Function to check if a service exists
service_exists() {
    local service_name=$1
    local namespace=${2:-default}
    kubectl get service "$service_name" -n "$namespace" >/dev/null 2>&1
}

# Function to remove enterprise routes if services don't exist
remove_enterprise_routes() {
    echo "🧹 Checking enterprise routes..."

    # Check if enterprise services exist
    if ! service_exists "proxy-gateway" "default"; then
        echo "   ⚠️  proxy-gateway service not found - enterprise routes may not work"
    else
        echo "   ✅ proxy-gateway service exists"
    fi

    if ! service_exists "dashboard-api" "default"; then
        echo "   ⚠️  dashboard-api service not found - enterprise dashboard routes may not work"
    else
        echo "   ✅ dashboard-api service exists"
    fi

    # Note: With the new ingress architecture, we don't need to patch individual routes
    # The ingress configurations are managed through separate YAML files
    echo "   ℹ️  Enterprise routes are managed through enterprise-frontend-ingress.yaml"
}

# Function to add enterprise routes
add_enterprise_routes() {
    echo "🏢 Checking enterprise routes..."

    # Check if enterprise ingress configurations exist
    if kubectl get ingress enterprise-frontend-ingress -n default >/dev/null 2>&1; then
        echo "   ✅ enterprise-frontend-ingress exists"
    else
        echo "   ❌ enterprise-frontend-ingress missing"
        echo "   ℹ️  Apply enterprise-frontend-ingress.yaml to enable enterprise routes"
    fi

    if kubectl get ingress enterprise-mcp-ingress -n default >/dev/null 2>&1; then
        echo "   ✅ enterprise-mcp-ingress exists"
    else
        echo "   ❌ enterprise-mcp-ingress missing"
        echo "   ℹ️  Apply enterprise-mcp-ingress.yaml to enable MCP routes"
    fi

    # Note: With the new ingress architecture, routes are managed through separate YAML files
    echo "   ℹ️  Enterprise routes are managed through dedicated ingress YAML files"
}

case $DEPLOYMENT_TYPE in
    "standard")
        echo "📋 Configuring ingress for Standard Edition only..."
        remove_enterprise_routes

        # Ensure dashboard API specific ingress exists
        echo "🔧 Ensuring dashboard API specific routes are configured..."
        if ! kubectl get ingress dashboard-api-specific-ingress -n default >/dev/null 2>&1; then
            echo "   Creating dashboard API specific ingress..."
            kubectl apply -f k8s/dashboard-api-ingress.yaml 2>/dev/null || echo "   Dashboard API ingress file not found, skipping..."
        else
            echo "   Dashboard API specific ingress already exists"
        fi

        echo "✅ Standard Edition ingress configuration complete!"
        ;;
    "enterprise")
        echo "📋 Configuring ingress for Enterprise Edition..."
        if service_exists "proxy-gateway" "default" && service_exists "dashboard-api" "default"; then
            echo "✅ Enterprise services found, routes should be working"
        else
            echo "⚠️  Warning: Enterprise services not found, but keeping routes for future deployment"
        fi
        ;;
    "both")
        echo "📋 Configuring ingress for both Standard and Enterprise Editions..."
        if service_exists "proxy-gateway" "default" && service_exists "dashboard-api" "default"; then
            echo "✅ Enterprise services found, keeping all routes"
        else
            echo "⚠️  Enterprise services not found, will add routes when services are deployed"
            remove_enterprise_routes
        fi
        ;;
    *)
        echo "❌ Invalid deployment type: $DEPLOYMENT_TYPE"
        echo "Usage: $0 [standard|enterprise|both]"
        exit 1
        ;;
esac

echo ""
echo "🔍 Current ingress configuration:"
echo "Available ingress configurations:"
kubectl get ingress -n default --no-headers | awk '{print "  - " $1}'

echo ""
echo "Main ingress configurations:"
if kubectl get ingress unified-ai-operations-hub-ingress -n default >/dev/null 2>&1; then
    echo "✅ unified-ai-operations-hub-ingress exists"
else
    echo "❌ unified-ai-operations-hub-ingress missing"
fi

if kubectl get ingress standard-api-ingress-fixed -n default >/dev/null 2>&1; then
    echo "✅ standard-api-ingress-fixed exists"
else
    echo "❌ standard-api-ingress-fixed missing"
fi

if kubectl get ingress enterprise-frontend-ingress -n default >/dev/null 2>&1; then
    echo "✅ enterprise-frontend-ingress exists"
else
    echo "❌ enterprise-frontend-ingress missing"
fi
