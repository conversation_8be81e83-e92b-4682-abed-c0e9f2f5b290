#!/bin/bash

# <PERSON><PERSON>t to create LLM API keys secret with all required keys
# This ensures all deployments have the complete set of API keys

set -e

NAMESPACE=${1:-standard-edition}

echo "🔑 Creating LLM API keys secret in namespace: $NAMESPACE"

# Check if namespace exists
if ! kubectl get namespace "$NAMESPACE" >/dev/null 2>&1; then
    echo "📦 Creating namespace: $NAMESPACE"
    kubectl create namespace "$NAMESPACE"
fi

# Create or update the secret with all required API keys
kubectl create secret generic llm-api-keys \
    --namespace="$NAMESPACE" \
    --from-literal=openai-api-key="your-openai-api-key-here" \
    --from-literal=google-api-key="your-google-api-key-here" \
    --from-literal=anthropic-api-key="your-anthropic-api-key-here" \
    --from-literal=cohere-api-key="your-cohere-api-key-here" \
    --from-literal=huggingface-api-key="your-huggingface-api-key-here" \
    --from-literal=mistral-api-key="your-mistral-api-key-here" \
    --from-literal=grok-api-key="your-grok-api-key-here" \
    --dry-run=client -o yaml | kubectl apply -f -

echo "✅ LLM API keys secret created/updated successfully!"
echo ""
echo "📝 To update with real API keys, run:"
echo "   kubectl edit secret llm-api-keys -n $NAMESPACE"
echo ""
echo "🔍 Available keys in secret:"
kubectl get secret llm-api-keys -n "$NAMESPACE" -o jsonpath='{.data}' | jq -r 'keys[]' 2>/dev/null || kubectl get secret llm-api-keys -n "$NAMESPACE" -o yaml | grep -E '^\s+[a-z-]+:' | sed 's/://g' | sed 's/^  /   - /'
