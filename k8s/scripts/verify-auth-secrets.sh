#!/bin/bash

# Verify Authentication Secrets Script
# This script verifies that all authentication secrets are properly configured
# and that services can access them correctly.

set -e

echo "🔐 Verifying Authentication Secrets Configuration"
echo "================================================"

# Check if we're in a Kubernetes cluster
if ! kubectl cluster-info &> /dev/null; then
    echo "❌ Error: Not connected to a Kubernetes cluster"
    exit 1
fi

echo "✅ Connected to Kubernetes cluster"

# Verify Google OAuth Secret
echo ""
echo "🔍 Checking Google OAuth Secret..."
if kubectl get secret google-oauth-secret -n default &> /dev/null; then
    CLIENT_ID=$(kubectl get secret google-oauth-secret -n default -o jsonpath='{.data.client-id}' | base64 -d 2>/dev/null || echo "")
    CLIENT_SECRET=$(kubectl get secret google-oauth-secret -n default -o jsonpath='{.data.client-secret}' | base64 -d 2>/dev/null || echo "")
    
    if [ -n "$CLIENT_ID" ] && [ -n "$CLIENT_SECRET" ]; then
        echo "✅ Google OAuth secret exists and is populated"
        echo "   Client ID: ${CLIENT_ID:0:20}..."
        echo "   Client Secret: [HIDDEN] (${#CLIENT_SECRET} characters)"
    else
        echo "❌ Google OAuth secret exists but is empty!"
        exit 1
    fi
else
    echo "❌ Google OAuth secret not found!"
    exit 1
fi

# Verify JWT Secret
echo ""
echo "🔍 Checking JWT Secret..."
if kubectl get secret jwt-secret -n default &> /dev/null; then
    JWT_SECRET=$(kubectl get secret jwt-secret -n default -o jsonpath='{.data.secret}' | base64 -d 2>/dev/null || echo "")
    
    if [ -n "$JWT_SECRET" ]; then
        echo "✅ JWT secret exists and is populated"
        echo "   JWT Secret: [HIDDEN] (${#JWT_SECRET} characters)"
    else
        echo "❌ JWT secret exists but is empty!"
        exit 1
    fi
else
    echo "❌ JWT secret not found!"
    exit 1
fi

# Verify Auth Config
echo ""
echo "🔍 Checking Auth Config..."
if kubectl get configmap auth-config -n default &> /dev/null; then
    ADMIN_EMAILS=$(kubectl get configmap auth-config -n default -o jsonpath='{.data.admin-emails}' 2>/dev/null || echo "")
    
    if [ -n "$ADMIN_EMAILS" ]; then
        echo "✅ Auth config exists and is populated"
        echo "   Admin Emails: $ADMIN_EMAILS"
    else
        echo "❌ Auth config exists but is empty!"
        exit 1
    fi
else
    echo "❌ Auth config not found!"
    exit 1
fi

# Verify ClickHouse Secret
echo ""
echo "🔍 Checking ClickHouse Secret..."
if kubectl get secret clickhouse-secret -n default &> /dev/null; then
    CH_PASSWORD=$(kubectl get secret clickhouse-secret -n default -o jsonpath='{.data.password}' | base64 -d 2>/dev/null || echo "")
    
    if [ -n "$CH_PASSWORD" ]; then
        echo "✅ ClickHouse secret exists and is populated"
        echo "   Password: [HIDDEN] (${#CH_PASSWORD} characters)"
    else
        echo "❌ ClickHouse secret exists but is empty!"
        exit 1
    fi
else
    echo "❌ ClickHouse secret not found!"
    exit 1
fi

# Check if services are using the secrets correctly
echo ""
echo "🔍 Checking Service Environment Variables..."

# Check auth-service
if kubectl get deployment auth-service -n default &> /dev/null; then
    echo "✅ Auth service deployment found"
    
    # Check if auth-service has the correct environment variables
    if kubectl exec deployment/auth-service -n default -- env | grep -q "GOOGLE_CLIENT_ID=790611531550"; then
        echo "✅ Auth service has correct Google Client ID"
    else
        echo "❌ Auth service missing or incorrect Google Client ID"
    fi
    
    if kubectl exec deployment/auth-service -n default -- env | grep -q "JWT_SECRET="; then
        echo "✅ Auth service has JWT secret configured"
    else
        echo "❌ Auth service missing JWT secret"
    fi
else
    echo "⚠️  Auth service deployment not found"
fi

# Check dashboard-api
if kubectl get deployment dashboard-api -n default &> /dev/null; then
    echo "✅ Dashboard API deployment found"
    
    if kubectl exec deployment/dashboard-api -n default -- env | grep -q "JWT_SECRET="; then
        echo "✅ Dashboard API has JWT secret configured"
    else
        echo "❌ Dashboard API missing JWT secret"
    fi
else
    echo "⚠️  Dashboard API deployment not found"
fi

# Check proxy-gateway
if kubectl get deployment proxy-gateway -n default &> /dev/null; then
    echo "✅ Proxy Gateway deployment found"
    
    if kubectl exec deployment/proxy-gateway -n default -- env | grep -q "JWT_SECRET="; then
        echo "✅ Proxy Gateway has JWT secret configured"
    else
        echo "❌ Proxy Gateway missing JWT secret"
    fi
else
    echo "⚠️  Proxy Gateway deployment not found"
fi

echo ""
echo "🎉 Authentication secrets verification complete!"
echo ""
echo "Next steps:"
echo "1. Test OAuth login at: https://scale-llm.com/enterprise"
echo "2. Verify that API requests work after authentication"
echo "3. Check that 401 errors are resolved for authenticated users"
