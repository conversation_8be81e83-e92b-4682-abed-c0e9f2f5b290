#!/bin/bash

# Setup authentication secrets for Google OAuth and JWT
# This script should be run before deploying the auth service

set -e

echo "Setting up authentication secrets..."

# Check if required environment variables are set
if [ -z "$GOOGLE_CLIENT_ID" ]; then
    echo "Error: GO<PERSON><PERSON>LE_CLIENT_ID environment variable is not set"
    echo "Please set it with your Google OAuth client ID"
    exit 1
fi

if [ -z "$GOOGLE_CLIENT_SECRET" ]; then
    echo "Error: GOOGLE_CLIENT_SECRET environment variable is not set"
    echo "Please set it with your Google OAuth client secret"
    exit 1
fi

# Generate JWT secret if not provided
if [ -z "$JWT_SECRET" ]; then
    echo "Generating random JWT secret..."
    JWT_SECRET=$(openssl rand -base64 32)
    echo "Generated JWT secret: $JWT_SECRET"
    echo "Please save this secret securely for future use"
fi

# Set admin emails (default to a placeholder if not provided)
ADMIN_EMAILS=${ADMIN_EMAILS:-"<EMAIL>,<EMAIL>"}

echo "Creating Google OAuth secret..."
kubectl create secret generic google-oauth-secret \
    --from-literal=client-id="$GOOGLE_CLIENT_ID" \
    --from-literal=client-secret="$GOOGLE_CLIENT_SECRET" \
    --dry-run=client -o yaml | kubectl apply -f -

echo "Creating JWT secret..."
kubectl create secret generic jwt-secret \
    --from-literal=secret="$JWT_SECRET" \
    --dry-run=client -o yaml | kubectl apply -f -

echo "Creating auth config..."
kubectl create configmap auth-config \
    --from-literal=admin-emails="$ADMIN_EMAILS" \
    --dry-run=client -o yaml | kubectl apply -f -

# Create ClickHouse secret if it doesn't exist
echo "Checking ClickHouse secret..."
if ! kubectl get secret clickhouse-secret >/dev/null 2>&1; then
    echo "Creating ClickHouse secret with default password..."
    CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-"defaultpassword"}
    kubectl create secret generic clickhouse-secret \
        --from-literal=password="$CLICKHOUSE_PASSWORD" \
        --dry-run=client -o yaml | kubectl apply -f -
else
    echo "ClickHouse secret already exists"
fi

echo "Authentication secrets setup complete!"
echo ""
echo "Next steps:"
echo "1. Make sure your Google OAuth application is configured with the correct redirect URI:"
echo "   https://scale-llm.com/auth/callback"
echo "2. Deploy the auth service using the deployment scripts"
echo "3. Update your frontend environment variables to use the auth service"
echo ""
echo "Admin emails configured: $ADMIN_EMAILS"
echo "These users will have admin access when they first log in."
