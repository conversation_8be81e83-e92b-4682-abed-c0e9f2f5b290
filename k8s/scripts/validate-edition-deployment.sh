#!/bin/bash

# AI Operations Hub - Edition Deployment Validation Script
# This script validates that both standard and enterprise editions can coexist

set -e

echo "🔍 AI Operations Hub - Edition Deployment Validation"
echo "=================================================="

# Function to check if a service is running
check_service() {
    local service_name=$1
    local namespace=$2
    local expected_replicas=$3
    
    echo "Checking service: $service_name in namespace: $namespace"
    
    if kubectl get service "$service_name" -n "$namespace" >/dev/null 2>&1; then
        echo "  ✅ Service $service_name exists"
        
        # Check if deployment is ready
        if kubectl get deployment "$service_name" -n "$namespace" >/dev/null 2>&1; then
            local ready_replicas=$(kubectl get deployment "$service_name" -n "$namespace" -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
            if [ "$ready_replicas" -ge "$expected_replicas" ]; then
                echo "  ✅ Deployment $service_name is ready ($ready_replicas/$expected_replicas replicas)"
            else
                echo "  ⚠️  Deployment $service_name is not fully ready ($ready_replicas/$expected_replicas replicas)"
            fi
        fi
    else
        echo "  ❌ Service $service_name not found"
        return 1
    fi
}

# Function to check ingress rules
check_ingress() {
    local ingress_name=$1
    local namespace=$2
    
    echo "Checking ingress: $ingress_name in namespace: $namespace"
    
    if kubectl get ingress "$ingress_name" -n "$namespace" >/dev/null 2>&1; then
        echo "  ✅ Ingress $ingress_name exists"
        
        # Check if ingress has an IP
        local ingress_ip=$(kubectl get ingress "$ingress_name" -n "$namespace" -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
        if [ -n "$ingress_ip" ]; then
            echo "  ✅ Ingress $ingress_name has IP: $ingress_ip"
        else
            echo "  ⚠️  Ingress $ingress_name is pending IP assignment"
        fi
    else
        echo "  ❌ Ingress $ingress_name not found"
        return 1
    fi
}

# Function to test API endpoint
test_endpoint() {
    local url=$1
    local expected_status=$2
    local description=$3
    
    echo "Testing endpoint: $description"
    echo "  URL: $url"
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
    
    if [ "$response" = "$expected_status" ]; then
        echo "  ✅ Endpoint returned expected status: $response"
    else
        echo "  ❌ Endpoint returned unexpected status: $response (expected: $expected_status)"
        return 1
    fi
}

echo ""
echo "🔍 Checking Standard Edition Services..."
echo "========================================"

# Check standard edition namespace
if kubectl get namespace standard-edition >/dev/null 2>&1; then
    echo "✅ Standard edition namespace exists"
    
    # Check standard edition services
    check_service "frontend-standard" "standard-edition" 1
    check_service "dashboard-api-standard" "standard-edition" 1
    check_service "policy-manager" "standard-edition" 1
    check_service "proxy-gateway-standard" "standard-edition" 1
    check_service "redis" "standard-edition" 1
    
    # Check standard edition ingress
    check_ingress "standard-edition-ingress" "standard-edition"
    
else
    echo "⚠️  Standard edition namespace not found - standard edition not deployed"
fi

echo ""
echo "🔍 Checking Enterprise Edition Services..."
echo "========================================="

# Check enterprise edition services in default namespace
if kubectl get deployment dashboard-api >/dev/null 2>&1; then
    echo "✅ Enterprise edition appears to be deployed"
    
    # Check enterprise edition services
    check_service "frontend-dashboard" "default" 1
    check_service "dashboard-api" "default" 1
    check_service "policy-manager" "default" 1
    check_service "proxy-gateway" "default" 1
    check_service "integration-service" "default" 1
    check_service "planning-service" "default" 1
    
    # Check enterprise edition ingress
    check_ingress "comprehensive-ingress" "default"
    
else
    echo "⚠️  Enterprise edition not found - enterprise edition not deployed"
fi

echo ""
echo "🔍 Testing API Endpoints..."
echo "=========================="

# Test standard edition endpoints
echo "Testing Standard Edition endpoints:"
test_endpoint "https://scale-llm.com/standard/" "200" "Standard Edition Frontend"
test_endpoint "https://scale-llm.com/standard/assets/index-D6ncwoCi.js" "200" "Standard Edition JS Assets"
test_endpoint "https://scale-llm.com/standard/assets/index-DwBqPFe3.css" "200" "Standard Edition CSS Assets"
test_endpoint "https://scale-llm.com/standard/api/prompts" "200" "Standard Edition Prompts API"
test_endpoint "https://scale-llm.com/standard/api/integration/gitops/status" "200" "Standard Edition GitOps Fallback"
test_endpoint "https://scale-llm.com/standard/dashboard/health" "200" "Standard Edition Dashboard API"

# Test enterprise edition endpoints
echo ""
echo "Testing Enterprise Edition endpoints:"
test_endpoint "https://scale-llm.com/enterprise/health" "200" "Enterprise Edition Health Check"
test_endpoint "https://scale-llm.com/enterprise/api/integration/gitops/status" "200" "Enterprise Edition GitOps"

echo ""
echo "🔍 Checking for Conflicts..."
echo "============================"

# Check for conflicting ingress rules
echo "Checking for conflicting ingress rules..."

# Get all ingress rules for scale-llm.com
ingress_rules=$(kubectl get ingress --all-namespaces -o json | jq -r '.items[] | select(.spec.rules[]?.host == "scale-llm.com") | "\(.metadata.namespace)/\(.metadata.name)"')

echo "Found ingress rules for scale-llm.com:"
for rule in $ingress_rules; do
    echo "  - $rule"
done

# Check for duplicate paths
echo ""
echo "Checking for duplicate API paths..."

# This is a simplified check - in a real scenario, you'd want more sophisticated conflict detection
if kubectl get ingress --all-namespaces -o json | jq -r '.items[] | select(.spec.rules[]?.host == "scale-llm.com") | .spec.rules[].http.paths[].path' | sort | uniq -d | grep -q .; then
    echo "⚠️  Potential path conflicts detected:"
    kubectl get ingress --all-namespaces -o json | jq -r '.items[] | select(.spec.rules[]?.host == "scale-llm.com") | .spec.rules[].http.paths[].path' | sort | uniq -d
else
    echo "✅ No obvious path conflicts detected"
fi

echo ""
echo "🎯 Validation Summary"
echo "===================="

# Count successful checks
standard_services=0
enterprise_services=0

if kubectl get namespace standard-edition >/dev/null 2>&1; then
    standard_services=$(kubectl get deployments -n standard-edition --no-headers 2>/dev/null | wc -l || echo "0")
    echo "Standard Edition: $standard_services services deployed"
fi

if kubectl get deployment dashboard-api >/dev/null 2>&1; then
    enterprise_services=$(kubectl get deployments -n default -l edition=enterprise --no-headers 2>/dev/null | wc -l || echo "0")
    echo "Enterprise Edition: $enterprise_services services deployed"
fi

echo ""
echo "🔧 Recommendations:"
echo "==================="

if [ "$standard_services" -gt 0 ] && [ "$enterprise_services" -gt 0 ]; then
    echo "✅ Both editions are deployed and should coexist properly"
    echo "   - Standard Edition: https://scale-llm.com/standard"
    echo "   - Enterprise Edition: https://scale-llm.com/enterprise"
elif [ "$standard_services" -gt 0 ]; then
    echo "✅ Only Standard Edition is deployed"
    echo "   - Access at: https://scale-llm.com/standard"
elif [ "$enterprise_services" -gt 0 ]; then
    echo "✅ Only Enterprise Edition is deployed"
    echo "   - Access at: https://scale-llm.com/enterprise"
else
    echo "❌ No editions appear to be properly deployed"
fi

echo ""
echo "📋 Next Steps:"
echo "=============="
echo "1. If you see any ❌ or ⚠️  above, address those issues"
echo "2. Test the frontend interfaces at the URLs above"
echo "3. Check that API calls work without 503 errors"
echo "4. Monitor logs: kubectl logs -f deployment/frontend-standard -n standard-edition"
echo "5. Monitor logs: kubectl logs -f deployment/frontend-dashboard -n default"

echo ""
echo "Validation completed!"
