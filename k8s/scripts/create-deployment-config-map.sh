#!/bin/bash

# Script to create a ConfigMap from deployment-config.env file
# This allows the Redis populator to read API keys from the deployment config

set -e

DEPLOYMENT_CONFIG_FILE="/Users/<USER>/Documents/TBD/ai-cost-performance-optimizer/deployment-config.env"
NAMESPACE=${1:-default}

echo "🔧 Creating deployment config ConfigMap in namespace: $NAMESPACE"

# Check if deployment-config.env file exists
if [ ! -f "$DEPLOYMENT_CONFIG_FILE" ]; then
    echo "❌ Error: deployment-config.env file not found at $DEPLOYMENT_CONFIG_FILE"
    exit 1
fi

echo "📄 Found deployment config file: $DEPLOYMENT_CONFIG_FILE"

# Create ConfigMap from the deployment-config.env file
kubectl create configmap deployment-config \
    --from-env-file="$DEPLOYMENT_CONFIG_FILE" \
    --namespace="$NAMESPACE" \
    --dry-run=client -o yaml | kubectl apply -f -

echo "✅ Deployment config ConfigMap created/updated successfully!"
echo ""
echo "🔍 ConfigMap contents:"
kubectl get configmap deployment-config -n "$NAMESPACE" -o yaml | grep -A 20 "data:"
