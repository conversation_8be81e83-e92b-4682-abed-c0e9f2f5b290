#!/bin/bash
# Exit immediately if a command exits with a non-zero status,
# exit if using an uninitialized variable,
# and exit if a pipeline fails.
set -euo pipefail

echo "Attempting to find ClickHouse pod and drop table..."

# Get a ClickHouse pod name, handling the case where no pods exist
CLICKHOUSE_POD=$(kubectl get pod -l app=clickhouse -o jsonpath="{.items[0].metadata.name}" 2>/dev/null || echo "")

if [ -z "$CLICKHOUSE_POD" ]; then
  echo "Warning: ClickHouse pod not found. Cannot drop table 'inference_logs'. This might be the first deployment, proceeding anyway."
  exit 0 # Exit successfully if pod not found, allowing initial deployments to pass
fi

echo "ClickHouse pod found: $CLICKHOUSE_POD"
echo "Dropping ClickHouse tables on pod: $CLICKHOUSE_POD"
kubectl exec -it "$CLICKHOUSE_POD" -- clickhouse-client -q "DROP TABLE IF EXISTS inference_logs;"
kubectl exec -it "$CLICKHOUSE_POD" -- clickhouse-client -q "DROP TABLE IF EXISTS llm_evaluation_results;"
kubectl exec -it "$CLICKHOUSE_POD" -- clickhouse-client -q "DROP TABLE IF EXISTS curated_data;"

echo "Successfully attempted to drop ClickHouse tables (inference_logs, llm_evaluation_results, curated_data)."
