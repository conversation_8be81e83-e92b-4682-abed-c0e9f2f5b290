#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# Get the directory where the script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "Script execution started."
echo "Script is running from: ${SCRIPT_DIR}"

# Define the root directory of your project based on the script's location
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
echo "Project root: ${PROJECT_ROOT}"

# --- Configuration Variables for GCP and GKE ---
# These variables are passed as environment variables from cloudbuild.yaml
# and are used throughout the script for image paths and GCP commands.
echo "GCP_PROJECT_ID: ${GCP_PROJECT_ID}"
echo "GCP_REGION: ${GCP_REGION}"
echo "GKE_CLUSTER_NAME: ${GKE_CLUSTER_NAME}"
echo "ARTIFACT_REGISTRY_REPO: ${ARTIFACT_REGISTRY_REPO}"

# Full image paths (also passed as environment variables from cloudbuild.yaml)
echo "PROXY_GATEWAY_IMAGE: ${PROXY_GATEWAY_IMAGE}"
echo "DATA_PROCESSOR_IMAGE: ${DATA_PROCESSOR_IMAGE}"
echo "KAFKA_TOPIC_CREATOR_IMAGE: ${KAFKA_TOPIC_CREATOR_IMAGE}"
echo "DASHBOARD_API_IMAGE: ${DASHBOARD_API_IMAGE}"
echo "POLICY_MANAGER_IMAGE: ${POLICY_MANAGER_IMAGE}"
echo "AI_OPTIMIZER_IMAGE: ${AI_OPTIMIZER_IMAGE}"
echo "FRONTEND_IMAGE: ${FRONTEND_IMAGE}"
echo "EVALUATION_SERVICE_IMAGE: ${EVALUATION_SERVICE_IMAGE}"
echo "SENTIMENT_SERVICE_IMAGE: ${SENTIMENT_SERVICE_IMAGE}"
echo "SOCIAL_INTEGRATION_SERVICE_IMAGE: ${SOCIAL_INTEGRATION_SERVICE_IMAGE}"

# Helper function to wait for ingress to be ready
wait_for_ingress() {
    local ingress_name="$1"
    local namespace="$2"
    local timeout="${3:-300}"

    echo "Waiting for ingress $ingress_name to be ready..."
    while [ $timeout -gt 0 ]; do
        if kubectl get ingress "$ingress_name" -n "$namespace" -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null | grep -q .; then
            echo "Ingress $ingress_name is ready."
            return 0
        fi
        echo "Waiting for ingress $ingress_name IP... ($timeout seconds remaining)"
        sleep 5
        timeout=$((timeout - 5))
    done
    echo "Warning: Ingress $ingress_name may take additional time to be ready"
    return 1
}
echo "GOVERNANCE_SERVICE_IMAGE: ${GOVERNANCE_SERVICE_IMAGE}"
echo "INTEGRATION_SERVICE_IMAGE: ${INTEGRATION_SERVICE_IMAGE}"
echo "PLANNING_SERVICE_IMAGE: ${PLANNING_SERVICE_IMAGE}"
echo "MULTI_AGENT_ORCHESTRATOR_IMAGE: ${MULTI_AGENT_ORCHESTRATOR_IMAGE}"
echo "BIAS_DETECTION_SERVICE_IMAGE: ${BIAS_DETECTION_SERVICE_IMAGE}"
echo "EXPLAINABILITY_SERVICE_IMAGE: ${EXPLAINABILITY_SERVICE_IMAGE}"
echo "ROBUSTNESS_TESTING_SERVICE_IMAGE: ${ROBUSTNESS_TESTING_SERVICE_IMAGE}"
echo "COMPLIANCE_SERVICE_IMAGE: ${COMPLIANCE_SERVICE_IMAGE}"
echo "LANDING_PAGE_IMAGE: ${LANDING_PAGE_IMAGE}"

# --- Authentication Configuration ---
# CRITICAL: Authentication secrets MUST be created BEFORE deploying any services
# that depend on them (auth-service, dashboard-api, proxy-gateway, etc.)
#
# This script automatically creates the following authentication secrets:
# 1. clickhouse-secret: ClickHouse password for auth service compatibility
# 2. google-oauth-secret: Google OAuth client ID and secret for user authentication
# 3. jwt-secret: JWT signing secret for session management
# 4. auth-config: ConfigMap with admin email addresses
#
# The script ensures secrets are properly populated by:
# - Always updating existing secrets with correct values (no skipping)
# - Verifying secret contents before proceeding with service deployments
# - Failing fast if any authentication secret is missing or empty
#
# Google OAuth credentials are configured for:
# - Client ID: 790611531550-pbtfshaldjt42dd9v6q6kult05bcjibu.apps.googleusercontent.com
# - Redirect URI: https://scale-llm.com/auth/callback
# - Admin emails: <EMAIL>,<EMAIL>
#
# To customize authentication:
# - Update GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET variables in the script
# - Modify ADMIN_EMAILS to include your actual admin email addresses
# - JWT secret is auto-generated for security (preserves existing if valid)

# --- GKE Context Setup (Minimal for Cloud Build) ---
# In Cloud Build, gcloud container clusters get-credentials is usually run as a separate step
# and sets the kubectl context. This section is mostly for verification or local runs.
echo "--- Configuring gcloud and kubectl for GKE ---"
gcloud config set project "${GCP_PROJECT_ID}"
gcloud config set compute/region "${GCP_REGION}"
echo "Verifying kubectl context..."
kubectl config current-context
echo "--- GKE Context Setup Complete ---"

# --- Artifact Registry Setup (Minimal for Cloud Build) ---
# Cloud Build's docker builder automatically uses Artifact Registry if configured.
echo "--- Setting up Google Artifact Registry ---"
echo "--- Artifact Registry Setup Complete (if pre-configured) ---"


# --- Comprehensive Cleanup of Previous Kubernetes Deployment ---
echo "--- Cleaning up previous Kubernetes deployment (if any) ---"

# --- Drop ClickHouse Tables (per initial plan) ---
# This is a separate script that handles dropping all ClickHouse tables.
echo "--- Dropping ClickHouse tables (inference_logs, llm_evaluation_results, curated_data) for a clean slate ---"
# Make sure drop_clickhouse_table.sh is executable and has correct paths
# Path confirmed: k8s/scripts/drop_clickhouse_table.sh
"${SCRIPT_DIR}/scripts/drop_clickhouse_table.sh" || echo "Warning: drop_clickhouse_table.sh failed or was not found. Continuing."
echo "ClickHouse tables drop attempt completed via drop_clickhouse_table.sh."


# Define the list of Kubernetes YAML files to delete resources from
MANIFESTS_TO_DELETE=(
  "${SCRIPT_DIR}/clickhouse/clickhouse-k8s.yaml"
  "${SCRIPT_DIR}/kafka/kafka-zookeeper-k8s.yaml"
  "${SCRIPT_DIR}/kafka_topic_creator/kafka-topic-creator-job.yaml"
  "${SCRIPT_DIR}/data-processor/data-processor.yaml"
  "${SCRIPT_DIR}/proxy-gateway/proxy-gateway.yaml"
  "${SCRIPT_DIR}/prometheus/prometheus-k8s.yaml"
  "${SCRIPT_DIR}/dashboard-api/dashboard-api.yaml"
  "${SCRIPT_DIR}/frontend/frontend.yaml"
  "${SCRIPT_DIR}/landing-page/landing-page.yaml"
  "${SCRIPT_DIR}/kafka/kafka-server-properties-configmap.yaml"
  "${SCRIPT_DIR}/kafka/kafka-log4j-configmap.yaml"
  "${SCRIPT_DIR}/redis/redis-k8s.yaml"
  "${SCRIPT_DIR}/policy-manager/policy-manager-k8s.yaml"
  "${SCRIPT_DIR}/ai-optimizer/ai-optimizer.yaml"
  "${SCRIPT_DIR}/evaluation-service/evaluation-service.yaml"
  "${SCRIPT_DIR}/governance-service/governance-service.yaml"
  "${SCRIPT_DIR}/integration-service/integration-service.yaml"
  "${SCRIPT_DIR}/planning-service/deployment.yaml"
  "${SCRIPT_DIR}/multi-agent-orchestrator/deployment.yaml"
  "${SCRIPT_DIR}/bias-detection-service/bias-detection-service.yaml"
  "${SCRIPT_DIR}/explainability-service/explainability-service.yaml"
  "${SCRIPT_DIR}/robustness-testing-service/robustness-testing-service.yaml"
  "${SCRIPT_DIR}/compliance-service/compliance-service.yaml"
)

echo "Attempting to delete existing Kubernetes resources from manifests..."
for manifest in "${MANIFESTS_TO_DELETE[@]}"; do
    if [ -f "$manifest" ]; then
        echo "Deleting resources from $manifest..."
        kubectl delete -f "$manifest" --ignore-not-found --force --grace-period=0 2>&1 | tee -a delete_output.log || {
            echo "Warning: Failed to delete resources from $manifest. Check kubectl output above."
        }
    else
        echo "Warning: Manifest file not found for deletion: $manifest. Skipping deletion for this file."
    fi
done

# Also attempt to delete specific resources by name if they are not covered by manifests
echo "Attempting to delete specific secrets and configmaps by name..."
kubectl delete secret clickhouse-credentials --ignore-not-found=true 2>&1 | tee -a delete_output.log
kubectl delete secret gcp-service-account-key --ignore-not-found=true 2>&1 | tee -a delete_output.log # If you still use this directly
kubectl delete secret openai-api-key --ignore-not-found=true 2>&1 | tee -a delete_output.log
kubectl delete secret google-api-key --ignore-not-found=true 2>&1 | tee -a delete_output.log
kubectl delete secret anthropic-api-key --ignore-not-found=true 2>&1 | tee -a delete_output.log
kubectl delete secret cohere-api-key --ignore-not-found=true 2>&1 | tee -a delete_output.log
kubectl delete secret huggingface-api-key --ignore-not-found=true 2>&1 | tee -a delete_output.log
kubectl delete secret mistral-api-key --ignore-not-found=true 2>&1 | tee -a delete_output.log
kubectl delete secret grok-api-key --ignore-not-found=true 2>&1 | tee -a delete_output.log
kubectl delete configmap proxy-gateway-config --ignore-not-found=true 2>&1 | tee -a delete_output.log
kubectl delete configmap frontend-nginx-config --ignore-not-found=true 2>&1 | tee -a delete_output.log # Ensure Nginx config is also deleted
echo "Attempted deletion of specified Kubernetes resources."

echo "--- Cleanup Complete ---"


# --- Workload Identity Setup ---
echo "--- Setting up Workload Identity for Policy Manager ---"

POLICY_MANAGER_KSA_NAME="policy-manager-ksa"
POLICY_MANAGER_GSA_NAME="policy-manager-gsa"

echo "Creating Google Service Account ${POLICY_MANAGER_GSA_NAME}..."
if ! gcloud iam service-accounts describe "${POLICY_MANAGER_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" &> /dev/null; then
    gcloud iam service-accounts create "${POLICY_MANAGER_GSA_NAME}" \
        --display-name="Service Account for Policy Manager"
else
    echo "Google Service Account ${POLICY_MANAGER_GSA_NAME} already exists."
fi

# IMPORTANT: Grant specific GCP roles needed by Policy Manager here (e.g., Secret Manager, other APIs)
# As per your note, Firestore Data Editor was removed. If Policy Manager uses Redis only, no specific
# GCP IAM roles related to data access (beyond Workload Identity User) might be needed.
# Example: gcloud projects add-iam-policy-binding "${GCP_PROJECT_ID}" --member="serviceAccount:${POLICY_MANAGER_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" --role="roles/secretmanager.secretAccessor"

echo "Creating Kubernetes Service Account ${POLICY_MANAGER_KSA_NAME}..."
kubectl create serviceaccount "${POLICY_MANAGER_KSA_NAME}" --dry-run=client -o yaml | kubectl apply -f - || echo "KSA might already exist. Continuing..."

echo "Annotating Kubernetes Service Account ${POLICY_MANAGER_KSA_NAME}"
kubectl annotate serviceaccount "${POLICY_MANAGER_KSA_NAME}" \
    iam.gke.io/gcp-service-account="${POLICY_MANAGER_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" \
    --overwrite

echo "Binding GSA ${POLICY_MANAGER_GSA_NAME} to KSA ${POLICY_MANAGER_KSA_NAME} for Workload Identity..."
gcloud iam service-accounts add-iam-policy-binding "${POLICY_MANAGER_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/iam.workloadIdentityUser" \
    --member="serviceAccount:${GCP_PROJECT_ID}.svc.id.goog[default/${POLICY_MANAGER_KSA_NAME}]" \
    --condition=None || echo "Binding might already exist. Continuing..."

echo "--- Workload Identity Setup for Policy Manager Complete ---"

echo "--- Setting up Workload Identity for Dashboard API ---"

DASHBOARD_API_KSA_NAME="dashboard-api-ksa"
DASHBOARD_API_GSA_NAME="dashboard-api-gsa"

echo "Creating Google Service Account ${DASHBOARD_API_GSA_NAME}..."
if ! gcloud iam service-accounts describe "${DASHBOARD_API_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" &> /dev/null; then
    gcloud iam service-accounts create "${DASHBOARD_API_GSA_NAME}" \
        --display-name="Service Account for Dashboard API"
else
    echo "Google Service Account ${DASHBOARD_API_GSA_NAME} already exists."
fi

# IMPORTANT: Grant specific GCP roles needed by Dashboard API here (e.g., BigQuery Data Viewer/Editor if ClickHouse is BigQuery, Cloud Monitoring Viewer)
# As per your note, Firestore Data Editor was removed.
# Example: gcloud projects add-iam-policy-binding "${GCP_PROJECT_ID}" --member="serviceAccount:${DASHBOARD_API_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" --role="roles/bigquery.dataViewer"

echo "Creating Kubernetes Service Account ${DASHBOARD_API_KSA_NAME}..."
kubectl create serviceaccount "${DASHBOARD_API_KSA_NAME}" --dry-run=client -o yaml | kubectl apply -f - || echo "KSA might already exist. Continuing..."

echo "Annotating Kubernetes Service Account ${DASHBOARD_API_KSA_NAME}"
kubectl annotate serviceaccount "${DASHBOARD_API_KSA_NAME}" \
    iam.gke.io/gcp-service-account="${DASHBOARD_API_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" \
    --overwrite

echo "Binding GSA ${DASHBOARD_API_GSA_NAME} to KSA ${DASHBOARD_API_KSA_NAME} for Workload Identity..."
gcloud iam service-accounts add-iam-policy-binding "${DASHBOARD_API_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/iam.workloadIdentityUser" \
    --member="serviceAccount:${GCP_PROJECT_ID}.svc.id.goog[default/${DASHBOARD_API_KSA_NAME}]" \
    --condition=None || echo "Binding might already exist. Continuing..."

echo "--- Workload Identity Setup for Dashboard API Complete ---"


# --- Create Kubernetes Secrets and ConfigMaps (non-GCP managed, or API Keys) ---
echo "--- Creating Kubernetes Secrets and ConfigMaps ---"

# ClickHouse Credentials Secret
SECRET_NAME="clickhouse-credentials"
SECRET_USER="test"
SECRET_PASSWORD="test"
SECRET_CLICKHOUSE_DB="default"

echo "Checking if secret '${SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${SECRET_NAME}" \
            --from-literal=user="${SECRET_USER}" \
            --from-literal=password="${SECRET_PASSWORD}" \
            --from-literal=clickhouse_db="${SECRET_CLICKHOUSE_DB}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create ClickHouse credentials secret. Exiting."
      exit 1
    fi
    echo "Secret '${SECRET_NAME}' created."
fi

# --- Authentication Secrets ---
echo "--- Creating Authentication Secrets ---"

# ClickHouse Secret (for auth service compatibility)
CLICKHOUSE_SECRET_NAME="clickhouse-secret"
CLICKHOUSE_SECRET_PASSWORD="test"

echo "Checking if secret '${CLICKHOUSE_SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${CLICKHOUSE_SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${CLICKHOUSE_SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${CLICKHOUSE_SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${CLICKHOUSE_SECRET_NAME}" \
            --from-literal=password="${CLICKHOUSE_SECRET_PASSWORD}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create ClickHouse secret. Exiting."
      exit 1
    fi
    echo "Secret '${CLICKHOUSE_SECRET_NAME}' created."
fi

# Google OAuth Secret
GOOGLE_OAUTH_SECRET_NAME="google-oauth-secret"
# !! IMPORTANT: Replace these with your actual Google OAuth credentials
GOOGLE_CLIENT_ID="790611531550-pbtfshaldjt42dd9v6q6kult05bcjibu.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

echo "Ensuring Google OAuth secret '${GOOGLE_OAUTH_SECRET_NAME}' has correct values..."
# Always create or update the secret to ensure it has the correct values
kubectl create secret generic "${GOOGLE_OAUTH_SECRET_NAME}" \
        --from-literal=client-id="${GOOGLE_CLIENT_ID}" \
        --from-literal=client-secret="${GOOGLE_CLIENT_SECRET}" \
        -n default \
        --dry-run=client -o yaml | kubectl apply -f -
if [ $? -ne 0 ]; then
  echo "Error: Failed to create/update Google OAuth secret. Exiting."
  exit 1
fi
echo "Secret '${GOOGLE_OAUTH_SECRET_NAME}' created/updated with correct values."

# JWT Secret
JWT_SECRET_NAME="jwt-secret"
# Generate a secure JWT secret if not already set
if [ -z "$JWT_SECRET_VALUE" ]; then
    JWT_SECRET_VALUE=$(openssl rand -base64 32)
    echo "Generated JWT secret: ${JWT_SECRET_VALUE}"
    echo "Please save this secret securely for future use"
fi

echo "Ensuring JWT secret '${JWT_SECRET_NAME}' has correct values..."
# Check if secret exists and has a value
if kubectl get secret "${JWT_SECRET_NAME}" -n default &> /dev/null; then
    # Check if the secret has a non-empty value
    EXISTING_JWT_SECRET=$(kubectl get secret "${JWT_SECRET_NAME}" -n default -o jsonpath='{.data.secret}' | base64 -d 2>/dev/null || echo "")
    if [ -n "$EXISTING_JWT_SECRET" ] && [ "$EXISTING_JWT_SECRET" != "" ]; then
        echo "Secret '${JWT_SECRET_NAME}' already exists with a valid value. Keeping existing secret."
        JWT_SECRET_VALUE="$EXISTING_JWT_SECRET"
    else
        echo "Secret '${JWT_SECRET_NAME}' exists but is empty. Updating with new value..."
        kubectl create secret generic "${JWT_SECRET_NAME}" \
                --from-literal=secret="${JWT_SECRET_VALUE}" \
                -n default \
                --dry-run=client -o yaml | kubectl apply -f -
        if [ $? -ne 0 ]; then
          echo "Error: Failed to update JWT secret. Exiting."
          exit 1
        fi
        echo "Secret '${JWT_SECRET_NAME}' updated with new value."
    fi
else
    echo "Secret '${JWT_SECRET_NAME}' not found. Creating new secret..."
    kubectl create secret generic "${JWT_SECRET_NAME}" \
            --from-literal=secret="${JWT_SECRET_VALUE}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create JWT secret. Exiting."
      exit 1
    fi
    echo "Secret '${JWT_SECRET_NAME}' created."
fi

# Auth Config ConfigMap
AUTH_CONFIG_NAME="auth-config"
ADMIN_EMAILS="<EMAIL>,<EMAIL>"

echo "Ensuring auth config '${AUTH_CONFIG_NAME}' has correct values..."
# Always create or update the configmap to ensure it has the correct values
kubectl create configmap "${AUTH_CONFIG_NAME}" \
        --from-literal=admin-emails="${ADMIN_EMAILS}" \
        -n default \
        --dry-run=client -o yaml | kubectl apply -f -
if [ $? -ne 0 ]; then
  echo "Error: Failed to create/update auth config. Exiting."
  exit 1
fi
echo "ConfigMap '${AUTH_CONFIG_NAME}' created/updated with correct values."

echo "--- Authentication Secrets Creation Complete ---"

# Verify that all authentication secrets are properly populated
echo "Verifying authentication secrets are properly populated..."

# Verify Google OAuth secret
OAUTH_CLIENT_ID=$(kubectl get secret "${GOOGLE_OAUTH_SECRET_NAME}" -n default -o jsonpath='{.data.client-id}' | base64 -d 2>/dev/null || echo "")
OAUTH_CLIENT_SECRET=$(kubectl get secret "${GOOGLE_OAUTH_SECRET_NAME}" -n default -o jsonpath='{.data.client-secret}' | base64 -d 2>/dev/null || echo "")

if [ -z "$OAUTH_CLIENT_ID" ] || [ -z "$OAUTH_CLIENT_SECRET" ]; then
    echo "ERROR: Google OAuth secret is not properly populated!"
    echo "Client ID: ${OAUTH_CLIENT_ID:0:20}..."
    echo "Client Secret: ${OAUTH_CLIENT_SECRET:0:10}..."
    exit 1
fi

# Verify JWT secret
JWT_SECRET_CHECK=$(kubectl get secret "${JWT_SECRET_NAME}" -n default -o jsonpath='{.data.secret}' | base64 -d 2>/dev/null || echo "")
if [ -z "$JWT_SECRET_CHECK" ]; then
    echo "ERROR: JWT secret is not properly populated!"
    exit 1
fi

# Verify auth config
AUTH_EMAILS_CHECK=$(kubectl get configmap "${AUTH_CONFIG_NAME}" -n default -o jsonpath='{.data.admin-emails}' 2>/dev/null || echo "")
if [ -z "$AUTH_EMAILS_CHECK" ]; then
    echo "ERROR: Auth config is not properly populated!"
    exit 1
fi

echo "✅ All authentication secrets verified and properly populated:"
echo "  - Google OAuth Client ID: ${OAUTH_CLIENT_ID:0:20}..."
echo "  - Google OAuth Client Secret: [HIDDEN]"
echo "  - JWT Secret: [HIDDEN] (${#JWT_SECRET_CHECK} characters)"
echo "  - Admin Emails: ${AUTH_EMAILS_CHECK}"
echo ""

# --- LLM API Key Secrets ---

# Exit immediately if a command exits with a non-zero status.
set -e

echo "--- LLM API Key Secrets Creation ---"
echo "WARNING: API keys are hardcoded directly in this script. This is NOT recommended for production environments."
echo "         Consider using Kubernetes Secrets or a proper secrets management system (e.g., Google Secret Manager) in production."

# --- OPENAI API Key Secret ---
OPENAI_API_KEY_SECRET_NAME="openai-api-key"
# !! IMPORTANT: These are PLACEHOLDER keys. REPLACE THEM WITH YOUR ACTUAL KEYS!
OPENAI_API_KEY_VALUE="********************************************************************************************************************************************************************"

echo "Checking if secret '${OPENAI_API_KEY_SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${OPENAI_API_KEY_SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${OPENAI_API_KEY_SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${OPENAI_API_KEY_SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${OPENAI_API_KEY_SECRET_NAME}" \
            --from-literal=OPENAI_API_KEY="${OPENAI_API_KEY_VALUE}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create OpenAI API Key secret. Exiting."
      exit 1
    fi
    echo "Secret '${OPENAI_API_KEY_SECRET_NAME}' created."
fi

# --- GOOGLE API Key Secret ---
GOOGLE_API_KEY_SECRET_NAME="google-api-key"
# !! IMPORTANT: These are PLACEHOLDER keys. REPLACE THEM WITH YOUR ACTUAL KEYS!
GOOGLE_API_KEY_VALUE="AIzaSyBzG0Wdhtm44BPP4Htrt739oZyNBXFZ46I"

echo "Checking if secret '${GOOGLE_API_KEY_SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${GOOGLE_API_KEY_SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${GOOGLE_API_KEY_SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${GOOGLE_API_KEY_SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${GOOGLE_API_KEY_SECRET_NAME}" \
            --from-literal=GOOGLE_API_KEY="${GOOGLE_API_KEY_VALUE}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create Google API Key secret. Exiting."
      exit 1
    fi
    echo "Secret '${GOOGLE_API_KEY_SECRET_NAME}' created."
fi

# --- ANTHROPIC API Key Secret ---
ANTHROPIC_API_KEY_SECRET_NAME="anthropic-api-key"
# !! IMPORTANT: These are PLACEHOLDER keys. REPLACE THEM WITH YOUR ACTUAL KEYS!
ANTHROPIC_API_KEY_VALUE="************************************************************************************************************"

echo "Checking if secret '${ANTHROPIC_API_KEY_SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${ANTHROPIC_API_KEY_SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${ANTHROPIC_API_KEY_SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${ANTHROPIC_API_KEY_SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${ANTHROPIC_API_KEY_SECRET_NAME}" \
            --from-literal=ANTHROPIC_API_KEY="${ANTHROPIC_API_KEY_VALUE}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create Anthropic API Key secret. Exiting."
      exit 1
    fi
    echo "Secret '${ANTHROPIC_API_KEY_SECRET_NAME}' created."
fi

# --- LLAMA3 API Key Secret ---
LLAMA3_API_KEY_SECRET_NAME="llama3-api-key"
# Placeholder for Llama3 API Key - REPLACE WITH YOUR ACTUAL KEY IF YOU HAVE ONE!
LLAMA3_API_KEY_VALUE="*************************************" # ADD YOUR ACTUAL LLAMA3 KEY HERE IF APPLICABLE

echo "Checking if secret '${LLAMA3_API_KEY_SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${LLAMA3_API_KEY_SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${LLAMA3_API_KEY_SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${LLAMA3_API_KEY_SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${LLAMA3_API_KEY_SECRET_NAME}" \
            --from-literal=LLAMA3_API_KEY="${LLAMA3_API_KEY_VALUE}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create Llama3 API Key secret. Exiting."
      exit 1
    fi
    echo "Secret '${LLAMA3_API_KEY_SECRET_NAME}' created."
fi

# --- COHERE API Key Secret ---
COHERE_API_KEY_SECRET_NAME="cohere-api-key"
# !! IMPORTANT: These are PLACEHOLDER keys. REPLACE THEM WITH YOUR ACTUAL KEYS!
COHERE_API_KEY_VALUE="your-cohere-api-key-here" # ADD YOUR ACTUAL COHERE KEY HERE

echo "Checking if secret '${COHERE_API_KEY_SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${COHERE_API_KEY_SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${COHERE_API_KEY_SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${COHERE_API_KEY_SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${COHERE_API_KEY_SECRET_NAME}" \
            --from-literal=COHERE_API_KEY="${COHERE_API_KEY_VALUE}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create Cohere API Key secret. Exiting."
      exit 1
    fi
    echo "Secret '${COHERE_API_KEY_SECRET_NAME}' created."
fi

# --- HUGGING FACE API Key Secret ---
HUGGINGFACE_API_KEY_SECRET_NAME="huggingface-api-key"
# !! IMPORTANT: These are PLACEHOLDER keys. REPLACE THEM WITH YOUR ACTUAL KEYS!
HUGGINGFACE_API_KEY_VALUE="hf_your-huggingface-token-here" # ADD YOUR ACTUAL HUGGING FACE TOKEN HERE

echo "Checking if secret '${HUGGINGFACE_API_KEY_SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${HUGGINGFACE_API_KEY_SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${HUGGINGFACE_API_KEY_SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${HUGGINGFACE_API_KEY_SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${HUGGINGFACE_API_KEY_SECRET_NAME}" \
            --from-literal=HUGGINGFACE_API_KEY="${HUGGINGFACE_API_KEY_VALUE}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create Hugging Face API Key secret. Exiting."
      exit 1
    fi
    echo "Secret '${HUGGINGFACE_API_KEY_SECRET_NAME}' created."
fi

# --- MISTRAL API Key Secret ---
MISTRAL_API_KEY_SECRET_NAME="mistral-api-key"
# !! IMPORTANT: These are PLACEHOLDER keys. REPLACE THEM WITH YOUR ACTUAL KEYS!
MISTRAL_API_KEY_VALUE="your-mistral-api-key-here" # ADD YOUR ACTUAL MISTRAL KEY HERE

echo "Checking if secret '${MISTRAL_API_KEY_SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${MISTRAL_API_KEY_SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${MISTRAL_API_KEY_SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${MISTRAL_API_KEY_SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${MISTRAL_API_KEY_SECRET_NAME}" \
            --from-literal=MISTRAL_API_KEY="${MISTRAL_API_KEY_VALUE}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create Mistral API Key secret. Exiting."
      exit 1
    fi
    echo "Secret '${MISTRAL_API_KEY_SECRET_NAME}' created."
fi

# --- GROK API Key Secret ---
GROK_API_KEY_SECRET_NAME="grok-api-key"
# !! IMPORTANT: These are PLACEHOLDER keys. REPLACE THEM WITH YOUR ACTUAL KEYS!
GROK_API_KEY_VALUE="your-grok-api-key-here" # ADD YOUR ACTUAL GROK/xAI KEY HERE

echo "Checking if secret '${GROK_API_KEY_SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${GROK_API_KEY_SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${GROK_API_KEY_SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${GROK_API_KEY_SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${GROK_API_KEY_SECRET_NAME}" \
            --from-literal=GROK_API_KEY="${GROK_API_KEY_VALUE}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create GROK API Key secret. Exiting."
      exit 1
    fi
    echo "Secret '${GROK_API_KEY_SECRET_NAME}' created."
fi

# --- End LLM API Key Secrets ---

# --- Git Credentials Secret for PromptOps GitOps ---
GIT_CREDENTIALS_SECRET_NAME="git-credentials"
# GitHub Personal Access Token for GitOps operations
GIT_TOKEN_VALUE="****************************************"
# Generate a secure webhook secret
GIT_WEBHOOK_SECRET_VALUE=$(openssl rand -hex 32 2>/dev/null || echo "ai-operations-hub-webhook-secret-$(date +%s)")

echo "Checking if secret '${GIT_CREDENTIALS_SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${GIT_CREDENTIALS_SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${GIT_CREDENTIALS_SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${GIT_CREDENTIALS_SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${GIT_CREDENTIALS_SECRET_NAME}" \
            --from-literal=token="${GIT_TOKEN_VALUE}" \
            --from-literal=webhook-secret="${GIT_WEBHOOK_SECRET_VALUE}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create Git credentials secret. Exiting."
      exit 1
    fi
    echo "Secret '${GIT_CREDENTIALS_SECRET_NAME}' created successfully."
    echo "Webhook secret for GitHub configuration: ${GIT_WEBHOOK_SECRET_VALUE}"
    echo "Configure your GitHub webhook with this secret at:"
    echo "https://github.com/datlaphani/ai-cost-performance-optimizer/settings/hooks"
fi

# --- End Git Credentials Secret ---

# Create proxy-gateway-config ConfigMap from local file
PROXY_GATEWAY_CONFIG_PATH="${SCRIPT_DIR}/proxy-gateway/config.yaml"
CONFIGMAP_NAME="proxy-gateway-config"

echo "Creating ConfigMap '${CONFIGMAP_NAME}' from ${PROXY_GATEWAY_CONFIG_PATH}..."
if [ -f "${PROXY_GATEWAY_CONFIG_PATH}" ]; then
    kubectl create configmap "${CONFIGMAP_NAME}" --from-file="${PROXY_GATEWAY_CONFIG_PATH}" --dry-run=client -o yaml | kubectl apply -f -
    if [ $? -ne 0 ]; then
        echo "Error: Failed to create ConfigMap '${CONFIGMAP_NAME}'. Exiting."
        exit 1
    fi
    echo "ConfigMap '${CONFIGMAP_NAME}' created."
else
    echo "Error: Proxy Gateway config.yaml not found at ${PROXY_GATEWAY_CONFIG_PATH}. Cannot create ConfigMap. Exiting."
    exit 1
fi

echo "--- Kubernetes Secrets and ConfigMaps Creation Complete ---"


# --- Prepare Kubernetes Manifests for Application (Perform Image Substitutions and Workload Identity modifications) ---
echo "--- Preparing Kubernetes Manifests (performing image substitutions and Workload Identity modifications) ---"

# Temporary directory for modified YAMLs
MODIFIED_MANIFESTS_DIR="${SCRIPT_DIR}/.gke_manifests"
mkdir -p "${MODIFIED_MANIFESTS_DIR}"

K8S_MANIFESTS_TO_PROCESS=(
    "${SCRIPT_DIR}/clickhouse/clickhouse-k8s.yaml"
    "${SCRIPT_DIR}/kafka/kafka-server-properties-configmap.yaml"
    "${SCRIPT_DIR}/kafka/kafka-log4j-configmap.yaml"
    "${SCRIPT_DIR}/kafka/kafka-zookeeper-k8s.yaml"
    "${SCRIPT_DIR}/redis/redis-k8s.yaml"
    "${SCRIPT_DIR}/prometheus/prometheus-k8s.yaml"
    "${SCRIPT_DIR}/data-processor/data-processor.yaml"
    "${SCRIPT_DIR}/dashboard-api/dashboard-api.yaml"
    "${SCRIPT_DIR}/policy-manager/policy-manager-k8s.yaml"
    "${SCRIPT_DIR}/proxy-gateway/proxy-gateway.yaml"
    "${SCRIPT_DIR}/frontend/frontend.yaml"
    "${SCRIPT_DIR}/landing-page/landing-page.yaml"
    "${SCRIPT_DIR}/kafka_topic_creator/kafka-topic-creator-job.yaml"
    "${SCRIPT_DIR}/ai-optimizer/ai-optimizer.yaml"
    "${SCRIPT_DIR}/evaluation-service/evaluation-service.yaml"
    "${SCRIPT_DIR}/governance-service/governance-service.yaml"
    "${SCRIPT_DIR}/integration-service/integration-service.yaml"
    "${SCRIPT_DIR}/planning-service/deployment.yaml"
    "${SCRIPT_DIR}/multi-agent-orchestrator/deployment.yaml"
    "${SCRIPT_DIR}/bias-detection-service/bias-detection-service.yaml"
    "${SCRIPT_DIR}/explainability-service/explainability-service.yaml"
    "${SCRIPT_DIR}/robustness-testing-service/robustness-testing-service.yaml"
    "${SCRIPT_DIR}/compliance-service/compliance-service.yaml"
    "${SCRIPT_DIR}/auth-service/auth-service.yaml"
    "${SCRIPT_DIR}/sentiment-service/sentiment-service.yaml"
    "${SCRIPT_DIR}/social-integration-service/social-integration-service.yaml"
)

# Enable debug logging for the loop
set -x

for manifest in "${K8S_MANIFESTS_TO_PROCESS[@]}"; do
    echo "Processing Kubernetes manifest: ${manifest}"
    if [ -f "${manifest}" ]; then
        # Copy the file to the temporary directory
        temp_manifest="${MODIFIED_MANIFESTS_DIR}/$(basename "$(dirname "${manifest}")")-$(basename "${manifest}")"
        cp "${manifest}" "${temp_manifest}"

        # Perform image substitutions using sed on the copied file
        # General substitutions for services (these patterns are confirmed correct)
        sed -i'' -e "s|image: ai-cost-performance-optimizer-proxy-gateway:latest|image: ${PROXY_GATEWAY_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-frontend:latest|image: ${FRONTEND_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-landing-page:latest|image: ${LANDING_PAGE_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-kafka-topic-creator:latest|image: ${KAFKA_TOPIC_CREATOR_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-data-processor:latest|image: ${DATA_PROCESSOR_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-dashboard-api:latest|image: ${DASHBOARD_API_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-policy-manager:latest|image: ${POLICY_MANAGER_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-ai-optimizer:latest|image: ${AI_OPTIMIZER_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-evaluation-service:latest|image: ${EVALUATION_SERVICE_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-governance-service:latest|image: ${GOVERNANCE_SERVICE_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-integration-service:latest|image: ${INTEGRATION_SERVICE_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-planning-service:latest|image: ${PLANNING_SERVICE_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-multi-agent-orchestrator:latest|image: ${MULTI_AGENT_ORCHESTRATOR_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-bias-detection-service:latest|image: ${BIAS_DETECTION_SERVICE_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-explainability-service:latest|image: ${EXPLAINABILITY_SERVICE_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-robustness-testing-service:latest|image: ${ROBUSTNESS_TESTING_SERVICE_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-compliance-service:latest|image: ${COMPLIANCE_SERVICE_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-auth-service:latest|image: ${AUTH_SERVICE_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-sentiment-service:latest|image: ${SENTIMENT_SERVICE_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-social-integration-service:latest|image: ${SOCIAL_INTEGRATION_SERVICE_IMAGE}|g" "${temp_manifest}"

        # Workload Identity modifications for Policy Manager (remove GCP credentials, add serviceAccountName)
        if [[ "$(basename "${manifest}")" == "policy-manager-k8s.yaml" ]]; then
            echo "Modifying policy-manager-k8s.yaml for Workload Identity (removing GCP creds & injecting KSA)..."
            # Remove GCP credential environment variables if present
            sed -i'' -e '/- name: GOOGLE_APPLICATION_CREDENTIALS/,+1d' "${temp_manifest}"
            # Remove volume mounts related to GCP credentials
            sed -i'' -e '/^\s*- name: gcp-credentials/d' "${temp_manifest}"
            sed -i'' -e '/^\s*mountPath: "\/etc\/gcp"/d' "${temp_manifest}"
            sed -i'' -e '/^\s*readOnly: true/d' "${temp_manifest}"
            # Remove volumes related to GCP credentials
            sed -i'' -e '/^\s*- name: gcp-credentials/d' "${temp_manifest}"
            sed -i'' -e '/^\s*secret:/d' "${temp_manifest}"
            sed -i'' -e '/^\s*secretName: gcp-service-account-key/d' "${temp_manifest}"
            sed -i'' -e '/^\s*items:/d' "${temp_manifest}"
            sed -i'' -e '/^\s*- key: service-account.json/d' "${temp_manifest}"
            sed -i'' -e '/^\s*path: service-account.json/d' "${temp_manifest}"
            # Add serviceAccountName to the Deployment spec for Workload Identity
            awk -v ksa_name="${POLICY_MANAGER_KSA_NAME}" '/containers:/ && !x { print "      serviceAccountName: " ksa_name; x=1 } { print }' "${temp_manifest}" > "${temp_manifest}.tmp" && mv "${temp_manifest}.tmp" "${temp_manifest}"
        fi

        # Workload Identity modifications for Dashboard API (remove GCP credentials, add serviceAccountName)
        if [[ "$(basename "${manifest}")" == "dashboard-api.yaml" ]]; then
            echo "Modifying dashboard-api.yaml for Workload Identity (removing GCP creds & injecting KSA)..."
            # Remove GCP credential environment variables if present
            sed -i'' -e '/- name: GOOGLE_APPLICATION_CREDENTIALS/,+1d' "${temp_manifest}"
            # Remove volume mounts related to GCP credentials
            sed -i'' -e '/^\s*- name: gcp-credentials/d' "${temp_manifest}"
            sed -i'' -e '/^\s*mountPath: "\/etc\/gcp"/d' "${temp_manifest}"
            sed -i'' -e '/^\s*readOnly: true/d' "${temp_manifest}"
            # Remove volumes related to GCP credentials
            sed -i'' -e '/^\s*- name: gcp-credentials/d' "${temp_manifest}"
            sed -i'' -e '/^\s*secret:/d' "${temp_manifest}"
            sed -i'' -e '/^\s*secretName: gcp-service-account-key/d' "${temp_manifest}"
            sed -i'' -e '/^\s*items:/d' "${temp_manifest}"
            sed -i'' -e '/^\s*- key: service-account.json/d' "${temp_manifest}"
            sed -i'' -e '/^\s*path: service-account.json/d' "${temp_manifest}"
            # Add serviceAccountName to the Deployment spec for Workload Identity
            awk -v ksa_name="${DASHBOARD_API_KSA_NAME}" '/containers:/ && !x { print "      serviceAccountName: " ksa_name; x=1 } { print }' "${temp_manifest}" > "${temp_manifest}.tmp" && mv "${temp_manifest}.tmp" "${temp_manifest}"
        fi

        # --- DEBUGGING ADDITION ---
        if [[ "$(basename "${manifest}")" == "frontend.yaml" ]]; then
            echo "--- DEBUG: Contents of frontend.yaml in temp directory AFTER sed:"
            cat "${temp_manifest}"
            echo "--- DEBUG: Value of FRONTEND_IMAGE: ${FRONTEND_IMAGE}"
            echo "--- END DEBUG ---"
        fi
        # --- END DEBUGGING ADDITION ---

    else
        echo "Warning: Kubernetes manifest not found at ${manifest}. Skipping processing."
    fi
done

echo "--- All manifests prepared in ${MODIFIED_MANIFESTS_DIR} ---"

# Disable debug logging for the rest of the script
set +x

# --- Apply Kubernetes Manifests in a dependency-aware sequence ---
echo "--- Applying Kubernetes Manifests in a dependency-aware sequence ---"

echo "Ensuring kubectl context is GKE before applying manifests..."
if ! kubectl config current-context | grep -q "${GKE_CLUSTER_NAME}"; then
    echo "Error: kubectl context is not set to ${GKE_CLUSTER_NAME}. Exiting."
    exit 1
fi

# --- Stage 1: Core Data Infrastructure (Databases, Message Queues, Monitoring) ---
echo "Applying core data infrastructure (ClickHouse, Kafka+Zookeeper, Redis, Prometheus)..."

# Install Prometheus Operator CRDs for ServiceMonitor support
echo "Installing Prometheus Operator CRDs for ServiceMonitor support..."
kubectl apply -f https://raw.githubusercontent.com/prometheus-operator/prometheus-operator/main/example/prometheus-operator-crd/monitoring.coreos.com_servicemonitors.yaml || echo "ServiceMonitor CRD may already exist"

kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/clickhouse-clickhouse-k8s.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/kafka-kafka-server-properties-configmap.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/kafka-kafka-log4j-configmap.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/kafka-kafka-zookeeper-k8s.yaml" # Deploys both Zookeeper and Kafka
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/redis-redis-k8s.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/prometheus-prometheus-k8s.yaml"

echo "Waiting for core infrastructure to be available..."
kubectl wait --for=condition=Available deployment/clickhouse -n default --timeout=600s
kubectl wait --for=condition=Available deployment/zookeeper -n default --timeout=600s
kubectl wait --for=condition=Available deployment/kafka -n default --timeout=600s
kubectl wait --for=condition=Available deployment/redis -n default --timeout=600s
kubectl wait --for=condition=Available deployment/prometheus -n default --timeout=600s
echo "Core infrastructure deployments are ready."

# --- Stage 2: Kafka Topic Creation ---
echo "Applying Kafka Topic Creator Job..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/kafka_topic_creator-kafka-topic-creator-job.yaml"

echo "Waiting for Kafka Topic Creator Job to complete..."
kubectl wait --for=condition=complete job/kafka-topic-creator -n default --timeout=600s
echo "Kafka topics created."


# --- Stage 3: Deploy Policy Manager (CRITICAL: Must be ready before populator job) ---
echo "Deploying Policy Manager..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/policy-manager-policy-manager-k8s.yaml"
echo "Waiting for Policy Manager to be available..."
kubectl wait --for=condition=Available deployment/policy-manager -n default --timeout=600s
echo "Policy Manager is ready."


# --- Stage 4: Run Redis Populator Job (after Policy Manager is ready) ---
echo "--- Starting Redis Populator Job ---"
# This script creates the job and waits for its completion. It relies on the policy-manager service being available.
"${SCRIPT_DIR}/redis-populator-job/create_redis_populator_job.sh"
echo "--- Redis Populator Job creation (within New_Autostart.sh) complete ---"


# --- Stage 5: Deploy Data Processor (Now that Redis is populated) ---
echo "Applying Data Processor deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/data-processor-data-processor.yaml"

echo "Waiting for Data Processor to be available..."
if kubectl wait --for=condition=Available deployment/data-processor -n default --timeout=600s; then
  echo "Data Processor deployment is ready."
else
  echo "Error: Data Processor deployment failed to become available within timeout."
  echo "Checking deployment status..."
  kubectl get deployment data-processor -o yaml || echo "Deployment not found"
  echo "Checking pod status..."
  kubectl get pods -l app=data-processor || echo "No pods found"
  echo "Checking recent events..."
  kubectl get events --sort-by='.lastTimestamp' | grep -i data-processor | tail -10
  exit 1
fi

# Verify that the Data Processor has created the inference_logs table
echo "Verifying that the inference_logs table has been created in ClickHouse..."
CLICKHOUSE_POD=$(kubectl get pod -l app=clickhouse -o jsonpath="{.items[0].metadata.name}" 2>/dev/null || echo "")
if [ -z "$CLICKHOUSE_POD" ]; then
  echo "Error: ClickHouse pod not found. Cannot verify table creation."
  exit 1
fi

# Wait for the inference_logs table to be created
MAX_RETRIES=30
RETRY_COUNT=0
TABLE_EXISTS=false

while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ "$TABLE_EXISTS" = false ]; do
  echo "Checking if inference_logs table exists (attempt $((RETRY_COUNT + 1))/${MAX_RETRIES})..."
  if kubectl exec "$CLICKHOUSE_POD" -- clickhouse-client -q "EXISTS TABLE inference_logs;" 2>/dev/null | grep -q "1"; then
    TABLE_EXISTS=true
    echo "Table 'inference_logs' exists in ClickHouse."
  else
    echo "Table 'inference_logs' not found yet. Waiting 10 seconds before retry..."
    sleep 10
    RETRY_COUNT=$((RETRY_COUNT+1))
  fi
done

if [ "$TABLE_EXISTS" = false ]; then
  echo "Error: inference_logs table was not created within the expected time."
  echo "Checking Data Processor logs for errors..."
  DATA_PROCESSOR_POD=$(kubectl get pod -l app=data-processor -o jsonpath="{.items[0].metadata.name}" 2>/dev/null || echo "")
  if [ -n "$DATA_PROCESSOR_POD" ]; then
    kubectl logs "$DATA_PROCESSOR_POD"
  else
    echo "Data Processor pod not found."
  fi
  exit 1
fi

echo "Data Processor is ready and has created the inference_logs table."


# --- Stage 6: Reset Kafka Offset ---
echo "Executing Kafka offset reset for data-processor-group..."
KAFKA_POD=$(kubectl get pod -l app=kafka -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
if [ -z "$KAFKA_POD" ]; then
    echo "Warning: Kafka pod not found, cannot reset offsets. Make sure Kafka is running."
else
    kubectl exec "$KAFKA_POD" -- /usr/bin/kafka-consumer-groups \
        --bootstrap-server localhost:9092 \
        --group data-processor-group \
        --topic inference-logs \
        --reset-offsets --to-earliest --execute || echo "Warning: Kafka offset reset failed. Continuing."
    echo "Kafka offset reset completed for data-processor-group on inference-logs."
fi


# --- Stage 7: AI Optimizer Deployment ---
echo "Applying AI Optimizer deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/ai-optimizer-ai-optimizer.yaml"
echo "Waiting for AI Optimizer to be available..."
kubectl wait --for=condition=Available deployment/ai-optimizer -n default --timeout=600s
echo "AI Optimizer is ready."


# --- Stage 8: Deploy Authentication Service First ---
echo "Deploying Authentication Service (required by other services)..."

if [ "${_DEPLOY_AUTH_SERVICE:-true}" = "true" ]; then
    echo "Applying Authentication Service deployment..."
    kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/auth-service-auth-service.yaml"
    echo "Waiting for Authentication Service to be available..."
    kubectl wait --for=condition=Available deployment/auth-service -n default --timeout=600s
    echo "Authentication Service is ready."
else
    echo "Skipping Authentication Service deployment as _DEPLOY_AUTH_SERVICE is not 'true'."
fi

# --- Stage 9: Deploy Remaining Services (with individual waits) ---
echo "Applying remaining service deployments with individual waits..."

# Deploy services one-by-one with waits to ensure dependencies are met.
echo "Applying Evaluation Service deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/evaluation-service-evaluation-service.yaml"
echo "Waiting for Evaluation Service to be available..."
kubectl wait --for=condition=Available deployment/evaluation-service -n default --timeout=600s
echo "Evaluation Service is ready."

echo "Applying Governance Service deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/governance-service-governance-service.yaml"
echo "Waiting for Governance Service to be available..."
kubectl wait --for=condition=Available deployment/governance-service -n default --timeout=600s
echo "Governance Service is ready."

echo "Applying Integration Service deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/integration-service-integration-service.yaml"
echo "Waiting for Integration Service to be available..."
kubectl wait --for=condition=Available deployment/integration-service -n default --timeout=600s
echo "Integration Service is ready."

# Deploy Sentiment Analysis Service
if [ "${_DEPLOY_SENTIMENT_SERVICE:-true}" = "true" ]; then
    echo "Applying Sentiment Analysis Service deployment..."
    kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/sentiment-service-sentiment-service.yaml"
    echo "Waiting for Sentiment Analysis Service to be available..."
    kubectl wait --for=condition=Available deployment/sentiment-service -n default --timeout=600s
    echo "Sentiment Analysis Service is ready."
else
    echo "Skipping Sentiment Analysis Service deployment as _DEPLOY_SENTIMENT_SERVICE is not 'true'."
fi

# Deploy Social Integration Service
if [ "${_DEPLOY_SOCIAL_INTEGRATION_SERVICE:-true}" = "true" ]; then
    echo "Applying Social Integration Service deployment..."
    kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/social-integration-service-social-integration-service.yaml"
    echo "Waiting for Social Integration Service to be available..."
    kubectl wait --for=condition=Available deployment/social-integration-service -n default --timeout=600s
    echo "Social Integration Service is ready."
else
    echo "Skipping Social Integration Service deployment as _DEPLOY_SOCIAL_INTEGRATION_SERVICE is not 'true'."
fi

# --- Stage 9: Deploy Responsible AI & Governance Services ---
echo "Applying Responsible AI & Governance Services..."

echo "Applying Bias Detection Service deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/bias-detection-service-bias-detection-service.yaml"
echo "Waiting for Bias Detection Service to be available..."
kubectl wait --for=condition=Available deployment/bias-detection-service -n default --timeout=600s
echo "Bias Detection Service is ready."

echo "Applying Explainability Service deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/explainability-service-explainability-service.yaml"
echo "Waiting for Explainability Service to be available..."
kubectl wait --for=condition=Available deployment/explainability-service -n default --timeout=600s
echo "Explainability Service is ready."

echo "Applying Robustness Testing Service deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/robustness-testing-service-robustness-testing-service.yaml"
echo "Waiting for Robustness Testing Service to be available..."
kubectl wait --for=condition=Available deployment/robustness-testing-service -n default --timeout=600s
echo "Robustness Testing Service is ready."

echo "Applying Compliance Service deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/compliance-service-compliance-service.yaml"
echo "Waiting for Compliance Service to be available..."
kubectl wait --for=condition=Available deployment/compliance-service -n default --timeout=600s
echo "Compliance Service is ready."

echo "All Responsible AI & Governance Services are ready."

echo "Applying Planning Service deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/planning-service-deployment.yaml"
echo "Waiting for Planning Service to be available..."
kubectl wait --for=condition=Available deployment/planning-service -n default --timeout=600s
echo "Planning Service is ready."

echo "Applying Multi-Agent Orchestrator deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/multi-agent-orchestrator-deployment.yaml"
echo "Waiting for Multi-Agent Orchestrator to be available..."
kubectl wait --for=condition=Available deployment/multi-agent-orchestrator -n default --timeout=600s
echo "Multi-Agent Orchestrator is ready."

echo "Applying Dashboard API deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/dashboard-api-dashboard-api.yaml"
echo "Waiting for Dashboard API to be available..."
kubectl wait --for=condition=Available deployment/dashboard-api -n default --timeout=600s
echo "Dashboard API is ready."

echo "Applying Proxy Gateway deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/proxy-gateway-proxy-gateway.yaml"
echo "Waiting for Proxy Gateway to be available..."
kubectl wait --for=condition=Available deployment/proxy-gateway -n default --timeout=600s
echo "Proxy Gateway is ready."

echo "Applying Frontend Dashboard deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/frontend-frontend.yaml"
echo "Waiting for Frontend Dashboard to be available..."
kubectl wait --for=condition=Available deployment/frontend-dashboard -n default --timeout=600s
echo "Frontend Dashboard is ready."

echo "Applying Landing Page deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/landing-page-landing-page.yaml"
echo "Waiting for Landing Page to be available..."
kubectl wait --for=condition=Available deployment/landing-page -n default --timeout=600s
echo "Landing Page is ready."

# ============================================================================
# INGRESS CONFIGURATION
# ============================================================================
# The ingress configuration is split into multiple files for better organization:
# 1. unified-ingress.yaml - Main ingress for landing page and root API routes
# 2. auth-ingress.yaml - Authentication service routes for all editions
# 3. enterprise-frontend-ingress.yaml - Enterprise frontend and API routes (no rewrite conflicts)
# 4. standard-api-ingress-fixed.yaml - Standard edition API routes with proper rewrites
# This separation prevents MIME type issues and ensures proper routing for both editions.

echo "Applying Unified AI Operations Hub Ingress configuration..."
kubectl apply -f "${SCRIPT_DIR}/unified-ingress.yaml"
wait_for_ingress "unified-ai-operations-hub-ingress" "default" 300

echo "Applying Authentication Service Ingress configuration..."
kubectl apply -f "${SCRIPT_DIR}/auth-ingress.yaml"
wait_for_ingress "auth-service-ingress" "default" 300
wait_for_ingress "enterprise-auth-ingress" "default" 300
wait_for_ingress "standard-auth-ingress" "default" 300

echo "Applying Enterprise API Ingress configuration..."
kubectl apply -f "${SCRIPT_DIR}/enterprise-api-ingress.yaml"
wait_for_ingress "enterprise-api-ingress" "default" 300

echo "Applying Enterprise Frontend Ingress configuration..."
kubectl apply -f "${SCRIPT_DIR}/enterprise-frontend-ingress.yaml"
wait_for_ingress "enterprise-frontend-ingress" "default" 300

echo "Restarting Frontend Dashboard to apply updated nginx configuration..."
kubectl rollout restart deployment/frontend-dashboard -n default
echo "Waiting for Frontend Dashboard rollout to complete..."
kubectl rollout status deployment/frontend-dashboard -n default --timeout=300s
echo "Frontend Dashboard restart completed."

echo "Verifying API endpoints..."
# Test key API endpoints to ensure they're accessible
ENDPOINTS=(
    "https://scale-llm.com/enterprise/v1/bias-metrics"
    "https://scale-llm.com/enterprise/v1/explanations"
    "https://scale-llm.com/enterprise/v1/robustness-tests"
    "https://scale-llm.com/enterprise/v1/compliance/assessments"
    "https://scale-llm.com/enterprise/v1/factsheets"
    "https://scale-llm.com/api/synthetic-data/status"
    "https://scale-llm.com/api/synthetic-data/metrics"
)

for endpoint in "${ENDPOINTS[@]}"; do
    echo "Testing endpoint: $endpoint"
    if curl -s -f "$endpoint" > /dev/null 2>&1; then
        echo "✅ $endpoint - OK"
    else
        echo "❌ $endpoint - Failed"
    fi
done
echo "API endpoint verification completed."

echo "Applying Standard Edition API Ingress configuration..."
kubectl apply -f "${SCRIPT_DIR}/standard-api-ingress-fixed.yaml"
wait_for_ingress "standard-api-ingress-fixed" "default" 300

echo "Applying Enterprise MCP Ingress configuration..."
kubectl apply -f "${SCRIPT_DIR}/enterprise-mcp-ingress.yaml"
wait_for_ingress "enterprise-mcp-ingress" "default" 300

# Configure ingress routes for enterprise deployment
echo "🌐 Configuring ingress routes for enterprise edition..."
if [ -f "${SCRIPT_DIR}/scripts/manage-ingress-routes.sh" ]; then
    bash "${SCRIPT_DIR}/scripts/manage-ingress-routes.sh" enterprise
else
    echo "⚠️  Ingress management script not found, skipping route configuration..."
fi

echo "All services have been deployed successfully."


#echo "Waiting for remaining deployments to be available..."
#kubectl wait --for=condition=Available deployment/governance-service --timeout=600s
#kubectl wait --for=condition=Available deployment/integration-service --timeout=600s
#echo "All remaining deployments are ready."

#kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/governance-service.yaml"
#kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/integration-service.yaml"

echo "--- Kubernetes Manifest Application Complete ---"

echo "--- Setup Complete ---"
echo "Your application components are being deployed to GKE."
echo "You can check the status of your pods with: kubectl get pods"
echo "You can check the status of your services with: kubectl get services"

echo "To access services exposed via LoadBalancer (e.g., proxy-gateway, frontend-dashboard, prometheus):"
echo "Run: kubectl get services -o wide"
echo "Look for the EXTERNAL-IP of the desired service. It might take a few minutes for the IP to provision."

echo "Script execution finished."