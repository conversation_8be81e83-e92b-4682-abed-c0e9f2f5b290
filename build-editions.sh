#!/bin/bash

# AI Operations Hub - Flexible Edition Builder
# This script allows you to build either Enterprise or Startup edition independently

set -e

EDITION=${1:-"help"}
PROJECT_ID="silken-zenith-460615-s7"
REGION="us-central1"
REPO="ai-optimizer-repo"

show_help() {
    echo "🏗️  AI Operations Hub Edition Builder"
    echo "======================================"
    echo ""
    echo "Usage: $0 [edition] [options]"
    echo ""
    echo "Available editions:"
    echo "  enterprise    - Build full enterprise edition"
    echo "  standard      - Build simplified standard edition (formerly startup)"
    echo "  landing       - Build landing page"
    echo "  both          - Build both enterprise and standard editions"
    echo "  all           - Build all editions including landing page"
    echo "  help          - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 enterprise                    # Build enterprise edition only"
    echo "  $0 standard                      # Build standard edition only"
    echo "  $0 landing                       # Build landing page only"
    echo "  $0 both                          # Build both enterprise and standard"
    echo "  $0 all                           # Build all editions"
    echo ""
    echo "Advanced Examples (using your build flags):"
    echo "  # Build only specific enterprise services:"
    echo "  gcloud builds submit --config=cloudbuild.yaml \\"
    echo "    --substitutions=_BUILD_FRONTEND=false,_BUILD_AI_OPTIMIZER=true"
    echo ""
    echo "  # Build only frontend for startup:"
    echo "  gcloud builds submit --config=cloudbuild-startup.yaml \\"
    echo "    --substitutions=_BUILD_FRONTEND_STARTUP=true,_BUILD_PROXY_GATEWAY_STARTUP=false"
    echo ""
    echo "  # Build without deploying:"
    echo "  gcloud builds submit --config=cloudbuild-startup.yaml \\"
    echo "    --substitutions=_DEPLOY_STARTUP_EDITION=false"
    echo ""
    echo "🔒 Safety: Each edition uses separate Dockerfiles and configs"
    echo "   Enterprise: k8s/frontend/Dockerfile (full features)"
    echo "   Standard:   k8s/frontend/Dockerfile.standard (simplified)"
    echo "   Landing:    k8s/landing-page/Dockerfile (static page)"
    echo ""
    echo "🎛️  Available Build Flags:"
    echo "   Enterprise: _BUILD_PROXY_GATEWAY, _BUILD_FRONTEND, _BUILD_AI_OPTIMIZER, etc."
    echo "   Standard:   _BUILD_PROXY_GATEWAY_STANDARD, _BUILD_FRONTEND_STANDARD, etc."
    echo "   Landing:    _BUILD_LANDING_PAGE"
}

build_enterprise() {
    echo "🏢 Building Enterprise Edition..."
    echo "================================="
    gcloud builds submit --config=cloudbuild.yaml
    echo "✅ Enterprise edition build completed!"
}

build_standard() {
    echo "🚀 Building Standard Edition..."
    echo "==============================="
    gcloud builds submit --config=cloudbuild-standard.yaml
    echo "✅ Standard edition build completed!"
}

build_landing() {
    echo "🏠 Building Landing Page..."
    echo "==========================="
    gcloud builds submit --config=cloudbuild-landing.yaml
    echo "✅ Landing page build completed!"
}

case $EDITION in
    "enterprise")
        build_enterprise
        ;;
    "standard")
        build_standard
        ;;
    "landing")
        build_landing
        ;;
    "both")
        echo "🔄 Building Enterprise and Standard Editions..."
        echo "==============================================="
        build_enterprise
        echo ""
        build_standard
        echo ""
        echo "✅ Both editions built successfully!"
        ;;
    "all")
        echo "🔄 Building All Editions..."
        echo "==========================="
        build_landing
        echo ""
        build_enterprise
        echo ""
        build_standard
        echo ""
        echo "✅ All editions built successfully!"
        ;;
    "help"|*)
        show_help
        ;;
esac

if [ "$EDITION" != "help" ] && [ "$EDITION" != "" ]; then
    echo ""
    echo "🌐 Access Points:"
    echo "  Landing Page: https://scale-llm.com"
    echo "  Enterprise:   https://scale-llm.com/enterprise"
    echo "  Standard:     https://scale-llm.com/standard"
    echo ""
    echo "📊 Check deployment status:"
    echo "  kubectl get pods -n default             # Landing & Enterprise"
    echo "  kubectl get pods -n enterprise-edition  # Enterprise services"
    echo "  kubectl get pods -n standard-edition    # Standard services"
fi
