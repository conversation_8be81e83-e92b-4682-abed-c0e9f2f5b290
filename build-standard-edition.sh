#!/bin/bash

# AI Operations Hub - Standard Edition Build Script
# Builds and deploys the standard edition with proper configuration

set -e

echo "🚀 Building AI Operations Hub - Standard Edition"
echo "================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if gcloud is configured
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    print_error "No active gcloud authentication found. Please run 'gcloud auth login'"
    exit 1
fi

# Get current project
PROJECT_ID=$(gcloud config get-value project)
if [ -z "$PROJECT_ID" ]; then
    print_error "No GCP project set. Please run 'gcloud config set project YOUR_PROJECT_ID'"
    exit 1
fi

print_status "Using GCP Project: $PROJECT_ID"

# Build options
BUILD_FRONTEND=${BUILD_FRONTEND:-true}
BUILD_BACKEND=${BUILD_BACKEND:-true}
DEPLOY=${DEPLOY:-true}

print_status "Build Configuration:"
echo "  Frontend: $BUILD_FRONTEND"
echo "  Backend Services: $BUILD_BACKEND"
echo "  Deploy: $DEPLOY"
echo ""

# Determine substitutions based on options
SUBSTITUTIONS="_BUILD_FRONTEND_STANDARD=$BUILD_FRONTEND"
SUBSTITUTIONS="$SUBSTITUTIONS,_BUILD_PROXY_GATEWAY_STANDARD=$BUILD_BACKEND"
SUBSTITUTIONS="$SUBSTITUTIONS,_BUILD_AI_OPTIMIZER_STANDARD=$BUILD_BACKEND"
SUBSTITUTIONS="$SUBSTITUTIONS,_BUILD_DASHBOARD_API_STANDARD=$BUILD_BACKEND"
SUBSTITUTIONS="$SUBSTITUTIONS,_DEPLOY_STANDARD_EDITION=$DEPLOY"

print_status "Starting Cloud Build with substitutions: $SUBSTITUTIONS"

# Submit the build
if gcloud builds submit --config=cloudbuild-standard.yaml --substitutions="$SUBSTITUTIONS"; then
    print_success "Standard Edition build completed successfully!"
    
    if [ "$DEPLOY" = "true" ]; then
        echo ""
        print_status "Deployment completed. Testing endpoints..."
        
        # Wait a moment for services to be ready
        sleep 10
        
        # Test the deployment
        echo ""
        print_status "Testing deployment..."
        
        # Test landing page
        if curl -s -o /dev/null -w "%{http_code}" https://scale-llm.com/ | grep -q "200"; then
            print_success "Landing page is accessible"
        else
            print_warning "Landing page may not be ready yet"
        fi
        
        # Test standard edition
        if curl -s -o /dev/null -w "%{http_code}" https://scale-llm.com/standard | grep -q "200"; then
            print_success "Standard edition frontend is accessible"
        else
            print_warning "Standard edition frontend may not be ready yet"
        fi
        
        # Test API health
        if curl -s -o /dev/null -w "%{http_code}" https://scale-llm.com/standard/api/health | grep -q "200"; then
            print_success "Standard edition API is healthy"
        else
            print_warning "Standard edition API may not be ready yet"
        fi
        
        echo ""
        print_success "Deployment Summary:"
        echo "  🌐 Landing Page: https://scale-llm.com/"
        echo "  🚀 Standard Edition: https://scale-llm.com/standard"
        echo "  🔧 API Health: https://scale-llm.com/standard/api/health"
        echo ""
        print_status "Next Steps:"
        echo "  1. Update API keys: kubectl edit secret llm-api-keys -n standard-edition"
        echo "  2. Monitor services: kubectl get pods -n standard-edition"
        echo "  3. Check logs: kubectl logs -f deployment/frontend-standard -n standard-edition"
    fi
else
    print_error "Build failed. Check the Cloud Build logs for details."
    exit 1
fi
