# AI Operations Hub - Ingress Architecture & Deployment Guide

This document explains how ingress routing works for different deployment scenarios and how to prevent the 503 errors that can occur during mixed deployments.

## 🏗️ Architecture Overview

The AI Operations Hub supports two deployment modes:

1. **Standard Edition** - Lightweight deployment for startups/SMBs
2. **Enterprise Edition** - Full-featured deployment for large organizations

Each edition uses different namespaces and ingress configurations to avoid conflicts.

## 🌐 Ingress Configuration

### Standard Edition Only
When deploying **only** the Standard Edition:

**Namespace**: `standard-edition`
**Ingress Files**: 
- `k8s/unified-ingress.yaml` (ai-operations-hub-ingress)
- `k8s/standard-api-ingress-fixed.yaml` (api-rewrite-ingress)

**Routes**:
```yaml
# Landing page (root)
/ → landing-page:80 (default namespace)

# Standard edition frontend  
/standard/* → frontend-standard-external:80 (default namespace)

# Standard edition API
/standard/api/* → proxy-gateway-standard-external:8080 (default namespace)

# Standard edition dashboard
/standard/dashboard/* → dashboard-api-standard-external:8081 (default namespace)
```

**Key Points**:
- Services run in `standard-edition` namespace
- External services in `default` namespace route to internal services
- NO enterprise routes (`/api`, `/dashboard`) to prevent 503 errors

### Enterprise Edition Only
When deploying **only** the Enterprise Edition:

**Namespace**: `default`
**Ingress Files**: 
- `k8s/comprehensive-ingress.yaml`

**Routes**:
```yaml
# Landing page (root)
/ → landing-page:80

# Enterprise frontend
/enterprise → frontend:80

# Enterprise API (direct)
/api → proxy-gateway:8080

# Enterprise dashboard (direct)  
/dashboard → dashboard-api:8081

# Standard edition (if co-deployed)
/standard/* → frontend-standard-external:80
```

### Both Editions (Co-deployment)
When deploying **both** editions:

**Namespaces**: `default` (enterprise) + `standard-edition` (standard)
**Ingress Strategy**: Dynamic route management

**Routes**:
```yaml
# Landing page
/ → landing-page:80

# Standard edition
/standard/* → frontend-standard-external:80
/standard/api/* → proxy-gateway-standard-external:8080  
/standard/dashboard/* → dashboard-api-standard-external:8081

# Enterprise edition
/enterprise → frontend:80
/api → proxy-gateway:8080
/dashboard → dashboard-api:8081
```

## 🔧 Deployment Scripts & Automation

### Automatic Issue Prevention

The deployment has been enhanced with automatic issue prevention:

#### 1. **Secret Management** (`k8s/scripts/create-llm-secrets.sh`)
- Creates all required API keys automatically
- Prevents `CreateContainerConfigError` issues
- Supports both namespaces

#### 2. **Ingress Route Management** (`k8s/scripts/manage-ingress-routes.sh`)
- Automatically removes enterprise routes when services don't exist
- Adds enterprise routes when services are deployed
- Prevents 503 errors from missing backend services

#### 3. **External Service Mapping** (`k8s/scripts/update-standard-external-services.sh`)
- Updates external service endpoints automatically
- Includes landing page mapping
- Cleans up problematic enterprise routes

#### 4. **Deployment Validation** (`k8s/scripts/validate-deployment.sh`)
- Validates deployment health
- Identifies and fixes common issues
- Supports both deployment types

### Updated Deployment Flow

#### Standard Edition Deployment
```bash
./deploy.sh -e standard
```

**What happens**:
1. Builds standard edition images
2. Deploys to `standard-edition` namespace
3. Creates complete LLM secrets
4. Updates external service mappings
5. **Removes enterprise routes** to prevent 503 errors
6. Validates deployment

#### Enterprise Edition Deployment  
```bash
./deploy.sh -e enterprise
```

**What happens**:
1. Builds all enterprise images
2. Deploys to `default` namespace
3. Creates complete LLM secrets
4. **Ensures enterprise routes exist**
5. Validates deployment

## 🚨 Common Issues & Solutions

### Issue 1: 503 Service Temporarily Unavailable
**Cause**: Ingress routes pointing to non-existent services
**Solution**: Run ingress route management
```bash
# For standard-only deployment
./k8s/scripts/manage-ingress-routes.sh standard

# For enterprise deployment
./k8s/scripts/manage-ingress-routes.sh enterprise
```

### Issue 2: CreateContainerConfigError
**Cause**: Missing API keys in secrets
**Solution**: Create complete secrets
```bash
# For standard edition
./k8s/scripts/create-llm-secrets.sh standard-edition

# For enterprise edition  
./k8s/scripts/create-llm-secrets.sh default
```

### Issue 3: Landing Page Not Loading
**Cause**: Missing external service mapping
**Solution**: Update external services
```bash
./k8s/scripts/update-standard-external-services.sh
```

### Issue 4: Mixed Deployment Conflicts
**Cause**: Conflicting ingress rules between editions
**Solution**: Use validation script
```bash
./k8s/scripts/validate-deployment.sh both
```

## 🔍 Troubleshooting Commands

### Check Current Ingress Configuration
```bash
kubectl describe ingress api-rewrite-ingress -n default
kubectl describe ingress ai-operations-hub-ingress -n default
```

### Check Service Status
```bash
# Standard edition
kubectl get all -n standard-edition

# Enterprise edition  
kubectl get all -n default
```

### Check Ingress Logs
```bash
kubectl logs -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx --tail=50
```

### Manual Route Management
```bash
# Remove problematic enterprise routes
kubectl patch ingress api-rewrite-ingress -n default --type='json' -p='[
  {"op": "remove", "path": "/spec/rules/0/http/paths/2"}
]'

# Add enterprise routes back
kubectl patch ingress api-rewrite-ingress -n default --type='json' -p='[
  {"op": "add", "path": "/spec/rules/0/http/paths/-", "value": {
    "path": "/api(/|$)(.*)", "pathType": "ImplementationSpecific",
    "backend": {"service": {"name": "proxy-gateway", "port": {"number": 8080}}}
  }}
]'
```

## 📋 Best Practices

1. **Always use the deployment scripts** - They include automatic issue prevention
2. **Run validation after deployment** - Use `validate-deployment.sh` to catch issues
3. **Check ingress logs** - Monitor for 503 errors during deployment
4. **Use namespace isolation** - Keep standard and enterprise editions separate
5. **Update external services** - Ensure service mappings are current

## 🔄 Migration Between Editions

### Standard → Enterprise
1. Deploy enterprise edition: `./deploy.sh -e enterprise`
2. Validate both: `./k8s/scripts/validate-deployment.sh both`
3. Test all endpoints

### Enterprise → Standard Only
1. Remove enterprise services: `kubectl delete namespace enterprise-edition`
2. Clean ingress routes: `./k8s/scripts/manage-ingress-routes.sh standard`
3. Validate: `./k8s/scripts/validate-deployment.sh standard`

This architecture ensures reliable deployments and prevents the 503 errors that previously occurred during mixed or incomplete deployments.
