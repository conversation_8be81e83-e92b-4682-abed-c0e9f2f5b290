#!/bin/bash

# AI Operations Hub - Quick Deployment Script
# Simple interface to choose between standard and enterprise editions

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}$1${NC}"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show welcome message
show_welcome() {
    clear
    print_header "🚀 AI Operations Hub - Quick Deploy"
    print_header "===================================="
    echo ""
    echo "Welcome to the AI Operations Hub deployment system!"
    echo "This script will help you deploy either the Standard or Enterprise edition."
    echo ""
}

# Function to show edition comparison
show_edition_comparison() {
    print_header "📊 Edition Comparison"
    print_header "====================="
    echo ""
    
    echo -e "${CYAN}Standard Edition (Startup/SMB):${NC}"
    echo "  ✅ AI Chat Interface with Optimizer Scores"
    echo "  ✅ Intelligent Model Routing"
    echo "  ✅ Cost Optimization"
    echo "  ✅ Basic Analytics Dashboard"
    echo "  ✅ Landing Page"
    echo "  ✅ Redis Caching"
    echo "  💰 Cost: ~\$50-100/month"
    echo "  ⚡ Resources: 2GB RAM, 2 CPU cores"
    echo ""
    
    echo -e "${PURPLE}Enterprise Edition (Large Organizations):${NC}"
    echo "  ✅ Everything in Standard Edition"
    echo "  ✅ Advanced Planning & Task Decomposition"
    echo "  ✅ Multi-Agent Orchestration"
    echo "  ✅ PromptOps with A/B Testing"
    echo "  ✅ Governance & Compliance"
    echo "  ✅ Advanced Analytics & Reporting"
    echo "  ✅ GitOps Integration"
    echo "  ✅ Synthetic Data Generation"
    echo "  ✅ Responsible AI Features"
    echo "  💰 Cost: ~\$200-500/month"
    echo "  ⚡ Resources: 8GB RAM, 4 CPU cores"
    echo ""
}

# Function to get user choice
get_user_choice() {
    while true; do
        echo -e "${YELLOW}Which edition would you like to deploy?${NC}"
        echo "1) Standard Edition (Recommended for startups/SMBs)"
        echo "2) Enterprise Edition (For large organizations)"
        echo "3) Show detailed comparison"
        echo "4) Exit"
        echo ""
        read -p "Enter your choice (1-4): " choice
        
        case $choice in
            1)
                EDITION="standard"
                break
                ;;
            2)
                EDITION="enterprise"
                break
                ;;
            3)
                show_edition_comparison
                ;;
            4)
                print_info "Deployment cancelled by user"
                exit 0
                ;;
            *)
                print_error "Invalid choice. Please enter 1, 2, 3, or 4."
                ;;
        esac
    done
}

# Function to validate GCP configuration
validate_gcp_config() {
    print_info "Validating GCP configuration..."
    
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI is not installed. Please install it first:"
        echo "  https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed. Please install it first:"
        echo "  https://kubernetes.io/docs/tasks/tools/"
        exit 1
    fi
    
    # Check if user is authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 > /dev/null; then
        print_error "You are not authenticated with gcloud. Please run:"
        echo "  gcloud auth login"
        exit 1
    fi
    
    # Get current project
    CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null || echo "")
    if [[ -z "$CURRENT_PROJECT" ]]; then
        print_error "No GCP project is set. Please run:"
        echo "  gcloud config set project YOUR_PROJECT_ID"
        exit 1
    fi
    
    print_success "GCP configuration validated"
    print_info "Current project: $CURRENT_PROJECT"
    
    # Ask user to confirm project
    read -p "Is this the correct project? (y/n): " confirm
    if [[ $confirm != "y" && $confirm != "Y" ]]; then
        print_info "Please set the correct project with:"
        echo "  gcloud config set project YOUR_PROJECT_ID"
        exit 1
    fi
    
    export GCP_PROJECT_ID="$CURRENT_PROJECT"
}

# Function to get deployment options
get_deployment_options() {
    print_header "🔧 Deployment Options"
    print_header "====================="
    echo ""
    
    # Build options
    echo -e "${CYAN}Build Options:${NC}"
    read -p "Build and push new images? (y/n) [default: y]: " build_choice
    BUILD_IMAGES=${build_choice:-y}
    
    # Deploy options
    echo -e "${CYAN}Deploy Options:${NC}"
    read -p "Deploy services after building? (y/n) [default: y]: " deploy_choice
    DEPLOY_SERVICES=${deploy_choice:-y}
    
    # Landing page option
    read -p "Include landing page? (y/n) [default: y]: " landing_choice
    INCLUDE_LANDING=${landing_choice:-y}
    
    echo ""
}

# Function to show deployment summary
show_deployment_summary() {
    print_header "📋 Deployment Summary"
    print_header "====================="
    echo ""
    echo "Edition: $EDITION"
    echo "GCP Project: $GCP_PROJECT_ID"
    echo "Build Images: $BUILD_IMAGES"
    echo "Deploy Services: $DEPLOY_SERVICES"
    echo "Include Landing Page: $INCLUDE_LANDING"
    echo ""
    
    read -p "Proceed with deployment? (y/n): " confirm
    if [[ $confirm != "y" && $confirm != "Y" ]]; then
        print_info "Deployment cancelled by user"
        exit 0
    fi
}

# Function to execute deployment
execute_deployment() {
    print_header "🚀 Starting Deployment"
    print_header "======================"
    echo ""
    
    # Build deployment command
    DEPLOY_CMD="./deploy.sh -e $EDITION"
    
    if [[ "$BUILD_IMAGES" == "n" || "$BUILD_IMAGES" == "N" ]]; then
        DEPLOY_CMD="$DEPLOY_CMD -d"  # Deploy only
    fi
    
    if [[ "$DEPLOY_SERVICES" == "n" || "$DEPLOY_SERVICES" == "N" ]]; then
        DEPLOY_CMD="$DEPLOY_CMD -b"  # Build only
    fi
    
    if [[ "$INCLUDE_LANDING" == "n" || "$INCLUDE_LANDING" == "N" ]]; then
        DEPLOY_CMD="$DEPLOY_CMD -s"  # Skip landing page
    fi
    
    print_info "Executing: $DEPLOY_CMD"
    echo ""
    
    # Execute deployment
    eval "$DEPLOY_CMD"
}

# Function to show post-deployment info
show_post_deployment_info() {
    print_header "🎉 Deployment Complete!"
    print_header "======================="
    echo ""
    
    if [[ "$EDITION" == "standard" ]]; then
        echo -e "${GREEN}Standard Edition Deployed Successfully!${NC}"
        echo ""
        echo "🌐 Access Points:"
        echo "  • Frontend: https://scale-llm.com/standard"
        echo "  • API Health: https://scale-llm.com/standard/api/health"
        echo "  • Dashboard: https://scale-llm.com/standard/dashboard/health"
        if [[ "$INCLUDE_LANDING" != "n" && "$INCLUDE_LANDING" != "N" ]]; then
            echo "  • Landing Page: https://scale-llm.com"
        fi
        echo ""
        echo "🔧 Next Steps:"
        echo "  1. Update API keys: kubectl edit secret llm-api-keys -n standard-edition"
        echo "  2. Monitor services: kubectl get pods -n standard-edition"
    else
        echo -e "${PURPLE}Enterprise Edition Deployed Successfully!${NC}"
        echo ""
        echo "🌐 Access Points:"
        echo "  • Frontend: https://scale-llm.com"
        echo "  • API Health: https://scale-llm.com/api/health"
        echo "  • Dashboard: https://scale-llm.com/dashboard/health"
        echo "  • Planning: https://scale-llm.com/api/planning/v1/goals"
        if [[ "$INCLUDE_LANDING" != "n" && "$INCLUDE_LANDING" != "N" ]]; then
            echo "  • Landing Page: https://scale-llm.com"
        fi
        echo ""
        echo "🔧 Next Steps:"
        echo "  1. Update API keys: kubectl edit secret llm-api-keys"
        echo "  2. Monitor services: kubectl get pods"
    fi
    
    echo ""
    echo -e "${CYAN}💡 Pro Tips:${NC}"
    echo "  • Test the AI chat interface with different models"
    echo "  • Check the live AI optimizer scores on the dashboard"
    echo "  • Explore the model comparison features"
    echo "  • Set up monitoring and alerts for production use"
    echo ""
    print_success "Your AI Operations Hub is ready to use! 🚀"
}

# Main function
main() {
    show_welcome
    show_edition_comparison
    get_user_choice
    validate_gcp_config
    get_deployment_options
    show_deployment_summary
    execute_deployment
    show_post_deployment_info
}

# Check if deploy.sh exists
if [[ ! -f "./deploy.sh" ]]; then
    print_error "deploy.sh not found in current directory"
    print_info "Please run this script from the project root directory"
    exit 1
fi

# Make deploy.sh executable
chmod +x ./deploy.sh

# Execute main function
main "$@"
