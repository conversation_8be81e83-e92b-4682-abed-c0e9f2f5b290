#!/bin/bash

# AI Operations Hub - Deployment Validation Script
# Validates that the deployment is working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Default configuration
EDITION="standard"
TIMEOUT=300  # 5 minutes timeout for health checks

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

# Function to show usage
show_usage() {
    cat << EOF
AI Operations Hub - Deployment Validation Script

Usage: $0 [OPTIONS]

OPTIONS:
    -e, --edition EDITION       Edition to validate: 'standard' or 'enterprise' (default: standard)
    -t, --timeout SECONDS       Timeout for health checks in seconds (default: 300)
    -h, --help                  Show this help message

EXAMPLES:
    $0                          # Validate standard edition
    $0 -e enterprise            # Validate enterprise edition
    $0 -e standard -t 600       # Validate with 10-minute timeout

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--edition)
            EDITION="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate edition
if [[ "$EDITION" != "standard" && "$EDITION" != "enterprise" ]]; then
    print_error "Invalid edition: $EDITION. Must be 'standard' or 'enterprise'"
    exit 1
fi

# Function to check if kubectl is available
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed or not in PATH"
        return 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        print_error "Cannot connect to Kubernetes cluster"
        return 1
    fi
    
    print_success "kubectl is available and connected to cluster"
    return 0
}

# Function to check if curl is available
check_curl() {
    if ! command -v curl &> /dev/null; then
        print_error "curl is not installed or not in PATH"
        return 1
    fi
    
    print_success "curl is available"
    return 0
}

# Function to validate Kubernetes resources
validate_k8s_resources() {
    print_header "🔍 Validating Kubernetes Resources"
    print_header "=================================="
    
    local namespace=""
    if [[ "$EDITION" == "standard" ]]; then
        namespace="-n standard-edition"
    fi
    
    # Check deployments
    print_status "Checking deployments..."
    local deployments
    if [[ "$EDITION" == "standard" ]]; then
        deployments=("ai-optimizer" "dashboard-api" "frontend" "proxy-gateway" "redis" "landing-page")
    else
        deployments=("ai-optimizer" "dashboard-api" "frontend" "proxy-gateway" "policy-manager" "data-processor" "planning-service" "evaluation-service" "integration-service" "landing-page")
    fi
    
    for deployment in "${deployments[@]}"; do
        if kubectl get deployment "$deployment" $namespace &> /dev/null; then
            local ready=$(kubectl get deployment "$deployment" $namespace -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
            local desired=$(kubectl get deployment "$deployment" $namespace -o jsonpath='{.spec.replicas}' 2>/dev/null || echo "1")
            
            if [[ "$ready" == "$desired" && "$ready" != "0" ]]; then
                print_success "Deployment $deployment is ready ($ready/$desired)"
            else
                print_warning "Deployment $deployment is not ready ($ready/$desired)"
            fi
        else
            print_error "Deployment $deployment not found"
        fi
    done
    
    # Check services
    print_status "Checking services..."
    local services
    if [[ "$EDITION" == "standard" ]]; then
        services=("ai-optimizer" "dashboard-api" "frontend" "proxy-gateway" "redis" "landing-page")
    else
        services=("ai-optimizer" "dashboard-api" "frontend" "proxy-gateway" "policy-manager" "data-processor" "planning-service" "evaluation-service" "integration-service" "landing-page")
    fi
    
    for service in "${services[@]}"; do
        if kubectl get service "$service" $namespace &> /dev/null; then
            print_success "Service $service exists"
        else
            print_warning "Service $service not found"
        fi
    done
    
    # Check secrets
    print_status "Checking secrets..."
    if kubectl get secret llm-api-keys $namespace &> /dev/null; then
        print_success "Secret llm-api-keys exists"
    else
        print_error "Secret llm-api-keys not found"
    fi

    if [[ "$EDITION" == "enterprise" ]]; then
        if kubectl get secret github-token &> /dev/null; then
            print_success "Secret github-token exists"
        else
            print_warning "Secret github-token not found (required for GitOps)"
        fi
    fi

    # Check ingress
    print_status "Checking ingress configuration..."
    if [[ "$EDITION" == "standard" ]]; then
        if kubectl get ingress standard-edition-ingress $namespace &> /dev/null; then
            print_success "Standard edition ingress exists"

            # Check ingress status
            local ingress_ready
            ingress_ready=$(kubectl get ingress standard-edition-ingress $namespace -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
            if [[ -n "$ingress_ready" ]]; then
                print_success "Standard edition ingress has external IP: $ingress_ready"
            else
                print_warning "Standard edition ingress is pending external IP assignment"
            fi
        else
            print_error "Standard edition ingress not found"
        fi
    else
        if kubectl get ingress comprehensive-ingress &> /dev/null; then
            print_success "Enterprise comprehensive ingress exists"

            # Check ingress status
            local ingress_ready
            ingress_ready=$(kubectl get ingress comprehensive-ingress -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
            if [[ -n "$ingress_ready" ]]; then
                print_success "Enterprise ingress has external IP: $ingress_ready"
            else
                print_warning "Enterprise ingress is pending external IP assignment"
            fi
        else
            print_error "Enterprise comprehensive ingress not found"
        fi
    fi
}

# Function to validate health endpoints
validate_health_endpoints() {
    print_header "🏥 Validating Health Endpoints"
    print_header "=============================="
    
    local base_url
    if [[ "$EDITION" == "standard" ]]; then
        base_url="https://scale-llm.com/standard"
    else
        base_url="https://scale-llm.com"
    fi
    
    # Health endpoints to check
    local endpoints=(
        "$base_url/api/health"
        "$base_url/dashboard/health"
    )
    
    if [[ "$EDITION" == "enterprise" ]]; then
        endpoints+=("$base_url/api/planning/v1/health")
    fi
    
    for endpoint in "${endpoints[@]}"; do
        print_status "Checking $endpoint..."
        
        local response_code
        response_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time 30 "$endpoint" || echo "000")
        
        if [[ "$response_code" == "200" ]]; then
            print_success "Health endpoint $endpoint is responding (HTTP $response_code)"
        elif [[ "$response_code" == "000" ]]; then
            print_error "Health endpoint $endpoint is not reachable (timeout or connection error)"
        else
            print_warning "Health endpoint $endpoint returned HTTP $response_code"
        fi
    done
}

# Function to validate AI optimizer scores
validate_ai_optimizer_scores() {
    print_header "🤖 Validating AI Optimizer Scores"
    print_header "================================="
    
    local base_url
    if [[ "$EDITION" == "standard" ]]; then
        base_url="https://scale-llm.com/standard"
    else
        base_url="https://scale-llm.com"
    fi
    
    local scores_endpoint="$base_url/api/model-capabilities"
    
    print_status "Checking AI optimizer scores endpoint..."
    
    local response
    response=$(curl -s --max-time 30 "$scores_endpoint" || echo "")
    
    if [[ -n "$response" ]]; then
        # Check if response contains expected model data
        if echo "$response" | grep -q "gpt-4o-mini\|gpt-3.5-turbo"; then
            print_success "AI optimizer scores are available and contain model data"
        else
            print_warning "AI optimizer scores endpoint responds but may not contain expected data"
        fi
    else
        print_error "AI optimizer scores endpoint is not responding"
    fi
}

# Function to validate edition-specific architecture
validate_edition_architecture() {
    print_header "🏗️ Validating Edition Architecture"
    print_header "================================="

    if [[ "$EDITION" == "standard" ]]; then
        print_status "Validating Standard Edition architecture..."

        # Ensure data-processor is NOT deployed in standard edition
        local namespace="-n standard-edition"
        if kubectl get deployment data-processor $namespace &> /dev/null; then
            print_error "data-processor should NOT be deployed in Standard Edition (uses ClickHouse/Kafka)"
        else
            print_success "data-processor correctly excluded from Standard Edition"
        fi

        # Ensure ClickHouse is NOT deployed
        if kubectl get deployment clickhouse $namespace &> /dev/null; then
            print_error "ClickHouse should NOT be deployed in Standard Edition"
        else
            print_success "ClickHouse correctly excluded from Standard Edition"
        fi

        # Ensure Kafka is NOT deployed
        if kubectl get deployment kafka $namespace &> /dev/null; then
            print_error "Kafka should NOT be deployed in Standard Edition"
        else
            print_success "Kafka correctly excluded from Standard Edition"
        fi

        # Ensure Redis IS deployed (required for standard edition)
        if kubectl get deployment redis $namespace &> /dev/null; then
            print_success "Redis is deployed (required for Standard Edition)"

            # Check if Redis populator job completed
            if kubectl get job redis-populator-standard $namespace &> /dev/null; then
                local job_status
                job_status=$(kubectl get job redis-populator-standard $namespace -o jsonpath='{.status.conditions[?(@.type=="Complete")].status}' 2>/dev/null || echo "")
                if [[ "$job_status" == "True" ]]; then
                    print_success "Redis populator job completed successfully"
                else
                    print_warning "Redis populator job may not have completed"
                fi
            else
                print_warning "Redis populator job not found (may affect model profiles)"
            fi
        else
            print_error "Redis is missing (required for Standard Edition)"
        fi

    else
        print_status "Validating Enterprise Edition architecture..."

        # Ensure data-processor IS deployed in enterprise edition
        if kubectl get deployment data-processor &> /dev/null; then
            print_success "data-processor is deployed (required for Enterprise Edition)"
        else
            print_warning "data-processor is missing (required for Enterprise Edition analytics)"
        fi

        # Ensure ClickHouse IS deployed
        if kubectl get deployment clickhouse &> /dev/null; then
            print_success "ClickHouse is deployed (required for Enterprise Edition)"
        else
            print_warning "ClickHouse is missing (required for Enterprise Edition analytics)"
        fi

        # Ensure Kafka IS deployed
        if kubectl get deployment kafka &> /dev/null; then
            print_success "Kafka is deployed (required for Enterprise Edition)"
        else
            print_warning "Kafka is missing (required for Enterprise Edition messaging)"
        fi

        # Check enterprise-specific services
        local enterprise_services=("planning-service" "evaluation-service" "integration-service" "policy-manager")
        for service in "${enterprise_services[@]}"; do
            if kubectl get deployment "$service" &> /dev/null; then
                print_success "Enterprise service $service is deployed"
            else
                print_warning "Enterprise service $service is missing"
            fi
        done
    fi
}

# Function to validate frontend access
validate_frontend_access() {
    print_header "🌐 Validating Frontend Access"
    print_header "============================="
    
    local frontend_url
    if [[ "$EDITION" == "standard" ]]; then
        frontend_url="https://scale-llm.com/standard"
    else
        frontend_url="https://scale-llm.com"
    fi
    
    print_status "Checking frontend accessibility..."
    
    local response_code
    response_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time 30 "$frontend_url" || echo "000")
    
    if [[ "$response_code" == "200" ]]; then
        print_success "Frontend is accessible at $frontend_url"
    elif [[ "$response_code" == "000" ]]; then
        print_error "Frontend is not reachable at $frontend_url"
    else
        print_warning "Frontend returned HTTP $response_code at $frontend_url"
    fi
    
    # Check landing page
    print_status "Checking landing page..."
    local landing_response_code
    landing_response_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time 30 "https://scale-llm.com" || echo "000")
    
    if [[ "$landing_response_code" == "200" ]]; then
        print_success "Landing page is accessible at https://scale-llm.com"
    else
        print_warning "Landing page may not be accessible (HTTP $landing_response_code)"
    fi
}

# Function to show validation summary
show_validation_summary() {
    print_header "📋 Validation Summary"
    print_header "===================="
    
    echo ""
    print_status "Edition: $EDITION"
    
    if [[ "$EDITION" == "standard" ]]; then
        echo -e "${GREEN}Standard Edition Validation Complete${NC}"
        echo ""
        echo "✅ Core services validated"
        echo "✅ Health endpoints checked"
        echo "✅ AI optimizer scores verified"
        echo "✅ Frontend accessibility confirmed"
        echo "✅ Ingress configuration validated"
        echo "✅ Standard Edition architecture verified (no data-processor/ClickHouse/Kafka)"
        echo ""
        echo "🌐 Your Standard Edition is ready at:"
        echo "   https://scale-llm.com/standard"
    else
        echo -e "${PURPLE}Enterprise Edition Validation Complete${NC}"
        echo ""
        echo "✅ All services validated"
        echo "✅ Health endpoints checked"
        echo "✅ AI optimizer scores verified"
        echo "✅ Frontend accessibility confirmed"
        echo "✅ Ingress configuration validated"
        echo "✅ Enterprise architecture verified (includes data-processor/ClickHouse/Kafka)"
        echo "✅ Enterprise features available"
        echo ""
        echo "🌐 Your Enterprise Edition is ready at:"
        echo "   https://scale-llm.com"
    fi
    
    echo ""
    print_success "🎉 Your AI Operations Hub deployment is validated and ready to use!"
}

# Main validation function
main() {
    print_header "🔍 AI Operations Hub - Deployment Validation"
    print_header "============================================="
    print_status "Validating $EDITION edition deployment..."
    print_status "Timeout: ${TIMEOUT}s"
    echo ""
    
    # Check prerequisites
    if ! check_kubectl || ! check_curl; then
        print_error "Prerequisites not met. Please install required tools."
        exit 1
    fi
    
    # Run validations
    validate_k8s_resources
    echo ""
    
    validate_health_endpoints
    echo ""
    
    validate_ai_optimizer_scores
    echo ""
    
    validate_frontend_access
    echo ""

    validate_edition_architecture
    echo ""

    show_validation_summary
}

# Execute main function
main "$@"
