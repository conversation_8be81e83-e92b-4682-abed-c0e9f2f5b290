#!/bin/bash

echo "🔍 Verifying AI Operations Hub Enhanced Deployment Files..."
echo "============================================================"

# Check if we're in the right directory
if [ ! -f "cloudbuild.yaml" ]; then
    echo "❌ Error: cloudbuild.yaml not found. Please run this script from the project root."
    exit 1
fi

echo "✅ Found cloudbuild.yaml"

# Check main deployment scripts
echo ""
echo "📋 Checking main deployment scripts..."
if [ -f "k8s/New_Autostart.sh" ]; then
    echo "✅ Found k8s/New_Autostart.sh"
else
    echo "❌ Missing k8s/New_Autostart.sh"
fi

# Check new services
echo ""
echo "🆕 Checking new enhanced services..."

# Sentiment Service
if [ -d "k8s/sentiment-service" ]; then
    echo "✅ Found sentiment-service directory"
    if [ -f "k8s/sentiment-service/main.go" ]; then
        echo "  ✅ Found main.go"
    else
        echo "  ❌ Missing main.go"
    fi
    if [ -f "k8s/sentiment-service/sentiment-service.yaml" ]; then
        echo "  ✅ Found sentiment-service.yaml"
    else
        echo "  ❌ Missing sentiment-service.yaml"
    fi
    if [ -f "k8s/sentiment-service/Dockerfile" ]; then
        echo "  ✅ Found Dockerfile"
    else
        echo "  ❌ Missing Dockerfile"
    fi
    if [ -f "k8s/sentiment-service/go.mod" ]; then
        echo "  ✅ Found go.mod"
    else
        echo "  ❌ Missing go.mod"
    fi
else
    echo "❌ Missing sentiment-service directory"
fi

# Social Integration Service
if [ -d "k8s/social-integration-service" ]; then
    echo "✅ Found social-integration-service directory"
    if [ -f "k8s/social-integration-service/main.go" ]; then
        echo "  ✅ Found main.go"
    else
        echo "  ❌ Missing main.go"
    fi
    if [ -f "k8s/social-integration-service/social-integration-service.yaml" ]; then
        echo "  ✅ Found social-integration-service.yaml"
    else
        echo "  ❌ Missing social-integration-service.yaml"
    fi
    if [ -f "k8s/social-integration-service/Dockerfile" ]; then
        echo "  ✅ Found Dockerfile"
    else
        echo "  ❌ Missing Dockerfile"
    fi
    if [ -f "k8s/social-integration-service/go.mod" ]; then
        echo "  ✅ Found go.mod"
    else
        echo "  ❌ Missing go.mod"
    fi
else
    echo "❌ Missing social-integration-service directory"
fi

# Check frontend components
echo ""
echo "🎨 Checking enhanced frontend components..."

frontend_components=(
    "SentimentDashboard.jsx"
    "SentimentOverview.jsx"
    "NoCodeWorkflowBuilder.jsx"
    "WorkflowNode.jsx"
    "NodePropertiesPanel.jsx"
    "SocialIntegrationDashboard.jsx"
    "SimplifiedGovernanceDashboard.jsx"
    "GovernanceOverview.jsx"
    "ProductMarketFitDashboard.jsx"
)

for component in "${frontend_components[@]}"; do
    if [ -f "k8s/frontend/src/components/$component" ]; then
        echo "  ✅ Found $component"
    else
        echo "  ❌ Missing $component"
    fi
done

# Check cloudbuild.yaml for new services
echo ""
echo "🏗️ Checking cloudbuild.yaml configuration..."

if grep -q "_BUILD_SENTIMENT_SERVICE" cloudbuild.yaml; then
    echo "✅ Found sentiment service build flag"
else
    echo "❌ Missing sentiment service build flag"
fi

if grep -q "_BUILD_SOCIAL_INTEGRATION_SERVICE" cloudbuild.yaml; then
    echo "✅ Found social integration service build flag"
else
    echo "❌ Missing social integration service build flag"
fi

if grep -q "sentiment-service:latest" cloudbuild.yaml; then
    echo "✅ Found sentiment service build steps"
else
    echo "❌ Missing sentiment service build steps"
fi

if grep -q "social-integration-service:latest" cloudbuild.yaml; then
    echo "✅ Found social integration service build steps"
else
    echo "❌ Missing social integration service build steps"
fi

# Check New_Autostart.sh for new services
echo ""
echo "🚀 Checking New_Autostart.sh deployment configuration..."

if grep -q "sentiment-service" k8s/New_Autostart.sh; then
    echo "✅ Found sentiment service deployment"
else
    echo "❌ Missing sentiment service deployment"
fi

if grep -q "social-integration-service" k8s/New_Autostart.sh; then
    echo "✅ Found social integration service deployment"
else
    echo "❌ Missing social integration service deployment"
fi

if grep -q "SENTIMENT_SERVICE_IMAGE" k8s/New_Autostart.sh; then
    echo "✅ Found sentiment service image replacement"
else
    echo "❌ Missing sentiment service image replacement"
fi

if grep -q "SOCIAL_INTEGRATION_SERVICE_IMAGE" k8s/New_Autostart.sh; then
    echo "✅ Found social integration service image replacement"
else
    echo "❌ Missing social integration service image replacement"
fi

echo ""
echo "🎯 Summary of AI Operations Hub Enhancements:"
echo "=============================================="
echo "✨ Sentiment-Driven Intelligence Enhancement"
echo "🚀 No-Code Workflow Acceleration" 
echo "🌐 Social Media & Integration Platform"
echo "🛡️ Simplified Governance & Compliance"
echo "📊 Product-Market Fit Analytics"
echo ""
echo "🔧 All deployment scripts have been updated to include the new services."
echo "🎨 Frontend has been enhanced with new dashboards and components."
echo "☁️ Cloud Build configuration supports building and deploying all new services."
echo ""
echo "✅ Verification complete! Your AI Operations Hub is ready for enhanced deployment."
echo ""
echo "🚀 To deploy, run: gcloud builds submit --config cloudbuild.yaml"
echo "📖 Or use your existing GCP Cloud Build pipeline."
