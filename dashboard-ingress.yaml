# dashboard-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: frontend-dashboard-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod" # MUST match the ClusterIssuer name
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  ingressClassName: nginx # Recommended: Use this field instead of the deprecated annotation
  tls:
  - hosts:
    - scale-llm.com # Pointing to the root domain
    secretName: dashboard-tls-secret # cert-manager will store the certificate here
  rules:
  - host: scale-llm.com # Pointing to the root domain
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-dashboard # Name of your frontend dashboard Kubernetes Service
            port:
              number: 80 # The port your frontend-dashboard Kubernetes Service listens on
