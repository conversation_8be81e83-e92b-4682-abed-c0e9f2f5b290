#!/bin/bash

# Add a basic routing strategy to Redis
kubectl exec -i redis-6559cfdc9f-6f89z -- redis-cli << 'EOF'
SET routing_strategy:default-fallback '{"id":"default-fallback","name":"Default Fallback Strategy","strategy":"default","task_type":"","policy_id":"","model_priorities":["gemini-2.5-flash-preview-05-20","gpt-3.5-turbo"],"parallel_models":[],"comparison_method":"","enable_fallback":false,"fallback_model_id":"","priority":100,"model_requirements":{"minimum_tier":1,"required_capabilities":[],"preferred_provider":""}}'
SET routing_strategy:factual-query-default '{"id":"factual-query-default","name":"Factual Query Default Strategy","strategy":"default","task_type":"factual_query","policy_id":"","model_priorities":["gpt-3.5-turbo","gemini-2.5-flash-preview-05-20"],"parallel_models":[],"comparison_method":"","enable_fallback":false,"fallback_model_id":"","priority":100,"model_requirements":{"minimum_tier":1,"required_capabilities":[],"preferred_provider":""}}'
SET routing_strategy:simple-chat-default '{"id":"simple-chat-default","name":"Simple Chat Default Strategy","strategy":"default","task_type":"simple_chat","policy_id":"","model_priorities":["gemini-2.5-flash-preview-05-20","gpt-3.5-turbo"],"parallel_models":[],"comparison_method":"","enable_fallback":false,"fallback_model_id":"","priority":100,"model_requirements":{"minimum_tier":1,"required_capabilities":[],"preferred_provider":""}}'
KEYS routing_strategy:*
EOF

echo "Routing strategies added to Redis. Restarting AI Optimizer to reload..."

# Restart the AI Optimizer to reload routing strategies
kubectl rollout restart deployment/ai-optimizer -n default
kubectl rollout status deployment/ai-optimizer -n default
