---
type: "manual"
---

# AI Operations Hub: Developer Guide

This guide provides developers with detailed instructions for integrating applications with the AI Operations Hub, featuring comprehensive intelligent routing, autonomous planning, multi-agent orchestration, and responsible AI governance capabilities.

## Integration Overview

The AI Operations Hub provides four main integration approaches:

1. **Direct LLM Integration**: Send individual LLM requests through our intelligent Proxy Gateway for cost optimization and semantic routing
2. **Autonomous Planning Integration**: Submit high-level goals to our Planning Engine for autonomous multi-step task execution
3. **Multi-Agent Orchestration**: Leverage our comprehensive multi-agent platform for complex collaborative AI workflows
4. **Model Context Protocol (MCP) Integration**: Connect Claude Desktop, IDEs, and custom applications via MCP for seamless tool integration

All approaches leverage the same underlying infrastructure for intelligent routing, cost optimization, policy enforcement, performance monitoring, responsible AI governance, and security.

## Platform Architecture

The AI Operations Hub is built as a comprehensive enterprise-grade microservices architecture with 15+ specialized services:

### Core Services
- **Proxy Gateway (Port 8080)**: Intelligent LLM request routing, semantic analysis, and optimization with MCP host integration
- **AI Optimizer (Port 8085)**: Capability-based model selection and intelligent routing with MCP server
- **Planning Service (Port 8082)**: Autonomous goal decomposition and task execution with MCP server
- **Multi-Agent Orchestrator (Port 8083)**: Agent coordination, workflow management, and marketplace integration

### Responsible AI & Governance Services
- **Bias Detection Service (Port 8084)**: Automated bias detection and mitigation using AIF360/Fairlearn
- **Explainability Service (Port 8085)**: LIME/SHAP explanations for AI decisions and model interpretability
- **Robustness Testing Service (Port 8086)**: Adversarial attack simulations and vulnerability assessment
- **Compliance Service (Port 8087)**: Regulatory compliance tracking and reporting (EU AI Act, NIST AI RMF)
- **Governance Service (Port 8080)**: Enhanced model factsheets, policy enforcement, and audit trails

### Supporting Services
- **Evaluation Service (Port 8088)**: Real-time LLM response quality assessment with MCP server
- **Integration Service (Port 8091)**: GitOps integration, external connectors, and MCP server
- **Data Processor (Port 8092)**: Advanced data transformation, analytics pipeline, and real-time processing
- **Dashboard API (Port 8089)**: Analytics aggregation, metrics processing, and report generation
- **Policy Manager (Port 8090)**: Policy configuration, rule management, and enforcement logic

### Frontend & User Interface
- **Frontend Dashboard (Port 3000)**: React-based UI with chat interface, planning dashboard, multi-agent interface, responsible AI dashboard, PromptOps management, and MCP dashboard

## Getting Started

### Core Endpoints

The system provides multiple integration points depending on your use case:

**Direct LLM API Endpoints (Full OpenAI Compatibility):**
```
# Chat & Completions
POST https://scale-llm.com/v1/chat/completions     # Chat completions (primary)
POST https://scale-llm.com/v1/completions          # Text completions (legacy)

# Embeddings & Vector Operations
POST https://scale-llm.com/v1/embeddings           # Text embeddings generation

# Image Generation
POST https://scale-llm.com/v1/images/generations    # AI image generation

# Audio Processing
POST https://scale-llm.com/v1/audio/speech          # Text-to-speech synthesis
POST https://scale-llm.com/v1/audio/transcriptions  # Speech-to-text transcription
POST https://scale-llm.com/v1/audio/translations    # Audio translation

# Content Moderation
POST https://scale-llm.com/v1/moderations          # Content safety checking

# Fine-tuning
POST https://scale-llm.com/v1/fine-tuning/jobs     # Model fine-tuning jobs

# Analytics & Management
GET  https://scale-llm.com/v1/analytics/cache      # Cache performance analytics
POST https://scale-llm.com/v1/cache/manage         # Cache management operations
```

**Autonomous Planning API Endpoints:**
```
POST https://scale-llm.com/v1/goals              # Create goal
POST https://scale-llm.com/v1/goals/{id}/plan    # Generate plan
POST https://scale-llm.com/v1/goals/{id}/execute # Execute plan
GET  https://scale-llm.com/v1/goals/{id}/status  # Monitor progress
GET  https://scale-llm.com/v1/goals/{id}/tasks   # Get task details
GET  https://scale-llm.com/v1/goals/{id}/results # Get execution results
```

**Multi-Agent Orchestration API Endpoints:**
```
# Agent Management
GET    /v1/agents                                # List agents
POST   /v1/agents                                # Register agent
GET    /v1/agents/{id}                           # Get agent details
PUT    /v1/agents/{id}                           # Update agent
DELETE /v1/agents/{id}                           # Unregister agent
POST   /v1/agents/{id}/health                    # Health check
GET    /v1/agents/{id}/insights                  # Performance insights
POST   /v1/agents/select                         # Optimal agent selection

# Workflow Management
GET    /v1/workflows                             # List workflows
POST   /v1/workflows                             # Create workflow
GET    /v1/workflows/{id}                        # Get workflow details
PUT    /v1/workflows/{id}                        # Update workflow
DELETE /v1/workflows/{id}                        # Delete workflow
POST   /v1/workflows/{id}/execute                # Execute workflow
POST   /v1/workflows/{id}/pause                  # Pause workflow
POST   /v1/workflows/{id}/resume                 # Resume workflow
POST   /v1/workflows/{id}/cancel                 # Cancel workflow
GET    /v1/workflows/{id}/status                 # Get execution status

# Analytics
GET    /v1/analytics/agents                      # Agent analytics
GET    /v1/analytics/workflows                   # Workflow analytics
GET    /v1/analytics/collaboration               # Collaboration metrics

# Evaluation & Quality Assurance
POST   /v1/evaluations                           # Create evaluation
GET    /v1/evaluations/{id}                      # Get evaluation results
GET    /v1/evaluations/metrics                   # Quality metrics

# Responsible AI & Governance
GET    /v1/governance/policies                   # List governance policies
POST   /v1/governance/audit                      # Create audit log
GET    /v1/governance/compliance                 # Compliance status
GET    /factsheets                               # List model factsheets
POST   /factsheets                               # Create model factsheet
GET    /factsheets/{id}                          # Get factsheet details

# Bias Detection & Fairness
POST   /bias-audits                              # Create bias audit
GET    /bias-metrics                             # Get bias metrics
GET    /bias-metrics/{id}                        # Get specific bias metrics
GET    /bias-metrics/model/{modelId}             # Get bias metrics by model

# Explainable AI (XAI)
POST   /explanations                             # Generate explanation
GET    /explanations                             # List explanations
GET    /explanations/{id}                        # Get specific explanation

# Robustness Testing
POST   /robustness-tests                         # Create robustness test
GET    /robustness-tests                         # List robustness tests
GET    /robustness-tests/{id}                    # Get test results

# Compliance Reporting
GET    /compliance/frameworks                    # List compliance frameworks
GET    /compliance/assessments                   # Get compliance assessments
POST   /compliance/reports                       # Generate compliance report

# Integration Management (GitOps & PromptOps)
GET    /gitops/status                            # GitOps status
POST   /gitops/deploy                            # Manual deployment trigger
POST   /gitops/sync                              # Sync with Git repository
POST   /promptops/export                         # Export prompts to Git
POST   /promptops/import                         # Import prompts from Git
POST   /promptops/validate                       # Validate prompt configurations

# Model Context Protocol (MCP) Endpoints
# Proxy Gateway MCP Host (Primary)
WS     https://scale-llm.com/mcp/connect           # MCP WebSocket connection (primary host)
GET    https://scale-llm.com/mcp/status            # MCP connection status

# Individual Service MCP Servers
WS     /mcp                                        # MCP WebSocket connection (available on multiple services)
```

**Dashboard UI:**
```
https://scale-llm.com/                              # Main dashboard with chat interface
https://scale-llm.com/#planning                     # Planning tab (direct access)
https://scale-llm.com/#multi-agent                  # Multi-Agent tab (direct access)
https://scale-llm.com/#responsible-ai               # Responsible AI tab (direct access)
https://scale-llm.com/#promptops                    # PromptOps tab (direct access)
https://scale-llm.com/#mcp                          # MCP tab (direct access)
```

**MCP Server Endpoints:**
```
# Proxy Gateway MCP Host (Recommended)
wss://scale-llm.com/mcp/connect                     # Primary MCP host with full capabilities
ws://localhost:8080/mcp/connect                     # Local development

# Individual Service MCP Servers
# AI Optimizer MCP Server
ws://localhost:8085/mcp or https://scale-llm.com/ai-optimizer/mcp

# Planning Service MCP Server
ws://localhost:8082/mcp or https://scale-llm.com/planning-service/mcp

# Evaluation Service MCP Server
ws://localhost:8088/mcp or https://scale-llm.com/evaluation-service/mcp

# Integration Service MCP Server
ws://localhost:8091/mcp or https://scale-llm.com/integration-service/mcp
```

## Integration Approaches

### Approach 1: Chat Interface Integration

For users who prefer a familiar chat experience with intelligent optimization:

**Features Available:**
- **Intelligent Routing**: Automatic model selection based on prompt analysis
- **Real-time Streaming**: Live response streaming with cost optimization
- **Conversation Management**: Multi-turn conversation tracking and context preservation
- **Cost Optimization**: Transparent cost savings and performance insights
- **Governance Integration**: Built-in responsible AI monitoring and compliance

**Getting Started:**
1. Navigate to the main dashboard at `https://scale-llm.com/`
2. Use the chat interface similar to ChatGPT
3. Your messages are automatically analyzed and routed to optimal models
4. Monitor cost savings and performance in real-time
5. Access conversation history and analytics

### Approach 2: Multi-Agent Dashboard UI Integration

For users who prefer comprehensive multi-agent orchestration:

**Features Available:**
- **Agent Registry**: Manage and monitor AI agents with health checks and performance insights
- **Agent Marketplace**: Discover and integrate external agents from multiple providers
- **Workflow Designer**: Create complex multi-agent workflows with visual design tools
- **Real-time Visualization**: Interactive workflow graphs with live execution monitoring
- **Template Library**: Access pre-built workflow templates for common use cases
- **Analytics Dashboard**: Comprehensive performance and collaboration analytics

**Getting Started:**
1. Navigate to the main dashboard at `https://scale-llm.com/`
2. Click on the "Multi-Agent" tab in the navigation
3. Register your first agent or browse the marketplace
4. Create workflows using templates or custom designs
5. Monitor execution with real-time visualization

### Approach 3: Planning Dashboard UI Integration

For autonomous goal-based workflows, access the Planning Dashboard:

**Features Available:**
- **Goal Creation**: User-friendly form with guided input for success criteria and constraints
- **Real-time Monitoring**: Live progress tracking with automatic status updates
- **Task Visualization**: Interactive dependency graphs showing execution flow
- **Cost Optimization**: AI-powered insights and recommendations for cost efficiency
- **Progress Analytics**: Detailed metrics on goal success rates and performance
- **Adaptive Re-planning**: Dynamic plan adjustment based on execution results

**Getting Started:**
1. Navigate to the main dashboard at `https://scale-llm.com/`
2. Click on the "Planning" tab in the navigation
3. Click "Create Goal" to define your first objective
4. Fill in the goal description, success criteria, and constraints
5. Generate a plan and execute it with real-time monitoring

### Approach 4: Direct LLM Integration

For traditional LLM usage, send requests to the Proxy Gateway using the standard Chat Completion API format:

```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {"role": "user", "content": "Your prompt here"}
  ],
  "temperature": 0.7,
  "max_tokens": 150,
  "conversation_id": "unique-conversation-id",
  "stream": true
}
```

**Enhanced Features:**
- **Semantic Analysis**: Automatic prompt analysis for optimal routing
- **Conversation Tracking**: Multi-turn conversation context management
- **Streaming Support**: Real-time response streaming
- **Governance Integration**: Automatic policy enforcement and compliance checking

### Approach 5: Multi-Agent API Integration

For programmatic multi-agent orchestration, use the comprehensive API:

```json
{
  "name": "Customer Analysis Workflow",
  "description": "Analyze customer feedback and generate insights",
  "agents": [
    {
      "role": "data_collector",
      "agent_id": "agent-123",
      "capabilities": ["data_collection", "web_scraping"],
      "trust_metrics": {
        "fairness_score": 0.95,
        "explainability_enabled": true,
        "robustness_score": 0.88
      }
    },
    {
      "role": "analyst",
      "agent_id": "agent-456",
      "capabilities": ["statistical_analysis", "sentiment_analysis"],
      "trust_metrics": {
        "fairness_score": 0.92,
        "explainability_enabled": true,
        "robustness_score": 0.91
      }
    }
  ],
  "tasks": [
    {
      "name": "Collect Feedback",
      "type": "data_collection",
      "assigned_role": "data_collector",
      "parameters": {
        "source": "customer_feedback_db",
        "time_range": "last_30_days"
      }
    },
    {
      "name": "Analyze Sentiment",
      "type": "analysis",
      "assigned_role": "analyst",
      "dependencies": ["Collect Feedback"],
      "governance_requirements": {
        "bias_detection": true,
        "explainability": true
      }
    }
  ]
}
```

### Approach 6: Autonomous Goal-Based Integration

For complex, multi-step workflows, submit high-level goals to the Planning Engine:

```json
{
  "description": "Analyze customer feedback from the last month and generate actionable insights",
  "trust_criteria": {
    "min_fairness_score": 0.8,
    "require_lime_explainability": true,
    "robustness_threshold": 0.85
  },
  "success_criteria": [
    {
      "description": "Process at least 100 feedback entries",
      "metric": "feedback_count",
      "target": 100,
      "operator": ">=",
      "weight": 0.8,
      "required": true
    },
    {
      "description": "Achieve high sentiment analysis accuracy",
      "metric": "accuracy_score",
      "target": 0.9,
      "operator": ">=",
      "weight": 0.2
    }
  ],
  "constraints": [
    {
      "type": "time",
      "description": "Complete within 2 hours",
      "limit": "2h",
      "operator": "<=",
      "severity": "hard"
    },
    {
      "type": "cost",
      "description": "Stay within budget",
      "limit": 50.0,
      "operator": "<=",
      "severity": "soft"
    }
  ],
  "governance_requirements": {
    "bias_detection": true,
    "explainability": true,
    "compliance_check": true
  },
  "priority": 8
}
```

### Approach 7: Model Context Protocol (MCP) Integration

For seamless tool integration with Claude Desktop and other MCP clients:

**Available MCP Servers:**
1. **AI Optimizer MCP Server**: Intelligent routing and cost optimization tools
2. **Planning Service MCP Server**: Goal decomposition and autonomous execution
3. **Evaluation Service MCP Server**: Quality assessment and performance evaluation
4. **Integration Service MCP Server**: GitOps and PromptOps workflow management

**Example MCP Client Configuration:**
```json
{
  "mcpServers": {
    "ai-operations-hub": {
      "command": "npx",
      "args": ["-y", "@ai-operations-hub/mcp-client"],
      "env": {
        "AI_OPS_HUB_URL": "https://scale-llm.com"
      }
    }
  }
}
```

## Key Features

### Multi-Agent Orchestration

The platform provides comprehensive multi-agent collaboration capabilities:

#### Agent Registry and Management
- **Agent Registration**: Register specialized AI agents with capabilities, performance metrics, and metadata
- **Health Monitoring**: Continuous health checks with automatic status updates
- **Performance Tracking**: Real-time metrics on agent utilization, success rates, and response times
- **Lifecycle Management**: Complete agent lifecycle from registration to decommissioning

#### Intelligent Agent Selection
- **Trust-Aware Agent Selection**: ML-driven selection now incorporates trust metrics (fairness, explainability, robustness) into the decision-making process.
- **Workload Balancing**: Automatic load distribution across available agents
- **Cost Optimization**: Budget-aware agent selection with cost constraints
- **Confidence Scoring**: Quality assessment is now based on a holistic view of performance, quality, and trust.

#### Workflow Orchestration
- **Visual Workflow Designer**: Drag-and-drop interface for creating complex multi-agent workflows
- **Template Library**: Pre-built workflow templates for common use cases
- **Dependency Management**: Automatic handling of task dependencies and execution order
- **Real-time Monitoring**: Live visualization of workflow execution with status updates

#### Agent Marketplace
- **Multi-provider Support**: Integration with HuggingFace, OpenAI, Anthropic, and custom providers
- **Agent Evaluation**: Automated benchmarking and security scanning
- **Community Ratings**: User reviews and popularity metrics
- **One-click Integration**: Seamless agent integration with configuration management

#### Advanced Security
- **Role-based Access Control**: Granular permissions for agents, workflows, and resources
- **Authentication**: JWT-based session management with secure token handling
- **Audit Logging**: Comprehensive security event tracking and compliance reporting
- **Encrypted Communication**: Secure channels for inter-agent communication
- **Governance Framework**: Policy enforcement and compliance monitoring
- **Security Scanning**: Automated security assessment for agents and workflows

### Intelligent LLM Routing

Based on configured policies, the proxy automatically selects the most appropriate backend LLM for each request. Routing decisions can be based on:
- Prompt content and complexity
- User roles and permissions
- Token length and cost constraints
- Performance/latency requirements

### Autonomous Task Decomposition

The Planning Engine automatically breaks down complex goals into executable tasks:
- **Natural Language Processing**: Parse goals from plain English descriptions
- **Intelligent Planning**: Use LLM-assisted decomposition with task templates
- **Dependency Management**: Handle complex task relationships and execution order
- **Resource Optimization**: Estimate and manage computational resources

### Cost & Performance Optimization

Unified optimization across both individual requests and multi-step workflows:
- `OPTIMIZE_LATENCY`: Prioritize response speed
- `OPTIMIZE_COST`: Prioritize lower cost
- `OPTIMIZE_BALANCED`: Balance performance and cost
- **Workflow-level optimization**: Optimize entire goal execution for cost and time
- **Real-time Evaluation**: Continuous quality assessment and performance monitoring
- **Predictive Analytics**: AI-powered cost and performance predictions

### Execution Orchestration

For goal-based workflows, the system provides:
- **Parallel Execution**: Run independent tasks concurrently
- **Failure Handling**: Automatic retry with exponential backoff
- **Progress Tracking**: Real-time monitoring of task completion
- **State Management**: Persistent context across multi-step executions

### Conversational Context Management

For multi-turn conversations, the proxy maintains context automatically:
1. For the first turn, omit `conversation_id` - the proxy will generate one
2. Include this `conversation_id` in subsequent requests
3. The proxy automatically retrieves prior messages before sending to the chosen LLM

### Policy Application

Benefit from centrally defined policies without implementing complex routing logic in your application. Policies can be configured through the admin dashboard and apply to both direct LLM calls and autonomous workflows.

## API Usage Examples

### Direct LLM API Examples

#### Basic Chat Completion

The proxy will automatically select the best LLM based on configured policies:

```bash
curl -X POST \
  https://scale-llm.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Tell me a short, imaginative story about a space squirrel."}
    ],
    "temperature": 0.7,
    "max_tokens": 150
  }'
```

### Conversational Turn 1 (New Conversation)

For the first turn, omit `conversation_id`:

```bash
curl -X POST \
  https://scale-llm.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-opus-********",
    "messages": [
      {"role": "user", "content": "Hi, who are you and what do you do?"}
    ]
  }'
```

The response will contain a `conversation_id` in the payload.

### Conversational Turn 2 (Continuing the Conversation)

Use the `conversation_id` from the first turn:

```bash
curl -X POST \
  https://scale-llm.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "<YOUR_CONVERSATION_ID_FROM_PREVIOUS_RESPONSE>",
    "model": "claude-3-opus-********",
    "messages": [
      {"role": "user", "content": "Can you elaborate on the second part of your previous answer?"}
    ]
  }'
```

### Manual Model Override

Override automatic routing by specifying a preferred LLM ID:

```bash
curl -X POST \
  https://scale-llm.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-Preferred-LLM-ID: gpt-4-turbo-us-east" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Explain quantum entanglement in simple terms."}
    ]
  }'
```

**Note:** The `model` field in the request body still acts as a hint or fallback if no policy matches or if the preferred LLM is unavailable.

### Multi-Agent Orchestration API Examples

#### Agent Registration and Management

**Register a New Agent:**
```bash
curl -X POST \
  https://scale-llm.com/v1/agents \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Data Analysis Expert",
    "type": "data_analyst",
    "description": "Specialized agent for statistical analysis and data visualization",
    "endpoint": "https://my-agent.com/api",
    "capabilities": [
      {
        "id": "statistical_analysis",
        "name": "Statistical Analysis",
        "description": "Advanced statistical analysis capabilities",
        "quality": 0.95,
        "cost": 0.10,
        "speed": 0.85
      },
      {
        "id": "data_visualization",
        "name": "Data Visualization",
        "description": "Create charts and graphs",
        "quality": 0.90,
        "cost": 0.08,
        "speed": 0.90
      }
    ],
    "metadata": {
      "version": "1.0",
      "provider": "custom",
      "max_concurrent_tasks": 5
    }
  }'
```

**Create Multi-Agent Workflow:**
```bash
curl -X POST \
  https://scale-llm.com/v1/workflows \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Customer Feedback Analysis Pipeline",
    "description": "Comprehensive analysis of customer feedback with insights generation",
    "tasks": [
      {
        "name": "Collect Feedback Data",
        "type": "data_collection",
        "description": "Gather customer feedback from multiple sources",
        "agent_requirements": {
          "type": "data_analyst",
          "capabilities": ["data_collection"]
        },
        "parameters": {
          "sources": ["support_tickets", "surveys", "reviews"],
          "time_range": "last_30_days"
        },
        "priority": 1
      },
      {
        "name": "Sentiment Analysis",
        "type": "analysis",
        "description": "Analyze sentiment of collected feedback",
        "agent_requirements": {
          "type": "data_analyst",
          "capabilities": ["sentiment_analysis"]
        },
        "dependencies": ["Collect Feedback Data"],
        "priority": 2
      },
      {
        "name": "Generate Insights Report",
        "type": "report_generation",
        "description": "Create comprehensive insights report",
        "agent_requirements": {
          "type": "content_writer",
          "capabilities": ["report_generation"]
        },
        "dependencies": ["Sentiment Analysis"],
        "priority": 3
      }
    ],
    "execution_config": {
      "max_concurrency": 3,
      "timeout": "1h",
      "retry_policy": {
        "max_retries": 3,
        "backoff_strategy": "exponential"
      }
    }
  }'
```

**Execute Workflow:**
```bash
curl -X POST \
  https://scale-llm.com/v1/workflows/{workflow_id}/execute \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Monitor Workflow Status:**
```bash
curl https://scale-llm.com/v1/workflows/{workflow_id}/status \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Agent Selection and Optimization

**Get Optimal Agent for Task:**
```bash
curl -X POST \
  https://scale-llm.com/v1/agents/select \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "task_requirements": {
      "type": "data_analysis",
      "capabilities": ["statistical_analysis", "data_visualization"],
      "complexity": "high",
      "estimated_tokens": 5000
    },
    "constraints": {
      "max_cost": 2.00,
      "max_latency": 30000,
      "quality_threshold": 0.85
    },
    "optimization_strategy": "balanced"
  }'
```

### Autonomous Planning API Examples

#### Create and Execute a Goal

**Step 1: Create a Goal**
```bash
curl -X POST \
  https://scale-llm.com/v1/goals \
  -H "Content-Type: application/json" \
  -H "X-User-ID: your-user-id" \
  -d '{
    "description": "Analyze customer support tickets from the last week and generate a comprehensive report with recommendations",
    "success_criteria": [
      {
        "description": "Process at least 100 tickets",
        "metric": "ticket_count",
        "target": 100,
        "operator": ">=",
        "weight": 0.8,
        "required": true
      }
    ],
    "constraints": [
      {
        "type": "time",
        "description": "Complete within 2 hours",
        "limit": "2h",
        "operator": "<=",
        "severity": "hard"
      }
    ],
    "priority": 8
  }'
```

**Step 2: Generate Execution Plan**
```bash
# Use the goal_id from step 1
curl -X POST \
  https://scale-llm.com/v1/goals/{goal_id}/plan \
  -H "X-User-ID: your-user-id"
```

**Step 3: Execute the Plan**
```bash
curl -X POST \
  https://scale-llm.com/v1/goals/{goal_id}/execute \
  -H "Content-Type: application/json" \
  -H "X-User-ID: your-user-id" \
  -d '{
    "max_concurrency": 3,
    "timeout": "30m"
  }'
```

**Step 4: Monitor Progress**
```bash
# Check execution status
curl https://scale-llm.com/v1/goals/{goal_id}/status \
  -H "X-User-ID: your-user-id"

# Get detailed task progress
curl https://scale-llm.com/v1/goals/{goal_id}/tasks \
  -H "X-User-ID: your-user-id"

# Get final results
curl https://scale-llm.com/v1/goals/{goal_id}/results \
  -H "X-User-ID: your-user-id"
```

#### Common Goal Types

**Data Analysis Workflow:**
```json
{
  "description": "Perform comprehensive sales analysis for Q4 and predict Q1 performance",
  "success_criteria": [
    {
      "description": "Analyze at least 6 months of data",
      "metric": "data_coverage_months",
      "target": 6,
      "operator": ">=",
      "weight": 0.9,
      "required": true
    }
  ],
  "context": {
    "data_sources": ["sales_db", "customer_db"],
    "analysis_period": "Q4_2023",
    "prediction_horizon": "Q1_2024"
  }
}
```

**Content Creation Workflow:**
```json
{
  "description": "Create a complete marketing campaign for our new product launch",
  "success_criteria": [
    {
      "description": "Generate 5 blog posts",
      "metric": "blog_posts_count",
      "target": 5,
      "operator": ">=",
      "weight": 0.8,
      "required": true
    },
    {
      "description": "Create 20 social media posts",
      "metric": "social_posts_count",
      "target": 20,
      "operator": ">=",
      "weight": 0.7,
      "required": true
    }
  ],
  "context": {
    "product_name": "AI Assistant Pro",
    "target_audience": "business professionals",
    "launch_date": "2024-03-15"
  }
}
```

## Planning Dashboard User Interface

The AI Cost-Performance Optimizer now includes a comprehensive Planning Dashboard that provides a user-friendly interface for creating, monitoring, and optimizing autonomous planning workflows.

### Dashboard Features

#### Goal Creation Interface
- **Intuitive Form**: User-friendly goal creation with guided input fields
- **Success Criteria**: Define measurable success metrics with targets and weights
- **Constraints**: Set time, cost, quality, and resource constraints with severity levels
- **Priority Management**: Set goal priorities from 1-10 with visual indicators
- **Validation**: Real-time form validation with helpful error messages

#### Real-time Monitoring
- **Live Progress Tracking**: Automatic status updates every 5 seconds for active goals
- **Visual Indicators**: Color-coded status badges and progress bars
- **Notification System**: Automatic alerts for status changes and task completions
- **Monitoring Controls**: Start/stop monitoring with visual feedback

#### Task Dependency Visualization
- **Interactive Graphs**: SVG-based dependency visualization with zoom and scroll
- **Topological Layout**: Automatic arrangement showing execution flow
- **Status Indicators**: Color-coded tasks showing current execution state
- **Responsive Design**: Adapts to different screen sizes and task counts

#### Cost Optimization Insights
- **Cost Analysis**: Real-time cost tracking with budget vs. actual comparisons
- **Efficiency Metrics**: Cost efficiency percentages and trend analysis
- **AI Recommendations**: Intelligent optimization suggestions based on execution patterns
- **Priority Breakdown**: Cost analysis by goal priority levels
- **Quick Actions**: One-click optimization tools and analysis shortcuts

### Accessing the Dashboard

1. **Web Interface**: Navigate to `https://scale-llm.com/` and click the "Planning" tab
2. **Direct Access**: Use `https://scale-llm.com/#planning` for direct planning dashboard access
3. **Mobile Support**: Responsive design works on tablets and mobile devices

### Dashboard Workflow

1. **Create Goals**: Click "Create Goal" and fill in the comprehensive form
2. **Generate Plans**: Click "Generate Plan" to decompose goals into executable tasks
3. **Monitor Execution**: Use real-time monitoring to track progress and status
4. **Analyze Performance**: Review cost insights and optimization recommendations
5. **Optimize**: Apply suggested optimizations for better cost efficiency

### Technical Implementation

The Planning Dashboard is built with modern web technologies and integrates seamlessly with the existing AI Optimizer infrastructure:

#### Frontend Architecture
- **Framework**: React 18 with modern hooks and functional components
- **Styling**: Tailwind CSS for consistent, responsive design
- **Icons**: Lucide React for modern, scalable iconography
- **State Management**: Custom hooks with local state management
- **Real-time Updates**: Polling-based monitoring system (5-second intervals)

#### Key Components
- **PlanningDashboard.jsx**: Main dashboard interface with goal management
- **GoalDetailView.jsx**: Detailed goal monitoring with real-time updates
- **TaskDependencyGraph.jsx**: Interactive SVG-based dependency visualization
- **PlanningCostInsights.jsx**: Cost analysis with AI-powered recommendations
- **GoalForm.jsx**: Comprehensive goal creation form with validation
- **MultiAgentDashboard.jsx**: Unified multi-agent orchestration interface
- **AgentRegistryView.jsx**: Agent management and health monitoring
- **WorkflowDesignerView.jsx**: Visual workflow creation and editing
- **AgentMarketplace.jsx**: Agent discovery and marketplace integration
- **MultiAgentAnalytics.jsx**: Comprehensive analytics and insights dashboard

#### API Integration
- **Planning Management Hook**: Complete CRUD operations for goals, plans, and tasks
- **Real-time Monitoring Hook**: Automatic polling and notification system
- **Type Safety**: Comprehensive type definitions for all planning entities
- **Error Handling**: Robust error handling with user-friendly messages

#### Performance Features
- **Lazy Loading**: Components load only when needed
- **Optimized Rendering**: Efficient re-rendering with React optimization patterns
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Caching**: Smart caching of API responses to reduce server load

## Responsible AI & Governance Integration

The AI Cost-Performance Optimizer includes comprehensive responsible AI and governance capabilities to ensure ethical, fair, and compliant AI operations.

### Responsible AI Services Overview

#### 1. Bias Detection & Mitigation Service (Port 8084)

**Purpose**: Automated detection and mitigation of bias in AI models and agent decisions.

**Key Features**:
- **Automated Bias Detection**: Uses AIF360 and Fairlearn methodologies
- **Comprehensive Metrics**: Disparate Impact, Equal Opportunity, Demographic Parity
- **Remediation Suggestions**: Automated recommendations with priority levels
- **Protected Attributes**: Support for gender, race, age, and custom attributes

**API Endpoints**:
```bash
# Create bias audit
POST /bias-audits
{
  "model_id": "gpt-4",
  "audit_type": "comprehensive",
  "protected_attributes": ["gender", "race", "age"],
  "target_variable": "outcome"
}

# Get bias metrics
GET /bias-metrics
GET /bias-metrics/{id}
GET /bias-metrics/model/{modelId}
```

#### 2. Explainable AI (XAI) Service (Port 8085)

**Purpose**: Provide explanations for AI decisions using LIME and SHAP methodologies.

**Key Features**:
- **Agent Selection Explanations**: Why specific agents were chosen
- **Planning Decision Explanations**: How planning strategies were determined
- **LLM Response Explanations**: Feature importance for critical responses
- **Multiple Methods**: LIME, SHAP, Integrated Gradients, Attention analysis

**API Endpoints**:
```bash
# Generate explanation
POST /explanations
{
  "type": "agent_selection",
  "model_id": "agent-selector-v1",
  "explanation_method": "shap",
  "input": {...},
  "output": {...}
}

# Get explanations
GET /explanations
GET /explanations/{id}
```

#### 3. Robustness Testing Service (Port 8086)

**Purpose**: Automated testing of AI model robustness against adversarial attacks.

**Key Features**:
- **Adversarial Testing**: FGSM, PGD, C&W attack simulations
- **Noise Sensitivity**: Gaussian noise tolerance testing
- **Drift Detection**: Model performance drift monitoring
- **Vulnerability Assessment**: CVSS scoring for identified vulnerabilities

**API Endpoints**:
```bash
# Create robustness test
POST /robustness-tests
{
  "model_id": "gpt-4",
  "test_type": "comprehensive",
  "configuration": {
    "adversarial_methods": ["FGSM", "PGD"],
    "noise_levels": [0.1, 0.2, 0.3]
  }
}

# Get test results
GET /robustness-tests
GET /robustness-tests/{id}
```

#### 4. Compliance Service (Port 8087)

**Purpose**: Track and report compliance with AI regulations and standards.

**Key Features**:
- **Regulatory Frameworks**: EU AI Act, NIST AI RMF, ISO/IEC 23053
- **Compliance Assessments**: Automated compliance scoring
- **Gap Analysis**: Identification of compliance gaps with remediation plans
- **Reporting**: Automated compliance reports for audits

**API Endpoints**:
```bash
# Get compliance frameworks
GET /compliance/frameworks

# Get compliance assessments
GET /compliance/assessments?model_id=gpt-4&framework_id=eu-ai-act

# Generate compliance report
POST /compliance/reports
{
  "title": "Q4 2024 Compliance Report",
  "report_type": "summary",
  "scope": {"models": ["gpt-4", "claude-3"]}
}
```

#### 5. Enhanced Governance Service (Port 8080)

**Purpose**: Comprehensive model documentation and governance.

**Key Features**:
- **Model Factsheets**: Automated model cards with comprehensive documentation
- **Audit Trails**: Complete logging of all model and agent activities
- **Risk Assessments**: Automated risk scoring and mitigation tracking
- **Environmental Impact**: Carbon footprint and energy consumption tracking

**API Endpoints**:
```bash
# Model factsheets
GET /factsheets
POST /factsheets
GET /factsheets/{id}
PUT /factsheets/{id}
```

### Responsible AI Dashboard

The enhanced Responsible AI Dashboard provides a unified interface for monitoring and managing all responsible AI metrics.

**Key Features**:
- **Overview Dashboard**: Key metrics and trends across all models
- **Fairness Audits**: Interactive bias detection results and remediation tracking
- **Explainability**: Visualization of AI decision explanations
- **Robustness**: Security testing results and vulnerability management
- **Compliance**: Real-time regulatory compliance status

**Access**: Navigate to the "Responsible AI" tab in the main dashboard at `https://scale-llm.com/`

### Integration Patterns

#### 1. Automated Monitoring

Set up automated monitoring for responsible AI metrics:

```python
# Example: Automated bias monitoring
def monitor_model_bias(model_id):
    bias_audit = {
        "model_id": model_id,
        "audit_type": "comprehensive",
        "protected_attributes": ["gender", "race", "age"]
    }

    response = requests.post(
        "http://bias-detection-service:8084/bias-audits",
        json=bias_audit
    )

    if response.status_code == 200:
        audit_result = response.json()
        if audit_result["fairness_status"] == "fail":
            # Trigger remediation workflow
            implement_bias_remediation(audit_result["remediation_suggestions"])
```

#### 2. Compliance Workflow

Integrate compliance checking into your deployment pipeline:

```python
# Example: Pre-deployment compliance check
def check_deployment_compliance(model_id):
    frameworks = ["eu-ai-act", "nist-ai-rmf"]

    for framework in frameworks:
        response = requests.get(
            f"http://compliance-service:8087/compliance/assessments",
            params={"model_id": model_id, "framework_id": framework}
        )

        if response.status_code == 200:
            assessment = response.json()[0]
            if assessment["overall_status"] != "compliant":
                raise Exception(f"Model {model_id} not compliant with {framework}")
```

#### 3. Explainability Integration

Add explanations to critical AI decisions:

```python
# Example: Generate explanation for agent selection
def explain_agent_selection(selection_context):
    explanation_request = {
        "type": "agent_selection",
        "model_id": "agent-selector-v1",
        "explanation_method": "shap",
        "input": selection_context["input"],
        "output": selection_context["output"]
    }

    response = requests.post(
        "http://explainability-service:8085/explanations",
        json=explanation_request
    )

    return response.json()
```

## Client Libraries

For improved developer experience, we provide client libraries that abstract the HTTP calls and handle direct LLM integration, autonomous planning workflows, and multi-agent orchestration.

### Python Client Library

#### Direct LLM Client

```python
import requests
import json
from typing import List, Dict, Any, Optional

class LLMOptimizerClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.chat_completions_endpoint = f"{self.base_url}/v1/chat/completions"

    def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        conversation_id: Optional[str] = None,
        preferred_llm_id: Optional[str] = None,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """
        Sends a chat completion request to the LLM Optimizer Proxy.

        Args:
            messages (List[Dict[str, str]]): List of message objects.
            model (str): The model name (used as a hint/fallback by the proxy).
            temperature (float): Sampling temperature.
            max_tokens (Optional[int]): Maximum number of tokens to generate.
            conversation_id (Optional[str]): ID to continue a conversation.
            preferred_llm_id (Optional[str]): Forces routing to a specific LLM.
            **kwargs: Additional parameters to pass to the LLM API.

        Returns:
            Dict[str, Any]: The response payload from the LLM.
        """
        payload = {
            "messages": messages,
            "model": model,
            "temperature": temperature,
            **kwargs
        }
        if max_tokens is not None:
            payload["max_tokens"] = max_tokens
        if conversation_id:
            payload["conversation_id"] = conversation_id

        headers = {"Content-Type": "application/json"}
        if preferred_llm_id:
            headers["X-Preferred-LLM-ID"] = preferred_llm_id

        try:
            response = requests.post(
                self.chat_completions_endpoint,
                headers=headers,
                json=payload
            )
            response.raise_for_status()  # Raise an exception for HTTP errors
            return response.json()
        except requests.exceptions.HTTPError as http_err:
            print(f"HTTP error occurred: {http_err} - {response.text}")
            raise
        except requests.exceptions.ConnectionError as conn_err:
            print(f"Connection error occurred: {conn_err}")
            raise
        except Exception as err:
            print(f"An unexpected error occurred: {err}")
            raise

#### Multi-Agent Orchestration Client

```python
class MultiAgentClient:
    def __init__(self, base_url: str, auth_token: str):
        self.base_url = base_url
        self.auth_token = auth_token
        self.agents_endpoint = f"{self.base_url}/v1/agents"
        self.workflows_endpoint = f"{self.base_url}/v1/workflows"

    def _get_headers(self):
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.auth_token}"
        }

    def register_agent(
        self,
        name: str,
        agent_type: str,
        description: str,
        endpoint: str,
        capabilities: List[Dict],
        metadata: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Register a new agent in the system."""
        payload = {
            "name": name,
            "type": agent_type,
            "description": description,
            "endpoint": endpoint,
            "capabilities": capabilities
        }
        if metadata:
            payload["metadata"] = metadata

        response = requests.post(
            self.agents_endpoint,
            headers=self._get_headers(),
            json=payload
        )
        response.raise_for_status()
        return response.json()

    def create_workflow(
        self,
        name: str,
        description: str,
        tasks: List[Dict],
        execution_config: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Create a new multi-agent workflow."""
        payload = {
            "name": name,
            "description": description,
            "tasks": tasks
        }
        if execution_config:
            payload["execution_config"] = execution_config

        response = requests.post(
            self.workflows_endpoint,
            headers=self._get_headers(),
            json=payload
        )
        response.raise_for_status()
        return response.json()

    def execute_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """Execute a workflow."""
        response = requests.post(
            f"{self.workflows_endpoint}/{workflow_id}/execute",
            headers=self._get_headers()
        )
        response.raise_for_status()
        return response.json()

    def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get workflow execution status."""
        response = requests.get(
            f"{self.workflows_endpoint}/{workflow_id}/status",
            headers=self._get_headers()
        )
        response.raise_for_status()
        return response.json()

    def select_optimal_agent(
        self,
        task_requirements: Dict,
        constraints: Optional[Dict] = None,
        optimization_strategy: str = "balanced"
    ) -> Dict[str, Any]:
        """Select optimal agent for a task."""
        payload = {
            "task_requirements": task_requirements,
            "optimization_strategy": optimization_strategy
        }
        if constraints:
            payload["constraints"] = constraints

        response = requests.post(
            f"{self.agents_endpoint}/select",
            headers=self._get_headers(),
            json=payload
        )
        response.raise_for_status()
        return response.json()
```

#### Autonomous Planning Client

```python
class PlanningClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.goals_endpoint = f"{self.base_url}/v1/goals"

    def create_goal(
        self,
        description: str,
        user_id: str,
        success_criteria: Optional[List[Dict]] = None,
        constraints: Optional[List[Dict]] = None,
        context: Optional[Dict] = None,
        priority: int = 5
    ) -> Dict[str, Any]:
        """Create a new goal for autonomous execution."""
        payload = {
            "description": description,
            "priority": priority
        }
        if success_criteria:
            payload["success_criteria"] = success_criteria
        if constraints:
            payload["constraints"] = constraints
        if context:
            payload["context"] = context

        headers = {
            "Content-Type": "application/json",
            "X-User-ID": user_id
        }

        response = requests.post(self.goals_endpoint, headers=headers, json=payload)
        response.raise_for_status()
        return response.json()

    def create_plan(self, goal_id: str, user_id: str) -> Dict[str, Any]:
        """Generate an execution plan for a goal."""
        headers = {"X-User-ID": user_id}
        response = requests.post(f"{self.goals_endpoint}/{goal_id}/plan", headers=headers)
        response.raise_for_status()
        return response.json()

    def execute_goal(
        self,
        goal_id: str,
        user_id: str,
        max_concurrency: int = 3,
        timeout: str = "30m"
    ) -> Dict[str, Any]:
        """Execute a goal's plan."""
        payload = {
            "max_concurrency": max_concurrency,
            "timeout": timeout
        }
        headers = {
            "Content-Type": "application/json",
            "X-User-ID": user_id
        }
        response = requests.post(
            f"{self.goals_endpoint}/{goal_id}/execute",
            headers=headers,
            json=payload
        )
        response.raise_for_status()
        return response.json()

    def get_status(self, goal_id: str, user_id: str) -> Dict[str, Any]:
        """Get goal execution status."""
        headers = {"X-User-ID": user_id}
        response = requests.get(f"{self.goals_endpoint}/{goal_id}/status", headers=headers)
        response.raise_for_status()
        return response.json()

    def get_results(self, goal_id: str, user_id: str) -> Dict[str, Any]:
        """Get goal execution results."""
        headers = {"X-User-ID": user_id}
        response = requests.get(f"{self.goals_endpoint}/{goal_id}/results", headers=headers)
        response.raise_for_status()
        return response.json()
```

### Usage Examples

#### Multi-Agent Orchestration Usage

```python
# Initialize the multi-agent client
multi_agent_client = MultiAgentClient(OPTIMIZER_BASE_URL, "your-auth-token")

# Example 1: Register a custom agent
try:
    agent = multi_agent_client.register_agent(
        name="Advanced Data Analyst",
        agent_type="data_analyst",
        description="Specialized in complex statistical analysis and ML modeling",
        endpoint="https://my-agent-service.com/api",
        capabilities=[
            {
                "id": "statistical_analysis",
                "name": "Statistical Analysis",
                "description": "Advanced statistical analysis capabilities",
                "quality": 0.95,
                "cost": 0.12,
                "speed": 0.88
            },
            {
                "id": "ml_modeling",
                "name": "Machine Learning Modeling",
                "description": "Build and train ML models",
                "quality": 0.92,
                "cost": 0.25,
                "speed": 0.75
            }
        ],
        metadata={
            "version": "2.1",
            "provider": "custom",
            "max_concurrent_tasks": 3,
            "specializations": ["time_series", "classification", "regression"]
        }
    )
    print(f"Registered agent: {agent['id']}")

    # Example 2: Create and execute a multi-agent workflow
    workflow = multi_agent_client.create_workflow(
        name="Customer Churn Analysis Pipeline",
        description="Comprehensive customer churn analysis with predictive modeling",
        tasks=[
            {
                "name": "Data Collection",
                "type": "data_collection",
                "description": "Collect customer data from multiple sources",
                "agent_requirements": {
                    "type": "data_analyst",
                    "capabilities": ["data_collection", "data_integration"]
                },
                "parameters": {
                    "sources": ["crm", "billing", "support_tickets"],
                    "time_range": "last_12_months"
                },
                "priority": 1
            },
            {
                "name": "Feature Engineering",
                "type": "data_processing",
                "description": "Create features for churn prediction",
                "agent_requirements": {
                    "type": "data_analyst",
                    "capabilities": ["feature_engineering", "data_preprocessing"]
                },
                "dependencies": ["Data Collection"],
                "priority": 2
            },
            {
                "name": "Churn Prediction Model",
                "type": "ml_modeling",
                "description": "Build and train churn prediction model",
                "agent_requirements": {
                    "type": "data_analyst",
                    "capabilities": ["ml_modeling", "model_evaluation"]
                },
                "dependencies": ["Feature Engineering"],
                "priority": 3
            },
            {
                "name": "Generate Insights Report",
                "type": "report_generation",
                "description": "Create comprehensive analysis report",
                "agent_requirements": {
                    "type": "content_writer",
                    "capabilities": ["report_generation", "data_visualization"]
                },
                "dependencies": ["Churn Prediction Model"],
                "priority": 4
            }
        ],
        execution_config={
            "max_concurrency": 2,
            "timeout": "2h",
            "retry_policy": {
                "max_retries": 3,
                "backoff_strategy": "exponential"
            }
        }
    )

    workflow_id = workflow["id"]
    print(f"Created workflow: {workflow_id}")

    # Execute the workflow
    execution = multi_agent_client.execute_workflow(workflow_id)
    print("Workflow execution started")

    # Monitor progress
    import time
    while True:
        status = multi_agent_client.get_workflow_status(workflow_id)
        print(f"Workflow status: {status['status']}")

        if status["status"] in ["completed", "failed", "cancelled"]:
            break
        time.sleep(10)

    print("Workflow completed!")

    # Example 3: Intelligent agent selection
    optimal_agent = multi_agent_client.select_optimal_agent(
        task_requirements={
            "type": "data_analysis",
            "capabilities": ["statistical_analysis", "data_visualization"],
            "complexity": "high",
            "estimated_tokens": 8000
        },
        constraints={
            "max_cost": 3.00,
            "max_latency": 45000,
            "quality_threshold": 0.90
        },
        optimization_strategy="quality_first"
    )

    print(f"Selected agent: {optimal_agent['agent']['name']}")
    print(f"Confidence score: {optimal_agent['confidence_score']}")
    print(f"Estimated cost: ${optimal_agent['estimated_cost']:.2f}")

except Exception as e:
    print(f"Error in multi-agent workflow: {e}")
```

#### Direct LLM Usage

```python
# Replace with your actual deployed frontend IP
OPTIMIZER_BASE_URL = "https://scale-llm.com"  # Or your actual IP/hostname

llm_client = LLMOptimizerClient(OPTIMIZER_BASE_URL)

# Example 1: Basic Request (Proxy chooses LLM)
try:
    response_basic = llm_client.create_chat_completion(
        messages=[{"role": "user", "content": "What is the capital of France?"}],
        model="gpt-3.5-turbo"  # Hint model
    )
    print("Basic Request Response:", json.dumps(response_basic, indent=2))
except Exception as e:
    print(f"Error during basic request: {e}")
```

#### Autonomous Planning Usage

```python
planning_client = PlanningClient(OPTIMIZER_BASE_URL)

# Example: Customer Service Analysis Workflow
try:
    # Step 1: Create goal
    goal = planning_client.create_goal(
        description="Analyze customer support tickets from last week and generate insights",
        user_id="analyst-team",
        success_criteria=[
            {
                "description": "Process at least 100 tickets",
                "metric": "ticket_count",
                "target": 100,
                "operator": ">=",
                "weight": 0.8,
                "required": True
            }
        ],
        constraints=[
            {
                "type": "time",
                "description": "Complete within 2 hours",
                "limit": "2h",
                "operator": "<=",
                "severity": "hard"
            }
        ],
        context={
            "data_source": "support_tickets_db",
            "time_range": "last_7_days"
        },
        priority=8
    )

    goal_id = goal["id"]
    print(f"Created goal: {goal_id}")

    # Step 2: Generate plan
    plan = planning_client.create_plan(goal_id, "analyst-team")
    print(f"Generated plan with {len(plan['tasks'])} tasks")

    # Step 3: Execute
    execution = planning_client.execute_goal(goal_id, "analyst-team", max_concurrency=3)
    print("Execution started")

    # Step 4: Monitor progress
    import time
    while True:
        status = planning_client.get_status(goal_id, "analyst-team")
        print(f"Status: {status['status']}")

        if status["status"] in ["completed", "failed"]:
            break
        time.sleep(10)

    # Step 5: Get results
    if status["status"] == "completed":
        results = planning_client.get_results(goal_id, "analyst-team")
        print("Results:", json.dumps(results, indent=2))

except Exception as e:
    print(f"Error during autonomous workflow: {e}")
```

### Other Available Client Libraries

- **JavaScript/TypeScript**: For Node.js backends or browser-based applications
- **Go**: Native integration for Go services
- **Ruby, Java, C#**: Available based on your development ecosystem

## Benefits of Integration

### "Set it and Forget it" Optimization
Write code once to hit our proxy, and the backend infrastructure automatically handles cost and performance decisions for individual requests, complex workflows, and multi-agent orchestration.

### Multi-Agent Collaboration
Leverage our comprehensive multi-agent platform to:
- **Orchestrate Complex Workflows**: Coordinate multiple specialized agents working together
- **Intelligent Agent Selection**: Automatically choose optimal agents based on capabilities and performance
- **Real-time Monitoring**: Track agent performance and workflow execution in real-time
- **Marketplace Integration**: Access a growing ecosystem of specialized AI agents
- **Template-based Workflows**: Use pre-built patterns for common multi-agent scenarios

### Autonomous Workflow Execution
Submit high-level goals and let the system automatically:
- Break down complex objectives into executable tasks
- Orchestrate multi-step workflows with proper dependencies
- Handle failures and retries intelligently
- Optimize costs across the entire workflow

### Enterprise-Grade Security
Benefit from comprehensive security features:
- **Role-based Access Control**: Granular permissions for agents, workflows, and resources
- **Audit Logging**: Complete security event tracking and compliance reporting
- **Secure Communication**: Encrypted channels for inter-agent communication
- **Authentication**: JWT-based session management with secure token handling

### Future-Proofing
As new LLMs emerge, agents become available, or orchestration capabilities improve, we can update backend policies and algorithms without requiring any code changes in your applications.

### Simplified AI Integration
- No need to manage API keys for multiple LLM providers or agent services
- No complex workflow orchestration logic in your application
- No manual task decomposition, dependency management, or agent coordination
- Unified interface for simple requests, complex workflows, and multi-agent collaboration

### Centralized Observability
Use our comprehensive dashboard to monitor:
- Individual LLM request performance and costs
- Goal execution progress and success rates
- Agent performance metrics and health status
- Workflow execution with real-time visualization
- Multi-agent collaboration analytics and insights
- Security events and compliance reporting

### Reduced Development Time
- Less boilerplate code for LLM and agent integration
- No need to build custom workflow orchestration or agent management
- Automatic handling of complex multi-step AI processes and agent coordination
- Built-in retry logic, error handling, and failure recovery
- Pre-built templates for common multi-agent scenarios

## Next Steps

- Review the [Setup Guide](setup_guide.md) for installation details
- Explore [Routing Examples](routing_examples.md) for policy configurations
- Learn about the [Cost Model](cost_model.md) for optimization strategies
- Understand the [System Architecture](architecture.md) for integration planning
- Check out [Planning Engine Examples](planning_engine_examples.md) for autonomous workflow patterns
- Read the [Planning Engine Integration Guide](planning_engine_integration.md) for advanced setup

## Prompt Management API

The Policy Manager provides API endpoints for managing LLM prompts.

**Create a Prompt**
```
POST /prompts
```
**Get All Prompts**
```
GET /prompts
```
**Get a Specific Prompt**
```
GET /prompts/{id}
```
**Update a Prompt**
```
PUT /prompts/{id}
```
**Delete a Prompt**
```
DELETE /prompts/{id}
```

For more details, refer to the Policy Manager documentation.

## Synthetic Data Generation

The data processor includes a synthetic data generation loop that periodically generates synthetic LLM inference data. This data is used for testing and evaluation purposes.

The synthetic data generation loop is configured using the following environment variables:

*   `SYNTHETIC_DATA_GENERATION_MODEL_ID`: Model ID for synthetic data generation.
*   `FEEDBACK_LOOP_INTERVAL`: Interval between synthetic data generation runs.

## Evaluation Results API

The Dashboard API provides endpoints for retrieving evaluation results and curated data.

**Get Evaluation Results**
```
GET /evaluation-results
```
**Get Curated Data**
```
GET /curated-data
```

For more details, refer to the Dashboard API documentation.

## Enhanced Evaluation Service with Analytics

The evaluation service has been significantly enhanced with analytics, feedback loops, and automated optimization capabilities.

### Core Features
- **Multi-dimensional evaluation**: Bias, safety, quality assessment
- **API-type specific evaluation**: Chat, embeddings, image, audio
- **Real-time performance monitoring**: Continuous tracking of model performance
- **Automated alerting**: Proactive notifications for performance issues
- **Smart recommendations**: AI-powered optimization suggestions
- **Feedback loops**: Automatic policy updates based on evaluation results

### Analytics Endpoints

#### Model Analytics
```bash
# Get analytics for all models
GET /analytics/models

# Get analytics for specific model
GET /analytics/models/{model_id}

# Get performance trends
GET /analytics/trends
```

#### Alerts and Recommendations
```bash
# Get active alerts
GET /analytics/alerts

# Get optimization recommendations
GET /analytics/recommendations

# Acknowledge an alert
POST /actions/acknowledge-alert/{alert_id}

# Apply a recommendation
POST /actions/apply-recommendation/{rec_id}

# Trigger manual optimization
POST /actions/trigger-optimization
```

### Analytics Features

#### Performance Monitoring
- **Score Tracking**: Average scores, pass rates, trend analysis
- **Usage Metrics**: Request counts, latency measurements
- **Quality Metrics**: Detailed breakdown of evaluation dimensions

#### Automated Alerting
- **Low Score Alerts**: When model performance drops below thresholds
- **Declining Trend Alerts**: When performance shows consistent decline
- **High Bias Alerts**: When bias metrics exceed acceptable levels
- **Safety Violation Alerts**: When safety scores are concerning

#### Smart Recommendations
- **Model Switch Recommendations**: Suggest better-performing models
- **Prompt Optimization**: Recommend prompt improvements
- **Parameter Tuning**: Suggest configuration adjustments

#### Feedback Loops
- **Policy Updates**: Automatically update routing policies based on performance
- **Prompt Performance Tracking**: Send performance data to policy manager
- **Real-time Optimization**: Continuous improvement based on evaluation results

### Configuration

Set these environment variables for analytics:
```bash
CLICKHOUSE_DSN=clickhouse://localhost:9000/default
REDIS_ADDR=localhost:6379
POLICY_MANAGER_URL=http://policy-manager:8080
```

## Enhanced Prompt Operations (PromptOps)

The policy manager now includes enterprise-grade prompt operations capabilities for managing the entire prompt lifecycle with advanced versioning, testing, and analytics.

### Core Features
- **Git-style Version Control**: Semantic versioning with rollback, diff, and clone operations
- **Advanced Template System**: Typed variables with validation and live preview
- **Interactive Playground**: Multi-model testing with real-time execution
- **A/B Testing Framework**: Statistical comparison with confidence intervals
- **Performance Analytics**: Cost, latency, quality, and success rate tracking
- **Variable Management**: Automatic extraction and type-safe validation
- **Multi-Model Support**: Test across OpenAI, Anthropic, Google, and other providers

### Enterprise Lifecycle Management (Roadmap)
- **Approval Workflows**: Multi-stage approval process for production deployments
- **Role-Based Access Control (RBAC)**: Owner assignment and granular permissions
- **Comprehensive Audit Logs**: Full audit trail of changes, approvals, and usage
- **CI/CD Pipeline Integration**: Automated testing and deployment workflows
- **GitOps Integration**: Git-based prompt management with branch protection

### Enhanced Versioning Endpoints
```bash
# Get all versions of a prompt
GET /api/prompts/{id}/versions

# Compare two versions (diff)
GET /api/prompts/{id}/diff?from=1.0.0&to=1.0.1

# Rollback to previous version
POST /api/prompts/{id}/rollback
{
  "to_version": "1.0.0",
  "change_log": "Rollback due to performance issues"
}

# Clone a prompt
POST /api/prompts/{id}/clone
{
  "new_id": "prompt_copy_123",
  "new_name": "My Prompt (Copy)",
  "description": "Copy of original prompt"
}
```

### Template & Variable Management
```bash
# Get all prompt templates
GET /api/prompts/templates

# Extract variables from content
POST /api/prompts/variables/extract
{
  "content": "Hello {{name}}, your order {{order_id}} is ready!"
}

# Response:
{
  "variables": [
    {"name": "name", "type": "string", "required": true},
    {"name": "order_id", "type": "string", "required": true}
  ],
  "count": 2
}
```

### Testing & Execution Endpoints
```bash
# Test a prompt with specific data
POST /api/prompts/{id}/test
{
  "test_data": {
    "variables": {"name": "John", "order_id": "12345"},
    "expected_type": "contains",
    "expected": "John"
  }
}

# Execute prompt with variables
POST /api/prompts/{id}/execute
{
  "model_id": "gpt-3.5-turbo",
  "variables": {"name": "Alice", "order_id": "67890"},
  "temperature": 0.7,
  "max_tokens": 1000
}
```

### A/B Testing Endpoints
```bash
# Create A/B test
POST /api/prompts/ab-tests

# Get all A/B tests
GET /api/prompts/ab-tests

# Get specific A/B test
GET /api/prompts/ab-tests/{id}

# Update A/B test
PUT /api/prompts/ab-tests/{id}

# Stop A/B test
POST /api/prompts/ab-tests/{id}/stop

# Delete A/B test
DELETE /api/prompts/ab-tests/{id}
```

### Performance Tracking Endpoints
```bash
# Get prompt performance metrics
GET /api/prompts/performance

# Get performance for specific prompt
GET /api/prompts/performance/{id}

# Update performance metrics
PUT /api/prompts/performance/{id}

# Get comprehensive analytics
GET /api/prompts/analytics
```

### Enhanced Prompt Template Example
```json
{
  "id": "customer_support_template",
  "name": "Customer Support Response Template",
  "description": "Template for generating customer support responses",
  "content": "Hello {{customer_name}},\n\nThank you for contacting us about {{issue_type}}. {{#if urgent}}This is marked as urgent and will be prioritized.{{/if}}\n\nBased on your description: {{issue_description}}\n\nOur recommended solution: {{solution_steps}}\n\nBest regards,\n{{agent_name}}",
  "version": "1.0.0",
  "status": "active",
  "tags": ["template", "customer_support", "stable"],
  "model_targets": ["gpt-3.5-turbo", "claude-3-haiku"],
  "variables": [
    {
      "name": "customer_name",
      "type": "string",
      "description": "Customer's full name",
      "required": true,
      "default_value": ""
    },
    {
      "name": "issue_type",
      "type": "string",
      "description": "Type of customer issue",
      "required": true,
      "validation": "billing|technical|general"
    },
    {
      "name": "urgent",
      "type": "boolean",
      "description": "Whether this is an urgent issue",
      "required": false,
      "default_value": false
    },
    {
      "name": "issue_description",
      "type": "string",
      "description": "Detailed description of the issue",
      "required": true
    },
    {
      "name": "solution_steps",
      "type": "array",
      "description": "List of solution steps",
      "required": true
    },
    {
      "name": "agent_name",
      "type": "string",
      "description": "Support agent name",
      "required": true,
      "default_value": "Support Team"
    }
  ],
  "change_log": "Initial template creation",
  "metadata": {
    "created_by": "support_team",
    "use_case": "customer_support",
    "complexity": "medium"
  }
}
```

### A/B Test Configuration
```json
{
  "id": "summarization-test-1",
  "name": "Summarization Prompt A/B Test",
  "prompt_a_id": "summarization-v1",
  "prompt_b_id": "summarization-v2",
  "traffic_split": 30,
  "status": "running"
}
```

## Enterprise PromptOps Lifecycle Management (Roadmap)

The following features are planned for enterprise-grade prompt lifecycle management:

### Approval Workflows

**Multi-stage approval process for production deployments:**

```bash
# Submit prompt for approval
POST /api/prompts/{id}/submit-for-approval
{
  "target_environment": "production",
  "approval_notes": "Ready for production deployment",
  "reviewers": ["<EMAIL>", "<EMAIL>"]
}

# Approve/reject prompt
POST /api/prompts/{id}/approve
{
  "decision": "approved", // or "rejected"
  "comments": "Approved after security review",
  "approver_id": "<EMAIL>"
}

# Get approval status
GET /api/prompts/{id}/approval-status
```

### Role-Based Access Control (RBAC)

**Owner assignment and granular permissions:**

```bash
# Assign prompt ownership
PUT /api/prompts/{id}/ownership
{
  "owner": "<EMAIL>",
  "team": "ai-engineering",
  "permissions": {
    "read": ["ai-engineering", "product-team"],
    "write": ["ai-engineering"],
    "approve": ["team-leads", "security-team"],
    "deploy": ["devops-team"]
  }
}

# Check user permissions
GET /api/prompts/{id}/permissions?user=<EMAIL>

# Get user's accessible prompts
GET /api/prompts?accessible_by=<EMAIL>
```

### Comprehensive Audit Logs

**Full audit trail of changes, approvals, and usage:**

```bash
# Get audit log for specific prompt
GET /api/prompts/{id}/audit-log

# Get system-wide audit logs
GET /api/audit-logs?entity_type=prompt&start_date=2024-01-01&end_date=2024-12-31

# Example audit log entry:
{
  "timestamp": "2024-07-10T19:30:00Z",
  "event_type": "prompt_modified",
  "user_id": "<EMAIL>",
  "prompt_id": "customer_support_template",
  "changes": {
    "version": "1.0.0 → 1.0.1",
    "content_changed": true,
    "variables_added": ["priority_level"]
  },
  "approval_status": "pending",
  "environment": "staging"
}
```

### CI/CD Pipeline Integration

**Automated testing and deployment workflows:**

```yaml
# .github/workflows/promptops.yml
name: PromptOps CI/CD
on:
  push:
    paths: ['prompts/**']
  pull_request:
    paths: ['prompts/**']

jobs:
  test-prompts:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Validate Prompt Templates
        run: |
          curl -X POST "${{ secrets.AI_OPS_URL }}/api/prompts/validate" \
            -H "Authorization: Bearer ${{ secrets.AI_OPS_TOKEN }}" \
            -H "Content-Type: application/json" \
            -d @prompts/customer_support.json

      - name: Run Prompt Tests
        run: |
          curl -X POST "${{ secrets.AI_OPS_URL }}/api/prompts/test-suite" \
            -H "Authorization: Bearer ${{ secrets.AI_OPS_TOKEN }}" \
            -d '{"test_suite": "regression", "environment": "staging"}'

  deploy-prompts:
    needs: test-prompts
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Production
        run: |
          curl -X POST "${{ secrets.AI_OPS_URL }}/api/prompts/deploy" \
            -H "Authorization: Bearer ${{ secrets.AI_OPS_TOKEN }}" \
            -d '{"environment": "production", "auto_approve": false}'
```

### GitOps Integration

**Git-based prompt management with branch protection:**

```bash
# Sync prompts from Git repository
POST /api/prompts/sync-from-git
{
  "repository_url": "https://github.com/company/ai-prompts.git",
  "branch": "main",
  "sync_strategy": "merge", // or "overwrite"
  "auto_deploy": false
}

# Export prompts to Git format
GET /api/prompts/export-git-format?format=yaml

# Git webhook integration for automatic sync
POST /api/webhooks/git-sync
{
  "repository": "company/ai-prompts",
  "ref": "refs/heads/main",
  "commits": [...]
}
```

### Environment Management

**Multi-environment prompt deployment:**

```bash
# Deploy prompt to specific environment
POST /api/prompts/{id}/deploy
{
  "target_environment": "production",
  "deployment_strategy": "blue_green", // or "rolling", "canary"
  "rollback_on_failure": true,
  "health_checks": ["response_time", "error_rate", "quality_score"]
}

# Get environment status
GET /api/environments/production/prompts

# Rollback deployment
POST /api/prompts/{id}/rollback-deployment
{
  "environment": "production",
  "target_version": "1.0.0"
}
```

## Planning Dashboard Troubleshooting

### Common Issues and Solutions

#### Dashboard Not Loading
**Problem**: Planning tab shows loading spinner indefinitely
**Solutions**:
1. Check that the planning service is running: `kubectl get pods | grep planning-service`
2. Verify API endpoints are accessible: `curl https://scale-llm.comcom/v1/goals`
3. Check browser console for JavaScript errors
4. Ensure proper CORS configuration in the planning service

#### Goals Not Creating
**Problem**: Goal creation form submission fails
**Solutions**:
1. Verify all required fields are filled (description is mandatory)
2. Check success criteria have valid metrics and targets
3. Ensure constraints have proper limit values and operators
4. Check network connectivity to the planning service API

#### Real-time Monitoring Not Working
**Problem**: Goal status doesn't update automatically
**Solutions**:
1. Check that monitoring is enabled (green indicator should be visible)
2. Verify the goal is in an active state (planning, executing)
3. Check browser console for polling errors
4. Ensure the planning service is responding to status requests

#### Task Dependency Graph Not Displaying
**Problem**: Dependency graph shows empty or broken visualization
**Solutions**:
1. Verify tasks have been generated (plan must exist)
2. Check that task dependencies are properly defined
3. Ensure SVG rendering is supported in the browser
4. Check for JavaScript errors in the browser console

#### Cost Insights Showing Incorrect Data
**Problem**: Cost analysis displays zero or incorrect values
**Solutions**:
1. Verify goals have completed execution to generate cost data
2. Check that the planning service is tracking execution costs
3. Ensure proper integration with the cost tracking system
4. Verify time range settings for cost analysis

### Performance Optimization

#### Slow Dashboard Loading
**Optimizations**:
1. Enable browser caching for static assets
2. Implement service worker for offline functionality
3. Use CDN for faster asset delivery
4. Optimize API response sizes

#### High Memory Usage
**Solutions**:
1. Limit the number of goals displayed per page
2. Implement virtual scrolling for large lists
3. Clear old monitoring data periodically
4. Optimize React component re-rendering

### Development Setup

#### Local Development
```bash
# Start the frontend development server
cd k8s/frontend
npm install
npm run dev

# Access the dashboard at http://localhost:5173/
```

#### Building for Production
```bash
# Build optimized production bundle
npm run build

# Preview production build
npm run preview
```

### API Integration Testing

#### Test Planning Service Connectivity
```bash
# Test goal creation
curl -X POST https://scale-llm.com/v1/goals \
  -H "Content-Type: application/json" \
  -H "X-User-ID: test-user" \
  -d '{
    "description": "Test goal for connectivity",
    "priority": 5
  }'

# Test goal listing
curl https://scale-llm.com/v1/goals \
  -H "X-User-ID: test-user"
```

#### Monitor API Performance
```bash
# Check API response times
curl -w "@curl-format.txt" -o /dev/null -s https://scale-llm.com/v1/goals

# Where curl-format.txt contains:
#     time_namelookup:  %{time_namelookup}\n
#        time_connect:  %{time_connect}\n
#     time_appconnect:  %{time_appconnect}\n
#    time_pretransfer:  %{time_pretransfer}\n
#       time_redirect:  %{time_redirect}\n
#  time_starttransfer:  %{time_starttransfer}\n
#                     ----------\n
#          time_total:  %{time_total}\n
```

## Troubleshooting

### Common Issues

1. **Connection Timeouts**: Check network connectivity and firewall settings
2. **Authentication Errors**: Verify API keys and token expiration
3. **Rate Limiting**: Implement exponential backoff for retries
4. **Model Availability**: Check model status and regional availability

### Service-Specific Issues

#### Evaluation Service
- **Quality Score Inconsistencies**: Check evaluation criteria configuration
- **Slow Evaluation Response**: Verify evaluation model availability
- **Missing Metrics**: Ensure proper evaluation pipeline setup

#### Governance Service
- **Policy Violations**: Review governance rules and compliance settings
- **Audit Log Gaps**: Check audit service connectivity and permissions
- **Compliance Failures**: Verify policy enforcement configuration

#### Integration Service
- **External API Failures**: Check third-party service status and credentials
- **Data Sync Issues**: Verify integration pipeline health and data formats
- **Webhook Failures**: Check endpoint availability and authentication

#### Multi-Agent Orchestration
- **Agent Selection Failures**: Verify agent registry and health status
- **Workflow Execution Errors**: Check task dependencies and agent capabilities
- **Collaboration Issues**: Review agent communication protocols and synergy scores

For additional support, refer to the planning service logs and dashboard API documentation.