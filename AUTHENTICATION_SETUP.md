# Authentication Setup Guide

This guide walks you through setting up Google OAuth authentication for your AI Operations Hub deployment.

## Overview

The AI Operations Hub now includes a comprehensive authentication system with:
- Google OAuth login/signup
- User profile management
- Role-based access control (user, admin)
- Admin user management interface
- JWT-based session management

## Prerequisites

1. Google Cloud Project with OAuth 2.0 credentials
2. Kubernetes cluster with the AI Operations Hub deployed
3. Domain name configured (e.g., scale-llm.com)

## Step 1: Google OAuth Setup

### 1.1 Create Google OAuth Credentials

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project or create a new one
3. Navigate to "APIs & Services" > "Credentials"
4. Click "Create Credentials" > "OAuth 2.0 Client IDs"
5. Choose "Web application" as the application type
6. Add authorized redirect URI: `https://scale-llm.com/auth/callback`
7. Save and note the Client ID and Client Secret

### 1.2 Enable Required APIs

Enable the following APIs in your Google Cloud project:
- Google+ API (for user profile information)
- People API (for user details)

## Step 2: Deploy Authentication Service

### 2.1 Set Environment Variables

```bash
export GOOGLE_CLIENT_ID="your-google-client-id-here"
export GOOGLE_CLIENT_SECRET="your-google-client-secret-here"
export JWT_SECRET="your-secure-jwt-secret"  # Optional - will be generated if not provided
export ADMIN_EMAILS="<EMAIL>,<EMAIL>"
```

### 2.2 Create Kubernetes Secrets

Run the setup script to create required secrets:

```bash
./k8s/scripts/setup-auth-secrets.sh
```

This script will create:
- `google-oauth-secret` with your OAuth credentials
- `jwt-secret` with JWT signing key
- `auth-config` ConfigMap with admin emails
- `clickhouse-secret` if it doesn't exist

### 2.3 Deploy the Service

The authentication service is automatically deployed when you run your standard deployment scripts.

For manual deployment:

**Enterprise Edition:**
```bash
kubectl apply -f k8s/auth-service/auth-service.yaml
```

**Standard Edition:**
```bash
kubectl apply -f k8s/standard-edition/auth-service-standard.yaml
```

## Step 3: Update Deployment Scripts

The authentication service has been integrated into your existing deployment scripts:

### 3.1 Cloud Build Integration

The `cloudbuild.yaml` has been updated to:
- Build the auth-service Docker image
- Deploy the auth-service to Kubernetes
- Set up proper environment variables

### 3.2 Deployment Scripts

The `New_Autostart.sh` script now:
- Deploys the auth-service early in the deployment sequence
- Configures proper image substitution
- Waits for the service to be ready before deploying dependent services

## Step 4: Frontend Integration

The frontend has been updated with authentication components:

### 4.1 New Components
- `AuthContext` - Authentication state management
- `LoginButton` - Google OAuth login button
- `UserProfile` - User profile management
- `ProtectedRoute` - Route protection wrapper
- `UserManagement` - Admin user management interface

### 4.2 Updated Apps
- Main App (Enterprise) - Wrapped with authentication
- StandardApp - Includes authentication with startup landing fallback
- Landing pages updated to use Google OAuth login

## Step 5: Service Integration

All services have been updated with authentication middleware:

### 5.1 Proxy Gateway
- Authentication middleware on all LLM API endpoints
- Optional authentication for analytics endpoints
- Admin-only access for cache management

### 5.2 Dashboard API
- Authentication required for all data endpoints
- User context passed to downstream services
- Public health check endpoints

### 5.3 Other Services
- Planning Service, Evaluation Service, etc. will receive user context
- Admin endpoints protected with role-based access

## Step 6: Testing the Setup

### 6.1 Verify Deployment

Check that the auth service is running:
```bash
kubectl get pods -l app=auth-service
kubectl logs deployment/auth-service
```

### 6.2 Test Authentication Flow

1. Navigate to your domain (e.g., https://scale-llm.com)
2. Click "Sign in with Google"
3. Complete the OAuth flow
4. Verify you're redirected back to the dashboard

### 6.3 Test Admin Access

If your email is in the admin list:
1. Log in with your admin account
2. Navigate to the "Admin" tab in the dashboard
3. Verify you can see and manage users

## Step 7: Production Considerations

### 7.1 Security
- Use strong, unique JWT secrets
- Regularly rotate OAuth credentials
- Monitor authentication logs
- Set up proper CORS policies

### 7.2 Scaling
- The auth service can be scaled horizontally
- JWT tokens are stateless for better scalability
- Consider Redis for session storage in high-traffic scenarios

### 7.3 Monitoring
- Set up alerts for authentication failures
- Monitor user registration patterns
- Track admin actions through audit logs

## Troubleshooting

### Common Issues

1. **OAuth redirect mismatch**
   - Ensure redirect URI in Google Console exactly matches your domain
   - Check for trailing slashes or protocol mismatches

2. **JWT token issues**
   - Verify JWT_SECRET is consistent across restarts
   - Check token expiration settings

3. **Admin access not working**
   - Verify admin emails are correctly configured in the ConfigMap
   - Check user roles in the database

4. **Service connectivity issues**
   - Ensure ClickHouse is accessible from the auth service
   - Check network policies and service discovery

### Debug Commands

```bash
# Check auth service logs
kubectl logs deployment/auth-service -f

# Check secrets
kubectl get secrets google-oauth-secret jwt-secret -o yaml

# Check ConfigMap
kubectl get configmap auth-config -o yaml

# Test database connectivity
kubectl exec -it deployment/clickhouse -- clickhouse-client -q "SELECT * FROM users LIMIT 5"

# Check ingress routing
kubectl get ingress -o yaml
```

## Next Steps

After setting up authentication:

1. **Configure API Keys**: Update LLM API keys for your services
2. **Set Up Monitoring**: Configure monitoring and alerting
3. **User Onboarding**: Create documentation for your users
4. **Backup Strategy**: Set up regular backups of user data
5. **Compliance**: Ensure GDPR/privacy compliance if required

## Support

For issues with authentication setup:
1. Check the troubleshooting section above
2. Review service logs for error messages
3. Verify all environment variables are correctly set
4. Ensure Google OAuth configuration matches your domain

The authentication system is designed to be robust and scalable, supporting both your current needs and future growth.
