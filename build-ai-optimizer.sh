#!/bin/bash

# Build script for AI Optimizer
echo "Building AI Optimizer..."
echo "Current directory: $(pwd)"
echo "Files in directory:"
ls -la

echo "Checking main.go content:"
grep -n "AI Optimizer starting" main.go || echo "AI Optimizer start message not found"

echo "Building with Cloud Build..."
gcloud builds submit --tag us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-ai-optimizer:$(date +%s) .
