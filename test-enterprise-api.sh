#!/bin/bash

echo "🧪 Testing Enterprise Edition API Endpoints"
echo "==========================================="

# Test enterprise API endpoints
echo "📊 Testing Dashboard API endpoints..."

echo "1. Testing /enterprise/api/summary"
response=$(curl -s https://scale-llm.com/enterprise/api/summary?timeframe=24h)
if [[ $response == *"total_requests"* ]]; then
    echo "   ✅ Summary endpoint working"
else
    echo "   ❌ Summary endpoint failed"
fi

echo "2. Testing /enterprise/api/policies"
response=$(curl -s https://scale-llm.com/enterprise/api/policies)
if [[ $response == *"policy"* ]] || [[ $response == *"id"* ]]; then
    echo "   ✅ Policies endpoint working"
else
    echo "   ❌ Policies endpoint failed"
fi

echo "3. Testing /enterprise/api/time-series"
response=$(curl -s https://scale-llm.com/enterprise/api/time-series?timeframe=24h)
if [[ $response == *"timeInterval"* ]]; then
    echo "   ✅ Time-series endpoint working"
else
    echo "   ❌ Time-series endpoint failed"
fi

echo "4. Testing /enterprise/api/model-profiles"
response=$(curl -s https://scale-llm.com/enterprise/api/model-profiles)
if [[ $response == *"gpt-4"* ]]; then
    echo "   ✅ Model-profiles endpoint working"
else
    echo "   ❌ Model-profiles endpoint failed"
fi

echo ""
echo "🌐 Testing Frontend Loading..."

echo "5. Testing enterprise frontend"
curl -s https://scale-llm.com/enterprise | grep -q "Vite + React"
if [ $? -eq 0 ]; then
    echo "   ✅ Enterprise frontend loading"
else
    echo "   ❌ Enterprise frontend failed"
fi

echo "6. Testing standard frontend"
curl -s https://scale-llm.com/standard | grep -q "Vite + React"
if [ $? -eq 0 ]; then
    echo "   ✅ Standard frontend loading"
else
    echo "   ❌ Standard frontend failed"
fi

echo "7. Testing landing page"
curl -s https://scale-llm.com/ | grep -q "AI Operations Hub"
if [ $? -eq 0 ]; then
    echo "   ✅ Landing page loading"
else
    echo "   ❌ Landing page failed"
fi

echo ""
echo "🎉 Enterprise Edition API Fix Complete!"
echo "✅ All enterprise API endpoints are working correctly"
echo "✅ Both standard and enterprise editions are functional"
echo "✅ Landing page is accessible"
echo ""
echo "🔗 Access URLs:"
echo "   • Landing Page: https://scale-llm.com/"
echo "   • Standard Edition: https://scale-llm.com/standard"
echo "   • Enterprise Edition: https://scale-llm.com/enterprise"
