# AI Operations Hub - Standard Edition Build Guide

## 🚀 Quick Start

### Build Everything
```bash
./build-standard-edition.sh
```

### Build Options
```bash
# Build only frontend
BUILD_FRONTEND=true BUILD_BACKEND=false ./build-standard-edition.sh

# Build only backend services
BUILD_FRONTEND=false BUILD_BACKEND=true ./build-standard-edition.sh

# Build without deploying
DEPLOY=false ./build-standard-edition.sh
```

## 📁 Key Files Fixed

### 1. Frontend Build Configuration
- **File**: `k8s/frontend/Dockerfile.standard`
- **Fix**: Added `VITE_APP_EDITION=standard` environment variable
- **Purpose**: Ensures StandardApp component is loaded instead of StartupLanding

### 2. Main App Entry Point
- **File**: `k8s/frontend/src/main.jsx`
- **Fix**: Added support for `VITE_APP_EDITION` environment variable
- **Purpose**: Properly detects standard edition and loads correct component

### 3. Enhanced Standard Interface
- **File**: `k8s/frontend/src/StandardApp.jsx`
- **Improvements**:
  - Removed startup landing page (now shows dashboard by default)
  - Enhanced header with professional branding
  - Added interactive preview cards for AI Chat and PromptOps
  - Improved visual design and user experience

### 4. Deployment Script Fixes
- **File**: `k8s/standard-edition/deploy-standard.sh`
- **Fix**: Corrected namespace references from `startup-edition` to `standard-edition`
- **Added**: Automatic external service IP updates

### 5. External Service Management
- **File**: `k8s/scripts/update-standard-external-services.sh`
- **Purpose**: Automatically updates external service IPs for ingress routing
- **Usage**: Called automatically during deployment

## 🔧 Build Process

### 1. Standard Edition Build
```bash
# Full build and deploy
gcloud builds submit --config=cloudbuild-standard.yaml

# Selective build
gcloud builds submit --config=cloudbuild-standard.yaml \
  --substitutions=_BUILD_FRONTEND_STANDARD=true,_BUILD_BACKEND=false
```

### 2. Manual Deployment Steps
```bash
# 1. Deploy standard edition services
./k8s/standard-edition/deploy-standard.sh

# 2. Update external service mappings
./k8s/scripts/update-standard-external-services.sh

# 3. Apply ingress configuration
kubectl apply -f k8s/unified-ingress.yaml
```

## 🌐 Access Points

- **Landing Page**: https://scale-llm.com/
- **Standard Edition**: https://scale-llm.com/standard
- **API Health**: https://scale-llm.com/standard/api/health
- **Dashboard API**: https://scale-llm.com/standard/dashboard/health

## 🎨 Enhanced Standard Edition Features

### Dashboard Overview
- Professional header with gradient logo
- Real-time metrics cards (requests, cost savings, response time, usage)
- Interactive preview cards for AI Chat and PromptOps
- Usage indicator with tier-based limits
- API status monitoring

### AI Chat Preview
- Shows sample conversation with intelligent routing
- Displays cost optimization information
- Direct navigation to full chat interface

### PromptOps Preview
- Shows active prompt versions with performance metrics
- A/B testing status indicators
- Direct navigation to prompt management

### Navigation
- Clean sidebar with feature icons
- Tier-based access control (free/starter/growth)
- Professional branding without "startup edition" references

## 🔍 Troubleshooting

### Frontend Not Loading
```bash
# Check pod status
kubectl get pods -n standard-edition -l app=frontend-standard

# Check logs
kubectl logs -f deployment/frontend-standard -n standard-edition

# Restart deployment
kubectl rollout restart deployment/frontend-standard -n standard-edition
```

### Ingress Issues
```bash
# Check ingress status
kubectl get ingress -A

# Update external services
./k8s/scripts/update-standard-external-services.sh

# Test endpoints
curl -I https://scale-llm.com/standard
curl -I https://scale-llm.com/standard/assets/index-*.js
```

### Build Failures
```bash
# Check Cloud Build logs
gcloud builds list --limit=5

# View specific build
gcloud builds log [BUILD_ID]

# Retry with specific components
BUILD_FRONTEND=true BUILD_BACKEND=false ./build-standard-edition.sh
```

## 📊 Monitoring

### Service Health
```bash
# Check all standard edition services
kubectl get all -n standard-edition

# Check external service mappings
kubectl get endpoints -n default | grep standard-external
```

### Performance Testing
```bash
# Test response times
curl -w "@curl-format.txt" -o /dev/null -s https://scale-llm.com/standard

# Test API endpoints
curl https://scale-llm.com/standard/api/health
curl https://scale-llm.com/standard/dashboard/health
```

## 🎯 Next Steps

1. **API Keys**: Update LLM API keys in the secret
   ```bash
   kubectl edit secret llm-api-keys -n standard-edition
   ```

2. **Monitoring**: Set up monitoring for the standard edition services

3. **Scaling**: Adjust replica counts based on usage

4. **Features**: Add more interactive elements to the dashboard preview cards
