#!/bin/bash

# PromptOps GitOps Setup Script
# This script helps set up the GitOps integration for PromptOps

set -e

echo "🚀 Setting up PromptOps GitOps Integration"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
GIT_REPO_URL="https://github.com/datlaphani/ai-cost-performance-optimizer.git"
GIT_BRANCH="main"
NAMESPACE="default"
SECRET_NAME="git-credentials"

echo -e "${BLUE}Repository: ${GIT_REPO_URL}${NC}"
echo -e "${BLUE}Branch: ${GIT_BRANCH}${NC}"
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command_exists kubectl; then
    echo -e "${RED}❌ kubectl is not installed${NC}"
    exit 1
fi

if ! command_exists gcloud; then
    echo -e "${YELLOW}⚠️  gcloud CLI not found (optional for Cloud Build)${NC}"
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"
echo ""

# Check if we're connected to the right cluster
echo "🔗 Checking Kubernetes connection..."
CURRENT_CONTEXT=$(kubectl config current-context 2>/dev/null || echo "none")
echo -e "${BLUE}Current context: ${CURRENT_CONTEXT}${NC}"

if [[ "$CURRENT_CONTEXT" == "none" ]]; then
    echo -e "${RED}❌ No Kubernetes context found${NC}"
    echo "Please connect to your cluster first:"
    echo "gcloud container clusters get-credentials ai-optimizer-cluster --region us-central1 --project silken-zenith-460615-s7"
    exit 1
fi

echo -e "${GREEN}✅ Connected to Kubernetes cluster${NC}"
echo ""

# Git credentials setup
echo "🔐 Git credentials will be set up automatically during deployment..."
echo "Your GitHub token and webhook secret are configured in the autostart script."
echo ""

# Deploy integration service
echo "🚀 Deploying enhanced integration service..."

# Apply the integration service configuration
kubectl apply -f k8s/integration-service/integration-service.yaml

# Wait for deployment to be ready
echo "Waiting for integration service to be ready..."
kubectl rollout status deployment/integration-service -n $NAMESPACE --timeout=300s

echo -e "${GREEN}✅ Integration service deployed successfully${NC}"
echo ""

# Get service information
echo "📋 Service Information:"
INTEGRATION_SERVICE_IP=$(kubectl get service integration-service -n $NAMESPACE -o jsonpath='{.spec.clusterIP}')
echo -e "${BLUE}Integration Service IP: ${INTEGRATION_SERVICE_IP}${NC}"

# Check if we have an ingress or load balancer
EXTERNAL_IP=$(kubectl get service integration-service -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
if [[ -n "$EXTERNAL_IP" ]]; then
    echo -e "${BLUE}External IP: ${EXTERNAL_IP}${NC}"
    WEBHOOK_URL="http://${EXTERNAL_IP}:8080/gitops/webhook"
else
    # Check for ingress
    INGRESS_HOST=$(kubectl get ingress -n $NAMESPACE -o jsonpath='{.items[0].spec.rules[0].host}' 2>/dev/null || echo "")
    if [[ -n "$INGRESS_HOST" ]]; then
        WEBHOOK_URL="https://${INGRESS_HOST}/api/integration/gitops/webhook"
    else
        WEBHOOK_URL="http://your-domain.com/api/integration/gitops/webhook"
    fi
fi

echo -e "${BLUE}Webhook URL: ${WEBHOOK_URL}${NC}"
echo ""

# Test the service
echo "🧪 Testing integration service..."
kubectl port-forward service/integration-service 8080:8080 -n $NAMESPACE &
PORT_FORWARD_PID=$!

sleep 5

if curl -s http://localhost:8080/health > /dev/null; then
    echo -e "${GREEN}✅ Integration service is healthy${NC}"
else
    echo -e "${RED}❌ Integration service health check failed${NC}"
fi

# Kill port-forward
kill $PORT_FORWARD_PID 2>/dev/null || true

echo ""

# Webhook setup instructions
echo "🪝 Webhook Setup Instructions:"
echo "================================"
echo ""
echo "1. Go to your GitHub repository:"
echo "   ${GIT_REPO_URL}/settings/hooks"
echo ""
echo "2. Click 'Add webhook'"
echo ""
echo "3. Configure the webhook:"
echo "   - Payload URL: ${WEBHOOK_URL}"
echo "   - Content type: application/json"
echo "   - Secret: (Check deployment logs for the generated webhook secret)"
echo "   - Events: Just the push event"
echo ""
echo "4. Click 'Add webhook'"
echo ""
echo "Note: The webhook secret is automatically generated during deployment."
echo "Check the deployment logs to get the actual webhook secret value."
echo ""

# Cloud Build setup
echo "☁️  Cloud Build Setup (Optional):"
echo "=================================="
echo ""
echo "To enable automated deployments:"
echo ""
echo "1. Enable Cloud Build API:"
echo "   gcloud services enable cloudbuild.googleapis.com"
echo ""
echo "2. Grant Cloud Build permissions:"
echo "   gcloud projects add-iam-policy-binding silken-zenith-460615-s7 \\"
echo "     --member=serviceAccount:$(gcloud projects describe silken-zenith-460615-s7 --format='value(projectNumber)')@cloudbuild.gserviceaccount.com \\"
echo "     --role=roles/container.developer"
echo ""
echo "3. The cloudbuild-prompts.yaml file is already configured"
echo ""

# Final instructions
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "Next steps:"
echo "1. Set up the webhook in GitHub (instructions above)"
echo "2. Test the GitOps integration in the PromptOps dashboard"
echo "3. Try exporting prompts to Git"
echo "4. Make changes in Git and watch them auto-deploy"
echo ""
echo "Useful commands:"
echo "- Check integration service logs: kubectl logs -f deployment/integration-service"
echo "- Check GitOps status: curl http://localhost:8080/gitops/status (with port-forward)"
echo "- Port forward for testing: kubectl port-forward service/integration-service 8080:8080"
echo ""
echo -e "${GREEN}🚀 PromptOps GitOps is ready to use!${NC}"
