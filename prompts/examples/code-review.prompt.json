{"id": "code-review-assistant", "name": "Code Review Assistant", "version": "1.1.0", "content": "Please review the following {{language}} code and provide detailed feedback:\n\n```{{language}}\n{{code_snippet}}\n```\n\n**Review Focus Areas:**\n{{focus_areas}}\n\n**Review Criteria:**\n- Code quality and readability\n- Performance considerations\n- Security best practices\n- Maintainability\n- {{custom_criteria}}\n\nPlease provide:\n1. **Summary**: Overall assessment of the code\n2. **Issues Found**: List any problems or concerns\n3. **Suggestions**: Specific improvement recommendations\n4. **Best Practices**: Relevant coding standards and patterns\n5. **Security**: Any security considerations\n\n**Severity Levels**: Use HIGH, MEDIUM, LOW for issue prioritization.\n\nFormat your response clearly with sections and bullet points for easy reading.", "variables": [{"name": "language", "type": "string", "description": "Programming language of the code being reviewed", "required": true, "default_value": "JavaScript", "validation": "^[A-Za-z+#]{1,20}$"}, {"name": "code_snippet", "type": "text", "description": "The actual code to be reviewed", "required": true, "default_value": "", "validation": "^[\\s\\S]{10,5000}$"}, {"name": "focus_areas", "type": "string", "description": "Specific areas to focus the review on", "required": false, "default_value": "Performance, security, and code organization", "validation": "^[A-Za-z0-9\\s,.-]{1,200}$"}, {"name": "custom_criteria", "type": "string", "description": "Additional custom review criteria", "required": false, "default_value": "Team coding standards compliance", "validation": "^[A-Za-z0-9\\s,.-]{1,150}$"}], "tags": ["code-review", "development", "quality-assurance", "programming"], "model_targets": ["gpt-4", "claude-3", "gpt-3.5-turbo"], "use_case": "development-tools", "metadata": {"author": "dev-team", "created_at": "2024-01-10T14:30:00Z", "updated_at": "2024-01-20T09:15:00Z", "environment": "production", "category": "development", "language": "en", "tone": "professional", "complexity": "intermediate", "usage_notes": "Ideal for automated code review processes and developer assistance", "example_usage": "Integrate with CI/CD pipelines, IDE extensions, or code review tools", "supported_languages": ["JavaScript", "Python", "Java", "C#", "Go", "Rust", "TypeScript"], "changelog": {"1.1.0": "Added custom criteria variable and improved formatting", "1.0.0": "Initial version with basic code review functionality"}}}