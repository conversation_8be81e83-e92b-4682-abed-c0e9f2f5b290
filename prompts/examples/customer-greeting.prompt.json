{"id": "customer-greeting", "name": "Customer Service Greeting", "version": "1.0.0", "content": "Hello {{customer_name}}! Welcome to {{company_name}}. I'm {{agent_name}}, your AI assistant. I'm here to help you with {{service_type}}. How can I assist you today?\n\nPlease feel free to ask me about:\n- {{service_options}}\n- General inquiries\n- Technical support\n\nI'll do my best to provide you with accurate and helpful information.", "variables": [{"name": "customer_name", "type": "string", "description": "The customer's name for personalization", "required": true, "default_value": "", "validation": "^[A-Za-z\\s]{1,50}$"}, {"name": "company_name", "type": "string", "description": "Name of the company providing service", "required": true, "default_value": "AI Operations Hub", "validation": "^[A-Za-z0-9\\s]{1,100}$"}, {"name": "agent_name", "type": "string", "description": "Name of the AI assistant", "required": false, "default_value": "<PERSON>", "validation": "^[A-Za-z\\s]{1,30}$"}, {"name": "service_type", "type": "string", "description": "Type of service being offered", "required": true, "default_value": "customer support", "validation": "^[A-Za-z\\s]{1,50}$"}, {"name": "service_options", "type": "string", "description": "List of available service options", "required": false, "default_value": "Account management, billing questions, technical issues", "validation": "^[A-Za-z0-9\\s,.-]{1,200}$"}], "tags": ["customer-service", "greeting", "personalization", "support"], "model_targets": ["gpt-4", "gpt-3.5-turbo", "claude-3", "claude-2"], "use_case": "customer-support", "metadata": {"author": "promptops-team", "created_at": "2024-01-15T10:00:00Z", "updated_at": "2024-01-15T10:00:00Z", "environment": "production", "category": "customer-service", "language": "en", "tone": "friendly", "complexity": "simple", "usage_notes": "Use this prompt to start customer service conversations with a warm, personalized greeting", "example_usage": "Perfect for chat interfaces, customer support portals, and help desk systems"}}