{"id": "data-analysis-assistant", "name": "Data Analysis Assistant", "version": "2.0.0", "content": "I'll help you analyze the {{data_type}} data you've provided. Let me examine the dataset and provide insights.\n\n**Dataset Overview:**\n- Data Type: {{data_type}}\n- Analysis Focus: {{analysis_focus}}\n- Key Metrics: {{key_metrics}}\n\n**Data to Analyze:**\n{{dataset}}\n\n**Analysis Approach:**\n1. **Data Quality Assessment**: Check for completeness, accuracy, and consistency\n2. **Descriptive Statistics**: Calculate mean, median, mode, standard deviation\n3. **Pattern Recognition**: Identify trends, correlations, and anomalies\n4. **{{analysis_type}} Analysis**: Perform specific analysis based on your requirements\n5. **Insights & Recommendations**: Provide actionable insights\n\n**Please provide:**\n- Summary statistics\n- Key findings and patterns\n- Visual representation suggestions\n- Recommendations for {{business_context}}\n- Potential data quality issues\n- Next steps for deeper analysis\n\n**Output Format**: {{output_format}}\n\nLet me analyze this data systematically and provide you with comprehensive insights.", "variables": [{"name": "data_type", "type": "string", "description": "Type of data being analyzed (e.g., sales, customer, financial)", "required": true, "default_value": "business", "validation": "^[A-Za-z\\s-]{1,30}$"}, {"name": "dataset", "type": "text", "description": "The actual dataset or data description to analyze", "required": true, "default_value": "", "validation": "^[\\s\\S]{20,10000}$"}, {"name": "analysis_focus", "type": "string", "description": "Specific aspect of data to focus analysis on", "required": true, "default_value": "trends and patterns", "validation": "^[A-Za-z0-9\\s,.-]{1,100}$"}, {"name": "key_metrics", "type": "string", "description": "Important metrics to calculate and analyze", "required": false, "default_value": "growth rate, conversion rate, average values", "validation": "^[A-Za-z0-9\\s,.-]{1,150}$"}, {"name": "analysis_type", "type": "string", "description": "Type of analysis to perform (descriptive, predictive, prescriptive)", "required": false, "default_value": "descriptive", "validation": "^(descriptive|predictive|prescriptive|exploratory)$"}, {"name": "business_context", "type": "string", "description": "Business context for recommendations", "required": false, "default_value": "business growth and optimization", "validation": "^[A-Za-z0-9\\s,.-]{1,100}$"}, {"name": "output_format", "type": "string", "description": "Preferred format for analysis output", "required": false, "default_value": "structured report with bullet points", "validation": "^[A-Za-z0-9\\s,.-]{1,50}$"}], "tags": ["data-analysis", "business-intelligence", "statistics", "insights"], "model_targets": ["gpt-4", "claude-3", "gpt-3.5-turbo"], "use_case": "data-analytics", "metadata": {"author": "analytics-team", "created_at": "2024-01-05T11:20:00Z", "updated_at": "2024-01-25T16:45:00Z", "environment": "production", "category": "analytics", "language": "en", "tone": "analytical", "complexity": "advanced", "usage_notes": "Comprehensive data analysis prompt for business intelligence and decision making", "example_usage": "Business dashboards, automated reporting, data science workflows", "supported_data_types": ["sales", "customer", "financial", "operational", "marketing", "web analytics"], "analysis_capabilities": ["descriptive", "diagnostic", "predictive", "prescriptive"], "changelog": {"2.0.0": "Major update with enhanced analysis types and business context integration", "1.2.0": "Added output format customization and key metrics specification", "1.1.0": "Improved data quality assessment and pattern recognition", "1.0.0": "Initial version with basic data analysis functionality"}}}