# AI Operations Hub - Prompt Repository

This directory contains prompt manifests for the AI Operations Hub PromptOps GitOps integration.

## Structure

```
prompts/
├── README.md                    # This file
├── examples/                    # Example prompts
│   ├── customer-greeting.prompt.json
│   ├── code-review.prompt.json
│   └── data-analysis.prompt.json
├── production/                  # Production prompts
└── templates/                   # Prompt templates
```

## Prompt Manifest Format

Each prompt is stored as a JSON file with the following structure:

```json
{
  "id": "unique-prompt-id",
  "name": "Human Readable Name",
  "version": "1.0.0",
  "content": "Your prompt content with {{variables}}",
  "variables": [
    {
      "name": "variable_name",
      "type": "string",
      "description": "Variable description",
      "required": true,
      "default_value": ""
    }
  ],
  "tags": ["tag1", "tag2"],
  "model_targets": ["gpt-4", "claude-3"],
  "use_case": "category",
  "metadata": {
    "author": "author-name",
    "created_at": "2024-01-15T10:00:00Z",
    "environment": "production"
  }
}
```

## GitOps Workflow

1. **Development**: Create/edit prompts in PromptOps dashboard
2. **Export**: Use "Export to Git" to push prompts to this repository
3. **Review**: Create pull requests for prompt changes
4. **Deploy**: Merge to main branch triggers automatic deployment
5. **Monitor**: Check deployment status in PromptOps dashboard

## File Naming Convention

- Use kebab-case for prompt IDs and filenames
- Include `.prompt.json` extension
- Example: `customer-greeting.prompt.json`

## Version Control

- Use semantic versioning (e.g., 1.0.0, 1.1.0, 2.0.0)
- Increment patch version for content fixes
- Increment minor version for new variables or features
- Increment major version for breaking changes

## Best Practices

1. **Test First**: Always test prompts in the playground before committing
2. **Clear Descriptions**: Provide detailed variable descriptions
3. **Meaningful Tags**: Use consistent tagging for organization
4. **Version Bumps**: Update version numbers when making changes
5. **Documentation**: Include usage examples in metadata
