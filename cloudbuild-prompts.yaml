# Cloud Build configuration for PromptOps GitOps deployments
# This build is triggered by the integration service when prompt changes are detected

options:
  default_logs_bucket_behavior: REGIONAL_USER_OWNED_BUCKET

steps:
# Step 1: Validate prompts
- name: 'gcr.io/cloud-builders/docker'
  id: 'Validate Prompts'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      echo "Validating prompt manifests..."
      
      # Check for required files
      if [ ! -d "prompts/" ]; then
        echo "Error: prompts/ directory not found"
        exit 1
      fi
      
      # Validate each prompt file
      for file in prompts/*.prompt.yaml prompts/*.prompt.json; do
        if [ -f "$file" ]; then
          echo "Validating $file..."
          
          # Basic JSON/YAML validation
          if [[ "$file" == *.json ]]; then
            python3 -m json.tool "$file" > /dev/null || exit 1
          elif [[ "$file" == *.yaml ]]; then
            python3 -c "import yaml; yaml.safe_load(open('$file'))" || exit 1
          fi
          
          echo "✓ $file is valid"
        fi
      done
      
      echo "All prompt files validated successfully"

# Step 2: Build integration service with latest changes
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build Integration Service'
  args: [
    'build',
    '-f', 'k8s/integration-service/Dockerfile',
    '--no-cache',
    '-t', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-integration-service:${_COMMIT_SHA}',
    '-t', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-integration-service:latest',
    'k8s/integration-service'
  ]
  dir: '.'

# Step 3: Push integration service image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push Integration Service'
  args: [
    'push', 
    '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-integration-service:${_COMMIT_SHA}'
  ]
  waitFor: ['Build Integration Service']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Push Integration Service Latest'
  args: [
    'push', 
    '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-integration-service:latest'
  ]
  waitFor: ['Build Integration Service']

# Step 4: Get GKE credentials
- name: 'gcr.io/cloud-builders/gcloud'
  id: 'Get GKE Credentials'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      gcloud container clusters get-credentials "${_GKE_CLUSTER_NAME}" --region "${_GCP_REGION}" --project "${_GCP_PROJECT_ID}"
  waitFor: ['Push Integration Service Latest']

# Step 5: Deploy updated integration service
- name: 'gcr.io/cloud-builders/kubectl'
  id: 'Deploy Integration Service'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      # Update the integration service deployment with new image
      kubectl set image deployment/integration-service \
        integration-service=${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-integration-service:${_COMMIT_SHA}
      
      # Wait for rollout to complete
      kubectl rollout status deployment/integration-service --timeout=300s
      
      echo "Integration service deployed successfully"
  waitFor: ['Get GKE Credentials']

# Step 6: Import prompts to PromptOps service
- name: 'gcr.io/cloud-builders/curl'
  id: 'Import Prompts'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      echo "Importing prompts to PromptOps service..."
      
      # Wait for integration service to be ready
      sleep 30
      
      # Get integration service URL
      INTEGRATION_SERVICE_URL="http://integration-service:8080"
      
      # Trigger prompt import
      curl -X POST "$INTEGRATION_SERVICE_URL/promptops/import" \
        -H "Content-Type: application/json" \
        -d '{"source": "git", "branch": "${_BRANCH_NAME}", "commit": "${_COMMIT_SHA}"}' \
        --fail --show-error --silent || {
          echo "Failed to import prompts"
          exit 1
        }
      
      echo "Prompts imported successfully"
  waitFor: ['Deploy Integration Service']

# Step 7: Run validation tests
- name: 'gcr.io/cloud-builders/curl'
  id: 'Validate Deployment'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      echo "Validating PromptOps deployment..."
      
      INTEGRATION_SERVICE_URL="http://integration-service:8080"
      
      # Check integration service health
      curl -f "$INTEGRATION_SERVICE_URL/health" || {
        echo "Integration service health check failed"
        exit 1
      }
      
      # Validate prompts
      curl -X POST "$INTEGRATION_SERVICE_URL/promptops/validate" \
        --fail --show-error --silent || {
          echo "Prompt validation failed"
          exit 1
        }
      
      # Get deployment status
      curl -f "$INTEGRATION_SERVICE_URL/gitops/status" || {
        echo "GitOps status check failed"
        exit 1
      }
      
      echo "PromptOps deployment validated successfully"
  waitFor: ['Import Prompts']

# Step 8: Send notification (optional)
- name: 'gcr.io/cloud-builders/gcloud'
  id: 'Send Notification'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      echo "PromptOps GitOps deployment completed successfully!"
      echo "Commit: ${_COMMIT_SHA}"
      echo "Branch: ${_BRANCH_NAME}"
      echo "Build ID: ${BUILD_ID}"
      
      # You can add Slack/email notifications here
      # Example: curl -X POST $SLACK_WEBHOOK_URL -d '{"text":"PromptOps deployed successfully"}'
  waitFor: ['Validate Deployment']

# Substitution variables
substitutions:
  _GCP_PROJECT_ID: 'silken-zenith-460615-s7'
  _GCP_REGION: 'us-central1'
  _GKE_CLUSTER_NAME: 'ai-optimizer-cluster'
  _ARTIFACT_REGISTRY_REPO: 'ai-optimizer-repo'
  _COMMIT_SHA: '${SHORT_SHA}'
  _BRANCH_NAME: '${BRANCH_NAME}'

# Timeout for the entire build
timeout: '1200s'
