# Google Cloud Build configuration for AI Operations Hub - LANDING PAGE
# Static landing page deployment for product overview and edition selection

steps:
# Get GKE credentials
- name: 'gcr.io/cloud-builders/gcloud'
  id: 'Get GKE Credentials'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      gcloud container clusters get-credentials "${_GKE_CLUSTER_NAME}" --region "${_GCP_REGION}" --project "${_GCP_PROJECT_ID}"

# Build Landing Page
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build landing-page'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_LANDING_PAGE}" = "true" ]; then
        echo "Building landing page..."
        docker build --no-cache \
          -f 'k8s/landing-page/Dockerfile' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-landing-page:latest" \
          'k8s/landing-page'
      else
        echo "Skipping landing page build (disabled)"
      fi
  dir: '.'

- name: 'gcr.io/cloud-builders/docker'
  id: 'Push landing-page'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_LANDING_PAGE}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-landing-page:latest"
      else
        echo "Skipping landing page push (build disabled)"
      fi
  waitFor: ['Build landing-page']

# Deploy Landing Page
- name: 'gcr.io/cloud-builders/kubectl'
  id: 'Deploy Landing Page'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      # Install gettext for envsubst
      echo "📦 Installing required packages..."
      apt-get update && apt-get install -y gettext-base

      # Export environment variables for landing page deployment
      export GCP_PROJECT_ID="${_GCP_PROJECT_ID}"
      export GCP_REGION="${_GCP_REGION}"
      export GKE_CLUSTER_NAME="${_GKE_CLUSTER_NAME}"
      export ARTIFACT_REGISTRY_REPO="${_ARTIFACT_REGISTRY_REPO}"
      export BUILD_ID="${_BUILD_ID}"

      # Export landing page image name
      export LANDING_PAGE_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-landing-page:latest"

      # Deploy landing page
      if [ "${_DEPLOY_LANDING_PAGE}" = "true" ]; then
        echo "🏠 Deploying AI Operations Hub Landing Page..."

        # First delete any existing ingress conflicts
        kubectl delete ingress comprehensive-ingress --ignore-not-found=true

        # Deploy with environment substitution
        envsubst < k8s/landing-page/landing-page.yaml | kubectl apply -f -

        # Wait for deployment to be ready
        kubectl wait --for=condition=ready pod -l app=landing-page --timeout=300s

        echo "✅ Landing page deployment completed successfully!"
        echo "🌐 Access at: https://scale-llm.com"
      else
        echo "⏭️  Skipping deployment (disabled)"
        echo "📦 Image built and pushed successfully"
      fi
  dir: '.'
  waitFor: ['Get GKE Credentials', 'Push landing-page']

# Define substitute variables
substitutions:
  _GCP_PROJECT_ID: 'silken-zenith-460615-s7'
  _GCP_REGION: 'us-central1'
  _GKE_CLUSTER_NAME: 'ai-optimizer-cluster'
  _ARTIFACT_REGISTRY_REPO: 'ai-optimizer-repo'
  _BUILD_ID: '${BUILD_ID}' # Cloud Build's built-in BUILD_ID

  # --- Landing Page Build Flags ---
  _BUILD_LANDING_PAGE: 'true'

  # --- Landing Page Deploy Flags ---
  _DEPLOY_LANDING_PAGE: 'true'

options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'
