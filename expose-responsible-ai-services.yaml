apiVersion: v1
kind: Service
metadata:
  name: bias-detection-loadbalancer
spec:
  type: LoadBalancer
  ports:
  - port: 8084
    targetPort: 8084
    protocol: TCP
  selector:
    app: bias-detection-service
---
apiVersion: v1
kind: Service
metadata:
  name: explainability-loadbalancer
spec:
  type: LoadBalancer
  ports:
  - port: 8085
    targetPort: 8085
    protocol: TCP
  selector:
    app: explainability-service
---
apiVersion: v1
kind: Service
metadata:
  name: robustness-testing-loadbalancer
spec:
  type: LoadBalancer
  ports:
  - port: 8086
    targetPort: 8086
    protocol: TCP
  selector:
    app: robustness-testing-service
---
apiVersion: v1
kind: Service
metadata:
  name: compliance-loadbalancer
spec:
  type: LoadBalancer
  ports:
  - port: 8087
    targetPort: 8087
    protocol: TCP
  selector:
    app: compliance-service
---
apiVersion: v1
kind: Service
metadata:
  name: governance-loadbalancer
spec:
  type: LoadBalancer
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
  selector:
    app: governance-service
