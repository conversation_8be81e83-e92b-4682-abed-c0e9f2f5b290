# cluster-issuer.yaml
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod # Use 'letsencrypt-staging' for testing to avoid hitting rate limits
spec:
  acme:
    email: phanid<PERSON><PERSON>@gmail.com # REPLACE with your actual email
    server: https://acme-v02.api.letsencrypt.org/directory # Production ACME server
    privateKeySecretRef:
      name: letsencrypt-prod-account-key
    solvers:
    - http01:
        ingress:
          class: nginx # Specify your Ingress Controller class
