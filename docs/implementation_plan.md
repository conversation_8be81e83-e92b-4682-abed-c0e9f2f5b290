# Intelligent Routing Implementation Plan

## Quick Start: Minimum Viable Implementation

### Phase 1: Basic Semantic Routing (Week 1)

#### Step 1: Enhance Proxy Gateway with Request Analysis
**File**: `k8s/proxy-gateway/main.go`

Add basic request analysis before routing:

```go
type RequestAnalysis struct {
    Intent       string   `json:"intent"`
    Complexity   string   `json:"complexity"`
    TaskType     string   `json:"task_type"`
    Keywords     []string `json:"keywords"`
    EstimatedTokens int   `json:"estimated_tokens"`
}

func analyzeRequest(prompt string) RequestAnalysis {
    // Basic keyword-based analysis
    analysis := RequestAnalysis{
        EstimatedTokens: len(strings.Fields(prompt)) * 2, // Rough estimate
    }
    
    // Intent detection
    if containsAny(prompt, []string{"write", "create", "generate"}) {
        analysis.Intent = "generation"
    } else if containsAny(prompt, []string{"review", "analyze", "check"}) {
        analysis.Intent = "analysis"
    } else if containsAny(prompt, []string{"what", "how", "why", "when"}) {
        analysis.Intent = "question"
    }
    
    // Task type classification
    if containsAny(prompt, []string{"code", "function", "python", "javascript"}) {
        analysis.TaskType = "code_generation"
    } else if containsAny(prompt, []string{"story", "creative", "poem"}) {
        analysis.TaskType = "creative_writing"
    } else if containsAny(prompt, []string{"summarize", "summary"}) {
        analysis.TaskType = "summarization"
    } else {
        analysis.TaskType = "general"
    }
    
    // Complexity assessment
    if len(prompt) > 1000 || containsAny(prompt, []string{"complex", "detailed", "comprehensive"}) {
        analysis.Complexity = "high"
    } else if len(prompt) > 200 {
        analysis.Complexity = "medium"
    } else {
        analysis.Complexity = "low"
    }
    
    return analysis
}
```

#### Step 2: Enhance Model Profiles with Capabilities
**File**: `k8s/policy-manager/main.go`

Extend ModelProfile struct:

```go
type ModelProfile struct {
    // Existing fields...
    ID                    string  `json:"id"`
    Name                  string  `json:"name"`
    CostPerInputToken     float64 `json:"cost_per_input_token"`
    CostPerOutputToken    float64 `json:"cost_per_output_token"`
    ExpectedLatencyMs     int32   `json:"expected_latency_ms"`
    
    // New capability fields
    Capabilities          map[string]float64 `json:"capabilities"`
    OptimalUseCases       []string          `json:"optimal_use_cases"`
    Strengths             []string          `json:"strengths"`
    Weaknesses            []string          `json:"weaknesses"`
    MaxContextLength      int               `json:"max_context_length"`
    QualityScore          float64           `json:"quality_score"`
    RobustnessScore       float64           `json:"robustness_score"`
}
```

#### Step 3: Upgrade AI Optimizer Routing Logic
**File**: `k8s/ai-optimizer/main.go`

Add capability-based routing:

```go
type RoutingRequest struct {
    // Existing fields...
    Prompt      string `json:"prompt"`
    Model       string `json:"model"`
    
    // New analysis fields
    Analysis    RequestAnalysis `json:"analysis"`
    UserPrefs   UserPreferences `json:"user_preferences"`
}

type UserPreferences struct {
    Priority    string  `json:"priority"` // "cost", "quality", "speed", "balanced"
    MaxCost     float64 `json:"max_cost,omitempty"`
    MaxLatency  int     `json:"max_latency_ms,omitempty"`
}

func selectOptimalModel(req RoutingRequest, models map[string]ModelProfile) string {
    scores := make(map[string]float64)
    
    for modelID, profile := range models {
        score := calculateModelScore(req.Analysis, profile, req.UserPrefs)
        scores[modelID] = score
    }
    
    // Return model with highest score
    return getHighestScoringModel(scores)
}

func calculateModelScore(analysis RequestAnalysis, profile ModelProfile, prefs UserPreferences) float64 {
    var score float64
    
    // Capability matching (40% weight)
    capabilityScore := getCapabilityScore(analysis.TaskType, profile)
    score += capabilityScore * 0.4
    
    // Cost efficiency (30% weight if cost priority)
    costScore := 1.0 - (profile.CostPerInputToken / 0.1) // Normalize to 0-1
    if prefs.Priority == "cost" {
        score += costScore * 0.5
    } else {
        score += costScore * 0.2
    }
    
    // Quality (30% weight if quality priority)
    if prefs.Priority == "quality" {
        score += profile.QualityScore * 0.5
    } else {
        score += profile.QualityScore * 0.2
    }
    
    // Latency (adjust based on complexity)
    latencyScore := 1.0 - (float64(profile.ExpectedLatencyMs) / 10000.0)
    if prefs.Priority == "speed" || analysis.Complexity == "low" {
        score += latencyScore * 0.3
    } else {
        score += latencyScore * 0.1
    }
    
    return score
}

func getCapabilityScore(taskType string, profile ModelProfile) float64 {
    if capability, exists := profile.Capabilities[taskType]; exists {
        return capability
    }
    
    // Default scoring based on use cases
    for _, useCase := range profile.OptimalUseCases {
        if useCase == taskType {
            return 0.9
        }
    }
    
    return 0.5 // Default moderate capability
}
```

### Phase 2: Enhanced Intelligence (Week 2)

#### Step 1: Create Request Analyzer Service
**New File**: `k8s/request-analyzer/main.go`

```go
package main

import (
    "encoding/json"
    "net/http"
    "strings"
    "regexp"
)

type RequestAnalyzer struct {
    // Service implementation
}

type AnalysisRequest struct {
    RequestID string `json:"request_id"`
    Prompt    string `json:"prompt"`
    Context   string `json:"context,omitempty"`
}

type AnalysisResponse struct {
    RequestID   string            `json:"request_id"`
    Intent      string            `json:"intent"`
    Complexity  string            `json:"complexity"`
    Domain      string            `json:"domain"`
    TaskType    string            `json:"task_type"`
    Keywords    []string          `json:"keywords"`
    Confidence  float64           `json:"confidence"`
    Metadata    map[string]string `json:"metadata"`
}

func (ra *RequestAnalyzer) analyzeRequest(w http.ResponseWriter, r *http.Request) {
    var req AnalysisRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }
    
    analysis := ra.performAnalysis(req.Prompt)
    analysis.RequestID = req.RequestID
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(analysis)
}

func (ra *RequestAnalyzer) performAnalysis(prompt string) AnalysisResponse {
    // Enhanced analysis logic
    return AnalysisResponse{
        Intent:     extractIntent(prompt),
        Complexity: assessComplexity(prompt),
        Domain:     identifyDomain(prompt),
        TaskType:   classifyTaskType(prompt),
        Keywords:   extractKeywords(prompt),
        Confidence: calculateConfidence(prompt),
    }
}
```

#### Step 2: Add Sample Model Capabilities
**File**: `k8s/policy-manager/main.go`

Add sample model profiles with capabilities:

```go
func createSampleModelProfiles(ctx context.Context) {
    sampleProfiles := []ModelProfile{
        {
            ID:   "gpt-4",
            Name: "GPT-4",
            Capabilities: map[string]float64{
                "code_generation":   0.95,
                "reasoning":         0.95,
                "creative_writing":  0.90,
                "analysis":          0.92,
                "factual_qa":        0.85,
                "summarization":     0.88,
                "translation":       0.80,
            },
            OptimalUseCases: []string{"code_generation", "complex_reasoning", "analysis"},
            Strengths:       []string{"complex_reasoning", "code_understanding", "analytical_tasks"},
            Weaknesses:      []string{"cost", "speed", "real_time_data"},
            QualityScore:    0.92,
            RobustnessScore: 0.88,
            MaxContextLength: 8192,
            CostPerInputToken: 0.03,
            CostPerOutputToken: 0.06,
            ExpectedLatencyMs: 2500,
        },
        {
            ID:   "gpt-3.5-turbo",
            Name: "GPT-3.5 Turbo",
            Capabilities: map[string]float64{
                "code_generation":   0.80,
                "reasoning":         0.75,
                "creative_writing":  0.85,
                "analysis":          0.70,
                "factual_qa":        0.80,
                "summarization":     0.85,
                "translation":       0.75,
            },
            OptimalUseCases: []string{"general_tasks", "simple_qa", "basic_writing"},
            Strengths:       []string{"speed", "cost_efficiency", "general_purpose"},
            Weaknesses:      []string{"complex_reasoning", "specialized_knowledge"},
            QualityScore:    0.78,
            RobustnessScore: 0.82,
            MaxContextLength: 4096,
            CostPerInputToken: 0.001,
            CostPerOutputToken: 0.002,
            ExpectedLatencyMs: 800,
        },
        {
            ID:   "claude-3-sonnet",
            Name: "Claude-3 Sonnet",
            Capabilities: map[string]float64{
                "code_generation":   0.88,
                "reasoning":         0.90,
                "creative_writing":  0.92,
                "analysis":          0.90,
                "factual_qa":        0.88,
                "summarization":     0.90,
                "translation":       0.85,
            },
            OptimalUseCases: []string{"long_context", "safety_critical", "creative_writing"},
            Strengths:       []string{"safety", "long_context", "nuanced_understanding"},
            Weaknesses:      []string{"speed", "cost", "code_specificity"},
            QualityScore:    0.89,
            RobustnessScore: 0.92,
            MaxContextLength: 200000,
            CostPerInputToken: 0.003,
            CostPerOutputToken: 0.015,
            ExpectedLatencyMs: 1800,
        },
    }
    
    // Save to Redis and memory
    for _, profile := range sampleProfiles {
        saveModelProfile(ctx, profile)
    }
}
```

### Phase 3: Integration & Testing (Week 3)

#### Step 1: Update Frontend Dashboard
Add routing insights to the dashboard:

```javascript
// New component: RoutingInsights.jsx
const RoutingInsights = () => {
  const [routingStats, setRoutingStats] = useState({});
  
  return (
    <div className="routing-insights">
      <h3>Intelligent Routing Analytics</h3>
      <div className="metrics-grid">
        <MetricCard 
          title="Capability Match Rate"
          value={`${(routingStats.capabilityMatchRate * 100).toFixed(1)}%`}
          description="Requests routed to optimal models"
        />
        <MetricCard 
          title="Quality Improvement"
          value={`+${(routingStats.qualityImprovement * 100).toFixed(1)}%`}
          description="vs. random routing"
        />
        <MetricCard 
          title="Cost Optimization"
          value={`${(routingStats.costSavings * 100).toFixed(1)}%`}
          description="Cost savings from smart routing"
        />
      </div>
    </div>
  );
};
```

#### Step 2: Add Monitoring & Metrics
Track routing effectiveness:

```go
type RoutingMetrics struct {
    TotalRequests       int64   `json:"total_requests"`
    CapabilityMatches   int64   `json:"capability_matches"`
    QualityScore        float64 `json:"avg_quality_score"`
    CostSavings         float64 `json:"cost_savings"`
    LatencyImprovement  float64 `json:"latency_improvement"`
}
```

## Expected Outcomes

### Week 1 Results
- Basic semantic routing operational
- 20-30% improvement in task-model matching
- Foundation for advanced features

### Week 2 Results
- Enhanced analysis capabilities
- 40-50% improvement in routing accuracy
- Better cost optimization for different task types

### Week 3 Results
- Full intelligent routing system
- Comprehensive analytics and monitoring
- 60-70% improvement in overall routing effectiveness

## Next Steps

1. **Start with Phase 1** - Basic implementation that provides immediate value
2. **Gather feedback** - Monitor routing decisions and outcomes
3. **Iterate quickly** - Improve analysis and routing algorithms based on real data
4. **Scale gradually** - Add more sophisticated analysis as the system learns

This approach gives you a working intelligent routing system quickly while building toward a more sophisticated solution.
