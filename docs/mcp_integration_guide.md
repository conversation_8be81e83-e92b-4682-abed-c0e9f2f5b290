# Model Context Protocol (MCP) Integration Guide

## Overview

The AI Operations Hub now supports the Model Context Protocol (MCP), a standardized way to connect AI applications with external data sources and tools. This integration transforms our microservices architecture into a composable AI ecosystem that external applications can easily integrate with.

## What is MCP?

Model Context Protocol (MCP) is an open standard that enables seamless integration between AI applications and external resources. Think of it as "USB-C for AI" - a universal connector that allows any MCP-compatible client to access your AI Operations Hub's capabilities.

### Key Benefits

- **Standardized Access**: Use any MCP-compatible client to access AI Operations Hub
- **Composable Workflows**: Combine AI Operations Hub tools with other MCP servers
- **Rich Context**: Access to comprehensive AI performance and cost data
- **Enhanced Integrations**: Connect with Claude Desktop, IDEs, and other AI tools

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                    External MCP Clients                        │
│  (Claude Desktop, IDEs, Custom AI Apps, etc.)                  │
└─────────────────────┬───────────────────────────────────────────┘
                      │ MCP JSON-RPC over WebSocket/HTTP
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                 Proxy-Gateway (MCP Host)                       │
│  • MCP Client Management                                       │
│  • Connection Routing                                          │
│  • Security & Authentication                                   │
│  • Capability Negotiation                                      │
│  • Existing LLM Routing (unchanged)                           │
└─────────────────────┬───────────────────────────────────────────┘
                      │ Internal MCP Connections
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                    MCP Server Layer                            │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │AI Optimizer │  │Planning Svc │  │Evaluation   │            │
│  │MCP Server   │  │MCP Server   │  │Service      │            │
│  │             │  │             │  │MCP Server   │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
│                                                                 │
│  ┌─────────────┐                                               │
│  │Integration  │                                               │
│  │Service      │                                               │
│  │MCP Server   │                                               │
│  └─────────────┘                                               │
└─────────────────────────────────────────────────────────────────┘
```

## Available MCP Servers

### 1. AI Optimizer MCP Server (Port 8085)

**Endpoint**: `ws://localhost:8085/mcp` or `https://scale-llm.com/ai-optimizer/mcp`

**Capabilities**: Tools, Resources

**Tools**:
- `optimize_llm_routing`: Intelligent LLM selection based on prompt analysis
- `get_cost_analysis`: Real-time cost analysis for different model options
- `analyze_prompt`: Advanced prompt classification and requirement analysis
- `get_performance_metrics`: Live performance data for all models

**Resources**:
- `ai-optimizer://model-profiles`: Current model capabilities and pricing
- `ai-optimizer://routing-policies`: Active routing rules and preferences
- `ai-optimizer://optimization-history`: Historical routing decisions and outcomes

### 2. Planning Service MCP Server (Port 8082)

**Endpoint**: `ws://localhost:8082/mcp` or `https://scale-llm.com/planning-service/mcp`

**Capabilities**: Tools, Prompts, Resources

**Tools**:
- `create_goal`: Create new autonomous goals for execution
- `decompose_goal`: Break down high-level goals into executable tasks
- `execute_plan`: Execute planned task sequences
- `monitor_execution`: Track goal execution progress

**Prompts**:
- `goal_creation_wizard`: Interactive goal creation with best practices
- `workflow_optimization`: Template for optimizing multi-step workflows
- `execution_monitoring`: Guided execution monitoring and troubleshooting

**Resources**:
- `planning://active-goals`: Currently executing goals and their status
- `planning://execution-history`: Historical goal execution data
- `planning://task-templates`: Reusable task patterns and workflows

### 3. Evaluation Service MCP Server (Port 8088)

**Endpoint**: `ws://localhost:8088/mcp` or `https://scale-llm.com/evaluation-service/mcp`

**Capabilities**: Tools, Resources

**Tools**:
- `evaluate_llm_response`: Comprehensive LLM response evaluation
- `run_quality_assessment`: Multi-dimensional quality analysis
- `perform_bias_detection`: Automated bias detection in responses
- `generate_evaluation_report`: Detailed evaluation reporting

**Resources**:
- `evaluation://metrics`: Available evaluation criteria and scoring methods
- `evaluation://benchmarks`: Performance benchmarks for different models
- `evaluation://history`: Historical evaluation results and trends

### 4. Integration Service MCP Server (Port 8080)

**Endpoint**: `ws://localhost:8080/mcp` or `https://scale-llm.com/integration-service/mcp`

**Capabilities**: Tools, Resources

**Tools**:
- `deploy_prompts`: Deploy prompts via GitOps workflow
- `sync_repository`: Synchronize with Git repositories
- `validate_prompts`: Validate prompt configurations
- `export_configurations`: Export system configurations

**Resources**:
- `integration://repositories`: Connected Git repositories and their status
- `integration://deployments`: GitOps deployment history and status
- `integration://templates`: Available prompt templates and configurations

## Quick Start Guide

### 1. Connect with Claude Desktop

1. Install Claude Desktop (if not already installed)
2. Open Claude Desktop settings
3. Add MCP server configuration:

```json
{
  "mcpServers": {
    "ai-operations-hub": {
      "command": "npx",
      "args": ["@modelcontextprotocol/server-websocket", "ws://localhost:8080/mcp/connect"],
      "env": {
        "X-User-ID": "your-user-id"
      }
    }
  }
}
```

4. Restart Claude Desktop
5. You should now see AI Operations Hub tools available in Claude

### 2. Connect with MCP Inspector

MCP Inspector is a debugging tool for MCP connections:

```bash
# Install MCP Inspector
npm install -g @modelcontextprotocol/inspector

# Connect to AI Operations Hub
mcp-inspector ws://localhost:8080/mcp/connect
```

### 3. Custom Client Integration

For custom applications, use any WebSocket library to connect:

```javascript
const WebSocket = require('ws');

const ws = new WebSocket('ws://localhost:8080/mcp/connect', {
  headers: {
    'X-User-ID': 'your-user-id'
  }
});

ws.on('open', function open() {
  // Send MCP initialization
  ws.send(JSON.stringify({
    jsonrpc: "2.0",
    id: 1,
    method: "initialize",
    params: {
      protocolVersion: "2025-06-18",
      capabilities: {
        tools: {},
        resources: {}
      },
      clientInfo: {
        name: "my-custom-client",
        version: "1.0.0"
      }
    }
  }));
});

ws.on('message', function message(data) {
  const response = JSON.parse(data);
  console.log('Received:', response);
});
```

## Authentication & Security

### User Authentication

All MCP connections require user authentication via the `X-User-ID` header:

```javascript
const headers = {
  'X-User-ID': 'your-user-id'
};
```

### Permissions

Each user has specific permissions that control access to tools and resources:

- `tools`: Access to execute MCP tools
- `resources`: Access to read MCP resources
- `prompts`: Access to MCP prompt templates

### Rate Limiting

MCP connections are subject to the same rate limiting as regular API calls:

- 100 requests per minute per user
- 1000 requests per hour per user

## Error Handling

MCP follows JSON-RPC 2.0 error conventions:

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "error": {
    "code": -32602,
    "message": "Invalid params",
    "data": {
      "details": "Missing required parameter: prompt"
    }
  }
}
```

Common error codes:
- `-32700`: Parse error
- `-32600`: Invalid request
- `-32601`: Method not found
- `-32602`: Invalid params
- `-32603`: Internal error

## Monitoring & Management

### Frontend Dashboard

Access the MCP Management dashboard at `https://scale-llm.com` → MCP Management tab:

- View connected clients
- Monitor server status
- Manage server configurations
- View connection analytics

### Health Checks

Check MCP host status:
```bash
curl https://scale-llm.com/mcp/status
```

### Logs

MCP activity is logged in the proxy-gateway service logs:
```bash
kubectl logs -f deployment/proxy-gateway -n default
```

## Troubleshooting

### Connection Issues

1. **Cannot connect to MCP host**:
   - Check if proxy-gateway service is running
   - Verify WebSocket endpoint is accessible
   - Check authentication headers

2. **Server not responding**:
   - Check individual service health
   - Verify MCP server initialization
   - Check service logs for errors

3. **Permission denied**:
   - Verify user ID is correct
   - Check user permissions
   - Ensure proper authentication

### Performance Issues

1. **Slow responses**:
   - Check service resource usage
   - Monitor network latency
   - Review connection pool settings

2. **Connection timeouts**:
   - Increase timeout settings
   - Check network stability
   - Monitor service health

## Next Steps

1. **Explore Tools**: Try different MCP tools to understand capabilities
2. **Build Workflows**: Combine multiple tools for complex workflows
3. **Monitor Usage**: Use the dashboard to track MCP usage and performance
4. **Extend Integration**: Build custom clients for specific use cases

For more detailed examples and advanced configurations, see the [MCP Examples](./mcp_examples.md) documentation.
