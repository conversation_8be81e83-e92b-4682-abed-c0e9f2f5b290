
# 📘 Semantic Conventions Specification

> Version: 1.0  
> Owner: Platform Engineering  
> Updated: 2025-07-15

---

## 🎯 Purpose
To establish standardized naming, tagging, and structure conventions across all telemetry signals (metrics, logs, and traces) to ensure consistency, reliability, and compatibility with observability platforms.

---

## 🔑 Core Naming Conventions

### General Rules
- Use lowercase with underscores: `http_status_code`, not `HttpStatusCode`
- Use semantic field names consistent with [OpenTelemetry specs](https://opentelemetry.io/docs/specs/otel/semantic_conventions/)
- Avoid abbreviations unless industry-standard (e.g., `ip`, `id`)

---

## 🧩 Common Attributes (All Signals)

| Attribute       | Description                       | Example        |
|-----------------|---------------------------------|----------------|
| `service.name`  | Logical name of the service      | `checkout`     |
| `env`           | Environment                     | `prod`         |
| `region`        | Cloud or datacenter region       | `us-west-2`    |
| `host.name`     | Hostname or container ID         | `host-abc`     |
| `instance.id`   | Unique ID of instance or pod     | `pod-xyz`      |

---

## 📈 Metrics

### Naming Convention
`<component>.<operation>.<unit>`  
Example: `http.server.duration.seconds`

### Required Tags
- `service.name`
- `env`
- `region`
- `status_code` (for HTTP)
- `method`, `route` (for API traffic)

### Best Practices
- Avoid high-cardinality tags like user IDs
- Use enums for fields like `status`, `outcome`

---

## 📊 Traces

### Span Naming
Use clear, operation-based names: 
- `GET /api/orders`
- `DB SELECT users`

### Required Attributes

| Key                | Description                        | Example          |
|--------------------|----------------------------------|------------------|
| `http.method`      | HTTP method                      | `GET`            |
| `http.route`       | Route pattern                   | `/orders/:id`    |
| `http.status_code` | Response status code            | `200`            |
| `db.system`        | DB engine                       | `postgresql`     |
| `db.statement`     | Query (truncate sensitive data) | `SELECT * ...`   |
| `messaging.system` | Queue/broker name               | `kafka`          |

---

## 📄 Logs

### Format
- Structured JSON format
- Include trace correlation: `trace_id`, `span_id`

### Required Fields

| Field             | Description                     | Example            |
|-------------------|---------------------------------|--------------------|
| `log.level`       | Severity                        | `info`, `error`    |
| `event.name`      | Business event name             | `order_placed`     |
| `trace_id`        | Trace ID for correlation       | `abc123`           |
| `span_id`         | Span ID if available           | `xyz456`           |
| `exception.type`  | Error type (if applicable)     | `TimeoutException` |
| `error.message`   | Descriptive error message      | `DB timeout`       |

---

## 🧪 Validation Checklist

Before deploying or releasing telemetry, verify:

- [ ] Every **span** includes these required attributes:  
 • `service.name`  
 • `http.route` (or equivalent operation name)  
 • `http.status_code` (for HTTP spans)  
 • Trace context (`trace_id`, `span_id`)  

- [ ] All **metrics** follow the naming convention `<component>.<operation>.<unit>`, and have required tags like:  
 • `service.name`  
 • `env`  
 • `region`  
 • `status_code` (for HTTP metrics)  
 • Avoid high-cardinality tags such as user IDs or request IDs unless explicitly approved  

- [ ] **Logs** are structured (preferably JSON) and include:  
 • `log.level`  
 • `event.name`  
 • `trace_id` and `span_id` for correlation  
 • Relevant error/exception fields (`exception.type`, `error.message`) where applicable  

- [ ] High-cardinality fields are avoided or controlled to prevent metrics explosion and storage cost issues  

- [ ] Telemetry covers all critical business workflows end-to-end for reliable monitoring and troubleshooting  

- [ ] Consistency across services and teams in attribute naming and value usage  

---

## 🚀 Enforcement & Tooling

- OpenTelemetry SDK wrappers with preset tags
- CI/CD validators for schema compliance
- Collector processors (rename, drop, normalize tags)
- Dashboards/alerts to flag missing tags or malformed telemetry

---

## 📚 References
- [OpenTelemetry Semantic Conventions](https://opentelemetry.io/docs/specs/otel/semantic_conventions/)
- [Google SRE - Monitoring Philosophy](https://sre.google/sre-book/monitoring-distributed-systems/)
