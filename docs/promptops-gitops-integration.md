# PromptOps GitOps Integration

This document describes the comprehensive GitOps integration for PromptOps, enabling version-controlled prompt management, automated CI/CD deployments, and A/B test winner deployments.

## Overview

The GitOps integration provides enterprise-grade prompt management with:
- **Version-controlled prompt storage** in Git repositories
- **Automated deployment pipelines** via CI/CD integration
- **A/B test winner deployment** to production environments
- **Complete rollback capabilities** with audit trails
- **Collaborative development workflows** with approval processes
- **Multi-environment support** (dev, staging, production)
- **Real-time routing updates** for optimal performance

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   PromptOps     │    │  Integration     │    │   Git Repository│
│   Dashboard     │◄──►│   Service        │◄──►│   (GitHub/Lab)  │
│                 │    │                  │    │                 │
│ • A/B Testing   │    │ • GitOps Ops     │    │ • prompts/      │
│ • Deploy Winner │    │ • Webhooks       │    │ • production/   │
│ • Versioning    │    │ • Validation     │    │ • staging/      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  Cloud Build     │    │  Proxy Gateway  │
                       │  CI/CD Pipeline  │◄──►│  Routing Rules  │
                       └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │  Policy Manager  │
                       │  Production DB   │
                       └──────────────────┘
```

## Components

### 1. A/B Test Winner Deployment System 🚀

**NEW**: Automated deployment of A/B test winners to production with full GitOps integration.

#### Deployment Process

When you click "Deploy Winner" in the PromptOps dashboard:

1. **Winner Analysis**
   - Compares `variant_a_score` vs `variant_b_score` from real traffic data
   - Validates sufficient test data for reliable decision making
   - Determines optimal prompt/model combination

2. **Production Prompt Creation**
   ```javascript
   // Creates production-ready prompt
   {
     "id": "original-prompt-id-production",
     "name": "Original Prompt Name (Production)",
     "version": "1.0.0-prod",
     "status": "production",
     "tags": ["production", "ab-test-winner"],
     "metadata": {
       "deployed_from_ab_test": "ab-test-123",
       "ab_test_variant": "A",
       "deployment_date": "2025-01-15T10:30:00Z"
     }
   }
   ```

3. **Routing Policy Updates**
   - Creates high-priority routing policy in proxy-gateway
   - Ensures 100% of future traffic uses winning configuration
   - Maintains optimal performance routing

4. **GitOps Export**
   - Exports production prompt to `prompts/production/`
   - Triggers CI/CD pipeline for infrastructure deployment
   - Creates deployment manifest with full traceability

5. **Rollback Preparation**
   - Preserves previous production version
   - Maintains deployment history for audit trails
   - Enables instant revert capability

### 2. Enhanced Integration Service

The integration service (`k8s/integration-service/`) provides:

- **GitOps Operations**: Clone, sync, and manage Git repositories
- **A/B Test Integration**: Automated winner deployment workflows
- **Webhook Handling**: Process GitHub/GitLab webhooks for automated deployments
- **Prompt Management**: Export/import prompts between PromptOps and Git
- **Validation**: Ensure prompt manifests are valid before deployment
- **CI/CD Integration**: Trigger Cloud Build deployments
- **Production Routing**: Update proxy-gateway routing rules

### 3. Enhanced Cloud Build Pipeline

The `cloudbuild-prompts.yaml` configuration now supports:

- **A/B Test Deployments**: Automated winner deployment workflows
- **Validation**: Check prompt manifest syntax and structure
- **Multi-Environment**: Deploy to dev, staging, and production
- **Build**: Create updated integration service images
- **Deploy**: Update Kubernetes deployments with new routing rules
- **Import**: Load prompts from Git to PromptOps service
- **Rollback**: Automated rollback capabilities for failed deployments

### 4. Proxy Gateway Integration

The proxy-gateway service now includes:

- **A/B Test Routing**: Intelligent traffic splitting for active tests
- **Winner Deployment**: Automatic routing updates for deployed winners
- **Performance Tracking**: Real-time metrics collection for A/B tests
- **Production Routing**: High-priority policies for production prompts
- **Notification**: Send deployment status updates

### 5. Frontend GitOps UI

The PromptOps dashboard includes:

- **A/B Test Management**: Create, monitor, and deploy test winners
- **GitOps Status**: View current branch, commit, and sync status
- **Export/Import**: Manual prompt synchronization controls
- **Diff Viewer**: Compare prompts between service and Git
- **Repository Management**: Sync and manage Git operations
- **Deployment History**: Track all production deployments

## Deployment Workflows

### 1. A/B Test Winner Deployment 🎯

**Complete automated deployment of A/B test winners to production:**

```mermaid
graph TD
    A[A/B Test Running] --> B[Collect Performance Data]
    B --> C[User Clicks 'Deploy Winner']
    C --> D[Analyze Winner]
    D --> E[Create Production Prompt]
    E --> F[Update Routing Policy]
    F --> G[Export to GitOps]
    G --> H[Trigger CI/CD]
    H --> I[Deploy to Production]
    I --> J[Verify Deployment]
    J --> K[Send Notifications]
```

**Step-by-step process:**

1. **Performance Analysis**
   - Real traffic data from proxy-gateway
   - Quality scores based on success rate and cost efficiency
   - Statistical significance validation

2. **Production Deployment**
   - Creates `{prompt-id}-production` with production tags
   - Updates proxy-gateway routing for 100% traffic
   - Maintains rollback capability

3. **GitOps Integration**
   - Exports to `prompts/production/{prompt-id}.prompt.json`
   - Triggers automated CI/CD pipeline
   - Creates deployment audit trail

4. **Infrastructure Updates**
   - Policy-manager database updates
   - Proxy-gateway routing rule changes
   - Integration service synchronization

### 2. Manual Prompt Deployment

**Traditional GitOps workflow for manual prompt management:**

```mermaid
graph TD
    A[Edit Prompt in Dashboard] --> B[Export to Git]
    B --> C[Create Pull Request]
    C --> D[Code Review]
    D --> E[Merge to Main]
    E --> F[Webhook Trigger]
    F --> G[Cloud Build Pipeline]
    G --> H[Deploy to Environment]
```

### 3. Multi-Environment Deployment

**Support for dev, staging, and production environments:**

- **Development**: `prompts/dev/` - Experimental prompts
- **Staging**: `prompts/staging/` - Pre-production testing
- **Production**: `prompts/production/` - Live production prompts

## Setup Instructions

### 1. Configure Git Repository

Create a dedicated repository for your prompts:

```bash
# Create repository structure with multi-environment support
mkdir ai-prompts
cd ai-prompts

# Create environment-specific directories
mkdir -p prompts/dev
mkdir -p prompts/staging
mkdir -p prompts/production
mkdir -p prompts/templates
mkdir -p prompts/ab-tests

# Create documentation
echo "# AI Prompts Repository" > README.md
echo "## Directory Structure" >> README.md
echo "- \`prompts/dev/\` - Development prompts" >> README.md
echo "- \`prompts/staging/\` - Pre-production testing" >> README.md
echo "- \`prompts/production/\` - Live production prompts" >> README.md
echo "- \`prompts/templates/\` - Reusable prompt templates" >> README.md
echo "- \`prompts/ab-tests/\` - A/B test configurations" >> README.md

# Initialize Git
git init
git add .
git commit -m "Initial commit with multi-environment structure"
git push origin main
```

### 2. Set Up Git Credentials

Create the Git credentials secret:

```bash
kubectl create secret generic git-credentials \
  --from-literal=token=your-github-personal-access-token \
  --from-literal=webhook-secret=your-webhook-secret
```

### 3. Configure Integration Service

Update the integration service environment variables in `k8s/integration-service/integration-service.yaml`:

```yaml
env:
  - name: GIT_REPO_URL
    value: "https://github.com/your-org/ai-prompts.git"
  - name: GIT_BRANCH
    value: "main"
  - name: CLOUD_BUILD_PROJECT
    value: "your-gcp-project-id"
```

### 4. Set Up Webhooks

Configure webhooks in your Git repository:

**GitHub:**
- URL: `https://your-domain.com/api/integration/gitops/webhook`
- Content type: `application/json`
- Secret: Your webhook secret
- Events: Push events

**GitLab:**
- URL: `https://your-domain.com/api/integration/gitops/webhook`
- Secret token: Your webhook secret
- Trigger: Push events

### 5. Deploy Integration Service

```bash
kubectl apply -f k8s/integration-service/git-credentials-secret.yaml
kubectl apply -f k8s/integration-service/integration-service.yaml
```

## Prompt Manifest Format

Prompts are stored as JSON files in the `prompts/` directory:

```json
{
  "id": "customer-greeting",
  "name": "Customer Greeting Prompt",
  "version": "1.2.0",
  "content": "Hello {{customer_name}}, welcome to {{company_name}}! How can I assist you today?",
  "variables": [
    {
      "name": "customer_name",
      "type": "string",
      "description": "Customer's name",
      "required": true,
      "default_value": ""
    },
    {
      "name": "company_name",
      "type": "string",
      "description": "Company name",
      "required": true,
      "default_value": "AI Operations Hub"
    }
  ],
  "tags": ["customer-service", "greeting"],
  "model_targets": ["gpt-4", "claude-3"],
  "use_case": "customer-support",
  "metadata": {
    "author": "prompt-team",
    "created_at": "2024-01-15T10:00:00Z",
    "environment": "production"
  }
}
```

## GitOps Workflows

### 1. A/B Test Winner Deployment Workflow 🚀

**Complete automated deployment from A/B test to production:**

1. **Create A/B Test**
   ```bash
   # In PromptOps Dashboard:
   # 1. Navigate to PromptOps → A/B Tests
   # 2. Click "Start New Test"
   # 3. Configure test parameters
   ```

2. **Monitor Performance**
   ```bash
   # Real-time metrics collection:
   # - Variant A/B usage statistics
   # - Quality scores (success rate + cost efficiency)
   # - Performance comparisons
   ```

3. **Deploy Winner**
   ```bash
   # When ready to deploy:
   # 1. Click "Deploy Winner" button
   # 2. System automatically:
   #    - Creates production prompt
   #    - Updates routing policies
   #    - Exports to GitOps repository
   #    - Triggers CI/CD pipeline
   ```

4. **Production Deployment Result**
   ```json
   // Created in prompts/production/
   {
     "id": "customer-greeting-production",
     "name": "Customer Greeting Prompt (Production)",
     "version": "1.2.0-prod",
     "status": "production",
     "tags": ["production", "ab-test-winner"],
     "metadata": {
       "deployed_from_ab_test": "ab-test-123",
       "ab_test_variant": "A",
       "deployment_date": "2025-01-15T10:30:00Z",
       "winning_score": 0.87
     }
   }
   ```

### 2. Traditional Development Workflow

1. **Create/Edit Prompts**: Use PromptOps dashboard to create and test prompts
2. **Export to Git**: Click "Export to Git" to push prompts to repository
3. **Review Changes**: Create pull request for prompt changes
4. **Merge**: Merge approved changes to main branch
5. **Auto-Deploy**: CI/CD pipeline automatically deploys changes

### 2. Collaborative Workflow

1. **Branch Creation**: Create feature branch for prompt changes
2. **Local Development**: Edit prompt manifests locally
3. **Testing**: Use PromptOps playground for testing
4. **Pull Request**: Submit changes for review
5. **Deployment**: Merge triggers automatic deployment

### 3. Rollback Workflow

1. **Identify Issue**: Detect problematic prompt deployment
2. **Git Revert**: Revert commit in Git repository
3. **Auto-Deploy**: CI/CD pipeline deploys previous version
4. **Verification**: Confirm rollback success

## API Endpoints

The integration service provides these GitOps endpoints:

### GitOps Operations
- `POST /gitops/webhook` - Handle Git webhooks
- `POST /gitops/deploy` - Manual deployment trigger
- `POST /gitops/sync` - Sync with Git repository
- `GET /gitops/status` - Get GitOps status

### PromptOps Operations
- `POST /promptops/export` - Export prompts to Git
- `POST /promptops/import` - Import prompts from Git
- `POST /promptops/validate` - Validate prompt manifests
- `POST /promptops/diff` - Compare prompts between service and Git

### A/B Test Operations
- `POST /api/integration/deploy-prompts` - Deploy A/B test winners
- `GET /api/prompts/ab-tests` - List active A/B tests
- `PUT /api/prompts/ab-tests/{id}` - Update A/B test status
- `POST /api/prompts/ab-tests/{id}/stop` - Stop running A/B test

## Monitoring and Verification

### 1. A/B Test Deployment Verification

**Verify successful A/B test winner deployment:**

```bash
# 1. Check production prompt was created
curl -s "https://scale-llm.com/api/prompts" | jq '.[] | select(.status=="production")'

# 2. Verify routing policy was updated
curl -s "https://scale-llm.com/api/policies" | jq '.[] | select(.metadata.source=="ab_test_deployment")'

# 3. Check GitOps repository
ls prompts/production/
git log --oneline | head -5

# 4. Test production routing
curl -X POST "https://scale-llm.com/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{"model": "gpt-4", "messages": [{"role": "user", "content": "test"}]}'
```

### 2. Dashboard Monitoring

**PromptOps Dashboard provides real-time monitoring:**

- **A/B Tests Tab**: Monitor active tests and performance metrics
- **Governance Tab**: View deployment audit logs
- **PromptOps Tab**: Check production prompt status
- **GitOps Status**: Verify repository synchronization

### 3. Performance Metrics

**Key metrics to monitor after deployment:**

```bash
# A/B test performance data
{
  "test_id": "ab-test-123",
  "variant_a_score": 0.85,
  "variant_b_score": 0.78,
  "variant_a_usage": 150,
  "variant_b_usage": 142,
  "winner": "A",
  "deployment_status": "deployed"
}

# Production routing metrics
{
  "policy_id": "ab-test-winner-123",
  "requests_routed": 1250,
  "success_rate": 0.97,
  "avg_latency": 245,
  "cost_efficiency": 0.89
}
```

## Troubleshooting

### 1. A/B Test Deployment Issues

**Common issues and solutions:**

```bash
# Issue: "No test results available to determine winner"
# Solution: Ensure A/B test has collected sufficient data
curl -s "https://scale-llm.com/api/prompts/ab-tests/{test-id}" | jq '.variant_a_usage, .variant_b_usage'

# Issue: "Failed to create production prompt"
# Solution: Check policy-manager service status
kubectl get pods -l app=policy-manager
kubectl logs deployment/policy-manager

# Issue: "GitOps export failed"
# Solution: Verify Git credentials and repository access
kubectl get secret git-credentials
kubectl logs deployment/integration-service | grep -i git
```

### 2. Service Logs

**Check service logs for debugging:**

```bash
# Integration service logs
kubectl logs -f deployment/integration-service

# Policy manager logs
kubectl logs -f deployment/policy-manager

# Proxy gateway logs
kubectl logs -f deployment/proxy-gateway | grep -i "ab-test"
```

Check Cloud Build logs:
```bash
gcloud builds list --limit=10
gcloud builds log BUILD_ID
```

### Common Issues

1. **Authentication Errors**: Verify Git credentials and permissions
2. **Webhook Failures**: Check webhook URL and secret configuration
3. **Build Failures**: Review Cloud Build configuration and permissions
4. **Sync Issues**: Ensure Git repository structure is correct

### Health Checks

Monitor GitOps health:
```bash
curl https://your-domain.com/api/integration/health
curl https://your-domain.com/api/integration/gitops/status
```

## Security Considerations

- Use personal access tokens with minimal required permissions
- Rotate webhook secrets regularly
- Implement branch protection rules
- Use separate repositories for different environments
- Enable audit logging for Git operations

## Best Practices

### 1. A/B Testing Best Practices

- **Sufficient Sample Size**: Ensure adequate traffic for statistical significance
- **Test Duration**: Run tests long enough to capture usage patterns
- **Clear Hypotheses**: Define what you're testing and expected outcomes
- **Performance Monitoring**: Track quality scores, latency, and cost metrics
- **Gradual Rollout**: Consider phased deployment for high-impact changes

### 2. GitOps Best Practices

- **Semantic Versioning**: Use semantic versioning for prompt versions (1.0.0, 1.1.0, 2.0.0)
- **Descriptive Commits**: Write clear commit messages for prompt changes
- **Testing**: Always test prompts in staging before production deployment
- **Documentation**: Document prompt purpose, variables, and usage in metadata
- **Environment Separation**: Use directory structure for dev/staging/production
- **Backup**: Regularly backup prompt repositories and deployment history
- **Access Control**: Implement proper Git repository access controls and branch protection

### 3. Production Deployment Best Practices

- **Rollback Planning**: Always have a rollback strategy before deployment
- **Monitoring**: Set up alerts for performance degradation after deployment
- **Gradual Traffic**: Consider gradual traffic shifting for major changes
- **Documentation**: Maintain deployment logs and change documentation
- **Testing**: Verify production deployment with test requests

## Current Capabilities ✅

The AI Operations Hub now provides enterprise-grade PromptOps with:

- **✅ Real A/B Testing**: Live traffic splitting with performance metrics
- **✅ Automated Winner Deployment**: One-click production deployment
- **✅ GitOps Integration**: Full version control and CI/CD pipeline
- **✅ Multi-Environment Support**: Dev, staging, and production workflows
- **✅ Routing Optimization**: Intelligent traffic routing based on test results
- **✅ Rollback Capabilities**: Instant revert for failed deployments
- **✅ Audit Trails**: Complete deployment and change history
- **✅ Performance Monitoring**: Real-time metrics and quality scoring

## Future Enhancements 🚀

Planned improvements include:

- **Advanced A/B Testing**: Multi-variate testing and statistical analysis
- **Canary Deployments**: Gradual traffic shifting for safer deployments
- **Automated Rollback**: AI-powered rollback triggers based on performance
- **Cross-Environment Promotion**: Automated promotion from staging to production
- **Advanced Analytics**: Detailed performance analysis and recommendations
- **Integration Ecosystem**: Webhooks and APIs for external tool integration

## Conclusion

The AI Operations Hub PromptOps GitOps integration provides a complete solution for enterprise prompt management, from development through production deployment. With real A/B testing, automated winner deployment, and comprehensive GitOps workflows, teams can confidently manage and optimize their LLM prompts at scale.

The system bridges the gap between prompt experimentation and production deployment, ensuring that only the best-performing prompts reach production while maintaining full audit trails and rollback capabilities.
- Integration with more Git providers
- Advanced rollback strategies
