# MCP Integration Examples

This document provides practical examples of using the AI Operations Hub's MCP integration for various workflows and use cases.

## Example 1: Intelligent LLM Routing with Cost Optimization

### Scenario
You want to automatically select the best LLM for a given prompt while considering cost and performance trade-offs.

### MCP Client Code (JavaScript)

```javascript
const WebSocket = require('ws');

class AIOptimizerClient {
  constructor(userId) {
    this.ws = new WebSocket('ws://localhost:8080/mcp/connect', {
      headers: { 'X-User-ID': userId }
    });
    this.requestId = 1;
  }

  async initialize() {
    return new Promise((resolve) => {
      this.ws.on('open', () => {
        this.send({
          method: 'initialize',
          params: {
            protocolVersion: '2025-06-18',
            capabilities: { tools: {}, resources: {} },
            clientInfo: { name: 'ai-optimizer-client', version: '1.0.0' }
          }
        });
      });

      this.ws.on('message', (data) => {
        const response = JSON.parse(data);
        if (response.result && response.result.capabilities) {
          resolve(response);
        }
      });
    });
  }

  async optimizeRouting(prompt, options = {}) {
    const response = await this.callTool('optimize_llm_routing', {
      prompt,
      user_id: options.userId || 'default',
      api_type: options.apiType || 'chat_completions',
      model: options.preferredModel
    });
    return response;
  }

  async getCostAnalysis(prompt, models = []) {
    const response = await this.callTool('get_cost_analysis', {
      prompt,
      models
    });
    return response;
  }

  async callTool(toolName, arguments) {
    return new Promise((resolve, reject) => {
      const id = this.requestId++;
      
      this.ws.send(JSON.stringify({
        jsonrpc: '2.0',
        id,
        method: 'tools/call',
        params: {
          name: toolName,
          arguments
        }
      }));

      const handler = (data) => {
        const response = JSON.parse(data);
        if (response.id === id) {
          this.ws.off('message', handler);
          if (response.error) {
            reject(new Error(response.error.message));
          } else {
            resolve(response.result);
          }
        }
      };

      this.ws.on('message', handler);
    });
  }

  send(params) {
    this.ws.send(JSON.stringify({
      jsonrpc: '2.0',
      id: this.requestId++,
      ...params
    }));
  }
}

// Usage Example
async function main() {
  const client = new AIOptimizerClient('user-123');
  await client.initialize();

  const prompt = "Explain quantum computing in simple terms";
  
  // Get cost analysis first
  const costAnalysis = await client.getCostAnalysis(prompt);
  console.log('Cost Analysis:', costAnalysis);

  // Get optimized routing recommendation
  const routing = await client.optimizeRouting(prompt, {
    userId: 'user-123',
    apiType: 'chat_completions'
  });
  console.log('Optimized Routing:', routing);
}

main().catch(console.error);
```

## Example 2: Autonomous Goal Execution Workflow

### Scenario
Create and execute a complex goal using the Planning Service MCP server.

### Python Client Example

```python
import asyncio
import websockets
import json

class PlanningServiceClient:
    def __init__(self, user_id):
        self.user_id = user_id
        self.request_id = 1
        self.ws = None

    async def connect(self):
        headers = {'X-User-ID': self.user_id}
        self.ws = await websockets.connect(
            'ws://localhost:8082/mcp',
            extra_headers=headers
        )
        
        # Initialize connection
        await self.send({
            'method': 'initialize',
            'params': {
                'protocolVersion': '2025-06-18',
                'capabilities': {'tools': {}, 'prompts': {}, 'resources': {}},
                'clientInfo': {'name': 'planning-client', 'version': '1.0.0'}
            }
        })
        
        response = await self.receive()
        return response

    async def create_goal(self, description, priority=5, constraints=None):
        return await self.call_tool('create_goal', {
            'description': description,
            'priority': priority,
            'constraints': constraints or [],
            'user_id': self.user_id
        })

    async def decompose_goal(self, goal_id):
        return await self.call_tool('decompose_goal', {
            'goal_id': goal_id
        })

    async def execute_plan(self, goal_id, dry_run=False):
        return await self.call_tool('execute_plan', {
            'goal_id': goal_id,
            'dry_run': dry_run
        })

    async def monitor_execution(self, goal_id):
        return await self.call_tool('monitor_execution', {
            'goal_id': goal_id
        })

    async def call_tool(self, tool_name, arguments):
        request_id = self.request_id
        self.request_id += 1
        
        await self.send({
            'method': 'tools/call',
            'params': {
                'name': tool_name,
                'arguments': arguments
            }
        }, request_id)
        
        response = await self.receive()
        if response.get('error'):
            raise Exception(f"Tool error: {response['error']['message']}")
        
        return response.get('result')

    async def send(self, params, request_id=None):
        if request_id is None:
            request_id = self.request_id
            self.request_id += 1
            
        message = {
            'jsonrpc': '2.0',
            'id': request_id,
            **params
        }
        await self.ws.send(json.dumps(message))

    async def receive(self):
        response = await self.ws.recv()
        return json.loads(response)

# Usage Example
async def autonomous_workflow():
    client = PlanningServiceClient('data-scientist-001')
    await client.connect()
    
    # Create a complex data analysis goal
    goal_response = await client.create_goal(
        description="Analyze customer churn data and create predictive model",
        priority=8,
        constraints=[
            {
                'type': 'time',
                'description': 'Complete within 2 hours',
                'limit': '2h'
            },
            {
                'type': 'cost',
                'description': 'Keep LLM costs under $10',
                'limit': '$10'
            }
        ]
    )
    
    goal_id = goal_response['content'][0]['text'].split('ID: ')[1].split('\n')[0]
    print(f"Created goal: {goal_id}")
    
    # Decompose into executable tasks
    decompose_response = await client.decompose_goal(goal_id)
    print("Goal decomposed:", decompose_response)
    
    # Execute the plan
    execution_response = await client.execute_plan(goal_id, dry_run=False)
    print("Execution started:", execution_response)
    
    # Monitor progress
    for i in range(10):  # Check 10 times
        await asyncio.sleep(30)  # Wait 30 seconds
        status = await client.monitor_execution(goal_id)
        print(f"Status check {i+1}:", status)
        
        if 'completed' in status['content'][0]['text'].lower():
            break

if __name__ == "__main__":
    asyncio.run(autonomous_workflow())
```

## Example 3: Quality Evaluation Pipeline

### Scenario
Automatically evaluate LLM responses for quality, bias, and safety using the Evaluation Service.

### Claude Desktop Configuration

Add this to your Claude Desktop MCP configuration:

```json
{
  "mcpServers": {
    "ai-operations-hub-evaluation": {
      "command": "node",
      "args": ["/path/to/mcp-websocket-client.js"],
      "env": {
        "MCP_SERVER_URL": "ws://localhost:8088/mcp",
        "X_USER_ID": "quality-assurance-team"
      }
    }
  }
}
```

### Example Conversation in Claude Desktop

```
User: I need to evaluate this LLM response for quality and bias. Can you help?

Response: "Women are naturally better at nurturing roles while men excel in leadership positions."

Claude: I'll help you evaluate that response using the AI Operations Hub's evaluation tools. Let me run a comprehensive analysis.

[Claude automatically uses the MCP tools]

Tool: evaluate_llm_response
Arguments: {
  "prompt": "Evaluate gender roles in workplace",
  "response": "Women are naturally better at nurturing roles while men excel in leadership positions.",
  "task_type": "opinion_analysis"
}

Result: The evaluation shows significant bias concerns:
- Overall Score: 0.23/1.0 (Poor)
- Bias Detection: High gender bias detected
- Quality Issues: Overgeneralization, lack of evidence

Tool: perform_bias_detection
Arguments: {
  "prompt": "Evaluate gender roles in workplace", 
  "response": "Women are naturally better at nurturing roles while men excel in leadership positions."
}

Result: Bias Analysis:
- Gender Bias: 0.89/1.0 (Very High)
- Detected Biases: Gender stereotyping, role essentialism
- Mitigation Suggestions: Avoid generalizations, present diverse perspectives, cite research

This response contains significant gender bias and should be revised to:
1. Avoid essentialist claims about gender
2. Present evidence-based information
3. Acknowledge diversity within gender groups
4. Include multiple perspectives on workplace roles
```

## Example 4: GitOps Prompt Deployment

### Scenario
Use the Integration Service to deploy and manage prompts through GitOps workflows.

### Bash Script Example

```bash
#!/bin/bash

# MCP GitOps Deployment Script
USER_ID="devops-engineer"
MCP_URL="ws://localhost:8080/mcp"

# Function to call MCP tool via curl (using HTTP fallback)
call_mcp_tool() {
    local tool_name=$1
    local arguments=$2
    
    curl -X POST "https://scale-llm.com/integration-service/mcp/tools/call" \
        -H "Content-Type: application/json" \
        -H "X-User-ID: $USER_ID" \
        -d "{
            \"jsonrpc\": \"2.0\",
            \"id\": 1,
            \"method\": \"tools/call\",
            \"params\": {
                \"name\": \"$tool_name\",
                \"arguments\": $arguments
            }
        }"
}

# Deploy prompts to staging
echo "Deploying prompts to staging..."
call_mcp_tool "deploy_prompts" '{
    "environment": "staging",
    "prompts": [
        {
            "id": "customer-support-v2",
            "name": "Customer Support Assistant",
            "content": "You are a helpful customer support assistant...",
            "version": "2.1.0"
        }
    ],
    "commit_message": "Deploy customer support prompt v2.1.0 to staging"
}'

# Validate prompts
echo "Validating prompt configurations..."
call_mcp_tool "validate_prompts" '{
    "prompts": [
        {
            "id": "customer-support-v2",
            "name": "Customer Support Assistant",
            "content": "You are a helpful customer support assistant..."
        }
    ],
    "strict": true
}'

# Sync repository
echo "Syncing with Git repository..."
call_mcp_tool "sync_repository" '{
    "force": false
}'

echo "Deployment pipeline completed!"
```

## Example 5: Multi-Service Workflow

### Scenario
Combine multiple MCP servers for a complete AI workflow: optimize routing, execute tasks, evaluate results, and deploy improvements.

### Node.js Orchestration Example

```javascript
const { MCPClient } = require('./mcp-client');

class AIWorkflowOrchestrator {
  constructor(userId) {
    this.userId = userId;
    this.clients = {
      optimizer: new MCPClient('ws://localhost:8085/mcp', userId),
      planning: new MCPClient('ws://localhost:8082/mcp', userId),
      evaluation: new MCPClient('ws://localhost:8088/mcp', userId),
      integration: new MCPClient('ws://localhost:8080/mcp', userId)
    };
  }

  async initialize() {
    await Promise.all(
      Object.values(this.clients).map(client => client.connect())
    );
  }

  async executeCompleteWorkflow(userPrompt) {
    try {
      // Step 1: Optimize LLM routing
      console.log('Step 1: Optimizing LLM routing...');
      const routing = await this.clients.optimizer.callTool('optimize_llm_routing', {
        prompt: userPrompt,
        user_id: this.userId
      });
      
      // Step 2: Create and execute goal
      console.log('Step 2: Creating autonomous goal...');
      const goalResponse = await this.clients.planning.callTool('create_goal', {
        description: `Process and respond to: ${userPrompt}`,
        priority: 7,
        user_id: this.userId
      });
      
      const goalId = this.extractGoalId(goalResponse);
      
      // Step 3: Decompose and execute
      await this.clients.planning.callTool('decompose_goal', { goal_id: goalId });
      await this.clients.planning.callTool('execute_plan', { goal_id: goalId });
      
      // Step 4: Monitor execution
      let executionComplete = false;
      while (!executionComplete) {
        await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10s
        const status = await this.clients.planning.callTool('monitor_execution', {
          goal_id: goalId
        });
        
        if (status.content[0].text.includes('completed')) {
          executionComplete = true;
        }
      }
      
      // Step 5: Evaluate results
      console.log('Step 5: Evaluating results...');
      const evaluation = await this.clients.evaluation.callTool('evaluate_llm_response', {
        prompt: userPrompt,
        response: "Generated response from planning execution",
        task_type: "general"
      });
      
      // Step 6: Deploy improvements if quality is good
      if (this.isHighQuality(evaluation)) {
        console.log('Step 6: Deploying improvements...');
        await this.clients.integration.callTool('export_configurations', {
          include_prompts: true,
          include_policies: true
        });
      }
      
      return {
        routing,
        goalId,
        evaluation,
        status: 'completed'
      };
      
    } catch (error) {
      console.error('Workflow error:', error);
      throw error;
    }
  }

  extractGoalId(response) {
    // Extract goal ID from response text
    const text = response.content[0].text;
    const match = text.match(/ID: ([a-f0-9-]+)/);
    return match ? match[1] : null;
  }

  isHighQuality(evaluation) {
    // Check if evaluation score is above threshold
    const text = evaluation.content[0].text;
    const scoreMatch = text.match(/Score: ([\d.]+)/);
    return scoreMatch && parseFloat(scoreMatch[1]) > 0.7;
  }
}

// Usage
async function main() {
  const orchestrator = new AIWorkflowOrchestrator('workflow-user');
  await orchestrator.initialize();
  
  const result = await orchestrator.executeCompleteWorkflow(
    "Create a comprehensive analysis of renewable energy trends"
  );
  
  console.log('Workflow completed:', result);
}

main().catch(console.error);
```

## Best Practices

### 1. Error Handling
Always implement proper error handling for MCP calls:

```javascript
try {
  const result = await client.callTool('tool_name', args);
  // Handle success
} catch (error) {
  if (error.code === -32601) {
    console.log('Tool not found');
  } else if (error.code === -32602) {
    console.log('Invalid parameters');
  } else {
    console.log('Unexpected error:', error.message);
  }
}
```

### 2. Connection Management
Implement connection pooling and reconnection logic:

```javascript
class RobustMCPClient {
  constructor(url, userId) {
    this.url = url;
    this.userId = userId;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  async connect() {
    try {
      this.ws = new WebSocket(this.url, {
        headers: { 'X-User-ID': this.userId }
      });
      
      this.ws.on('close', () => this.handleDisconnect());
      this.ws.on('error', (error) => this.handleError(error));
      
    } catch (error) {
      await this.reconnect();
    }
  }

  async reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.pow(2, this.reconnectAttempts) * 1000;
      setTimeout(() => this.connect(), delay);
    }
  }
}
```

### 3. Rate Limiting
Respect rate limits and implement backoff:

```javascript
class RateLimitedClient {
  constructor(client) {
    this.client = client;
    this.requestQueue = [];
    this.processing = false;
  }

  async callTool(toolName, args) {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ toolName, args, resolve, reject });
      this.processQueue();
    });
  }

  async processQueue() {
    if (this.processing || this.requestQueue.length === 0) return;
    
    this.processing = true;
    
    while (this.requestQueue.length > 0) {
      const { toolName, args, resolve, reject } = this.requestQueue.shift();
      
      try {
        const result = await this.client.callTool(toolName, args);
        resolve(result);
      } catch (error) {
        if (error.code === 429) { // Rate limited
          await new Promise(resolve => setTimeout(resolve, 60000)); // Wait 1 minute
          this.requestQueue.unshift({ toolName, args, resolve, reject });
        } else {
          reject(error);
        }
      }
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    this.processing = false;
  }
}
```

These examples demonstrate the power and flexibility of the AI Operations Hub's MCP integration. You can combine these patterns to build sophisticated AI workflows that leverage the full capabilities of the platform.
