#!/bin/bash

# AI Operations Hub - Unified Deployment Script
# Supports both Standard and Enterprise editions with automatic Cloud Build integration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Default configuration
EDITION="standard"
BUILD_ONLY=false
DEPLOY_ONLY=false
SKIP_LANDING=false
DRY_RUN=false
VERBOSE=false

# GCP Configuration (can be overridden by environment variables)
GCP_PROJECT_ID="${GCP_PROJECT_ID:-your-gcp-project-id}"
GCP_REGION="${GCP_REGION:-us-central1}"
GKE_CLUSTER_NAME="${GKE_CLUSTER_NAME:-ai-optimizer-cluster}"
ARTIFACT_REGISTRY_REPO="${ARTIFACT_REGISTRY_REPO:-ai-optimizer-repo}"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

# Function to show usage
show_usage() {
    cat << EOF
AI Operations Hub - Unified Deployment Script

Usage: $0 [OPTIONS]

OPTIONS:
    -e, --edition EDITION       Deployment edition: 'standard' or 'enterprise' (default: standard)
    -b, --build-only           Only build and push images, skip deployment
    -d, --deploy-only          Only deploy (assumes images are already built)
    -s, --skip-landing         Skip landing page deployment
    -n, --dry-run              Show what would be deployed without actually deploying
    -v, --verbose              Enable verbose output
    -h, --help                 Show this help message

EXAMPLES:
    $0                         # Deploy standard edition (default)
    $0 -e enterprise           # Deploy enterprise edition
    $0 -e standard -b          # Build standard edition images only
    $0 -e enterprise -d        # Deploy enterprise edition (skip build)
    $0 -s                      # Deploy without landing page
    $0 -n                      # Dry run to see what would be deployed

ENVIRONMENT VARIABLES:
    GCP_PROJECT_ID             Google Cloud Project ID
    GCP_REGION                 Google Cloud Region (default: us-central1)
    GKE_CLUSTER_NAME           GKE cluster name (default: ai-optimizer-cluster)
    ARTIFACT_REGISTRY_REPO     Artifact Registry repository name

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--edition)
            EDITION="$2"
            shift 2
            ;;
        -b|--build-only)
            BUILD_ONLY=true
            shift
            ;;
        -d|--deploy-only)
            DEPLOY_ONLY=true
            shift
            ;;
        -s|--skip-landing)
            SKIP_LANDING=true
            shift
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate edition
if [[ "$EDITION" != "standard" && "$EDITION" != "enterprise" ]]; then
    print_error "Invalid edition: $EDITION. Must be 'standard' or 'enterprise'"
    exit 1
fi

# Validate GCP configuration
if [[ "$GCP_PROJECT_ID" == "your-gcp-project-id" ]]; then
    print_error "Please set GCP_PROJECT_ID environment variable or update the script"
    exit 1
fi

# Main deployment function
main() {
    print_header "🚀 AI Operations Hub Deployment"
    print_header "=================================="
    print_status "Edition: $EDITION"
    print_status "GCP Project: $GCP_PROJECT_ID"
    print_status "GCP Region: $GCP_REGION"
    print_status "Artifact Registry: $ARTIFACT_REGISTRY_REPO"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_warning "DRY RUN MODE - No actual deployment will occur"
    fi
    
    echo ""

    # Step 1: Build and push images (unless deploy-only)
    if [[ "$DEPLOY_ONLY" != "true" ]]; then
        build_and_push_images
    fi

    # Step 2: Deploy services (unless build-only)
    if [[ "$BUILD_ONLY" != "true" ]]; then
        deploy_services
    fi

    print_success "🎉 Deployment completed successfully!"
    show_post_deployment_info
}

# Function to build and push images using Cloud Build
build_and_push_images() {
    print_header "📦 Building and Pushing Images"
    print_header "==============================="

    local cloudbuild_file
    if [[ "$EDITION" == "standard" ]]; then
        cloudbuild_file="cloudbuild-standard.yaml"
    else
        cloudbuild_file="cloudbuild.yaml"
    fi

    print_status "Using Cloud Build configuration: $cloudbuild_file"

    if [[ "$DRY_RUN" == "true" ]]; then
        print_warning "Would execute: gcloud builds submit --config=$cloudbuild_file"
        return
    fi

    # Submit build to Cloud Build
    print_status "Submitting build to Google Cloud Build..."
    gcloud builds submit \
        --config="$cloudbuild_file" \
        --substitutions="_GCP_PROJECT_ID=$GCP_PROJECT_ID,_GCP_REGION=$GCP_REGION,_GKE_CLUSTER_NAME=$GKE_CLUSTER_NAME,_ARTIFACT_REGISTRY_REPO=$ARTIFACT_REGISTRY_REPO" \
        .

    print_success "Images built and pushed successfully"
}

# Function to deploy services
deploy_services() {
    print_header "🚀 Deploying Services"
    print_header "====================="

    if [[ "$EDITION" == "standard" ]]; then
        deploy_standard_edition
    else
        deploy_enterprise_edition
    fi

    # Deploy landing page (unless skipped or already deployed by standard edition)
    if [[ "$SKIP_LANDING" != "true" && "$EDITION" != "standard" ]]; then
        deploy_landing_page
    fi
}

# Function to deploy standard edition
deploy_standard_edition() {
    print_status "Deploying Standard Edition..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_warning "Would execute: k8s/standard-edition/deploy-standard.sh"
        return
    fi

    # Set environment variables for standard deployment
    export GCP_PROJECT_ID="$GCP_PROJECT_ID"
    export GCP_REGION="$GCP_REGION"
    export ARTIFACT_REGISTRY_REPO="$ARTIFACT_REGISTRY_REPO"
    export AI_OPTIMIZER_IMAGE="$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/ai-cost-performance-optimizer-ai-optimizer-standard:latest"
    export DASHBOARD_API_IMAGE="$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/ai-cost-performance-optimizer-dashboard-api-standard:latest"
    export FRONTEND_IMAGE="$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/ai-cost-performance-optimizer-frontend-standard:latest"
    export PROXY_GATEWAY_IMAGE="$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/ai-cost-performance-optimizer-proxy-gateway-standard:latest"

    # Execute standard deployment script
    bash k8s/standard-edition/deploy-standard.sh
}

# Function to deploy enterprise edition
deploy_enterprise_edition() {
    print_status "Deploying Enterprise Edition..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_warning "Would execute: k8s/New_Autostart.sh"
        return
    fi

    # Set environment variables for enterprise deployment
    export GCP_PROJECT_ID="$GCP_PROJECT_ID"
    export GCP_REGION="$GCP_REGION"
    export ARTIFACT_REGISTRY_REPO="$ARTIFACT_REGISTRY_REPO"
    export GKE_CLUSTER_NAME="${GKE_CLUSTER_NAME:-ai-optimizer-cluster}"

    # Set all image environment variables for enterprise
    export PROXY_GATEWAY_IMAGE="$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/ai-cost-performance-optimizer-proxy-gateway:latest"
    export DATA_PROCESSOR_IMAGE="$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/ai-cost-performance-optimizer-data-processor:latest"
    export DASHBOARD_API_IMAGE="$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/ai-cost-performance-optimizer-dashboard-api:latest"
    export POLICY_MANAGER_IMAGE="$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/ai-cost-performance-optimizer-policy-manager:latest"
    export AI_OPTIMIZER_IMAGE="$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/ai-cost-performance-optimizer-ai-optimizer:latest"
    export FRONTEND_IMAGE="$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/ai-cost-performance-optimizer-frontend:latest"
    export EVALUATION_SERVICE_IMAGE="$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/ai-cost-performance-optimizer-evaluation-service:latest"
    export INTEGRATION_SERVICE_IMAGE="$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/ai-cost-performance-optimizer-integration-service:latest"
    export PLANNING_SERVICE_IMAGE="$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/ai-cost-performance-optimizer-planning-service:latest"

    # Execute enterprise deployment script
    bash k8s/New_Autostart.sh

    print_success "Enterprise Edition deployment completed!"
}

# Function to deploy landing page
deploy_landing_page() {
    print_status "Deploying Landing Page..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_warning "Would deploy landing page"
        return
    fi

    export LANDING_PAGE_IMAGE="$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/ai-cost-performance-optimizer-landing-page:latest"
    
    # Deploy landing page
    envsubst < k8s/landing-page/landing-page.yaml | kubectl apply -f -
    
    print_success "Landing page deployed"
}

# Function to show post-deployment information
show_post_deployment_info() {
    print_header "📋 Post-Deployment Information"
    print_header "==============================="
    
    if [[ "$EDITION" == "standard" ]]; then
        echo -e "${CYAN}Standard Edition Endpoints:${NC}"
        echo "  • Frontend: https://scale-llm.com/standard"
        echo "  • Prompts API: https://scale-llm.com/standard/api/prompts"
        echo "  • Dashboard API: https://scale-llm.com/standard/dashboard/health"
        echo "  • Integration Fallback: https://scale-llm.com/standard/api/integration/gitops/status"
        echo ""
        echo -e "${GREEN}✅ All endpoints tested and working!${NC}"
        echo ""
        echo -e "${YELLOW}Next Steps:${NC}"
        echo "  1. Update API keys: kubectl edit secret llm-api-keys -n standard-edition"
        echo "  2. Monitor services: kubectl get pods -n standard-edition"
        echo "  3. Run validation: ./k8s/scripts/validate-edition-deployment.sh"
    else
        echo -e "${CYAN}Enterprise Edition Endpoints:${NC}"
        echo "  • Frontend: https://scale-llm.com"
        echo "  • API Health: https://scale-llm.com/api/health"
        echo "  • Dashboard API: https://scale-llm.com/dashboard/health"
        echo "  • Planning Service: https://scale-llm.com/api/planning/v1/goals"
        echo ""
        echo -e "${YELLOW}Next Steps:${NC}"
        echo "  1. Update API keys: kubectl edit secret llm-api-keys"
        echo "  2. Monitor services: kubectl get pods"
    fi
    
    if [[ "$SKIP_LANDING" != "true" ]]; then
        echo "  • Landing Page: https://scale-llm.com"
    fi
    
    echo ""
    echo -e "${GREEN}🎉 Your AI Operations Hub is ready!${NC}"
}

# Execute main function
main "$@"
