#!/bin/bash

# AI Operations Hub - Deployment Test Script
# Tests all URL endpoints for the new structure

set -e

echo "🧪 Testing AI Operations Hub Deployment"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test function
test_url() {
    local url=$1
    local expected_status=$2
    local description=$3
    
    echo -n "Testing $description... "
    
    # Use HTTPS and skip SSL verification for testing
    status=$(curl -s -o /dev/null -w "%{http_code}" "$url" || echo "000")
    
    if [ "$status" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} ($status)"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} (got $status, expected $expected_status)"
        return 1
    fi
}

# Test function for JSON responses
test_json_endpoint() {
    local url=$1
    local description=$2
    
    echo -n "Testing $description... "
    
    response=$(curl -s "$url" 2>/dev/null || echo "ERROR")
    
    if echo "$response" | python3 -m json.tool >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC} (valid JSON)"
        echo "   Response: $response"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} (invalid JSON or error)"
        echo "   Response: $response"
        return 1
    fi
}

echo ""
echo "🏠 Testing Landing Page"
echo "----------------------"
test_url "https://scale-llm.com" "200" "Landing page root"
test_url "https://scale-llm.com/health" "200" "Landing page health"

echo ""
echo "📊 Testing Standard Edition"
echo "---------------------------"
test_url "https://scale-llm.com/standard" "200" "Standard edition frontend"
test_json_endpoint "https://scale-llm.com/standard/api/health" "Standard API health"
test_json_endpoint "https://scale-llm.com/standard/dashboard/health" "Standard dashboard health"
test_json_endpoint "https://scale-llm.com/standard/dashboard/metrics" "Standard dashboard metrics"

echo ""
echo "🏢 Testing Enterprise Edition"
echo "-----------------------------"
test_url "https://scale-llm.com/enterprise" "200" "Enterprise edition frontend"
test_json_endpoint "https://scale-llm.com/enterprise/api/health" "Enterprise API health"
test_json_endpoint "https://scale-llm.com/enterprise/dashboard/health" "Enterprise dashboard health"

# Legacy enterprise endpoints (should still work)
echo ""
echo "🔄 Testing Legacy Enterprise Endpoints"
echo "--------------------------------------"
test_json_endpoint "https://scale-llm.com/api/health" "Legacy API health"
test_json_endpoint "https://scale-llm.com/dashboard/health" "Legacy dashboard health"

echo ""
echo "📋 Deployment Summary"
echo "===================="

# Check pod status
echo ""
echo "🔍 Pod Status:"
echo "Landing Page:"
kubectl get pods -l app=landing-page --no-headers 2>/dev/null | awk '{print "  " $1 ": " $3}' || echo "  No landing page pods found"

echo "Standard Edition:"
kubectl get pods -n standard-edition --no-headers 2>/dev/null | awk '{print "  " $1 ": " $3}' || echo "  No standard edition pods found"

echo "Enterprise Edition:"
kubectl get pods -l app=frontend-dashboard --no-headers 2>/dev/null | awk '{print "  " $1 ": " $3}' || echo "  No enterprise edition pods found"

echo ""
echo "🌐 Access Points:"
echo "  Landing Page: https://scale-llm.com"
echo "  Standard:     https://scale-llm.com/standard"
echo "  Enterprise:   https://scale-llm.com/enterprise"

echo ""
echo "✅ Test completed!"
