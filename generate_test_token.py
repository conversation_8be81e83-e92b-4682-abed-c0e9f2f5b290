#!/usr/bin/env python3
"""
Generate a test JWT token for testing the AI Operations Hub APIs.
This script creates a simple JWT token that can be used for testing purposes.
"""

import jwt
import json
import datetime
import sys

def generate_test_token():
    """Generate a test JWT token for API testing"""
    
    # Test secret key (should match the one in proxy-gateway or use default)
    secret_key = "test-secret-key-for-development-only"
    
    # Token payload
    payload = {
        "user_id": "test-user",
        "email": "<EMAIL>", 
        "name": "Test User",
        "roles": ["user", "admin"],
        "iat": datetime.datetime.utcnow(),
        "exp": datetime.datetime.utcnow() + datetime.timedelta(hours=24)
    }
    
    # Generate token
    token = jwt.encode(payload, secret_key, algorithm="HS256")
    
    return token

def main():
    """Main function"""
    try:
        token = generate_test_token()
        print("Generated test JWT token:")
        print(token)
        print("\nTo use this token in your tests, set the environment variable:")
        print(f"export TEST_AUTH_TOKEN='{token}'")
        print("\nOr use it directly in curl commands:")
        print(f"curl -H 'Authorization: Bearer {token}' ...")
        
    except ImportError:
        print("Error: PyJWT library not found. Install it with:")
        print("pip install PyJWT")
        sys.exit(1)
    except Exception as e:
        print(f"Error generating token: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
