# ==============================================================================
# AI Cost-Performance Optimizer: Sample Model Profiles and Policies
# ==============================================================================
#
# IMPORTANT:
# 1. Replace YOUR_ACTUAL_PROXY_IP_HERE with the actual external IP address
#    of your proxy-gateway service.
#
#
# These commands will create various Model Profiles (representing your LLM backends)
# and Policies (routing rules) in your system.
#
# ==============================================================================

# --- Configuration ---
PROXY_IP="YOUR_ACTUAL_PROXY_IP_HERE" # <--- REPLACE THIS WITH YOUR PROXY'S EXTERNAL IP


echo "--- 1. Creating Model Profiles ---"
echo "These define the characteristics and endpoints of your LLM backends."
echo ""



# Model Profile 1: Simulated Google Gemini Pro (e.g., balanced performance/cost)
curl -X POST http://$PROXY_IP:8080/api/model_profiles \
-H "Content-Type: application/json" \
-d '{
  "name": "google-gemini-pro",
  "aliases": ["gemini-balanced", "default-llm"],
  "capabilities": ["text-generation", "chat", "summarization"],
  "pricing_tier": "standard",
  "data_sensitivity": "low",
  "expected_latency_ms": 150,
  "expected_cost": 0.000005,
  "backend_url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",
  "backend_type": "google",
  "cost_per_input_token": 0.0000005,
  "cost_per_output_token": 0.0000015
}'
echo ""
echo "----------------------------------------------------------------"

# Model Profile 5: Simulated Anthropic Claude 3 Opus (e.g., very high-end, high cost)
curl -X POST http://$PROXY_IP:8080/api/model_profiles \
-H "Content-Type: application/json" \
-d '{
  "name": "anthropic-claude-3-opus",
  "aliases": ["claude-opus", "enterprise-llm"],
  "capabilities": ["text-generation", "chat", "complex-reasoning"],
  "pricing_tier": "enterprise",
  "data_sensitivity": "high",
  "expected_latency_ms": 300,
  "expected_cost": 0.00009,
  "backend_url": "http://mock-anthropic:5005/v1/messages",
  "backend_type": "anthropic",
  "cost_per_input_token": 0.000015,
  "cost_per_output_token": 0.000075
}'
echo ""
echo "----------------------------------------------------------------"


echo "--- 2. Creating Policies ---"
echo "These define the routing rules based on request attributes."
echo "Policies are evaluated by priority (lower number = higher priority)."
echo ""

# Policy 1: Block High Data Sensitivity Requests (Highest Priority)
curl -X POST http://$PROXY_IP:8080/api/policies \
-H "Content-Type: application/json" \
-d '{
  "name": "Block High Sensitivity Data",
  "description": "Rejects requests marked with high data sensitivity.",
  "criteria": [
    { "field": "X-Data-Sensitivity", "operator": "=", "value": "high" }
  ],
  "action": "REJECT",
  "backend_id": "",
  "priority": 10,
  "rules": ""
}'
echo ""
echo "----------------------------------------------------------------"

# Policy 2: Route "fast-chat" model to vLLM-GPU1 (High Priority)
curl -X POST http://$PROXY_IP:8080/api/policies \
-H "Content-Type: application/json" \
-d '{
  "name": "Route Fast Chat to vLLM-GPU1",
  "description": "Routes requests for 'fast-chat' model to vLLM-GPU1 for low latency.",
  "criteria": [
    { "field": "model_requested", "operator": "=", "value": "fast-chat" }
  ],
  "action": "ROUTE_TO_BACKEND",
  "backend_id": "vllm-gpu1",
  "priority": 20,
  "rules": ""
}'
echo ""
echo "----------------------------------------------------------------"

# Policy 3: Route "long-form-content" with large prompts to Google Gemini Pro (Medium Priority)
curl -X POST http://$PROXY_IP:8080/api/policies \
-H "Content-Type: application/json" \
-d '{
  "name": "Route Long Form to Gemini Pro",
  "description": "Routes long text generation requests to Gemini Pro, which handles longer contexts well.",
  "criteria": [
    { "field": "model_requested", "operator": "=", "value": "long-form-content" },
    { "field": "prompt_length", "operator": ">", "value": "500" }
  ],
  "action": "ROUTE_TO_BACKEND",
  "backend_id": "google-gemini-pro",
  "priority": 30,
  "rules": ""
}'
echo ""
echo "----------------------------------------------------------------"

# Policy 4: Route "premium-content" to OpenAI GPT-4 Turbo (Medium-Low Priority)
curl -X POST http://$PROXY_IP:8080/api/policies \
-H "Content-Type: application/json" \
-d '{
  "name": "Route Premium Content to GPT-4 Turbo",
  "description": "Routes requests for premium content generation to OpenAI GPT-4 Turbo.",
  "criteria": [
    { "field": "model_requested", "operator": "=", "value": "premium-content" }
  ],
  "action": "ROUTE_TO_BACKEND",
  "backend_id": "openai-gpt4-turbo",
  "priority": 40,
  "rules": ""
}'
echo ""
echo "----------------------------------------------------------------"

# Policy 5: Default Fallback for general chat (Lowest Priority)
curl -X POST http://$PROXY_IP:8080/api/policies \
-H "Content-Type: application/json" \
-d '{
  "name": "Default General Chat to vLLM-GPU2",
  "description": "Default routing for general chat requests to vLLM-GPU2 if no other specific policy matches.",
  "criteria": [
    { "field": "model_requested", "operator": "=", "value": "general-chat" }
  ],
  "action": "ROUTE_TO_BACKEND",
  "backend_id": "vllm-gpu2",
  "priority": 90,
  "rules": ""
}'
echo ""
echo "----------------------------------------------------------------"

# Policy 6: Catch-all for any unmatched request (Very Low Priority)
curl -X POST http://$PROXY_IP:8080/api/policies \
-H "Content-Type: application/json" \
-d '{
  "name": "Catch-all Default to Gemini Pro",
  "description": "Routes any unmatched inference request to Google Gemini Pro.",
  "criteria": [],
  "action": "ROUTE_TO_BACKEND",
  "backend_id": "google-gemini-pro",
  "priority": 100,
  "rules": ""
}'
echo ""
echo "----------------------------------------------------------------"


echo "--- 3. Sample Inference Requests to Proxy Gateway ---"
echo "These requests demonstrate how to interact with the proxy after policies are set."
echo "Observe the proxy-gateway logs to see which backend was selected."
echo ""

# Test Request 1: Should be rejected by Policy 1 (High Data Sensitivity)
echo "Attempting to send a high-sensitivity request (should be REJECTED)..."
curl -X POST http://$PROXY_IP:8080/v1/chat/completions \
-H "Content-Type: application/json" \
-H "X-Data-Sensitivity: high" \
-d '{
  "model": "any-model",
  "messages": [
    {"role": "user", "content": "Process highly confidential financial data."}
  ],
  "max_tokens": 50
}'
echo ""
echo "----------------------------------------------------------------"

# Test Request 2: Should be routed to vLLM-GPU1 by Policy 2
echo "Sending a 'fast-chat' request (should route to vllm-gpu1)..."
curl -X POST http://$PROXY_IP:8080/v1/chat/completions \
-H "Content-Type: application/json" \
-d '{
  "model": "fast-chat",
  "messages": [
    {"role": "user", "content": "Tell me a very short joke."}
  ],
  "max_tokens": 30,
  "temperature": 0.8
}'
echo ""
echo "----------------------------------------------------------------"

# Test Request 3: Should be routed to Google Gemini Pro by Policy 3 (long prompt)
echo "Sending a 'long-form-content' request with a long prompt (should route to google-gemini-pro)..."
curl -X POST http://$PROXY_IP:8080/v1/chat/completions \
-H "Content-Type: application/json" \
-d '{
  "model": "long-form-content",
  "messages": [
    {"role": "user", "content": "Write a detailed essay about the economic impacts of climate change, focusing on renewable energy investments and their potential to mitigate adverse effects. Discuss both short-term and long-term implications, and include a section on policy recommendations. This essay should be at least 600 words long and cover various sectors like agriculture, infrastructure, and public health. Provide specific examples of countries or regions that have successfully implemented green initiatives and the measurable outcomes. Also, consider the challenges and barriers to widespread adoption of these policies globally. The response should be comprehensive and analytical, providing a nuanced perspective on the subject matter, and should conclude with a strong summary of the key arguments presented throughout the essay. The essay must clearly articulate the benefits of proactive measures versus reactive approaches to climate change, emphasizing the cost-effectiveness of early investments. Furthermore, elaborate on the role of international cooperation and technological innovation in accelerating the transition to a sustainable economy. Consider the ethical dimensions of climate policy and intergenerational equity. The essay should be structured with an introduction, several body paragraphs, and a conclusion, each addressing distinct aspects of the topic. Ensure that the language is academic and persuasive, suitable for a policy brief aimed at national and international decision-makers. The content should be original and insightful, drawing upon current scientific understanding and economic principles. This extensive request is designed to test the model''s ability to handle and generate long-form content effectively and efficiently."}
  ],
  "max_tokens": 800,
  "temperature": 0.6
}'
echo ""
echo "----------------------------------------------------------------"

# Test Request 4: Should be routed to OpenAI GPT-4 Turbo by Policy 4
echo "Sending a 'premium-content' request (should route to openai-gpt4-turbo)..."
curl -X POST http://$PROXY_IP:8080/v1/chat/completions \
-H "Content-Type: application/json" \
-d '{
  "model": "premium-content",
  "messages": [
    {"role": "user", "content": "Analyze the philosophical implications of quantum entanglement."}
  ],
  "max_tokens": 120,
  "temperature": 0.5
}'
echo ""
echo "----------------------------------------------------------------"

# Test Request 5: Should be routed to vLLM-GPU2 by Policy 5 (default general chat)
echo "Sending a 'general-chat' request (should route to vllm-gpu2)..."
curl -X POST http://$PROXY_IP:8080/v1/chat/completions \
-H "Content-Type: application/json" \
-d '{
  "model": "general-chat",
  "messages": [
    {"role": "user", "content": "Tell me a short story about a cat and a dog."}
  ],
  "max_tokens": 100,
  "temperature": 0.7
}'
echo ""
echo "----------------------------------------------------------------"

# Test Request 6: Should be routed to Google Gemini Pro by Policy 6 (catch-all)
echo "Sending an unmatched model request (should route to google-gemini-pro by catch-all policy)..."
curl -X POST http://$PROXY_IP:8080/v1/chat/completions \
-H "Content-Type: application/json" \
-d '{
  "model": "unspecified-model",
  "messages": [
    {"role": "user", "content": "What is the capital of France?"}
  ],
  "max_tokens": 20
}'
echo ""
echo "----------------------------------------------------------------"

echo "All sample requests sent. Check your proxy-gateway and data-processor logs for routing and processing details."
