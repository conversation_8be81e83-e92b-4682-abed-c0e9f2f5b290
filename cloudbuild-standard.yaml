# Google Cloud Build configuration for AI Operations Hub - STANDARD EDITION
# Simplified deployment with core services only for startup/SMB market
# Includes landing page and AI optimizer score enhancements

# This build will store logs in a regional, user-owned bucket.
options:
  default_logs_bucket_behavior: REGIONAL_USER_OWNED_BUCKET

# Substitutions for flexible configuration
substitutions:
  _GCP_PROJECT_ID: 'silken-zenith-460615-s7'
  _GCP_REGION: 'us-central1'
  _GKE_CLUSTER_NAME: 'ai-optimizer-cluster'
  _ARTIFACT_REGISTRY_REPO: 'ai-optimizer-repo'
  _BUILD_ID: '${BUILD_ID}' # Cloud Build's built-in BUILD_ID

  # --- Standard Edition Build Flags (Selective Build Support) ---
  _BUILD_PROXY_GATEWAY_STANDARD: "true"
  _BUILD_AI_OPTIMIZER_STANDARD: "true"
  _BUILD_DASHBOARD_API_STANDARD: "true"
  _BUILD_FRONTEND_STANDARD: "true"
  _BUILD_LANDING_PAGE: "true"

  # --- Standard Edition Deploy Flags ---
  _DEPLOY_STANDARD_EDITION: "true"

steps:
# --- Step 1: Build and Push Core Services Only ---

# --- Core Service Builds (Standard Edition) ---

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build proxy-gateway'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_PROXY_GATEWAY_STANDARD}" = "true" ]; then
        echo "Building proxy-gateway for Standard Edition..."
        docker build --no-cache \
          -f 'k8s/proxy-gateway/Dockerfile' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-proxy-gateway-standard:latest" \
          'k8s/proxy-gateway'
      else
        echo "Skipping proxy-gateway build (disabled)"
      fi
  dir: '.'

- name: 'gcr.io/cloud-builders/docker'
  id: 'Push proxy-gateway'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_PROXY_GATEWAY_STANDARD}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-proxy-gateway-standard:latest"
      else
        echo "Skipping proxy-gateway push (build disabled)"
      fi
  waitFor: ['Build proxy-gateway']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build ai-optimizer'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_AI_OPTIMIZER_STANDARD}" = "true" ]; then
        echo "Building ai-optimizer for Standard Edition..."
        docker build --no-cache \
          -f 'k8s/ai-optimizer/Dockerfile' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-ai-optimizer-standard:latest" \
          'k8s/ai-optimizer'
      else
        echo "Skipping ai-optimizer build (disabled)"
      fi
  dir: '.'

- name: 'gcr.io/cloud-builders/docker'
  id: 'Push ai-optimizer'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_AI_OPTIMIZER_STANDARD}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-ai-optimizer-standard:latest"
      else
        echo "Skipping ai-optimizer push (build disabled)"
      fi
  waitFor: ['Build ai-optimizer']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build dashboard-api'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_DASHBOARD_API_STANDARD}" = "true" ]; then
        echo "Building dashboard-api for Standard Edition..."
        docker build --no-cache \
          -f 'k8s/dashboard-api/Dockerfile' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-dashboard-api-standard:latest" \
          'k8s/dashboard-api'
      else
        echo "Skipping dashboard-api build (disabled)"
      fi
  dir: '.'

- name: 'gcr.io/cloud-builders/docker'
  id: 'Push dashboard-api'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_DASHBOARD_API_STANDARD}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-dashboard-api-standard:latest"
      else
        echo "Skipping dashboard-api push (build disabled)"
      fi
  waitFor: ['Build dashboard-api']

# Build Standard Frontend (Simplified)
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build standard-frontend'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_FRONTEND_STANDARD}" = "true" ]; then
        echo "Building simplified frontend for Standard Edition..."
        # Build with standard-specific Dockerfile (completely separate from enterprise)
        docker build \
          --no-cache \
          -f k8s/frontend/Dockerfile.standard \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-frontend-standard:latest" \
          'k8s/frontend'
      else
        echo "Skipping frontend build (disabled)"
      fi
  dir: '.'
  waitFor: ['Build dashboard-api']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Push standard-frontend'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_FRONTEND_STANDARD}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-frontend-standard:latest"
      else
        echo "Skipping frontend push (build disabled)"
      fi
  waitFor: ['Build standard-frontend']

# --- Landing Page Build ---

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build landing-page'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_LANDING_PAGE}" = "true" ]; then
        echo "Building landing page..."
        docker build --no-cache \
          -f 'k8s/landing-page/Dockerfile' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-landing-page:latest" \
          'k8s/landing-page'
      else
        echo "Skipping landing page build (disabled)"
      fi
  dir: '.'

- name: 'gcr.io/cloud-builders/docker'
  id: 'Push landing-page'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_LANDING_PAGE}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-landing-page:latest"
      else
        echo "Skipping landing page push (build disabled)"
      fi
  waitFor: ['Build landing-page']

# --- Policy Manager Build ---

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build policy-manager'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      echo "Building policy manager..."
      docker build --no-cache \
        -f 'k8s/policy-manager/Dockerfile' \
        -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-policy-manager:latest" \
        'k8s/policy-manager'
  dir: '.'

- name: 'gcr.io/cloud-builders/docker'
  id: 'Push policy-manager'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-policy-manager:latest"
  waitFor: ['Build policy-manager']

# --- Kubernetes Deployment ---

# Get GKE Credentials
- name: 'gcr.io/cloud-builders/gcloud'
  id: 'Get GKE Credentials'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      gcloud container clusters get-credentials "${_GKE_CLUSTER_NAME}" --region "${_GCP_REGION}" --project "${_GCP_PROJECT_ID}"
  waitFor:
    - 'Push proxy-gateway'
    - 'Push ai-optimizer'
    - 'Push dashboard-api'
    - 'Push standard-frontend'
    - 'Push landing-page'
    - 'Push policy-manager'

# Make standard deployment script executable
- name: 'bash'
  id: 'Make Standard Script Executable'
  args: ['chmod', '+x', 'k8s/standard-edition/deploy-standard.sh', 'k8s/scripts/update-standard-external-services.sh']
  dir: '.'
  waitFor: ['Get GKE Credentials']

# Deploy Standard Edition Services
- name: 'gcr.io/cloud-builders/kubectl'
  id: 'Deploy Standard Edition'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      # Install gettext for envsubst
      echo "📦 Installing required packages..."
      apt-get update && apt-get install -y gettext-base

      # Export environment variables for standard deployment
      export GCP_PROJECT_ID="${_GCP_PROJECT_ID}"
      export GCP_REGION="${_GCP_REGION}"
      export GKE_CLUSTER_NAME="${_GKE_CLUSTER_NAME}"
      export ARTIFACT_REGISTRY_REPO="${_ARTIFACT_REGISTRY_REPO}"
      export BUILD_ID="${_BUILD_ID}"

      # Export standard-specific image names
      export PROXY_GATEWAY_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-proxy-gateway-standard:latest"
      export AI_OPTIMIZER_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-ai-optimizer-standard:latest"
      export DASHBOARD_API_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-dashboard-api-standard:latest"
      export FRONTEND_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-frontend-standard:latest"
      export POLICY_MANAGER_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-policy-manager:latest"

      # Execute standard deployment script with error handling
      if [ "${_DEPLOY_STANDARD_EDITION}" = "true" ]; then
        echo "🚀 Starting AI Operations Hub Standard Edition deployment..."
        if ./k8s/standard-edition/deploy-standard.sh; then
          echo "✅ Deployment completed successfully!"
        else
          echo "❌ Deployment encountered issues. Check the logs above."
          echo "📊 Current cluster status:"
          kubectl get all -n standard-edition
          exit 1
        fi
      else
        echo "⏭️  Skipping deployment (disabled)"
        echo "📦 Images built and pushed successfully"
      fi
  dir: '.'
  waitFor: ['Make Standard Script Executable']


