#!/usr/bin/env python3
"""
Quick test script to verify endpoint routing
"""
import requests
import json

BASE_URL = "https://scale-llm.com"

def test_endpoint(url, description):
    """Test an endpoint and print results"""
    print(f"\n🔍 Testing: {description}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ Success - Response type: {type(data)}")
                if isinstance(data, list):
                    print(f"   Items count: {len(data)}")
                elif isinstance(data, dict):
                    print(f"   Keys: {list(data.keys())[:5]}...")  # Show first 5 keys
            except:
                print(f"✅ Success - Response length: {len(response.text)} chars")
        else:
            print(f"❌ Failed - {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error: {e}")

def main():
    print("🚀 Testing AI Cost-Performance Optimizer Endpoints")
    print("=" * 60)
    
    # Test planning endpoints
    test_endpoint(f"{BASE_URL}/v1/goals", "Planning Service - Direct Goals")
    test_endpoint(f"{BASE_URL}/api/planning/v1/goals", "Planning Service - Via API Route")
    
    # Test evaluation endpoints
    test_endpoint(f"{BASE_URL}/analytics/models", "Evaluation Service - Direct Analytics")
    test_endpoint(f"{BASE_URL}/api/evaluation/analytics/models", "Evaluation Service - Via API Route")
    
    # Test policy manager endpoints
    test_endpoint(f"{BASE_URL}/api/prompts", "Policy Manager - Prompts")
    test_endpoint(f"{BASE_URL}/api/prompts/analytics", "Policy Manager - Prompt Analytics")
    
    # Test basic proxy
    test_endpoint(f"{BASE_URL}/v1/models", "Proxy Gateway - Models")
    
    print("\n" + "=" * 60)
    print("✅ Endpoint testing complete!")

if __name__ == "__main__":
    main()
